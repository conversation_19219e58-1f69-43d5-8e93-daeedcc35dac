# FramePack Batch GUI Drag and Drop Fix

This document explains the fixes applied to the FramePack Batch GUI to improve drag and drop functionality.

## Overview

The drag and drop functionality in the FramePack GUI has been fixed to allow users to easily drag files from their file explorer and drop them into the application. This fix applies to both the main GUI (`framepack_gui.py`) and the batch launchers (`framepack_batch_gui.bat` and `launch_framepack_gui.bat`).

## Changes Made

The following changes were made to fix the drag and drop functionality:

1. **Simplified Widget Hierarchy**: The widget hierarchy was simplified to match the working test script, using a frame with a visible border to contain the listbox.

2. **Improved Event Binding**: The drag and drop events are now properly bound to the key widgets (listbox and its container frame).

3. **Enhanced Visual Feedback**: Visual feedback during drag operations has been improved, making it clearer to the user when they're over a valid drop target.

4. **Comprehensive Error Handling**: Better error handling has been added to the drag and drop operations, with clear error messages and proper cleanup.

## How to Use

The drag and drop functionality works with both launchers:

1. **Using framepack_batch_gui.bat**:
   - Double-click `framepack_batch_gui.bat` to open the GUI
   - Drag image files from your file explorer and drop them onto the file list area
   - The files will be added to the list and ready for processing

2. **Using launch_framepack_gui.bat**:
   - Double-click `launch_framepack_gui.bat` to open the GUI
   - Drag image files from your file explorer and drop them onto the file list area
   - The files will be added to the list and ready for processing

## Debugging

If you encounter any issues with the drag and drop functionality, you can use the debug launcher:

```
run_framepack_gui_debug.bat
```

This will start the FramePack GUI with debugging enabled, showing detailed information about drag and drop events in the console.

## Supported File Types

The following image file types are supported for drag and drop:
- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- WebP (.webp)

## Technical Details

The drag and drop functionality is implemented using the `tkinterdnd2` library, which provides a bridge between Tkinter and the operating system's drag and drop functionality. The key components are:

1. **Widget Creation**:
```python
# Create a frame with a visible border for the listbox
self.listbox_frame = ttk.Frame(files_list_frame, borderwidth=2, relief="solid")
self.listbox_frame.pack(fill=tk.BOTH, expand=True)

# Create the listbox
self.files_listbox = tk.Listbox(self.listbox_frame, width=50, height=5)
self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)
```

2. **Event Registration**:
```python
# Register the listbox as a drop target
self.files_listbox.drop_target_register(DND_FILES)
self.files_listbox.dnd_bind('<<Drop>>', self.drop_files)
self.files_listbox.dnd_bind('<<DragEnter>>', self.drag_enter)
self.files_listbox.dnd_bind('<<DragLeave>>', self.drag_leave)

# Also register the listbox frame
self.listbox_frame.drop_target_register(DND_FILES)
self.listbox_frame.dnd_bind('<<Drop>>', self.drop_files)
self.listbox_frame.dnd_bind('<<DragEnter>>', self.drag_enter)
self.listbox_frame.dnd_bind('<<DragLeave>>', self.drag_leave)
```

3. **Visual Feedback**:
```python
# Change appearance to indicate it's a drop target
self.files_listbox.config(background="#e0f0ff")  # Light blue background
self.listbox_frame.config(relief="groove", borderwidth=3)
self.drag_drop_label.config(text="Drop files here!", foreground="#0066cc")
```

## Troubleshooting

If drag and drop is not working:

1. Make sure `tkinterdnd2` is installed: `pip install tkinterdnd2`
2. Check the console for any error messages
3. Try using the "Add Files..." button as an alternative
4. Make sure you're dragging valid image files (jpg, jpeg, png, bmp, webp)
5. Run `run_framepack_gui_debug.bat` to see detailed debugging information
