# FramePack GUI Config Loading Feature

## Overview

The FramePack GUI now supports loading configuration files via command line arguments, enabling automated batch processing and custom settings management.

## New Command Line Arguments

### `--config <path>`
Load settings from a JSON configuration file.

```bash
python framepack_gui.py --config path/to/config.json
```

### `--iterations <number>`
Set the number of iterations for batch processing (1-100).

```bash
python framepack_gui.py --iterations 5
```

### `--auto-run`
Automatically start batch processing after loading the GUI.

```bash
python framepack_gui.py --auto-run
```

## Combined Usage Examples

### Basic config loading:
```bash
python framepack_gui.py --config my_settings.json
```

### Config with custom iterations:
```bash
python framepack_gui.py --config my_settings.json --iterations 10
```

### Full automated processing:
```bash
python framepack_gui.py --config my_settings.json --iterations 5 --auto-run
```

### Using the batch file:
```bash
launch_framepack_gui.bat --config my_settings.json --iterations 3 --auto-run
```

## Special Behavior: framepack_default_settings.json

When loading a config file named `framepack_default_settings.json`, the system will automatically:

1. Load the config file from the specified path
2. Look for `quick_list.json` in the same directory as the config file
3. Load the quick prompts from that directory instead of the default location

### Example:
```bash
# This will load both the config and quick_list.json from /path/to/configs/
python framepack_gui.py --config /path/to/configs/framepack_default_settings.json
```

## Configuration File Format

The config file should be a JSON file containing any of the FramePack settings:

```json
{
    "input_dir": "my_input",
    "output_dir": "my_output", 
    "fallback_prompt": "My default prompt",
    "seed": 42,
    "video_length": 5.0,
    "steps": 25,
    "distilled_cfg": 10.0,
    "iterations": 3,
    "use_teacache": true,
    "randomize_order": false,
    "selected_files": [
        "path/to/image1.jpg",
        "path/to/image2.png"
    ]
}
```

## Quick List File Format

The `quick_list.json` file should contain an array of prompt strings:

```json
{
    "prompts": [
        "A character dancing gracefully",
        "Simple body movements",
        "Elegant motion sequence"
    ]
}
```

## Auto-Run Behavior

When `--auto-run` is specified:

1. The GUI loads normally with the specified config
2. After 1 second delay (to ensure GUI is ready), it automatically:
   - Adds current settings to the queue (if files are available)
   - Starts queue processing
   - Begins batch generation

## Use Cases

### 1. Automated Processing Pipeline
```bash
# Set up a config with specific settings and file list
launch_framepack_gui.bat --config production_settings.json --auto-run
```

### 2. Different Project Configurations
```bash
# Load different configs for different projects
launch_framepack_gui.bat --config project_a_settings.json
launch_framepack_gui.bat --config project_b_settings.json
```

### 3. Batch Processing with Custom Iterations
```bash
# Override iterations from command line
launch_framepack_gui.bat --config base_settings.json --iterations 10
```

## Notes

- The `--iterations` argument overrides any iterations setting in the config file
- All settings from the config file are applied when the GUI starts
- The auto-run feature requires either files in the selected_files list or files to be added to the batch queue
- The GUI will show status messages indicating successful config loading
- If config loading fails, the GUI will start with default settings

## Error Handling

- If the config file doesn't exist, an error message is displayed and default settings are used
- If the config file contains invalid JSON, an error message is displayed
- If quick_list.json is not found in the config directory, the default quick list is used
- Missing settings in the config file will use default values
