# FramePack Batch Processing, Queue Management, and Prompt Chain System Documentation

This document captures the current implementation of FramePack's batch processing, job queue management, iteration handling, and prompt chain functionality as of 2025-06-18.

## 1. Batch Processing System

### 1.1 Core Batch Scripts

The system uses multiple batch processing scripts:

- **`batch.py`** - Standard FramePack batch processing
- **`batch_f1.py`** - F1 model batch processing  
- **`batch_f1_video.py`** - F1 video-to-video processing
- **`batch_f1_lock.py`** - F1 routing script that determines which F1 script to use

### 1.2 Input Processing Modes

The batch system supports multiple input modes through command line arguments:

```python
# Individual files
--files file1.png file2.png

# File list (text file with file paths)
--file-list files.txt

# URL list (text file with image URLs)  
--url-list urls.txt

# Combined list (mixed files and URLs)
--combined-list mixed.txt

# Unified list (directories, files, and URLs with DIR: prefix for directories)
--unified-list unified.txt
```

### 1.3 Batch Processing Loop

<augment_code_snippet path="batch_f1.py" mode="EXCERPT">
````python
# Process each image
for img_idx, image_path in enumerate(image_files):
    print(f"\nProcessing image {img_idx + 1}/{len(image_files)}: {image_path}")

    # Check for stop queue flag before processing each image (terminates entire batch)
    stop_queue_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_queue.flag")
    if os.path.exists(stop_queue_flag_path):
        print(f"\n⚠️ Stop queue flag detected. Terminating entire batch process...")
        # Remove the stop queue flag file
        try:
            os.remove(stop_queue_flag_path)
            print(f"Removed stop queue flag file: {stop_queue_flag_path}")
        except Exception as e:
            print(f"Error removing stop queue flag file: {e}")
        break
````
</augment_code_snippet>

## 2. Job Queue System

### 2.1 Queue Data Structure

The GUI maintains a job queue using a simple list structure:

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
# Queue system variables
self.job_queue = []  # List to store queued jobs
self.current_job = None  # Currently running job
self.current_job_iteration = 0  # Current iteration of the current job
self.is_processing_queue = False  # Flag to indicate if queue is being processed
self.iterations_var = tk.IntVar(value=1)  # Number of iterations for the current job
````
</augment_code_snippet>

### 2.2 Job Structure

Each job in the queue contains all necessary settings:

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
job = {
    "selected_files": self.selected_files.copy(),
    "input_mode": self.input_mode.get(),
    "fallback_prompt": self.fallback_prompt.get(),
    "seed": self.seed.get(),
    "video_length": self.video_length.get(),
    "steps": self.steps.get(),
    "distilled_cfg": self.distilled_cfg.get(),
    "flow_shift": self.flow_shift.get(),
    "gpu_memory": self.gpu_memory.get(),
    "use_teacache": self.use_teacache.get(),
    "mp4_crf": self.mp4_crf.get(),
    "latent_window_size": self.latent_window_size.get(),
    "overwrite": self.overwrite.get(),
    "fix_encoding": self.fix_encoding.get(),
    "use_prompt_list_file": self.use_prompt_list_file.get(),
    "prompt_list_file": self.prompt_list_file.get(),
    "copy_to_input": self.copy_to_input.get(),
    "allow_duplicates": self.allow_duplicates.get(),
    "apply_all_prompts": self.apply_all_prompts.get(),
    "iterations": self.iterations_var.get(),
    "file_trim_settings": self.file_trim_settings.copy()
}
````
</augment_code_snippet>

### 2.3 Queue Processing Logic

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
def start_queue_processing(self):
    """Start processing the queue"""
    if not self.job_queue:
        self.show_status("The queue is empty. Add jobs to the queue first.", "warning")
        return

    if self.is_processing_queue:
        self.show_status("The queue is already being processed.", "info")
        return

    # Start queue processing
    self.is_processing_queue = True
    self.current_job = None
    self.current_job_iteration = 0

    # Enable the Stop Queue button
    self.stop_queue_button.config(state="normal")

    # Update the queue display
    self.update_queue_display()

    # Start processing the first job
    self.process_next_queue_item()

    self.show_status("Queue processing has started.", "success")
````
</augment_code_snippet>

## 3. Iteration System

### 3.1 Iteration Control

Each job can have multiple iterations controlled by the `iterations_var`:

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
# Iterations control
ttk.Label(queue_controls_frame, text="Iterations:").pack(side=tk.LEFT, padx=(0, 5))
ttk.Spinbox(queue_controls_frame, from_=1, to=100, textvariable=self.iterations_var, width=5).pack(side=tk.LEFT, padx=(0, 10))
````
</augment_code_snippet>

### 3.2 Iteration Processing

The system tracks current iteration and processes each iteration sequentially:

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
def process_next_queue_item(self):
    """Process the next item in the queue or next iteration of current job"""
    if not self.job_queue:
        # Queue is empty, stop processing
        self.is_processing_queue = False
        self.current_job = None
        self.current_job_iteration = 0
        self.stop_queue_button.config(state="disabled")
        self.update_queue_display()
        self.show_status("Queue processing completed.", "success")
        return

    # Check if we need to start a new job or continue with current job iterations
    if self.current_job is None:
        # Start the first job in the queue
        self.current_job = self.job_queue[0]
        self.current_job_iteration = 1
    else:
        # Check if we need to continue with more iterations of the current job
        if self.current_job_iteration < self.current_job['iterations']:
            # Continue with next iteration of current job
            self.current_job_iteration += 1
        else:
            # Current job is complete, remove it and start next job
            if self.current_job in self.job_queue:
                self.job_queue.remove(self.current_job)
            
            if self.job_queue:
                # Start next job
                self.current_job = self.job_queue[0]
                self.current_job_iteration = 1
            else:
                # No more jobs, stop processing
                self.is_processing_queue = False
                self.current_job = None
                self.current_job_iteration = 0
                self.stop_queue_button.config(state="disabled")
                self.update_queue_display()
                self.show_status("Queue processing completed.", "success")
                return
````
</augment_code_snippet>

## 4. Prompt Chain System

### 4.1 Prompt Chain Data Structure

Prompt chains are stored as a list of dictionaries:

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
def add_current_to_chain(self):
    """Add current settings to the prompt chain"""
    try:
        # Create a prompt chain item with current settings
        chain_item = {
            "prompt": self.fallback_prompt.get(),
            "seed": self.seed.get(),
            "video_length": self.video_length.get(),
            "steps": self.steps.get(),
            "distilled_cfg": self.distilled_cfg.get()
        }

        self.prompt_chain_data.append(chain_item)

        # Update the listbox display
        self.update_prompt_chain_display()

        print(f"Added prompt chain item: {chain_item['prompt'][:50]}...")
    except Exception as e:
        print(f"Error adding to prompt chain: {e}")
````
</augment_code_snippet>

### 4.2 Prompt Chain Display

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
def update_prompt_chain_display(self):
    """Update the prompt chain listbox display"""
    try:
        self.prompt_chain_listbox.delete(0, tk.END)

        for i, item in enumerate(self.prompt_chain_data):
            # Create a display string for the item
            prompt_preview = item['prompt'][:40] + "..." if len(item['prompt']) > 40 else item['prompt']
            display_text = f"{i+1}. {prompt_preview} (seed:{item['seed']}, len:{item['video_length']}s)"
            self.prompt_chain_listbox.insert(tk.END, display_text)
    except Exception as e:
        print(f"Error updating prompt chain display: {e}")
````
</augment_code_snippet>

### 4.3 Prompt Chain Processing Arguments

The batch scripts support prompt chain processing through command line arguments:

```bash
--prompt_chain_mode     # Enable prompt chain processing
--chain_index          # Current step in the chain (0-based)
--chain_total          # Total number of prompts in chain
--use_chain_input      # Use previous video's end frame as input
```

## 5. Batch Command Generation

### 5.1 Temporary Batch File Generation

The GUI generates temporary batch files for execution:

<augment_code_snippet path="temp_run_framepack.bat" mode="EXCERPT">
````batch
@echo off
setlocal enabledelayedexpansion
title FramePack Batch Processing
color 0A
echo ===============================================
echo             FRAMEPACK BATCH PROCESSING         
echo ===============================================
echo.
echo Running FramePack with the following settings:
echo.
echo Queue Job: 1/1
echo Iteration: 2/3
echo.
echo Mode:                Combined (Files and URLs)
echo Number of Items:     1 (1 files, 0 URLs)
echo File 1:             20250618_015243_250618_025622_105_1821_start.png
echo Output Directory:   A:\AI\FramePack\outputs
````
</augment_code_snippet>

### 5.2 Command Execution

The batch file activates the virtual environment and executes the Python script:

<augment_code_snippet path="temp_run_framepack.bat" mode="EXCERPT">
````batch
echo Virtual environment activated successfully.
echo.
echo Running command: A:\AI\FramePack\venv\Scripts\python.exe batch_f1_lock.py --unified-list "temp_combined_list.txt" --output_dir "A:\AI\FramePack\outputs" --prompt "..." --seed -1 --video_length 3.0 --steps 25 --distilled_cfg 10.0 --flow_shift 0.0 --gpu_memory 6.0 --mp4_crf 17 --latent_window_size 9 --use_teacache --no_image_prompt --no_fix_encoding --no_copy_to_input --apply_all_prompts --lora_file_1 "lora\fmachine-deepthroat-180epo.safetensors" --lora_multiplier_1 1.0 --job_id 20250618_105834
echo.

call A:\AI\FramePack\venv\Scripts\python.exe batch_f1_lock.py [same arguments]
set COMMAND_RESULT=%ERRORLEVEL%
````
</augment_code_snippet>

## 6. Stop Mechanisms

### 6.1 Stop Flag System

The system uses flag files for stopping operations:

- **`stop_framepack.flag`** - Legacy stop flag for backward compatibility
- **`stop_queue.flag`** - Terminates entire batch process

<augment_code_snippet path="batch_f1.py" mode="EXCERPT">
````python
# Check for stop queue flag before processing each image (terminates entire batch)
stop_queue_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_queue.flag")
if os.path.exists(stop_queue_flag_path):
    print(f"\n⚠️ Stop queue flag detected. Terminating entire batch process...")
    # Remove the stop queue flag file
    try:
        os.remove(stop_queue_flag_path)
        print(f"Removed stop queue flag file: {stop_queue_flag_path}")
    except Exception as e:
        print(f"Error removing stop queue flag file: {e}")
    break
````
</augment_code_snippet>

### 6.2 GUI Stop Controls

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
def stop_queue(self):
    """Stop the current generation and cancel all queue items"""
    try:
        # Create a stop flag file that batch.py will check for
        stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
        with open(stop_flag_path, 'w') as f:
            f.write(f"Stop queue requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")

        # Update button states
        self.stop_button.config(state="disabled")
        self.stop_queue_button.config(state="disabled")

        # Cancel any scheduled safety stop
        if hasattr(self, 'safety_stop_job') and self.safety_stop_job is not None:
            self.root.after_cancel(self.safety_stop_job)
            self.safety_stop_job = None
            print("Cancelled scheduled safety stop")
````
</augment_code_snippet>

## 7. Queue Display and Status

### 7.1 Queue Status Display

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
def update_queue_display(self):
    """Update the queue listbox and status label"""
    # Clear the listbox
    self.queue_listbox.delete(0, tk.END)

    # Add each job to the listbox
    for i, job in enumerate(self.job_queue):
        # Create a description for the job
        desc = f"Job {i+1}: {job['input_mode'].title()} mode"
        
        # Add file count info
        if job['selected_files']:
            desc += f" - {len(job['selected_files'])} item(s)"

        # Add iterations info
        desc += f" - {job['iterations']} iteration(s)"

        # Highlight the current job
        if self.is_processing_queue and self.current_job and job == self.current_job:
            desc = f"▶ {desc} (Current)"

        # Add to listbox
        self.queue_listbox.insert(tk.END, desc)

    # Update status label
    if not self.job_queue:
        self.queue_status_label.config(text="Queue: Empty")
    else:
        self.queue_status_label.config(text=f"Queue: {len(self.job_queue)} job(s)")
````
</augment_code_snippet>

## 8. Key Features Summary

### 8.1 Current Capabilities

1. **Multiple Input Modes**: Files, URLs, combined lists, unified lists with directory support
2. **Job Queue System**: Add multiple jobs with different settings to a processing queue
3. **Iteration Support**: Each job can run multiple iterations with the same settings
4. **Prompt Chains**: Sequential prompt execution using previous video end frames as input
5. **Stop Controls**: Multiple stop mechanisms (stop current, stop queue, stop after current)
6. **Progress Tracking**: Real-time display of queue progress and current job status
7. **Persistent Settings**: Each job stores complete settings independently
8. **Virtual Environment**: All processing runs in isolated Python venv

### 8.2 Processing Flow

1. User configures settings and adds jobs to queue
2. Queue processing starts with first job
3. Each job processes all selected files/URLs
4. Multiple iterations of same job run sequentially
5. Next job in queue starts after current job completes
6. Stop flags can interrupt processing at various levels
7. Temporary files and progress tracking maintained throughout

This documentation captures the current state of the batch processing, queue management, iteration handling, and prompt chain systems as implemented in FramePack.

## 9. Detailed Code Implementations

### 9.1 Task Queue Class (task_queue.py)

The system includes a separate TaskQueue class for advanced queue management:

<augment_code_snippet path="task_queue.py" mode="EXCERPT">
````python
class TaskQueue:
    def __init__(self):
        self.queue: List[Task] = []
        self.completed_tasks: List[Task] = []
        self.current_task: Optional[Task] = None
        self.lock = threading.Lock()
        self.is_processing = False
        self.on_queue_update: Optional[Callable] = None

    def add_task(self, params: Dict[str, Any]) -> str:
        """Add a new task to the queue and return its ID"""
        with self.lock:
            task_id = str(uuid.uuid4())
            task = Task(id=task_id, params=params)
            self.queue.append(task)
            if self.on_queue_update:
                self.on_queue_update()
            return task_id
````
</augment_code_snippet>

### 9.2 Batch Processing Single Image Function

The core processing function handles individual image processing:

<augment_code_snippet path="batch_f1.py" mode="EXCERPT">
````python
@torch.no_grad()
def process_single_image(image_path, output_dir, prompt="", n_prompt="", seed=-1,
                         video_length=5.0, steps=25, gs=10.0, flow_shift=0.0, gpu_memory=6.0,
                         use_teacache=True, high_vram=False, latent_window_size=9,
                         text_encoder=None, text_encoder_2=None, tokenizer=None, tokenizer_2=None,
                         vae=None, feature_extractor=None, image_encoder=None, transformer=None,
                         fix_encoding=True, copy_to_input=True, mp4_crf=16, show_latent_preview=True,
                         temp_dir="temp", job_id=None, chain_index=None, chain_total=None, prompt_chain_mode=False,
                         # Text-to-video parameters
                         use_noise=False, pixel_trick=False,
                         lora_file=None, lora_multiplier=0.8, fp8_optimization=False):
    """Process a single image to generate a video using FramePack F1"""
````
</augment_code_snippet>

### 9.3 Unified List Processing

The system supports complex input processing including directories, files, and URLs:

<augment_code_snippet path="batch_f1.py" mode="EXCERPT">
````python
# Process unified list (directories, files, and URLs) from --unified-list parameter
if args.unified_list:
    if os.path.exists(args.unified_list):
        print(f"Reading unified list from {args.unified_list}")
        try:
            # Ensure the temp directory exists
            os.makedirs(args.temp_dir, exist_ok=True)

            with open(args.unified_list, 'r', encoding='utf-8') as f:
                for line in f:
                    item = line.strip()
                    if not item:
                        continue

                    # Check if it's a directory, URL, or a file path
                    if item.startswith('DIR:'):
                        # It's a directory, extract the path and process all images in it
                        dir_path = item[4:]  # Remove the DIR: prefix
                        print(f"Processing directory from unified list: {dir_path}")
````
</augment_code_snippet>

### 9.4 GUI Run FramePack Method

The main GUI method that handles different processing modes:

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
def run_framepack(self):
    current_mode = self.input_mode.get()

    # Check if we have files/URLs selected in files, URLs, or combined mode
    if (current_mode == "files" or current_mode == "urls" or current_mode == "combined") and not self.selected_files:
        messagebox.showerror("Error", "No items selected. Please select at least one item to process.")
        return

    # Remove any existing stop flag file
    stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
    if os.path.exists(stop_flag_path):
        try:
            os.remove(stop_flag_path)
            print(f"Removed existing stop flag file: {stop_flag_path}")
        except Exception as e:
            print(f"Warning: Could not remove existing stop flag file: {e}")

    # Build command
    cmd = [sys.executable, "batch.py"]
````
</augment_code_snippet>

### 9.5 Prompt Chain Management Methods

<augment_code_snippet path="framepack_gui.py" mode="EXCERPT">
````python
def remove_from_chain(self):
    """Remove selected item from the prompt chain"""
    try:
        selected_indices = self.prompt_chain_listbox.curselection()
        if not selected_indices:
            return

        # Remove in reverse order to avoid index shifting
        for index in sorted(selected_indices, reverse=True):
            if 0 <= index < len(self.prompt_chain_data):
                removed_item = self.prompt_chain_data.pop(index)
                print(f"Removed prompt chain item: {removed_item['prompt'][:50]}...")

        # Update the listbox display
        self.update_prompt_chain_display()
    except Exception as e:
        print(f"Error removing from prompt chain: {e}")

def clear_chain(self):
    """Clear all items from the prompt chain"""
    try:
        self.prompt_chain_data.clear()
        self.update_prompt_chain_display()
        print("Cleared all prompt chain items")
    except Exception as e:
        print(f"Error clearing prompt chain: {e}")
````
</augment_code_snippet>

### 9.6 Batch Processing Settings Display

The batch scripts provide detailed settings output:

<augment_code_snippet path="batch_f1.py" mode="EXCERPT">
````python
# Print batch processing settings
print("\nProcessing Settings:")
if args.unified_list:
    print(f"  Mode: Unified (Directories, Files, and URLs)")
    print(f"  Number of Items: {len(image_files)}")
elif args.combined_list:
    print(f"  Mode: Combined (Files and URLs)")
    print(f"  Number of Items: {len(image_files)}")
elif args.url_list:
    print(f"  Mode: URL List")
    print(f"  Number of URLs: {len(image_files)}")
elif args.files or args.file_list:
    print(f"  Mode: Individual Files")
    print(f"  Number of Files: {len(image_files)}")
else:
    print(f"  Mode: Batch Directory")
    print(f"  Input Directory: {args.input_dir}")
print(f"  Output Directory: {args.output_dir}")
````
</augment_code_snippet>

### 9.7 Stop Flag Checking in Processing Loop

<augment_code_snippet path="batch.py" mode="EXCERPT">
````python
# Check for legacy stop flag for backward compatibility
stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
if os.path.exists(stop_flag_path):
    print(f"\n⚠️ Legacy stop generation flag detected. Stopping batch processing...")
    # Remove the stop flag file
    try:
        os.remove(stop_flag_path)
        print(f"Removed stop flag file: {stop_flag_path}")
    except Exception as e:
        print(f"Error removing stop flag file: {e}")
    break

print(f"\n[{i+1}/{len(image_files)}] Processing {image_path}")
````
</augment_code_snippet>

## 10. File Structure and Dependencies

### 10.1 Key Files

- **`framepack_gui.py`** - Main GUI application with queue management
- **`batch.py`** - Standard FramePack batch processing script
- **`batch_f1.py`** - F1 model batch processing script
- **`batch_f1_lock.py`** - F1 routing script
- **`batch_f1_video.py`** - F1 video-to-video processing
- **`task_queue.py`** - Advanced task queue management class
- **`temp_run_framepack.bat`** - Generated temporary batch execution file
- **`temp_combined_list.txt`** - Generated temporary file list for processing

### 10.2 Signal Files

- **`stop_framepack.flag`** - Legacy stop signal for backward compatibility
- **`stop_queue.flag`** - Modern stop signal that terminates entire batch
- **`framepack_completed.signal`** - Completion signal file
- **`framepack_progress.txt`** - Progress tracking file for GUI updates

### 10.3 Virtual Environment Integration

All processing runs within a Python virtual environment:

```batch
# Activate virtual environment
call A:\AI\FramePack\venv\Scripts\activate.bat
if errorlevel 1 (
    echo Failed to activate virtual environment
    pause
    exit /b 1
)
echo Virtual environment activated successfully.
```

This comprehensive documentation preserves the current implementation details for future reference and restoration if needed.
