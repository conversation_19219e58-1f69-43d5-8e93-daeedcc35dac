# FramePack Drag and Drop Troubleshooting Guide

This document provides troubleshooting steps for fixing drag and drop functionality in the FramePack GUI.

## Symptoms

- The drag and drop functionality doesn't work
- The GUI shows a message "Drag and drop not available" in red
- When dragging files over the listbox, you see a "no-drop" cursor

## Troubleshooting Steps

### 1. Verify tkinterdnd2 Installation

First, make sure the tkinterdnd2 package is properly installed:

```
python -c "import tkinterdnd2; print('tkinterdnd2 is installed')"
```

If you see an error, install tkinterdnd2:

```
pip install tkinterdnd2
```

### 2. Test with Standalone Scripts

We've provided several test scripts to help isolate the issue:

- **test_tkdnd.py**: A simple test to verify that tkinterdnd2 is working
- **standalone_dnd_test.py**: A standalone implementation of the drag and drop functionality
- **test_listbox_dnd.py**: The original test script for listbox drag and drop

Run these scripts using the provided batch files:

- **run_standalone_test.bat**: Runs the standalone test
- **run_framepack_debug.bat**: Runs a debug version of the FramePack GUI
- **run_framepack_fixed.bat**: Runs the FramePack GUI without using a virtual environment

### 3. Check for Virtual Environment Issues

The issue might be related to the virtual environment. Try running the application directly with Python:

```
python framepack_gui.py
```

### 4. Check Python Version Compatibility

Make sure you're using a compatible version of Python. tkinterdnd2 works best with Python 3.7-3.10.

### 5. Check for Path Issues

The issue might be related to path handling. Try using absolute paths when importing tkinterdnd2:

```python
import sys
sys.path.append("path/to/site-packages")
from tkinterdnd2 import TkinterDnD, DND_FILES
```

### 6. Check for Widget Hierarchy Issues

The drag and drop functionality relies on proper widget hierarchy. Make sure the listbox is properly contained within its parent frame.

### 7. Check for Event Binding Issues

Make sure the drag and drop events are properly bound to the widgets:

```python
self.files_listbox.drop_target_register(DND_FILES)
self.files_listbox.dnd_bind('<<Drop>>', self.drop_files)
self.files_listbox.dnd_bind('<<DragEnter>>', self.drag_enter)
self.files_listbox.dnd_bind('<<DragLeave>>', self.drag_leave)
```

## Solutions

### Solution 1: Use the Fixed Batch File

Run the application using the fixed batch file:

```
run_framepack_fixed.bat
```

This batch file runs the application directly with Python, bypassing the virtual environment.

### Solution 2: Install tkinterdnd2 in the Virtual Environment

If you're using a virtual environment, make sure tkinterdnd2 is installed in that environment:

```
venv\Scripts\activate
pip install tkinterdnd2
```

### Solution 3: Use the Standalone Test

If the drag and drop functionality still doesn't work, use the standalone test to verify that tkinterdnd2 is working correctly:

```
run_standalone_test.bat
```

If the standalone test works but the main application doesn't, there might be an issue with the widget hierarchy or event binding in the main application.

## Additional Resources

- [TkinterDnD2 Documentation](https://github.com/pmgagne/tkinterdnd2)
- [Tkinter Documentation](https://docs.python.org/3/library/tkinter.html)
- [Python Virtual Environments](https://docs.python.org/3/tutorial/venv.html)
