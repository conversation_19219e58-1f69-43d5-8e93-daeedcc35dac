# Enhanced Stop Functionality for FramePack

## Overview

The FramePack GUI now includes comprehensive stop functionality that ensures all batch generation processes are completely terminated when using "Stop Queue" or "Skip Generation" buttons. This addresses the issue where background processes would continue running even after stop commands were issued.

## Key Improvements

### 1. Comprehensive Process Termination

**Stop Queue Button:**
- Immediately terminates ALL running FramePack generation processes
- Uses `psutil` for precise process identification and termination
- Falls back to system commands if `psutil` is unavailable
- Preserves the GUI while stopping background processes
- Automatically resets UI state after termination

**Skip Generation Button (Enhanced):**
- Now detects if it's the only generation in the queue
- If it's the only generation OR not processing a queue, it forcefully terminates all processes
- If there are multiple items in the queue, it gracefully skips to the next item
- Automatically applies UI reset after process termination

### 2. Signal Handling in Batch Scripts

All batch scripts (`batch.py`, `batch_f1.py`, `batch_f1_video.py`) now include:
- Signal handlers for immediate termination (SIGTERM, SIGINT, SIGQUIT)
- Global shutdown flag that's checked during generation loops
- Automatic cleanup of temporary files on termination
- CUDA cache clearing during shutdown

### 3. Multi-Level Process Detection

The termination system targets:
- `batch.py` processes (standard FramePack generation)
- `batch_f1.py` and `batch_f1_video.py` processes (F1 model generation)
- `batch_f1_lock.py` processes (F1 routing script)
- `temp_run_framepack.bat` processes (batch wrapper scripts)
- `cmd.exe` processes running FramePack batch files

**Important:** The GUI process (`framepack_gui.py`) is NEVER terminated, ensuring the interface remains responsive.

### 4. Comprehensive Cleanup

When processes are terminated, the system cleans up:
- `temp_run_framepack.bat` files
- `framepack_completed.signal` files
- `stop_framepack.flag` files
- `temp_command_result.txt` files
- Stale lock files
- Progress tracking files

### 5. Automatic UI Reset

After process termination:
- UI state is automatically reset and synchronized
- Backend state is updated to reflect the stopped processes
- Button states are restored to normal operation
- Status messages confirm successful termination

## Technical Implementation

### Process Termination Logic

```python
def terminate_all_framepack_processes(self):
    """Terminate all running FramePack generation processes immediately"""
    try:
        import psutil
        current_pid = os.getpid()  # Don't kill ourselves
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            cmdline_str = ' '.join(proc.info['cmdline'] or []).lower()
            
            # Target specific FramePack processes
            should_terminate = False
            if 'batch.py' in cmdline_str and 'framepack_gui.py' not in cmdline_str:
                should_terminate = True
            elif any(script in cmdline_str for script in ['batch_f1_lock.py', 'batch_f1_video.py']):
                should_terminate = True
            elif 'temp_run_framepack.bat' in cmdline_str:
                should_terminate = True
            
            if should_terminate:
                proc.terminate()
                # Force kill if graceful termination fails
                try:
                    proc.wait(timeout=2)
                except psutil.TimeoutExpired:
                    proc.kill()
    except ImportError:
        # Fallback to system commands
        self.terminate_processes_fallback()
```

### Signal Handling in Batch Scripts

```python
import signal
import sys
import atexit

shutdown_requested = False

def signal_handler(signum, frame):
    global shutdown_requested
    print(f"⚠️ Received termination signal {signum}. Initiating immediate shutdown...")
    shutdown_requested = True
    
    # Immediate cleanup
    cleanup_temp_files()
    clear_cuda_cache()
    sys.exit(1)

# Register signal handlers
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)
```

### Enhanced Stop Detection

```python
def callback(d):
    # Check for immediate shutdown signal first
    global shutdown_requested
    if shutdown_requested:
        raise StopGenerationRequestedException("Immediate shutdown requested")
    
    # Check for stop flag file
    if os.path.exists("stop_framepack.flag"):
        raise StopGenerationRequestedException("Stop generation requested by user")
```

## User Experience

### Stop Queue Button
1. Click "Stop Queue" → Immediate termination of all generation processes
2. Automatic UI reset after 2 seconds
3. Status message confirms successful termination
4. Ready to start new generations immediately

### Skip Generation Button
1. Click "Skip Generation" → Intelligent behavior based on queue state
2. Single generation: Complete termination (same as Stop Queue)
3. Multiple generations: Skip to next item in queue
4. Automatic UI reset for single generations

### No More Background Processes
- No more hidden Python processes consuming resources
- No more CUDA memory leaks from stopped generations
- No more need to manually kill processes in Task Manager
- Clean slate for each new generation session

## Compatibility

- Works on Windows (primary target) and Unix-like systems
- Graceful fallback if `psutil` is not available
- Compatible with all FramePack models (standard and F1)
- Preserves existing functionality while adding robust termination

## Testing

Run `test_stop_functionality.py` to verify the enhanced stop functionality:
- Tests process enumeration and termination logic
- Verifies signal handling capabilities
- Simulates GUI stop queue functionality
- Confirms cleanup procedures work correctly

The enhanced stop functionality ensures that when you click "Stop Queue" or "Skip Generation", all batch processes are completely shut down, freeing up system resources and ensuring a clean state for future generations.
