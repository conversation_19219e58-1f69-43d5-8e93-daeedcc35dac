# FramePack Installation Guide

This guide explains how to install the dependencies for FramePack based on your needs.

## Quick Start (Recommended)

For most users, install all dependencies with:

```bash
pip install -r requirements.txt
```

## Modular Installation

FramePack provides several requirements files for different use cases:

### 1. Minimal Installation (Core Functionality Only)

For basic batch processing without GUI:

```bash
pip install -r requirements-minimal.txt
```

This includes:
- Core ML/AI libraries (torch, diffusers, transformers)
- Essential image processing (opencv, pillow, numpy)
- Basic utilities

### 2. GUI Installation

For full GUI functionality, install minimal requirements first, then:

```bash
pip install -r requirements-minimal.txt
pip install -r requirements-gui.txt
```

This adds:
- Drag & drop support (tkinterdnd2)
- Video preview (tkvideoplayer, moviepy)
- Clipboard operations (pyperclip)
- Enhanced face detection (mediapipe, face-recognition)

### 3. Development Installation

For development work:

```bash
pip install -r requirements-minimal.txt
pip install -r requirements-gui.txt
pip install -r requirements-dev.txt
```

This adds:
- Web interface (gradio)
- Testing tools (pytest)
- Code formatting (black, flake8)
- Documentation tools (sphinx)

### 4. Optional Features

For enhanced functionality:

```bash
pip install -r requirements-optional.txt
```

This adds:
- Advanced video processing
- Memory optimization tools
- Additional image formats support

## Platform-Specific Notes

### Windows
- Make sure you have Visual C++ Build Tools installed for some packages
- For face-recognition, you may need to install dlib separately
- Use `python -m pip install` if `pip` command is not found

### macOS
- Install Xcode Command Line Tools: `xcode-select --install`
- For face-recognition, install cmake first: `brew install cmake`

### Linux
- Install system dependencies:
  ```bash
  sudo apt-get update
  sudo apt-get install python3-dev cmake libopenblas-dev liblapack-dev
  ```

## Troubleshooting

### Face Detection Issues
If face detection features don't work:

1. Run the face detection installer:
   ```bash
   python install_face_detection_deps.py
   ```

2. Or install manually:
   ```bash
   pip install mediapipe face-recognition
   ```

### Video Preview Issues
If video previews don't work in the GUI:

```bash
pip install tkvideoplayer moviepy
```

### CUDA/GPU Support
For GPU acceleration, install PyTorch with CUDA support:

```bash
# For CUDA 11.8
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# For CUDA 12.1
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

### Memory Issues
For large models or limited memory:

```bash
pip install psutil  # For memory monitoring
```

## Verification

After installation, verify everything works:

1. Test basic functionality:
   ```bash
   python batch.py --help
   ```

2. Test GUI (if installed):
   ```bash
   python framepack_gui.py
   ```

3. Test face detection (if installed):
   ```bash
   python test_face_detection.py
   ```

## Virtual Environment (Recommended)

It's recommended to use a virtual environment:

```bash
# Create virtual environment
python -m venv framepack_env

# Activate it
# Windows:
framepack_env\Scripts\activate
# macOS/Linux:
source framepack_env/bin/activate

# Install dependencies
pip install -r requirements.txt
```

## Docker Installation

For a containerized setup, see the Docker documentation (if available).

## Getting Help

If you encounter issues:

1. Check the troubleshooting section above
2. Ensure you have the latest Python version (3.8+)
3. Try installing in a fresh virtual environment
4. Check the GitHub issues for known problems
