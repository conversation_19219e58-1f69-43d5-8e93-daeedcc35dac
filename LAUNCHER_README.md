# FramePack Launcher

The FramePack Launcher is a unified tool that provides a convenient way to start FramePack with various input types.

## Features

The launcher supports three types of inputs:

1. **Image Files**: Drag and drop one or more image files onto the launcher
2. **Directories**: Drag and drop one or more directories containing image files
3. **URLs**: Drag and drop or paste URLs to online images

## Usage

### Basic Usage

Double-click `framepack_launcher.bat` to start FramePack with no inputs.

### With Image Files

Drag and drop one or more image files onto `framepack_launcher.bat` to load them into FramePack.

```
framepack_launcher.bat "C:\path\to\image1.jpg" "C:\path\to\image2.png"
```

### With Directories

Drag and drop one or more directories onto `framepack_launcher.bat` to load all image files from those directories.

```
framepack_launcher.bat "C:\path\to\image_folder"
```

### With URLs

Drag and drop or paste URLs to online images onto `framepack_launcher.bat` to download and load them into FramePack.

```
framepack_launcher.bat "https://example.com/image.jpg"
```

### Mixed Inputs

You can mix different input types in a single command:

```
framepack_launcher.bat "C:\path\to\image.jpg" "C:\path\to\folder" "https://example.com/image.png"
```

## Supported Image Formats

- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- WebP (.webp)

## Notes

- URLs must point directly to image files
- When using directories, only supported image formats will be loaded
- Downloaded images from URLs are saved to the temp directory
- The launcher requires an active internet connection to download images from URLs

## Troubleshooting

If you encounter issues:

1. Make sure Python is installed and in your PATH
2. Check that all required packages are installed
3. Verify that URLs point directly to image files
4. Check your internet connection when using URLs

For more help, please refer to the main FramePack documentation.
