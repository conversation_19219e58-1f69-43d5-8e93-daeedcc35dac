# LoRA Keywords Auto-Append Feature

## Overview

This feature automatically appends keywords from text files associated with LoRA files to the prompt during generation. This allows users to automatically include relevant style or content keywords without manually typing them each time.

## How It Works

1. **Text File Association**: For each LoRA file (e.g., `style.safetensors`), the system looks for a corresponding text file with the same name but `.txt` extension (e.g., `style.txt`).

2. **Keyword Reading**: The system reads the first line of the text file and treats it as keywords to append to the prompt.

3. **Automatic Appending**: When the "Append Keywords" setting is enabled, these keywords are automatically added to the end of the prompt with a ", " separator.

## User Interface

### New Setting
- **Location**: LoRA Settings section
- **Control**: Checkbox labeled "Auto-append keywords from LoRA .txt files"
- **Default**: Disabled (unchecked)
- **Tooltip**: "When enabled, automatically appends keywords from .txt files with the same name as LoRA files. For example, if using 'style.safetensors', keywords from 'style.txt' will be added to the prompt. Keywords are appended with ', ' separator at the end of the prompt during generation."

## File Structure

```
lora/
├── example_style.safetensors    # LoRA file
├── example_style.txt            # Keywords file (same name, .txt extension)
├── another_lora.safetensors     # Another LoRA file
└── another_lora.txt             # Its keywords file
```

## Example Usage

### Example 1: Basic Usage
- **LoRA File**: `artistic_style.safetensors`
- **Keywords File**: `artistic_style.txt` containing: `painting, painterly, brush strokes, artistic style, oil painting`
- **Original Prompt**: `a beautiful sunset`
- **Final Prompt**: `a beautiful sunset, painting, painterly, brush strokes, artistic style, oil painting`

### Example 2: Empty Prompt
- **Original Prompt**: *(empty)*
- **Final Prompt**: `painting, painterly, brush strokes, artistic style, oil painting`

### Example 3: Multiple LoRAs
- **LoRA 1**: `style.safetensors` with keywords: `painting, artistic`
- **LoRA 2**: `character.safetensors` with keywords: `anime, detailed face`
- **Original Prompt**: `a girl in a garden`
- **Final Prompt**: `a girl in a garden, painting, artistic, anime, detailed face`

## Technical Implementation

### Key Functions Added

1. **`get_lora_keywords()`**: Reads keywords from all enabled LoRA text files
2. **`append_keywords_to_prompt()`**: Appends keywords to the original prompt
3. **Integration**: Keywords are appended during command building, before prompt sanitization

### File Search Logic

The system searches for text files in the following order:
1. If LoRA file is in the `lora/` folder, looks for `lora/filename.txt`
2. If LoRA file has a full path, looks for `path/filename.txt`

### Settings Persistence

The "Append Keywords" setting is saved and loaded with other GUI settings:
- Saved in `framepack_default_settings.json`
- Included in prompt chain settings
- Preserved across GUI sessions

## Benefits

1. **Consistency**: Ensures relevant keywords are always included when using specific LoRAs
2. **Convenience**: No need to manually type the same keywords repeatedly
3. **Organization**: Keywords are stored alongside their corresponding LoRA files
4. **Flexibility**: Can be easily enabled/disabled per session
5. **Multi-LoRA Support**: Automatically combines keywords from multiple active LoRAs

## File Format

### Keywords Text File
- **Encoding**: UTF-8
- **Content**: First line contains comma-separated keywords
- **Example**:
  ```
  painting, painterly, brush strokes, artistic style, oil painting
  ```

### Notes
- Only the first line of the text file is read
- Keywords are appended exactly as written in the file
- Empty or missing text files are silently ignored
- The feature works with all three LoRA slots (LoRA 1, 2, and 3)

## Testing

A test script (`test_lora_keywords.py`) is included to demonstrate the functionality:

```bash
python test_lora_keywords.py
```

This script shows how the feature works with various prompt scenarios and when the setting is enabled/disabled.
