# LoRA Merger for FramePack

This tool allows you to permanently merge LoRA weights into FramePack model weights, creating a new model that doesn't require runtime LoRA loading. This can improve performance and simplify deployment.

## Features

- **Permanent LoRA Merging**: Merge LoRA weights directly into model weights
- **Multiple LoRA Support**: Merge up to 3 LoRAs simultaneously
- **Model Compatibility**: Works with both standard FramePack and F1 models
- **Format Support**: Supports Mu<PERSON>bi Tuner, Diffusion Pipeline, and HunyuanVideo LoRA formats
- **GUI Interface**: Easy-to-use graphical interface
- **Command Line**: Full command-line interface for automation
- **Smart Splitting**: Automatically splits large models into multiple files

## Supported Models

- `lllyasviel/FramePackI2V_HY` (Standard FramePack model)
- `lllyasviel/FramePack_F1_I2V_HY_20250503` (F1 FramePack model)

## Supported LoRA Formats

- **Musubi Tuner format** (`lora_unet_*`)
- **Diffusion pipeline format** (`diffusion_model.*`)
- **HunyuanVideo format** (`double_blocks.*`, `single_blocks.*`) - automatically converted

## Installation

No additional installation required! The merger uses the same dependencies as FramePack.

## Usage

### GUI Interface (Recommended)

1. **Run the GUI:**
   ```bash
   python lora_merger_gui.py
   ```
   Or double-click `lora_merger_gui.bat`

2. **Select Base Model:**
   - Choose between standard FramePack or F1 model

3. **Choose Merge Mode:**
   - **Single LoRA**: Merge one LoRA file
   - **Multiple LoRAs**: Merge up to 3 LoRA files

4. **Configure LoRA Settings:**
   - Browse and select LoRA files (.safetensors)
   - Adjust multiplier/strength (0.0 - 2.0, default: 0.8)

5. **Set Output Directory:**
   - Choose where to save the merged model

6. **Start Merge:**
   - Click "Start Merge" and wait for completion

### Command Line Interface

#### Single LoRA Merge
```bash
python lora_merger.py --model_name "lllyasviel/FramePackI2V_HY" --lora_file "my_lora.safetensors" --multiplier 0.8 --output_dir "merged_models"
```

#### Multiple LoRA Merge
```bash
python lora_merger.py --model_name "lllyasviel/FramePackI2V_HY" --lora_file_1 "style_lora.safetensors" --multiplier_1 0.8 --lora_file_2 "character_lora.safetensors" --multiplier_2 0.6 --output_dir "merged_models"
```

#### F1 Model Merge
```bash
python lora_merger.py --model_name "lllyasviel/FramePack_F1_I2V_HY_20250503" --lora_file "f1_lora.safetensors" --multiplier 0.8 --output_dir "merged_models"
```

## Command Line Arguments

### Required
- `--model_name`: Base model name (FramePackI2V_HY or FramePack_F1_I2V_HY_20250503)

### Single LoRA Options
- `--lora_file`: Path to LoRA file (.safetensors)
- `--multiplier`: LoRA strength (default: 0.8)

### Multiple LoRA Options
- `--lora_file_1/2/3`: Paths to LoRA files
- `--multiplier_1/2/3`: LoRA strengths (default: 0.8)

### General Options
- `--output_dir`: Output directory (default: "merged_models")
- `--force`: Overwrite existing merged models

## Output Structure

The merged model will be saved in the following structure:
```
merged_models/
└── lllyasviel_FramePackI2V_HY_my_lora_x0.8/
    ├── config.json
    ├── diffusion_pytorch_model.safetensors (or split files)
    ├── diffusion_pytorch_model.safetensors.index.json (if split)
    └── README.md
```

## Using Merged Models

### Option 1: Replace Original Model (Recommended)
1. Backup your original model cache
2. Copy merged model files to the original model location in HuggingFace cache
3. Use FramePack normally without LoRA parameters

### Option 2: Custom Path Loading
Modify your FramePack code to load from the custom merged model path:
```python
transformer = HunyuanVideoTransformer3DModelPacked.from_pretrained(
    '/path/to/merged_models/lllyasviel_FramePackI2V_HY_my_lora_x0.8',
    torch_dtype=torch.bfloat16
).cpu()
```

## Performance Benefits

- **Faster Loading**: No runtime LoRA merging required
- **Memory Efficiency**: Eliminates need to keep both base and LoRA weights in memory
- **Simplified Deployment**: Single model file instead of base + LoRA files
- **Consistent Results**: Eliminates potential runtime merging variations

## Important Notes

### Prerequisites
- Base model must be downloaded first (run FramePack at least once)
- LoRA files must be in .safetensors format
- Sufficient disk space for merged model (~8-12GB per model)

### Limitations
- Merged models cannot be "un-merged" - keep original files as backup
- Large models may be split into multiple files automatically
- Merging process requires significant RAM (recommend 32GB+ for large models)

### Best Practices
- Test merged models before deleting originals
- Use descriptive output directory names
- Keep multipliers between 0.5-1.0 for best results
- Backup original model cache before replacing

## Troubleshooting

### "Model not found in cache"
- Run FramePack first to download the base model
- Check that the model name is correct

### "Out of memory" errors
- Close other applications
- Use CPU-only mode (models are processed on CPU by default)
- Try merging one LoRA at a time

### "Permission denied" errors
- Run as administrator (Windows)
- Check file permissions
- Ensure output directory is writable

### Large file handling
- Models >4.5GB are automatically split into multiple files
- This is normal and the split models work identically to single files

## Examples

### Style Transfer LoRA
```bash
python lora_merger.py --model_name "lllyasviel/FramePackI2V_HY" --lora_file "anime_style.safetensors" --multiplier 0.7
```

### Character + Style Combination
```bash
python lora_merger.py --model_name "lllyasviel/FramePackI2V_HY" --lora_file_1 "character.safetensors" --multiplier_1 0.8 --lora_file_2 "style.safetensors" --multiplier_2 0.6
```

### F1 Model with Custom LoRA
```bash
python lora_merger.py --model_name "lllyasviel/FramePack_F1_I2V_HY_20250503" --lora_file "f1_enhancement.safetensors" --multiplier 0.9
```

## Support

For issues or questions:
1. Check the log output in the GUI
2. Verify all file paths are correct
3. Ensure sufficient disk space and memory
4. Try with a single LoRA first before multiple LoRAs

The merger tool uses the same LoRA utilities as the main FramePack application, ensuring full compatibility with your existing LoRA files.
