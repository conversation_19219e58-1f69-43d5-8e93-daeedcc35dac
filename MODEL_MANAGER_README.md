# FramePack Model Manager

A comprehensive GUI application for managing FramePack models, LoRA merging, and model organization. This tool combines model management with LoRA merging capabilities in a single, user-friendly interface.

## 🚀 Features

### Model Management
- **Visual Model Browser**: See all available models with size, type, and status
- **Model Backup & Restore**: Create and restore model backups safely
- **Model Import/Export**: Import external models and export merged models
- **Active Model Tracking**: Track which models are currently active
- **Model Installation**: Install merged models as active models

### LoRA Merging
- **Single & Multiple LoRA Support**: Merge 1-3 LoRAs simultaneously
- **Real-time Progress**: Live progress tracking and detailed logs
- **Format Compatibility**: Supports all LoRA formats (Musubi Tuner, Diffusion Pipeline, HunyuanVideo)
- **Strength Control**: Individual multiplier control for each LoRA (0.0-2.0)

### System Management
- **Path Configuration**: Manage HuggingFace cache and backup directories
- **System Information**: View detailed system and model status
- **Backup Management**: Organize and clean up model backups
- **Settings Persistence**: Save and restore application settings

## 📋 Requirements

- Python 3.8+
- All FramePack dependencies (torch, safetensors, etc.)
- tkinter (usually included with Python)
- Existing FramePack installation

## 🎯 Quick Start

### Launch the Application
```bash
python model_manager_gui.py
```
Or double-click `model_manager_gui.bat`

### First Time Setup
1. **Check Model Status**: Click "Refresh List" to scan for available models
2. **Configure Paths**: Go to Settings tab to verify HuggingFace cache location
3. **Create Backups**: Backup your original models before making changes

## 📖 User Guide

### Model Manager Tab

#### Model List
The main model list shows:
- **Name**: Model display name
- **Type**: Standard, F1, or Merged
- **Status**: Active, Available, or Not Downloaded
- **Size**: Total model size
- **Modified**: Last modification date

#### Model Actions
- **Refresh List**: Scan for new/changed models
- **Set as Active**: Mark a model as the active model for its type
- **Create Backup**: Create a timestamped backup of the selected model
- **Restore Backup**: Restore from a previous backup
- **Import Model**: Import a model from external directory
- **Export Model**: Export a merged model to external directory
- **Delete Model**: Delete a merged model (originals cannot be deleted)

#### Status Display
Shows currently active models:
- **Active FramePack Model**: Current standard model
- **Active F1 Model**: Current F1 model

### LoRA Merger Tab

#### Model Selection
- **Target Model**: Choose which model to merge LoRAs into
- **Output Directory**: Where to save merged models

#### Merge Modes

**Single LoRA Mode:**
- Select one LoRA file
- Set multiplier strength (0.0-2.0)
- Simple and fast

**Multiple LoRA Mode:**
- Select up to 3 LoRA files
- Individual strength control for each
- Sequential merging for complex combinations

#### Merge Process
1. Select target model and LoRA files
2. Configure multiplier values
3. Click "Start Merge"
4. Monitor progress in the log
5. Find merged model in output directory

#### Post-Merge Actions
- **Install Merged Model**: Replace original model with merged version
- **Open Output Folder**: Browse to merged model location

### Settings Tab

#### Path Configuration
- **HuggingFace Cache**: Location of downloaded models
- **Backup Directory**: Where model backups are stored

#### System Actions
- **Save Settings**: Apply and save current configuration
- **Reset to Defaults**: Restore default settings
- **Clear All Backups**: Delete all backup files (permanent!)

#### System Information
View detailed information about:
- System platform and Python version
- Configured paths
- Model status and sizes
- Backup statistics
- Merged model inventory

## 🔧 Advanced Usage

### Model Installation Workflow
1. **Create Backup**: Always backup original models first
2. **Merge LoRAs**: Create merged model with desired LoRAs
3. **Test Merged Model**: Verify the merged model works correctly
4. **Install Model**: Replace original with merged model
5. **Verify Installation**: Check that FramePack uses the new model

### Backup Management
- **Automatic Timestamping**: All backups include creation timestamp
- **Type Tracking**: Distinguishes between original and merged model backups
- **Size Monitoring**: Track backup storage usage
- **Selective Restore**: Restore specific backups without affecting others

### Model Organization
- **Naming Convention**: Merged models use descriptive names with LoRA info
- **Directory Structure**: Organized folder structure for easy navigation
- **Metadata Preservation**: Config files and model information preserved
- **Cross-Platform**: Works on Windows, macOS, and Linux

## 🛡️ Safety Features

### Backup Protection
- **Automatic Backups**: Original models backed up before installation
- **Confirmation Dialogs**: Multiple confirmations for destructive actions
- **Rollback Capability**: Easy restoration from backups
- **Separate Storage**: Backups stored separately from active models

### Error Handling
- **Validation Checks**: Input validation before operations
- **Error Recovery**: Graceful handling of failed operations
- **Detailed Logging**: Comprehensive logs for troubleshooting
- **Safe Defaults**: Conservative default settings

## 📁 File Structure

```
FramePack/
├── model_manager_gui.py          # Main application
├── model_manager_gui.bat         # Windows launcher
├── lora_merger.py                # LoRA merging backend
├── model_manager_config.json     # Application settings
├── model_backups/                # Model backup storage
│   ├── FramePackI2V_HY_20241214_143022/
│   └── F1_Model_20241214_150315/
├── merged_models/                # Merged model output
│   ├── FramePackI2V_HY_style_lora_x0.8/
│   └── F1_character_combo_x0.7/
└── hf_download/                  # HuggingFace cache
    └── hub/
        ├── models--lllyasviel--FramePackI2V_HY/
        └── models--lllyasviel--FramePack_F1_I2V_HY_20250503/
```

## 🔍 Troubleshooting

### Common Issues

**"Model not found in cache"**
- Run FramePack at least once to download models
- Check HuggingFace cache path in Settings
- Verify internet connection for model downloads

**"Permission denied" errors**
- Run as administrator (Windows)
- Check file/folder permissions
- Ensure target directories are writable

**"Out of memory" during merge**
- Close other applications
- Merge one LoRA at a time
- Check available RAM (recommend 32GB+ for large models)

**GUI not responding**
- Large operations run in background threads
- Check log output for progress
- Wait for operation to complete

### Performance Tips
- **SSD Storage**: Use SSD for model storage and backups
- **Sufficient RAM**: 32GB+ recommended for large model operations
- **Clean Workspace**: Regularly clean up old merged models and backups
- **Batch Operations**: Group similar operations together

## 🔄 Integration with FramePack

### Using Merged Models
Once a model is installed via the Model Manager:
1. **Automatic Detection**: FramePack automatically uses the installed model
2. **No LoRA Parameters**: Remove LoRA parameters from FramePack commands
3. **Normal Operation**: Use FramePack normally with merged model benefits

### Reverting Changes
To revert to original models:
1. **Use Restore Backup**: Select original model backup
2. **Manual Restoration**: Copy backup files to HuggingFace cache
3. **Verify Restoration**: Check model list shows original model

## 📊 Benefits

### Performance Improvements
- **Faster Loading**: No runtime LoRA merging
- **Memory Efficiency**: Single model instead of base + LoRA
- **Consistent Results**: Eliminates runtime merging variations
- **Simplified Deployment**: One model file instead of multiple

### Workflow Improvements
- **Visual Management**: See all models at a glance
- **Safe Operations**: Backup protection for all changes
- **Batch Processing**: Handle multiple models efficiently
- **Organized Storage**: Clean, organized model storage

## 🆘 Support

### Getting Help
1. **Check Logs**: Review detailed logs in each tab
2. **System Info**: Use Settings tab to gather system information
3. **Backup First**: Always create backups before major changes
4. **Test Incrementally**: Test with single LoRAs before multiple

### Best Practices
- **Regular Backups**: Create backups before experimenting
- **Test Merged Models**: Verify merged models work before installation
- **Monitor Storage**: Keep track of disk space usage
- **Document Changes**: Keep notes on successful LoRA combinations

The Model Manager provides a comprehensive solution for managing your FramePack models and LoRA combinations, making it easy to experiment, organize, and deploy custom models safely and efficiently.
