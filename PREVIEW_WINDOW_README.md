# FramePack Standalone Preview Window

A standalone preview window that displays the third column content from the main FramePack GUI, featuring image preview, latent animation preview, and output video preview with control buttons.

## Features

### Preview Sections
1. **Current Image Preview** - Shows the currently selected input image
2. **Latent Preview** - Real-time animation of the generation process (low-resolution)
3. **Output Video Preview** - Final output video display (high-resolution)

### Control Buttons
- **Force Stop All** - Completely terminates all batch processes and queue operations
- **Stop** - Stops the current generation after the current frame completes
- **Skip** - Skips the current generation and moves to the next item

### Window Features
- **Auto-resizing panes** - All preview sections automatically resize when window dimensions change
- **F5 key support** - Press F5 to reset window size and fix any sizing issues
- **Responsive layout** - Maintains proper aspect ratios and scaling
- **Real-time monitoring** - Automatically detects and loads new preview content

## Usage

### Starting the Preview Window

#### Method 1: Batch File
```batch
framepack_preview_window.bat
```

#### Method 2: Direct Python
```bash
python framepack_preview_window.py
```

#### Method 3: From Python Code
```python
import tkinter as tk
from framepack_preview_window import FramePackPreviewWindow

root = tk.Tk()
app = FramePackPreviewWindow(root)
root.mainloop()
```

### Window Controls
- **Click on videos** to pause/resume playback
- **F5 key** to reset window size if it gets stuck
- **Control buttons** at the top for generation control
- **Window resizing** automatically adjusts all panes

## Technical Details

### Dependencies
- **Required**: `tkinter` (built-in with Python)
- **Optional**: `PIL/Pillow` (for image preview)
- **Optional**: `tkVideoPlayer` or `tkintervideo` (for video preview)
- **Optional**: `tk_gif_player` (for GIF latent previews)

### File Monitoring
The window automatically monitors these directories:
- **Images**: Current directory and `temp/` folder for image files
- **Latent Previews**: `latent_previews/` folder for animation files
- **Output Videos**: `outputs/` folder for final video files

### Supported Formats
- **Images**: JPG, JPEG, PNG, BMP, TIFF
- **Videos**: MP4, AVI, MOV
- **Animations**: GIF, MP4

### Smart Player Selection
- **GIF files**: Uses custom TkGifPlayer for better compatibility
- **MP4 files**: Automatically switches to TkinterVideo when needed
- **Fallback**: Gracefully handles missing dependencies

## Control Button Functions

### Force Stop All
Creates multiple stop flag files:
- `stop_framepack.flag` - Main stop signal
- `stop_queue.flag` - Queue stop signal  
- `skip_generation.flag` - Skip signal

This ensures complete termination of all batch processes.

### Stop
Creates `stop_framepack.flag` to stop current generation after the current frame completes.

### Skip
Creates `skip_generation.flag` to skip the current generation and move to the next item.

## Window Layout

```
┌─────────────────────────────────────────┐
│ [Force Stop All] [Stop] [Skip]          │
├─────────────────────────────────────────┤
│ Current Image                           │
│ ┌─────────────────────────────────────┐ │
│ │                                     │ │
│ │        Image Preview Area           │ │
│ │                                     │ │
│ └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│ Latent Preview                          │
│ ┌─────────────────────────────────────┐ │
│ │                                     │ │
│ │      Latent Animation Area          │ │
│ │                                     │ │
│ └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│ Output Video Preview                    │
│ ┌─────────────────────────────────────┐ │
│ │                                     │ │
│ │       Final Video Area              │ │
│ │                                     │ │
│ └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## Troubleshooting

### Window Size Issues
- Press **F5** to reset window size
- Minimum size: 400x600 pixels
- Default size: 600x800 pixels

### Video Not Playing
- Check if `tkVideoPlayer` or `tkintervideo` is installed
- Verify video files exist in the expected directories
- Click on video to toggle playback

### Image Not Showing
- Check if `PIL/Pillow` is installed
- Verify image files exist in current directory or temp folder
- Supported formats: JPG, JPEG, PNG, BMP, TIFF

### Control Buttons Not Working
- Ensure you have write permissions in the FramePack directory
- Check if flag files are being created
- Verify the main FramePack process is monitoring for flag files

## Integration with Main GUI

The standalone preview window is designed to work alongside the main FramePack GUI:
- **Independent operation** - Can run separately from main GUI
- **Shared file monitoring** - Monitors same directories as main GUI
- **Compatible controls** - Uses same flag file system as main GUI
- **Same features** - Maintains all preview functionality from main GUI
