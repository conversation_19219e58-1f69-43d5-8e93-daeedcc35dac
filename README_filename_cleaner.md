# FramePack Filename Cleaner

This tool helps you identify and remove files that don't match the FramePack output filename conventions. It's useful for cleaning up your outputs folder and removing temporary or cancelled files.

## FramePack Naming Conventions

The tool checks for the following naming conventions:

1. **MP4 Files**:
   - Timestamp format: `DATE_TIME_XXX_YYYY(_ZZ)(_seed#######)(_XXs).mp4`
     - Example: `250420_121919_242_3623_37.mp4`
     - Example: `250420_121919_242_3623_37_24s.mp4`
     - Example: `250426_085120_706_7278_19_seed357798872.mp4`
   - Generic filename format: `image_name(_seed#######)(_XXs).mp4`
     - Example: `image_name.mp4`
     - Example: `image_name_5s.mp4`
     - Example: `image_name_seed123456.mp4`

2. **PNG Files**:
   - Must have `_start.png` or `_end.png` suffix
   - Example: `250420_121919_242_3623_start.png`
   - Example: `250420_121919_242_3623_end.png`

## Files Included

1. **framepack_filename_cleaner.py** - The main Python script
2. **framepack_filename_cleaner.bat** - Interactive batch file with menu options
3. **clean_framepack_files.bat** - Simple batch file for quick command-line use

## Usage

### Python Script

```
python framepack_filename_cleaner.py [--folder FOLDER] [--dry-run] [--auto-remove] [--filter {all,cancelled,no-suffix,mp4}]
```

Options:
- `--folder FOLDER`: Path to the folder to scan (default: current directory)
- `--dry-run`: Only list files without removing them
- `--auto-remove`: Automatically remove all non-conforming files without prompting
- `--filter`: Filter which types of non-conforming files to process
  - `all`: All non-conforming files (default)
  - `cancelled`: Only files with '_cancelled' suffix
  - `no-suffix`: Only PNG files without '_start' or '_end' suffix
  - `mp4`: Only non-conforming MP4 files

### Interactive Batch File

Run `framepack_filename_cleaner.bat` to open an interactive menu where you can:
1. Set the folder to scan
2. Choose a filter type
3. Toggle dry run mode
4. Toggle auto remove mode
5. Run the cleaner with your selected settings

### Quick Batch File

```
clean_framepack_files.bat [folder] [filter] [--dry-run] [--auto-remove]
```

Examples:
- `clean_framepack_files.bat outputs all --dry-run`
- `clean_framepack_files.bat outputs cancelled --auto-remove`
- `clean_framepack_files.bat outputs no-suffix`

## Common Use Cases

1. **List all non-conforming files without removing them**:
   ```
   python framepack_filename_cleaner.py --folder outputs --dry-run
   ```
   or
   ```
   clean_framepack_files.bat outputs all --dry-run
   ```

2. **Remove all cancelled files automatically**:
   ```
   python framepack_filename_cleaner.py --folder outputs --filter cancelled --auto-remove
   ```
   or
   ```
   clean_framepack_files.bat outputs cancelled --auto-remove
   ```

3. **Interactively choose which PNG files without proper suffixes to remove**:
   ```
   python framepack_filename_cleaner.py --folder outputs --filter no-suffix
   ```
   or
   ```
   clean_framepack_files.bat outputs no-suffix
   ```

## Safety Features

- The tool always shows you which files will be removed before taking action
- Dry run mode lets you see what would happen without making changes
- Interactive mode lets you confirm each file removal individually
- The tool only processes MP4 and PNG files, ignoring other file types

## Requirements

- Python 3.6 or higher
- Windows operating system (for batch files)
