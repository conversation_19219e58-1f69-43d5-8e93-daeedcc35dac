# FramePack Command Line Configuration Features - Reconstruction Outline

## Overview
This outline details the exact reconstruction of the command line configuration features that were previously implemented and then rolled back. All settings, syntax, and behavior should be maintained exactly as before.

## Feature 1: Command Line Arguments for framepack_gui.py

### 1.1 Add New Command Line Arguments
**Location:** `framepack_gui.py` - `main()` function, argument parser section

**Current code to modify:**
```python
def main():
    # Parse command line arguments for files
    import argparse
    import signal
    parser = argparse.ArgumentParser(description="FramePack GUI")
    parser.add_argument("files", nargs="*", help="Image or video files to process")
    parser.add_argument("--file-list", help="Path to a text file containing a list of image or video files to process")
    args = parser.parse_args()
```

**Add these lines after the existing arguments:**
```python
    parser.add_argument("--config", help="Path to a JSON configuration file to load settings from")
    parser.add_argument("--auto-run", action="store_true", help="Automatically start batch processing after loading (requires --config)")
    parser.add_argument("--iterations", type=int, default=1, help="Number of iterations for auto-run batch processing (default: 1)")
```

### 1.2 Add Config Loading Logic
**Location:** `framepack_gui.py` - `main()` function, after app creation

**Insert after:** `app = FramePackGUI(root)  # Create the application`

**Add this code block:**
```python
    # Load configuration file if provided
    config_loaded = False
    if args.config:
        if os.path.exists(args.config):
            print(f"Loading configuration from: {args.config}")
            config_loaded = app.load_settings_from_file(args.config)
            if config_loaded:
                print("Configuration loaded successfully")
            else:
                print("Failed to load configuration")
        else:
            print(f"Configuration file not found: {args.config}")

    # Validate auto-run requirements
    if args.auto_run:
        if not args.config:
            print("Error: --auto-run requires --config to be specified")
            sys.exit(1)
        if not config_loaded:
            print("Error: --auto-run requires successful config loading")
            sys.exit(1)
```

### 1.3 Add Auto-Run Logic
**Location:** `framepack_gui.py` - `main()` function, before `root.mainloop()`

**Insert before:** `root.mainloop()`

**Add this code block:**
```python
    # Handle auto-run functionality
    if args.auto_run and config_loaded:
        print(f"Auto-run enabled with {args.iterations} iterations")
        
        # Check if we have files to process
        if not app.selected_files:
            print("Warning: Auto-run requested but no files in batch queue")
        else:
            # Set the iterations
            app.iterations_var.set(args.iterations)
            print(f"Set iterations to {args.iterations}")
            
            # Schedule the batch processing to start after the GUI is fully loaded
            def start_auto_run():
                try:
                    print("Starting auto-run batch processing...")
                    app.add_current_settings_to_queue()
                    app.start_queue_processing()
                    print("Auto-run batch processing started")
                except Exception as e:
                    print(f"Error starting auto-run: {e}")
                    app.show_status(f"Auto-run failed: {str(e)}", "error")
            
            # Schedule the auto-run to start after a short delay to ensure GUI is ready
            root.after(1000, start_auto_run)
```

## Feature 2: Config Path Tracking for Auto-Save

### 2.1 Add Config Path Tracking Variable
**Location:** `framepack_gui.py` - `__init__` method of FramePackGUI class

**Find this line:**
```python
        # Auto-save variables
        self._auto_save_job = None
```

**Add after it:**
```python
        self._loaded_config_path = None  # Track the path of the loaded config file
```

### 2.2 Create load_settings_from_file Method
**Location:** `framepack_gui.py` - after the existing `load_settings` method

**Current load_settings method starts with:**
```python
    def load_settings(self):
        filename = filedialog.askopenfilename(
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")],
            initialdir=".",
            title="Load Settings"
        )

        if not filename:
            return

        try:
            with open(filename, 'r') as f:
                settings = json.load(f)
```

**Modify the load_settings method to:**
```python
    def load_settings(self):
        filename = filedialog.askopenfilename(
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")],
            initialdir=".",
            title="Load Settings"
        )

        if not filename:
            return

        self.load_settings_from_file(filename)

    def load_settings_from_file(self, filename):
        """Load settings from a specific file path"""
        if not filename or not os.path.exists(filename):
            print(f"Settings file not found: {filename}")
            return False

        try:
            with open(filename, 'r') as f:
                settings = json.load(f)
            
            # Track the loaded config path for auto-saving
            self._loaded_config_path = os.path.abspath(filename)
```

**Then copy the rest of the settings loading logic from the original load_settings method and add:**
```python
            self.show_status(f"Settings loaded from {filename}", "success")
            return True
        except Exception as e:
            self.show_status(f"Failed to load settings: {str(e)}", "error")
            return False
```

### 2.3 Modify Auto-Save Logic
**Location:** `framepack_gui.py` - `save_default_settings` method

**Find this code:**
```python
        try:
            with open("framepack_default_settings.json", 'w') as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            print(f"Failed to save default settings: {str(e)}")
```

**Replace with:**
```python
        try:
            # Determine the save path based on loaded config
            save_path = "framepack_default_settings.json"  # Default path
            
            # If we loaded framepack_default_settings.json from a specific path, save to that path
            if (hasattr(self, '_loaded_config_path') and self._loaded_config_path and 
                os.path.basename(self._loaded_config_path) == "framepack_default_settings.json"):
                save_path = self._loaded_config_path
                print(f"Auto-saving to loaded config path: {save_path}")
            
            with open(save_path, 'w') as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            print(f"Failed to save default settings: {str(e)}")
```

## Feature 3: GUI Launcher Bat File Config Support

### 3.1 Add Config Argument Detection
**Location:** `framepack_batch_gui.bat` - after the existing argument processing

**Find this section:**
```batch
REM Check if arguments were provided (for drag and drop or send to)
if "%~1" == "" goto :no_args
```

**Modify to:**
```batch
REM Check if arguments were provided (for drag and drop, send to, or config)
if "%~1" == "" goto :no_args

REM Check if the first argument is a config file option
set "first_arg=%~1"
if "%first_arg%" == "--config" goto :handle_config
if "%first_arg:~-5%" == ".json" goto :handle_config_file
```

### 3.2 Add Config Handling Sections
**Location:** `framepack_batch_gui.bat` - before the `:no_args` section

**Add these new sections:**
```batch
:handle_config
REM Handle --config argument
echo Detected --config argument
if "%~2" == "" (
    echo Error: --config requires a configuration file path
    echo Usage: framepack_batch_gui.bat --config path\to\config.json [--auto-run] [--iterations N]
    pause
    exit /b 1
)

set "config_file=%~2"
echo Config file: %config_file%

REM Check if config file exists
if not exist "%config_file%" (
    echo Error: Configuration file not found: %config_file%
    pause
    exit /b 1
)

REM Build command with config
set "gui_command=call venv\Scripts\python.exe framepack_gui.py --config "%config_file%""

REM Check for additional arguments
set "arg_index=3"
:check_next_arg
if "%~3" == "" goto :run_with_config
if "%~3" == "--auto-run" (
    set "gui_command=%gui_command% --auto-run"
    echo Added --auto-run flag
)
if "%~3" == "--iterations" (
    if "%~4" == "" (
        echo Error: --iterations requires a number
        pause
        exit /b 1
    )
    set "gui_command=%gui_command% --iterations %~4"
    echo Added --iterations %~4
    shift
)
shift
goto :check_next_arg

:run_with_config
echo Running: %gui_command%
%gui_command%
exit /b %ERRORLEVEL%

:handle_config_file
REM Handle direct .json file argument
echo Detected JSON config file: %first_arg%

REM Check if config file exists
if not exist "%first_arg%" (
    echo Error: Configuration file not found: %first_arg%
    pause
    exit /b 1
)

echo Launching GUI with config file: %first_arg%
call venv\Scripts\python.exe framepack_gui.py --config "%first_arg%"
exit /b %ERRORLEVEL%
```

## Implementation Order

1. **Step 1:** Add command line arguments to framepack_gui.py main() function
2. **Step 2:** Add config path tracking variable to FramePackGUI.__init__
3. **Step 3:** Create load_settings_from_file method
4. **Step 4:** Modify existing load_settings method to use new method
5. **Step 5:** Add config loading logic to main() function
6. **Step 6:** Add auto-run logic to main() function
7. **Step 7:** Modify save_default_settings method for alternate path auto-saving
8. **Step 8:** Add config argument detection to framepack_batch_gui.bat
9. **Step 9:** Add config handling sections to framepack_batch_gui.bat

## Testing Commands

After implementation, test with these exact commands:

```bash
# Test help
python framepack_gui.py --help

# Test config loading
python framepack_gui.py --config test_config.json

# Test auto-run
python framepack_gui.py --config test_config.json --auto-run --iterations 3

# Test bat file
framepack_batch_gui.bat --config test_config.json
framepack_batch_gui.bat --config test_config.json --auto-run --iterations 2

# Test alternate path auto-save
python framepack_gui.py --config C:\temp\framepack_default_settings.json
```

## Key Behavior Requirements

1. **Config Loading:** Must load all GUI settings from JSON file
2. **Auto-Run:** Must require --config, validate file exists, start processing automatically
3. **Iterations:** Must set the iterations value in GUI before starting
4. **Auto-Save:** Must save to alternate path only for framepack_default_settings.json files
5. **Bat File:** Must support all command line options with proper error handling
6. **Validation:** Must exit with error codes for invalid combinations
7. **Compatibility:** Must maintain all existing functionality

This outline preserves the exact same settings, syntax, and behavior as the original implementation.
