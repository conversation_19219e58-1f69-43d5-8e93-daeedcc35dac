# FramePack Standalone Commands

This directory contains standalone batch files and Python scripts that can send commands to FramePack even when the GUI is frozen or unresponsive.

## Available Commands

### 1. Skip Generation
**Files:** `skip_generation.bat` and `skip_generation.py`

**Purpose:** Skips the current generation and continues with the next item in the queue (if any).

**How it works:** Creates a `skip_generation.flag` file that FramePack batch processes check for during generation loops.

**When to use:** 
- Current generation is taking too long
- You want to skip a problematic image/video
- GUI is frozen but you want to continue with the next item

### 2. Force Terminate All
**Files:** `force_terminate_all.bat` and `force_terminate_all.py`

**Purpose:** Immediately terminates ALL FramePack generation processes while keeping the GUI running.

**How it works:** 
- Uses `psutil` (if available) for precise process identification and termination
- Falls back to system commands if `psutil` is not available
- Targets specific FramePack processes: `batch.py`, `batch_f1*.py`, `temp_run_framepack.bat`
- **NEVER** terminates the GUI process (`framepack_gui.py`)
- Cleans up temporary files and locks

**When to use:**
- GUI is frozen and unresponsive
- Generation processes are stuck or consuming too many resources
- Emergency stop needed
- Queue processing has stalled

### 3. Stop FramePack (Legacy)
**File:** `stop_framepack.bat`

**Purpose:** Creates the legacy stop flag for backward compatibility.

**How it works:** Creates a `stop_framepack.flag` file that older FramePack versions check for.

**When to use:** Only if you're using an older version of FramePack that doesn't support the newer skip functionality.

## Usage Instructions

### Method 1: Double-click the .bat files
1. Navigate to your FramePack directory
2. Double-click the desired `.bat` file
3. Follow the on-screen prompts
4. The command will execute and show results

### Method 2: Run Python scripts directly
1. Open Command Prompt or PowerShell in your FramePack directory
2. Run: `python skip_generation.py` or `python force_terminate_all.py`
3. Follow the on-screen prompts

### Method 3: Run from Command Line
```batch
# Skip current generation
skip_generation.bat

# Force terminate all processes
force_terminate_all.bat

# Legacy stop command
stop_framepack.bat
```

## How FramePack Processes Check for Flags

The FramePack batch scripts (`batch.py`, `batch_f1.py`, `batch_f1_video.py`) check for flag files at these points:

1. **During generation loops** - Between frames and processing steps
2. **Before processing each item** - At the start of each image/video processing
3. **During model loading** - When loading models and preparing for generation

This means the commands will take effect:
- Almost immediately if generation is between frames
- Within a few seconds during active generation
- Immediately if FramePack is idle or loading

## Safety Features

### Force Terminate All Safety
- **GUI Protection:** Never terminates the GUI process, ensuring you can still interact with FramePack
- **Confirmation Prompt:** Asks for confirmation before terminating processes
- **Graceful Termination:** Attempts graceful shutdown first, then force kills if necessary
- **Cleanup:** Automatically cleans up temporary files and locks
- **Process Targeting:** Only targets specific FramePack generation processes

### Skip Generation Safety
- **Queue Continuation:** Skips current item but continues processing the queue
- **Signal Creation:** Creates completion signals for the GUI to detect the skip
- **File Cleanup:** Automatically removes the skip flag after processing

## Troubleshooting

### If commands don't work:
1. **Check file permissions:** Ensure you can create files in the FramePack directory
2. **Run as Administrator:** Some process termination may require elevated privileges
3. **Check Python installation:** Python scripts require Python to be installed and in PATH
4. **Manual cleanup:** If processes are still running, use Task Manager to kill them manually

### If GUI remains frozen after force terminate:
1. The GUI process should still be running - try clicking on it
2. Use the "Reset UI" button in the GUI if available
3. If completely unresponsive, close and restart the GUI (your settings will be preserved)

### If processes keep restarting:
1. Check if "Reload Queue and Proceed" is enabled in the GUI
2. Disable automatic queue processing before using force terminate
3. Clear the job queue in the GUI after terminating processes

## Technical Details

### Process Identification Patterns
The force terminate command looks for processes with these patterns in their command line:
- `batch.py` (but not `framepack_gui.py`)
- `batch_f1_lock.py`
- `batch_f1_video.py`
- `temp_run_framepack.bat`
- `cmd.exe` processes running FramePack batch files

### Files Cleaned Up
- `temp_run_framepack.bat`
- `framepack_completed.signal`
- `stop_framepack.flag`
- `skip_generation.flag`
- `temp_command_result.txt`
- `framepack_progress.txt`

### Dependencies
- **Python scripts:** Require Python 3.x
- **psutil (optional):** For precise process termination - falls back to system commands if not available
- **Windows:** Batch files are designed for Windows systems

## Integration with GUI

These standalone commands work alongside the GUI buttons:
- **Skip Generation** = GUI "Skip Generation" button
- **Force Terminate All** = GUI "Stop Queue" button (when it terminates all processes)
- **Stop FramePack** = GUI "Stop" button (legacy behavior)

The advantage of standalone commands is they work even when the GUI is frozen or unresponsive.
