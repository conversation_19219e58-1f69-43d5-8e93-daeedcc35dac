# FramePack Standalone Preview Window

This is a standalone preview window that contains only the third column preview components from the main FramePack GUI:

- **Image Preview** (top left) - Shows the current input/start image
- **Latent Animation Preview** (top right) - Shows the real-time latent preview during generation
- **Output Video Preview** (bottom) - Shows the final generated video

## Features

### Auto-Resizing Panes
- All panes automatically resize when the window dimensions are adjusted
- Maintains proper aspect ratios for images and videos
- Double-click the horizontal sash to set equal sizes for image and latent previews
- Window state (position, size, pane sizes) is automatically saved and restored

### Control Buttons
- **Force Stop All** - Creates all stop flag files to terminate all FramePack processes
- **Stop** - Creates stop_queue.flag to stop the current queue
- **Skip** - Creates skip_generation.flag to skip the current generation

### Preview Features
- **Real-time Updates** - Automatically monitors for new preview files every 2 seconds
- **Double-click Refresh** - Double-click any status label to force refresh that preview
- **High-Performance Video Playback** - Uses MoviePyPlayer for smooth video preview
- **Automatic File Detection** - Finds latest files in temp/, latent_previews/, and outputs/ folders

## How to Use

### Method 1: Run the batch file
```
run_standalone_preview.bat
```

### Method 2: Run directly with Python
```
python standalone_preview_window.py
```

## File Locations

The preview window automatically monitors these locations:

- **Start Images**: Current directory (files with 'start' or 'input' in name)
- **Latent Previews**: 
  - `temp/latest_latent_preview_*.mp4` (highest priority)
  - `latent_previews/*.mp4` (fallback)
- **Output Videos**:
  - `outputs/*.mp4` (highest priority)
  - Current directory `*.mp4` files (fallback)

## Window State Persistence

The window automatically saves and restores:
- Window position (supports negative coordinates for multi-monitor setups)
- Window size
- Maximized state
- Pane sizes and positions

Configuration is saved to `standalone_preview_window.ini`

## Requirements

- Python with tkinter
- PIL (Pillow)
- MoviePy (for video playback)
- OpenCV (for video processing)

The same dependencies as the main FramePack GUI.

## Notes

- The preview window operates independently of the main GUI
- Control buttons create the same flag files that the main GUI uses
- Preview updates continue even when the main GUI is not running
- Window can be used alongside the main GUI for dual-monitor setups
