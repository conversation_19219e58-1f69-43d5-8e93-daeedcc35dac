[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:build in the image_photobashing.pyw functionality into the framepack gui. DESCRIPTION:add a button near the batch queue that will add a way to edit the selected image in the batch queue. this will only work for single images. the button is named "Photo Bash" and then the photobashing interface will load with the selected image already in there loaded as the background. add a button to the photo bashing gui that will allow to transfer a copy of the background to a bashing image that will have the rembg applied so that it can be manipulated if desired. allow the image to still be saved but add a "Send to Framepack" button and it will save a copy of the image to a sub folder in the framepack folder called "photo_bashing" and then that image will replace the previous image in the batch queue (the old one will be removed)
-[x] NAME:make it so that when the list items are changed it doesn't change the window size DESCRIPTION:for some reason when i add or remove batch queue list items like images the window size changes and i have to resize it . make it so that this doesn't happen

its also doing it when i paste text into the text box for prompt