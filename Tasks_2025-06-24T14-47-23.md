[x] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:add an option to enable an auto crop to face mode with adjustable fill percent like in the manage queue section but this is a separate option. there needs to be a toggle for it and the crop to face will be applied at the generation time so it'll have to go into the batch scripts.   when enabled it will automatically crop to face in the same way that the current option does but it will do it for every image in the batch. make it so that it's on a per batch logic and will apply to any items in the queue, directory, image list. right before the image is fed into the model for processing is when the auto crop should happen if the check box is enabled.   you can make it so that if the option is enabled then it will automatically look for the _cropped version in the temp folder and then you can apply the crop right before the generation happens so that the file will; actually be there when it goes to process it, unless you can think of a better way to deal with the pre-compiled list issue DESCRIPTION:
-[x] NAME:when i have multiple items in the job queue the job queue is reading negative. for example i have 4 jobs and it's showing the status as : Processing job -2/1, iteration 1/1 - Files: 7. everything is correct except for the negative job number DESCRIPTION:
-[x] NAME:exclude text to video generations from the black/transparent filter. you can base it on the file name <PERSON>SC<PERSON><PERSON><PERSON><PERSON>:
-[x] NAME:add auto save and load settings for any new settings we added so that they arre synced with teh gui state DESCRIPTION:
-[x] NAME:make it so that pressing f5 when the gui is focused that it will resize and fix any issues with the window size DESCRIPTION:
-[x] NAME:the job queue processing order should not be randomized if the option is checked. the random processing should only apply to the batch items themselves (batch.py, batch_f1.py, etc) but not to the job processing order, that should go sequentually per the list order DESCRIPTION: