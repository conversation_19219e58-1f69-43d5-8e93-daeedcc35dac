# FramePack URL Launcher

This tool allows you to download images from URLs and use them as input for FramePack video generation.

## Usage

1. Drag and drop a URL onto the `url_launcher.bat` file
2. Or run the launcher from the command line:
   ```
   url_launcher.bat "https://example.com/image.jpg"
   ```

## Features

- Downloads images from URLs to the temp directory
- Handles URLs with query parameters
- Automatically launches the FramePack GUI with the downloaded image
- Cleans up temporary files after processing

## Examples

```
url_launcher.bat "https://example.com/image.jpg"
url_launcher.bat "https://example.com/image.png?w=1000&h=800"
```

## Notes

- The downloaded image is saved to the `temp` directory with a timestamp to ensure uniqueness
- The URL must point directly to an image file (JPG, PNG, WEBP, etc.)
- The launcher requires an active internet connection to download the image
- If the download fails, an error message will be displayed

## Troubleshooting

If you encounter issues:

1. Make sure the URL points directly to an image file
2. Check your internet connection
3. Verify that the URL is accessible from your browser
4. Try using a different URL

For more help, please refer to the main FramePack documentation.
