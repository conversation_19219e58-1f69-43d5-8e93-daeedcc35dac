# FramePack URL Processing Feature

This document explains how to use the URL processing feature in FramePack.

## Overview

FramePack now supports processing images directly from URLs. This allows you to:

1. Generate videos from images hosted online without having to download them manually
2. Drag and drop URLs from your browser into the FramePack GUI
3. Create a list of URLs to process in batch mode

## How It Works

When you provide a URL to FramePack, it:

1. Downloads the image to a temporary directory
2. Processes the image to generate a video
3. Cleans up the temporary files when processing is complete

## Usage Options

### 1. Using the GUI with URL Drag and Drop

1. Run `run_framepack_url_fixed.bat` to start the GUI
2. Select the "Process URLs" radio button
3. Either:
   - Drag and drop URLs from your browser into the file list area
   - Click "Add URLs..." to manually enter URLs
4. Click "Run FramePack" to process the URLs

### 2. Using the Command Line with a URL List

1. Create a text file containing one URL per line
2. Run FramePack with the `--url-list` parameter:
   ```
   python batch.py --url-list your_url_list.txt --output_dir output
   ```

### 3. Testing URL Processing

You can test the URL processing feature using the provided test script:
```
test_url_processing.bat
```

This script:
- Creates a temporary file with test URLs
- Runs FramePack with these URLs
- Cleans up the temporary file when done

## Supported URL Types

The URL processing feature supports images with the following extensions:
- .jpg / .jpeg
- .png
- .bmp
- .webp

If a URL doesn't have a recognizable image extension, FramePack will attempt to download it and verify if it's a valid image.

## Troubleshooting

If you encounter issues with URL processing:

1. **Connection Issues**: Make sure you have an active internet connection
2. **Invalid URLs**: Verify that the URLs point directly to image files
3. **Permission Issues**: Ensure FramePack has permission to create temporary files
4. **Timeout Issues**: Very large images might time out during download

## Technical Details

- Downloaded images are stored in the `temp` directory in the FramePack folder
- Each downloaded image is given a unique filename based on the URL or a random number
- All temporary files are automatically cleaned up after processing
- The URL processing feature uses the Python `requests` library for downloading images
