# FramePack GUI Window Size Update

This document explains the changes made to update the window size of the FramePack GUI.

## Changes Made

The window size of the FramePack GUI has been updated from 563x830 to 563x865 pixels. This change was made to provide more vertical space for the GUI elements without changing the width.

### File Modified

- `framepack_gui.py`: Updated the window size in the `__init__` method of the `FramePackGUI` class.

### Before:

```python
self.root.geometry("563x830")  # Specific size requested by user
```

### After:

```python
self.root.geometry("563x865")  # Updated size requested by user
```

## Testing

To test the updated window size, run the `run_framepack_updated.bat` batch file. This will launch the FramePack GUI with the new window size.

## Notes

- The window size change does not affect any other elements of the GUI.
- The window is still resizable, so users can adjust it to their preferences.
- The width remains the same at 563 pixels.
- The height has been increased by 35 pixels to 865 pixels.

## Verification

After making this change, verify that:

1. The GUI opens with the correct size (563x865)
2. All elements are visible and properly arranged
3. The drag and drop functionality works correctly
4. All other functionality works as expected
