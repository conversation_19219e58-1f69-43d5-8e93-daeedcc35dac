#!/usr/bin/env python
"""
auto_sorter.py

This module provides functions to automatically sort video files
in the outputs folder after a generation is completed.

The module selects the longest video by duration from each group of files
with the same base identifier and seed value.

IMPORTANT: This module preserves all original files in the outputs directory.
It copies files to a 'sorted' subfolder without deleting any files.
No files are deleted in either the outputs folder or the sorted folder.

It is designed to be imported and used by the main FramePack application.
"""

import os
import re
import shutil
import time
import subprocess
import traceback
from collections import defaultdict


def parse_filename(filename):
    """
    Parse a filename to extract its base group identifier and seed value.
    Examples:
    - 250420_121919_242_3623_37.mp4 -> (250420_121919_242_3623, None)
    - 250420_121919_242_3623_37_24s.mp4 -> (250420_121919_242_3623, None)
    - 250426_085120_706_7278_19_seed357798872.mp4 -> (250426_085120_706_7278, 357798872)
    - 250426_085120_706_7278_19_seed357798872_24s.mp4 -> (250426_085120_706_7278, 357798872)
    - 250425_065610_277_3996_414_seed1614875401.mp4 -> (250425_065610_277_3996, 1614875401)
    - 250425_065610_277_3996_414_seed1614875401_24s.mp4 -> (250425_065610_277_3996, 1614875401)
    - image_name_5s.mp4 -> (image_name, None)
    - image_name_seed123456_5s.mp4 -> (image_name, 123456)

    Returns a tuple of (base_id, seed) or None if the filename doesn't match the pattern.
    """
    # Only match MP4 files
    if not filename.lower().endswith('.mp4'):
        return None

    # Pattern matches various formats:
    # 1. DATE_TIME_XXX_YYYY(_ZZ).mp4
    # 2. DATE_TIME_XXX_YYYY(_ZZ_seed#######).mp4
    # 3. DATE_TIME_XXX_YYYY(_###).mp4
    # 4. DATE_TIME_XXX_YYYY(_###_seed#######).mp4
    # 5. Any of the above with _XXs.mp4 suffix (where XX is duration in seconds)
    # 6. image_name(_seed#######)(_XXs).mp4 - For non-timestamp filenames

    # Extract the base identifier and seed if available
    # Updated pattern to handle _XXs duration suffix before .mp4 and non-timestamp filenames
    # First try timestamp pattern
    pattern = r'^(\d+_\d+_\d+_\d+)(?:_\d+)?(?:_seed(\d+))?(?:_\d+s)?\.mp4$'
    match = re.match(pattern, filename)

    if match:
        base_id = match.group(1)
        seed = match.group(2) if match.group(2) else None
        return (base_id, seed)

    # If timestamp pattern doesn't match, try generic filename pattern
    # This handles filenames like "image_name_seed123456_5s.mp4"
    pattern2 = r'^(.+?)(?:_seed(\d+))?(?:_\d+s)?\.mp4$'
    match2 = re.match(pattern2, filename)

    if match2:
        base_id = match2.group(1)
        seed = match2.group(2) if match2.group(2) else None
        return (base_id, seed)

    return None


def group_files(file_paths):
    """
    Group files based on their base identifier and seed value.
    Returns a dictionary where keys are (base_id, seed) tuples and values are lists of file paths.
    Files with the same base_id and seed are considered duplicates.
    Used for both initial grouping and deduplication to ensure consistent results.
    """
    groups = defaultdict(list)

    for file_path in file_paths:
        if not os.path.isfile(file_path):
            continue

        filename = os.path.basename(file_path)
        result = parse_filename(filename)

        if result:
            base_id, seed = result
            # Use a tuple of (base_id, seed) as the key
            # This groups files with the same base_id and seed together
            group_key = (base_id, seed)
            groups[group_key].append(file_path)

    return groups


# group_files_by_seed is now identical to group_files, so we'll use group_files for both purposes


def get_video_duration(file_path):
    """
    Get the duration of a video file in seconds using FFmpeg.

    Args:
        file_path: Path to the video file

    Returns:
        The duration in seconds as a float, or 0 if the duration couldn't be determined
    """
    try:
        # Use FFmpeg to get the duration
        cmd = ['ffmpeg', '-i', file_path, '-hide_banner']
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)

        # Parse the output to find the duration
        duration_match = re.search(r'Duration: (\d+):(\d+):(\d+\.\d+)', result.stderr)
        if duration_match:
            hours = int(duration_match.group(1))
            minutes = int(duration_match.group(2))
            seconds = float(duration_match.group(3))
            total_seconds = hours * 3600 + minutes * 60 + seconds
            return total_seconds

        # If we couldn't find the duration, try another method
        cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', file_path]
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
        if result.stdout.strip():
            return float(result.stdout.strip())

        # If we still couldn't find the duration, return 0
        return 0
    except Exception as e:
        print(f"  ⚠️ Error getting duration for {os.path.basename(file_path)}: {str(e)}")
        return 0


def get_longest_file(file_paths):
    """
    Return the path to the longest video file in the given list.

    Args:
        file_paths: List of paths to video files

    Returns:
        The path to the longest video file, or None if no files were provided
    """
    if not file_paths:
        return None

    # Get durations for all files
    files_with_durations = []
    for path in file_paths:
        filename = os.path.basename(path)
        size = os.path.getsize(path)
        duration = get_video_duration(path)
        files_with_durations.append((path, duration, size, filename))

    # Sort by duration (longest first)
    sorted_files = sorted(files_with_durations, key=lambda x: (x[1], x[2]), reverse=True)

    # Print detailed information about each file
    print("  Detailed video duration comparison:")
    for path, duration, size, filename in sorted_files:
        has_seconds = bool(re.search(r'_\d+s\.mp4$', filename.lower()))
        seconds_info = f" - Has seconds indicator" if has_seconds else ""
        print(f"    {filename} - {duration:.2f} seconds - {size} bytes{seconds_info}")

    # Return the path of the longest file
    longest_file = sorted_files[0][0]
    longest_filename = os.path.basename(longest_file)
    print(f"  Selected longest file: {longest_filename} - {sorted_files[0][1]:.2f} seconds - {sorted_files[0][2]} bytes")

    return longest_file


def get_largest_file(file_paths):
    """
    Return the path to the largest file in the given list.
    Includes detailed logging to help diagnose size comparison issues.
    """
    if not file_paths:
        return None

    # Get file sizes and sort by size (largest first)
    files_with_sizes = [(path, os.path.getsize(path)) for path in file_paths]

    # Sort by size in descending order
    sorted_files = sorted(files_with_sizes, key=lambda x: x[1], reverse=True)

    # Print detailed information about each file
    print("  Detailed file size comparison:")
    for path, size in sorted_files:
        filename = os.path.basename(path)
        has_seconds = "_s.mp4" in filename.lower()
        print(f"    {filename} - {size} bytes - {'Has seconds indicator' if has_seconds else 'No seconds indicator'}")

    # Return the path of the largest file
    largest_file = sorted_files[0][0]
    largest_filename = os.path.basename(largest_file)
    print(f"  Selected largest file: {largest_filename} - {sorted_files[0][1]} bytes")

    return largest_file


def extract_file_info(filename):
    """
    Extract detailed information from a filename.
    Returns a dictionary with extracted information.
    """
    info = {
        "filename": filename,
        "base_id": None,
        "suffix": None,
        "seed": None,
        "duration": None
    }

    # Extract base_id, suffix, seed, and duration
    # First try timestamp pattern
    base_match = re.match(r'^(\d+_\d+_\d+_\d+)(?:_(\d+))?(?:_seed(\d+))?(?:_(\d+)s)?\.mp4$', filename)
    if base_match:
        info["base_id"] = base_match.group(1)
        info["suffix"] = base_match.group(2) if base_match.group(2) else ""
        info["seed"] = base_match.group(3) if base_match.group(3) else ""
        info["duration"] = base_match.group(4) if base_match.group(4) else ""
        return info

    # If timestamp pattern doesn't match, try generic filename pattern
    # This handles filenames like "image_name_seed123456_5s.mp4"
    base_match2 = re.match(r'^(.+?)(?:_seed(\d+))?(?:_(\d+)s)?\.mp4$', filename)
    if base_match2:
        info["base_id"] = base_match2.group(1)
        info["suffix"] = ""  # No suffix in this pattern
        info["seed"] = base_match2.group(2) if base_match2.group(2) else ""
        info["duration"] = base_match2.group(3) if base_match2.group(3) else ""

    return info


def process_duplicates(directory, is_sorted_dir=False, specific_files=None):
    """
    Process files in the specified directory to find and remove duplicates:
    1. Group them based on filename patterns and seed values
    2. For each group with multiple files, identify duplicates
    3. Automatically delete smaller duplicates without confirmation

    IMPORTANT: This function should ONLY be used on the 'sorted' directory,
    never on the main outputs directory. The is_sorted_dir parameter is a
    safety check to prevent accidental deletion of files in the outputs directory.

    Args:
        directory: Directory to process
        is_sorted_dir: Safety check to ensure we only delete files in the sorted directory
        specific_files: Optional list of filenames to process. If provided, only these files
                       will be considered for deduplication. If None, all files are processed.
    """
    # Safety check to prevent deleting files in the outputs directory
    if not is_sorted_dir:
        print(f"⚠️ SAFETY CHECK FAILED: Deduplication is only allowed in the 'sorted' directory.")
        print(f"⚠️ Refusing to process directory: {directory}")
        print(f"⚠️ No files will be deleted.")
        return 0

    print(f"Processing duplicates in sorted directory: {directory}")

    # If specific files are provided, log them
    if specific_files:
        print(f"Only processing {len(specific_files)} specific files")

    # Get all MP4 files in the directory
    file_paths = []
    for file in os.listdir(directory):
        # Skip script files
        if file.startswith("framepack_"):
            continue

        # Only process MP4 files
        if not file.lower().endswith('.mp4'):
            continue

        # If specific files are provided, only process those files
        if specific_files and file not in specific_files:
            continue

        full_path = os.path.join(directory, file)
        if os.path.isfile(full_path):
            file_paths.append(full_path)

    # Check if we found any files to process
    if not file_paths:
        print(f"No MP4 files found to process in the directory: {directory}")
        return 0

    print(f"Found {len(file_paths)} MP4 files to process")

    # Group the files by base_id and seed
    groups = group_files(file_paths)

    # Check if we have any groups after parsing
    if not groups:
        print("No valid file groups found. Check if filenames match the expected pattern.")
        return 0

    print(f"Grouped into {len(groups)} distinct groups based on base_id and seed")

    # Find groups with multiple files (duplicates)
    duplicate_groups = {group_key: files for group_key, files in groups.items() if len(files) > 1}

    if not duplicate_groups:
        print("No duplicate files found.")
        return 0

    # Process each group with duplicates
    deleted_files = []

    print(f"\nFound {len(duplicate_groups)} groups with duplicate files:")

    for group_key, files in duplicate_groups.items():
        base_id, seed = group_key
        seed_info = f" (seed: {seed})" if seed else ""
        print(f"\nGroup: {base_id}{seed_info}")

        # Sort files by size (largest first)
        sorted_files = sorted(files, key=os.path.getsize, reverse=True)

        # Display files with their sizes and additional info
        for i, file_path in enumerate(sorted_files):
            size = os.path.getsize(file_path)
            filename = os.path.basename(file_path)
            file_info = extract_file_info(filename)

            # Add suffix, seed, and duration info if available
            suffix_str = f", suffix: {file_info['suffix']}" if file_info['suffix'] else ""
            seed_str = f", seed: {file_info['seed']}" if file_info['seed'] else ""
            duration_str = f", duration: {file_info['duration']}s" if file_info['duration'] else ""

            status = "KEEP (largest)" if i == 0 else "DELETE (smaller)"
            print(f"  {i+1}. {filename} - {size} bytes{suffix_str}{seed_str}{duration_str} - {status}")

        # Automatically delete smaller files without asking for confirmation
        print(f"Automatically deleting {len(sorted_files)-1} smaller files in this group...")

        # Keep the largest file, delete the rest
        for file_path in sorted_files[1:]:
            try:
                os.remove(file_path)
                deleted_files.append(os.path.basename(file_path))
                print(f"  Deleted: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"  Error deleting {os.path.basename(file_path)}: {str(e)}")

    # Print summary
    print("\n--- Deduplication Summary ---")
    print(f"Processed {len(duplicate_groups)} groups with duplicates")
    print(f"Deleted {len(deleted_files)} duplicate files")
    return len(deleted_files)


def sort_outputs(outputs_folder):
    """
    Main function to sort output files:
    1. Create a 'sorted' subfolder in the outputs folder
    2. Copy the longest file from each group to the 'sorted' subfolder

    IMPORTANT: This function preserves all files in the original outputs folder.
    No files are deleted in either the outputs folder or the sorted folder.

    Returns a tuple of (number of files copied, list of filenames that were copied)
    """
    print("\n=== Starting Automatic Sorting ===")
    print(f"Processing files in: {outputs_folder}")

    # Create the sorted directory if it doesn't exist
    sorted_dir = os.path.join(outputs_folder, "sorted")
    os.makedirs(sorted_dir, exist_ok=True)

    # Get all MP4 files in the directory (excluding the sorted directory and script files)
    file_paths = []
    for file in os.listdir(outputs_folder):
        # Skip the sorted directory and script files
        if file == "sorted" or file.startswith("framepack_"):
            continue

        # Only process MP4 files
        if not file.lower().endswith('.mp4'):
            continue

        full_path = os.path.join(outputs_folder, file)
        if os.path.isfile(full_path):
            file_paths.append(full_path)

    # Check if we found any files to process
    if not file_paths:
        print("No MP4 files found to process in the outputs directory.")
        return (0, [])

    # Group the files by both base_id and seed
    groups = group_files(file_paths)

    # Check if we have any groups after parsing
    if not groups:
        print("No valid file groups found. Check if filenames match the expected pattern.")
        return (0, [])

    # Print detailed information about the grouping
    print(f"\nDetailed grouping information:")
    for group_key, files_in_group in groups.items():
        base_id, seed = group_key
        seed_info = f" (seed: {seed})" if seed else ""
        print(f"  Group: {base_id}{seed_info} - {len(files_in_group)} files")
        for file_path in files_in_group:
            filename = os.path.basename(file_path)
            has_seconds = "_s.mp4" in filename.lower()
            print(f"    {filename} - {'Has seconds indicator' if has_seconds else 'No seconds indicator'}")

    # Process each group
    copied_files = []
    skipped_files = []

    print("\nProcessing file groups:")
    for group_key, files_in_group in groups.items():
        base_id, seed = group_key
        seed_info = f" (seed: {seed})" if seed else ""
        print(f"\nGroup: {base_id}{seed_info} - {len(files_in_group)} files")

        # Display files with their durations and sizes
        print("  Files in this group:")
        for i, file_path in enumerate(files_in_group):
            size = os.path.getsize(file_path)
            filename = os.path.basename(file_path)
            print(f"  {i+1}. {filename} - {size} bytes")

        # Get the longest file by duration
        print("  Finding the longest video by duration...")
        longest_file = get_longest_file(files_in_group)
        if longest_file:
            dest_filename = os.path.basename(longest_file)
            dest_path = os.path.join(sorted_dir, dest_filename)

            if os.path.exists(dest_path):
                skipped_files.append(dest_filename)
                print(f"  Skipped copying {dest_filename} (already exists in destination)")
            else:
                shutil.copy2(longest_file, dest_path)
                copied_files.append(dest_filename)
                print(f"  Copied {dest_filename} to sorted directory")

    # Print summary
    print(f"\nProcessed {len(groups)} groups of files.")
    print(f"\nCopied {len(copied_files)} files to {sorted_dir}")

    if skipped_files:
        print(f"\nSkipped {len(skipped_files)} files (already exist in destination)")

    print("\n=== Automatic Sorting Completed ===")
    return (len(copied_files), copied_files)


# This function will be called from the main application after generation is complete
def sort_and_dedupe_outputs(outputs_folder):
    """
    Main function to sort output files and deduplicate in the sorted folder:
    1. Create a 'sorted' subfolder in the outputs folder
    2. Copy the longest file from each group to the 'sorted' subfolder
    3. Deduplicate files in the sorted folder (only for files just copied)

    IMPORTANT: This function preserves all files in the original outputs folder.
    It only deletes duplicate files in the sorted folder, never in the outputs folder.

    Args:
        outputs_folder: Path to the folder containing the output files

    Returns:
        A tuple of (num_copied, num_deleted) where:
        - num_copied: Number of files copied to the sorted directory
        - num_deleted: Number of duplicate files deleted in the sorted directory
    """
    print("\n=== Starting Automatic Sorting and Deduplication ===")
    print(f"Processing files in: {outputs_folder}")

    # First, perform the sorting operation
    num_copied, copied_files = sort_outputs(outputs_folder)

    # If no files were copied, there's nothing to deduplicate
    if num_copied == 0:
        print("No files were copied to the sorted directory. Skipping deduplication.")
        return (0, 0)

    # Get the sorted directory path
    sorted_dir = os.path.join(outputs_folder, "sorted")

    # Only deduplicate files in the sorted directory that were just copied
    print("\n--- Starting Deduplication in Sorted Directory ---")
    print(f"Only deduplicating the {len(copied_files)} files that were just copied")
    num_deleted = process_duplicates(sorted_dir, is_sorted_dir=True, specific_files=copied_files)

    print("\n=== Automatic Sorting and Deduplication Completed ===")
    return (num_copied, num_deleted)


def auto_sort_after_generation(outputs_folder='./outputs/', recent_file=None, input_dir=None, original_input_file=None):
    """
    Automatically sort output files after generation is complete.
    This function is designed to be called from the main application.

    The function processes only the most recently generated file, copying it to
    the sorted folder if it's the longest in its group, and deduplicating any
    duplicates of that specific file in the sorted folder.

    IMPORTANT: This function preserves all files in the original outputs folder.
    It only deletes duplicate files in the sorted folder, never in the outputs folder.

    Args:
        outputs_folder: Path to the folder containing the output files
        recent_file: The filename of the most recently generated file (without path)
        input_dir: Directory where the processed_files.txt file should be updated
        original_input_file: The original input file path that was processed

    Returns:
        A tuple of (num_copied, num_deleted) where:
        - num_copied: Number of files copied to the sorted directory (0 or 1)
        - num_deleted: Number of duplicate files deleted in the sorted directory
    """
    try:
        print("\n=== Starting Automatic Post-Generation Sorting ===")
        # Wait a moment to ensure all files are fully written
        time.sleep(2)

        # If no recent file is provided, we can't do anything
        if not recent_file:
            print("No recent file provided. Skipping auto-sorting.")
            return (0, 0)

        print(f"Processing recently generated file: {recent_file}")

        # Process only the most recent file
        num_copied, num_deleted = process_recent_file(outputs_folder, recent_file)

        # Update the processed_files.txt file if input_dir and original_input_file are provided
        if input_dir and original_input_file:
            if update_processed_files_txt(original_input_file, input_dir):
                print(f"✅ Successfully added {original_input_file} to {os.path.join(input_dir, 'processed_files.txt')}")
            else:
                print(f"❌ Failed to add {original_input_file} to processed_files.txt")

        print(f"Auto-sorting complete: {num_copied} files copied, {num_deleted} duplicates removed")
        if original_input_file:
            print(f"✅ Successfully completed processing for {original_input_file}")

        print("\n=== Automatic Post-Generation Sorting Completed ===")
        return (num_copied, num_deleted)
    except Exception as e:
        print(f"\nError during automatic sorting: {str(e)}")
        traceback.print_exc()
        return (0, 0)


def update_processed_files_txt(file_path, input_dir=None, debug=False):
    """
    Update the processed_files.txt file with a new entry.
    This function adds the file path to the processed_files.txt file in the input directory,
    but only if the file path doesn't already exist in the file.

    Args:
        file_path (str): The path of the file to add to the processed_files.txt
        input_dir (str): The directory where the processed_files.txt file is located.
                         If None, it will use the parent directory of the file.
        debug (bool): Whether to print debug information

    Returns:
        bool: True if the file was updated successfully or already exists, False otherwise
    """
    try:
        # If input_dir is not provided, use the parent directory of the file
        if input_dir is None:
            # Check if file_path is a full path or just a filename
            if os.path.dirname(file_path):
                input_dir = os.path.dirname(file_path)
            else:
                input_dir = os.path.dirname(os.path.abspath(file_path))

        # Ensure the input directory exists
        os.makedirs(input_dir, exist_ok=True)

        # Define the path to the processed_files.txt file
        processed_files_path = os.path.join(input_dir, "processed_files.txt")

        if debug:
            print(f"DEBUG: Checking processed_files.txt at {processed_files_path}")
            print(f"DEBUG: Checking for file: {file_path}")

        # First check if the file already exists in processed_files.txt
        file_already_exists = False
        if os.path.exists(processed_files_path):
            with open(processed_files_path, 'r') as f:
                content = f.read().splitlines()
                if file_path in content:
                    file_already_exists = True
                    if debug:
                        print(f"DEBUG: File path already exists in processed_files.txt, skipping append")
                    return True

        # Only append if the file doesn't already exist in the list
        if not file_already_exists:
            if debug:
                print(f"DEBUG: File path not found in processed_files.txt, appending")

            # Append the file path to the processed_files.txt file
            with open(processed_files_path, 'a') as f:
                f.write(f"{file_path}\n")
                # Flush the file to ensure it's written immediately
                f.flush()
                os.fsync(f.fileno())

            # Verify the file was updated correctly
            if os.path.exists(processed_files_path):
                with open(processed_files_path, 'r') as f:
                    content = f.read()
                    if file_path in content:
                        if debug:
                            print(f"DEBUG: Verified file path was successfully added to processed_files.txt")
                        return True
                    else:
                        print(f"Warning: File path was not found in processed_files.txt after update")
                        return False
            else:
                print(f"Warning: processed_files.txt does not exist after update attempt")
                return False

        return True
    except Exception as e:
        print(f"Error updating processed_files.txt: {e}")
        traceback.print_exc()
        return False

def process_recent_file(outputs_folder, recent_file):
    """
    Process only the most recently generated file:
    1. Find other files with the same base identifier and seed
    2. If the recent file is the longest, copy it to the sorted folder
    3. Deduplicate any duplicates of this file in the sorted folder

    Args:
        outputs_folder: Path to the folder containing the output files
        recent_file: The filename of the most recently generated file (without path)

    Returns:
        A tuple of (num_copied, num_deleted) where:
        - num_copied: Number of files copied to the sorted directory (0 or 1)
        - num_deleted: Number of duplicate files deleted in the sorted directory
    """
    print(f"\n=== Processing Recent File: {recent_file} ===")

    # Create the sorted directory if it doesn't exist
    sorted_dir = os.path.join(outputs_folder, "sorted")
    os.makedirs(sorted_dir, exist_ok=True)

    # Get the full path of the recent file
    recent_file_path = os.path.join(outputs_folder, recent_file)

    # Check if the file exists
    if not os.path.isfile(recent_file_path):
        print(f"Recent file not found: {recent_file_path}")
        return (0, 0)

    # Parse the filename to get base_id and seed
    result = parse_filename(recent_file)
    if not result:
        print(f"Could not parse filename: {recent_file}")
        return (0, 0)

    base_id, seed = result
    group_key = (base_id, seed)
    print(f"File belongs to group: {base_id}" + (f" (seed: {seed})" if seed else ""))

    # Find all files in the same group (with the same base_id and seed)
    group_files = []
    for file in os.listdir(outputs_folder):
        # Skip the sorted directory and script files
        if file == "sorted" or file.startswith("framepack_"):
            continue

        # Only process MP4 files
        if not file.lower().endswith('.mp4'):
            continue

        file_path = os.path.join(outputs_folder, file)
        if not os.path.isfile(file_path):
            continue

        # Check if this file belongs to the same group
        file_result = parse_filename(file)
        if file_result and file_result == group_key:
            group_files.append(file_path)

    # If no files found in the group (shouldn't happen), return
    if not group_files:
        print(f"No files found in the same group as {recent_file}")
        return (0, 0)

    print(f"Found {len(group_files)} files in the same group")

    # Get the longest file by duration
    print("Finding the longest video by duration...")
    longest_file = get_longest_file(group_files)
    if not longest_file:
        print("Could not determine the longest file")
        return (0, 0)

    # Check if the longest file is the recent file
    longest_filename = os.path.basename(longest_file)
    if longest_filename != recent_file:
        print(f"Recent file is not the longest in its group. Longest is: {longest_filename}")
        return (0, 0)

    # Copy the file to the sorted directory
    dest_path = os.path.join(sorted_dir, recent_file)
    if os.path.exists(dest_path):
        print(f"File already exists in sorted directory: {recent_file}")
        copied = 0
    else:
        shutil.copy2(recent_file_path, dest_path)
        print(f"Copied {recent_file} to sorted directory")
        copied = 1

    # Check for duplicates in the sorted directory
    print("\n--- Checking for duplicates in sorted directory ---")

    # Get all files in the sorted directory with the same base_id and seed
    sorted_group_files = []
    for file in os.listdir(sorted_dir):
        # Skip script files
        if file.startswith("framepack_"):
            continue

        # Only process MP4 files
        if not file.lower().endswith('.mp4'):
            continue

        file_path = os.path.join(sorted_dir, file)
        if not os.path.isfile(file_path):
            continue

        # Check if this file belongs to the same group
        file_result = parse_filename(file)
        if file_result and file_result == group_key:
            sorted_group_files.append(file_path)

    # If only one file in the group, no duplicates
    if len(sorted_group_files) <= 1:
        print("No duplicates found in sorted directory")
        return (copied, 0)

    print(f"Found {len(sorted_group_files)} files in the same group in sorted directory")

    # Sort files by size (largest first)
    sorted_files = sorted(sorted_group_files, key=os.path.getsize, reverse=True)

    # Display files with their sizes
    for i, file_path in enumerate(sorted_files):
        size = os.path.getsize(file_path)
        filename = os.path.basename(file_path)
        status = "KEEP (largest)" if i == 0 else "DELETE (smaller)"
        print(f"  {i+1}. {filename} - {size} bytes - {status}")

    # Delete smaller duplicates
    deleted_files = []
    if len(sorted_files) > 1:
        print(f"Deleting {len(sorted_files)-1} smaller duplicates...")

        # Keep the largest file, delete the rest
        for file_path in sorted_files[1:]:
            try:
                os.remove(file_path)
                deleted_filename = os.path.basename(file_path)
                deleted_files.append(deleted_filename)
                print(f"  Deleted: {deleted_filename}")
            except Exception as e:
                print(f"  Error deleting {os.path.basename(file_path)}: {str(e)}")

    print(f"\nDeleted {len(deleted_files)} duplicate files")
    return (copied, len(deleted_files))


# For testing purposes
if __name__ == "__main__":
    # When run directly, process the current directory
    # Look for the most recent MP4 file in the current directory
    import glob
    mp4_files = glob.glob("*.mp4")
    if mp4_files:
        # Sort by modification time (newest first)
        recent_file = sorted(mp4_files, key=os.path.getmtime, reverse=True)[0]
        print(f"Found most recent file: {recent_file}")
        auto_sort_after_generation(".", recent_file)
    else:
        print("No MP4 files found in current directory")
        auto_sort_after_generation()
