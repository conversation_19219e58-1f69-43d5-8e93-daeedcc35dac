# FramePack Drag and Drop Fix

This document provides instructions for fixing drag and drop functionality in the FramePack GUI.

## Quick Fix

1. Run the `run_framepack_gui_debug.bat` file to start FramePack with drag and drop debugging enabled.
2. Try dragging and dropping files onto the file list area.
3. Check the console for any error messages or debugging information.

## Testing Drag and Drop

If you're still experiencing issues, you can test basic drag and drop functionality:

1. Run `python test_listbox_dnd.py` to open a simple test window.
2. Try dragging and dropping files onto the listbox.
3. If this works but the main application doesn't, there may be an issue with the specific widgets or layout in the main application.

## Manual Fixes

If you're still having issues, try these manual fixes:

### 1. Update tkinterdnd2

Make sure you have the latest version of tkinterdnd2:

```
pip install --upgrade tkinterdnd2
```

### 2. Check Windows Settings

On Windows, make sure drag and drop is enabled in your system:

1. Open the Registry Editor (regedit)
2. Navigate to `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer`
3. Look for a DWORD value called `MouseDragDropScrollDelay`
4. If it exists and is set to a high value, try setting it to a lower value (e.g., 100) or delete it

### 3. Try a Different Approach

If drag and drop still doesn't work, you can use the "Add Files..." button to select files instead.

## Technical Details

The drag and drop functionality in FramePack uses the tkinterdnd2 library, which provides a bridge between Tkinter and the operating system's drag and drop functionality. The main components involved are:

1. The listbox widget that displays the selected files
2. The container frame that holds the listbox
3. Event handlers for drag enter, drag leave, and drop events

If you're experiencing issues, it may be due to:

- Incompatibility between tkinterdnd2 and your operating system
- Issues with the specific widgets or layout in the application
- Problems with the event handling code

## Reporting Issues

If you continue to experience issues, please provide the following information:

1. Your operating system and version
2. The version of Python you're using
3. The version of tkinterdnd2 you have installed
4. Any error messages or debugging information from the console
5. A description of what happens when you try to drag and drop files

This information will help diagnose and fix the issue.
