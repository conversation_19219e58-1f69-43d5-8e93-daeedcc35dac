# FramePack GUI Drag and Drop Implementation

This document explains the implementation of drag and drop functionality in the FramePack GUI.

## Overview

The drag and drop functionality allows users to drag image files from their file explorer and drop them onto the file list in the FramePack GUI. This makes it easier to add files to the processing list without having to use the file browser dialog.

## Implementation Details

The implementation uses the `tkinterdnd2` library to enable drag and drop functionality in Tkinter. The key components are:

1. **Widget Hierarchy**: The listbox is placed inside a frame with a visible border to make the drop target more visible.
2. **Event Binding**: The listbox and its container frame are registered as drop targets and bound to event handlers.
3. **Visual Feedback**: The appearance of the widgets changes when files are dragged over them to provide visual feedback to the user.
4. **Error Handling**: Comprehensive error handling is implemented to handle various edge cases.

## Key Components

### Widget Creation

```python
# Create a frame with a visible border for the listbox
self.listbox_frame = ttk.Frame(files_list_frame, borderwidth=2, relief="solid")
self.listbox_frame.pack(fill=tk.BOTH, expand=True)

# Create the listbox
self.files_listbox = tk.Listbox(self.listbox_frame, width=50, height=5)
self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)

# Add a status label for drag and drop
self.drag_drop_label = ttk.Label(self.files_frame, text="Ready for drag and drop", foreground="gray")
```

### Event Registration

```python
# Register the listbox as a drop target
self.files_listbox.drop_target_register(DND_FILES)
self.files_listbox.dnd_bind('<<Drop>>', self.drop_files)
self.files_listbox.dnd_bind('<<DragEnter>>', self.drag_enter)
self.files_listbox.dnd_bind('<<DragLeave>>', self.drag_leave)

# Also register the listbox frame
self.listbox_frame.drop_target_register(DND_FILES)
self.listbox_frame.dnd_bind('<<Drop>>', self.drop_files)
self.listbox_frame.dnd_bind('<<DragEnter>>', self.drag_enter)
self.listbox_frame.dnd_bind('<<DragLeave>>', self.drag_leave)
```

### Event Handlers

The implementation includes three main event handlers:

1. **drop_files**: Handles the actual dropping of files onto the widget.
2. **drag_enter**: Changes the appearance of the widgets when files are dragged over them.
3. **drag_leave**: Restores the normal appearance when files are dragged away.

## Testing

To test the drag and drop functionality:

1. Run `run_framepack_gui_debug.bat` to start FramePack with drag and drop debugging enabled.
2. Try dragging and dropping files onto the file list area.
3. Check the console for any error messages or debugging information.

If you encounter issues, you can also run `python test_listbox_dnd.py` to test a simplified version of the drag and drop functionality.

## Troubleshooting

If drag and drop is not working:

1. Make sure `tkinterdnd2` is installed: `pip install tkinterdnd2`
2. Check the console for any error messages
3. Try using the "Add Files..." button as an alternative
4. Make sure you're dragging valid image files (jpg, jpeg, png, bmp, webp)

## References

- [TkinterDnD2 Documentation](https://github.com/pmgagne/tkinterdnd2)
- [Tkinter Documentation](https://docs.python.org/3/library/tkinter.html)
