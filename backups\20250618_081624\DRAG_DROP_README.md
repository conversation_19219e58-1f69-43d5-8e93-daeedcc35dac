# Drag and Drop Support for FramePack GUI

The FramePack GUI now supports drag and drop functionality for image files, making it easier to add files to the processing list.

## Requirements

To use the drag and drop feature, you need to install the `tkinterdnd2` package:

```
pip install tkinterdnd2
```

This package is included in the `requirements.txt` file, so if you've already installed all requirements, you should have it.

## How to Use Drag and Drop

1. Start the FramePack GUI application
2. Switch to "Individual Files" mode if not already selected
3. Drag image files from your file explorer and drop them onto the files list
4. The files will be automatically added to the list and ready for processing

## Supported File Types

The following image file types are supported for drag and drop:
- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- WebP (.webp)

## Notes

- When you drop files, any existing files in the list will be cleared first
- Only valid image files will be added to the list
- The application will automatically switch to "Individual Files" mode when files are dropped
- You can still use the "Add Files..." button to add more files using the file browser
- Use the "Remove" button to remove selected files from the list
- Use the "Clear All" button to remove all files from the list

## Troubleshooting

If drag and drop is not working:

1. Make sure you have installed the `tkinterdnd2` package
2. Check the console output for any error messages
3. Try restarting the application
4. If problems persist, you can still use the "Add Files..." button to add files

