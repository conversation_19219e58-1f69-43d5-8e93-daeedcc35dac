# Multiple LoRA Support in FramePack

FramePack now supports merging up to **3 LoRAs simultaneously** for enhanced video generation! This allows you to combine different aspects like style, character, and lighting effects in a single generation.

## 🚀 New Features

- **Up to 3 LoRAs**: Merge multiple LoRA files in a single generation
- **Individual Strength Control**: Each LoRA can have its own multiplier (0.0-1.0)
- **Sequential Merging**: LoRAs are applied in order (1 → 2 → 3)
- **Backward Compatibility**: Old single LoRA syntax still works
- **Efficient Loading**: Model only reloads when LoRA configuration changes

## 📝 Command Line Arguments

### New Multiple LoRA Arguments
```bash
--lora_file_1 PATH          # Path to first LoRA file
--lora_multiplier_1 FLOAT   # First LoRA strength (0.0-1.0, default: 0.8)
--lora_file_2 PATH          # Path to second LoRA file  
--lora_multiplier_2 FLOAT   # Second LoRA strength (0.0-1.0, default: 0.8)
--lora_file_3 PATH          # Path to third LoRA file
--lora_multiplier_3 FLOAT   # Third LoRA strength (0.0-1.0, default: 0.8)
```

### Backward Compatibility Arguments
```bash
--lora_file PATH            # Maps to --lora_file_1 (old syntax)
--lora_multiplier FLOAT     # Maps to --lora_multiplier_1 (old syntax)
```

## 💡 Usage Examples

### Example 1: Three LoRAs (Style + Character + Lighting)
```bash
python batch.py \
  --input_dir input \
  --output_dir output \
  --prompt "a beautiful landscape with dramatic lighting" \
  --lora_file_1 "loras/cinematic_style.safetensors" --lora_multiplier_1 0.8 \
  --lora_file_2 "loras/character_enhance.safetensors" --lora_multiplier_2 0.6 \
  --lora_file_3 "loras/dramatic_lighting.safetensors" --lora_multiplier_3 0.4
```

### Example 2: Two LoRAs (Style + Environment)
```bash
python batch.py \
  --input_dir input \
  --output_dir output \
  --prompt "anime character in cyberpunk setting" \
  --lora_file_1 "loras/anime_style.safetensors" --lora_multiplier_1 0.9 \
  --lora_file_2 "loras/cyberpunk_env.safetensors" --lora_multiplier_2 0.7
```

### Example 3: Single LoRA (Backward Compatible)
```bash
python batch.py \
  --input_dir input \
  --output_dir output \
  --prompt "realistic portrait" \
  --lora_file "loras/portrait_realism.safetensors" \
  --lora_multiplier 0.8
```

## 🔧 How It Works

1. **Sequential Merging**: LoRAs are merged into the model state dict in order:
   - First: Base model + LoRA 1
   - Then: Previous result + LoRA 2  
   - Finally: Previous result + LoRA 3

2. **Individual Strength**: Each LoRA has its own multiplier for fine control:
   - `0.0`: No effect
   - `0.3-0.5`: Subtle effect
   - `0.6-0.8`: Moderate effect
   - `0.9-1.0`: Strong effect

3. **Efficient Loading**: The model is only reloaded when:
   - Any LoRA file path changes
   - Any LoRA multiplier changes
   - FP8 optimization setting changes

## 🎯 Best Practices

### LoRA Combination Strategies
- **Style + Character + Lighting**: Most comprehensive approach
- **Style + Environment**: Good for scene-specific generations
- **Character + Pose**: Great for character-focused content
- **Style + Quality**: Enhance overall visual quality

### Multiplier Guidelines
- **Primary effect**: 0.7-1.0 (main style or character)
- **Secondary effect**: 0.5-0.7 (supporting elements)
- **Subtle enhancement**: 0.3-0.5 (minor adjustments)

### Memory Considerations
- Multiple LoRAs use more GPU memory during loading
- Consider reducing other settings if you encounter memory issues
- Use `--gpu_memory` to adjust memory preservation

## 🔄 Migration from Single LoRA

Your existing scripts will continue to work! The old syntax automatically maps to the new system:

```bash
# Old syntax (still works)
--lora_file "my_lora.safetensors" --lora_multiplier 0.8

# Equivalent new syntax
--lora_file_1 "my_lora.safetensors" --lora_multiplier_1 0.8
```

## 🐛 Troubleshooting

### Common Issues
1. **Out of Memory**: Reduce multipliers or use fewer LoRAs
2. **Slow Loading**: Normal for multiple LoRAs, only happens when config changes
3. **Unexpected Results**: Try different multiplier combinations

### Debug Tips
- Start with single LoRAs to test individual effects
- Use lower multipliers initially (0.5) then increase
- Check LoRA compatibility with your base model

## 📁 File Organization

Recommended directory structure:
```
FramePack/
├── batch.py
├── loras/
│   ├── styles/
│   │   ├── cinematic.safetensors
│   │   └── anime.safetensors
│   ├── characters/
│   │   ├── enhance.safetensors
│   │   └── realistic.safetensors
│   └── effects/
│       ├── lighting.safetensors
│       └── atmosphere.safetensors
├── input/
└── output/
```

## 🚀 Advanced Usage

Run the example script to see more usage patterns:
```bash
python example_multiple_loras.py
python example_multiple_loras.py --help
```

This will show various combination strategies and command examples you can adapt for your needs.
