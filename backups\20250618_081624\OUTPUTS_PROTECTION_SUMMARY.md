# Outputs Folder Protection - Investigation Summary

## Issue
User reported that the outputs folder is being cleared for some reason, and only the temp folder should be cleared.

## Investigation Results

### ✅ FramePack GUI is NOT the Problem
After thorough investigation, **the FramePack GUI itself NEVER deletes files from the outputs folder**. All cleanup functions in the GUI only remove:

1. **Temporary files** (temp_command_output.txt, temp_line_count.txt, etc.)
2. **Communication files** (framepack_completed.signal, stop_framepack.flag)
3. **Lock files** (framepack.lock, batch_lock.lock)
4. **Memory references** (video players, cached images)

### ⚠️ External Cleanup Scripts ARE the Problem
The investigation found **external cleanup scripts** that CAN delete files from the outputs folder:

1. **`framepack_filename_cleaner.py`** - Removes files that don't match FramePack naming conventions
2. **`clean_framepack_files.bat`** - Batch file that runs the cleaner (defaults to "outputs" folder)
3. **`framepack_filename_cleaner.bat`** - Interactive version of the cleaner

## Root Cause
The outputs are being cleared by **external cleanup scripts**, not by the FramePack GUI. These scripts may be:
- Run manually by the user
- Called by other processes or batch files
- Triggered by shortcuts or automation

## Solution Implemented

### 1. Enhanced Safety Checks
Added explicit safety checks to all GUI cleanup functions:

```python
# Safety check: ensure we're not accidentally touching the outputs folder
if "outputs" in file_path.lower() or file_path.startswith("outputs"):
    print(f"SAFETY CHECK: Skipping {file_path} - contains 'outputs' path")
    continue
```

### 2. Clear Documentation
Added clear documentation to all cleanup functions stating they NEVER touch the outputs folder:

```python
"""Clean up temporary files that might be left over from previous runs

IMPORTANT: This function ONLY cleans up temporary files and communication files.
It NEVER touches the outputs folder or any generated video/image files.
"""
```

### 3. Warning System
Added automatic detection and warning about external cleanup scripts:

```python
def check_for_external_cleanup_scripts(self):
    """Check for external cleanup scripts that might affect the outputs folder"""
    # Warns user about scripts that can delete output files
```

### 4. Console Logging
Enhanced logging to clearly show what is being cleaned and what is protected:

```
Cleaning up temporary files (outputs folder is NEVER touched)...
SAFETY NOTE: This cleanup NEVER touches the outputs folder or generated files
CONFIRMED: No files in outputs folder were affected by memory cleanup
```

## Protection Verification
Created and ran comprehensive tests that confirm:
- ✅ GUI cleanup functions protect outputs folder
- ✅ Temporary files are properly cleaned
- ✅ Safety checks work correctly
- ⚠️ External cleanup scripts detected (source of the problem)

## Recommendations for Users

### Immediate Actions
1. **Check for external cleanup scripts** - Look for the scripts mentioned above
2. **Review any batch files or shortcuts** - Make sure they don't call cleanup scripts
3. **Use --dry-run flag** - Always test cleanup scripts with --dry-run first

### Best Practices
1. **Only run cleanup scripts manually** when you specifically want to clean files
2. **Backup important outputs** to a separate folder before running any cleanup
3. **Read script documentation** before running any cleanup tools
4. **Monitor console output** - The GUI now clearly logs what it's cleaning

### Safe Cleanup Usage
If you need to use the external cleanup scripts:

```bash
# Always test first with dry-run
python framepack_filename_cleaner.py --folder outputs --dry-run

# Only run actual cleanup after reviewing what would be deleted
python framepack_filename_cleaner.py --folder outputs
```

## Conclusion
The FramePack GUI has been enhanced with multiple layers of protection to ensure it never accidentally deletes output files. The actual source of output deletion is external cleanup scripts that should be used carefully and only when specifically needed.

**The outputs folder is now fully protected from GUI cleanup operations.**
