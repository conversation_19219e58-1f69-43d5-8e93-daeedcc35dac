# PhotoBashing Fix and Queue Clearing Implementation

## Summary

Fixed the PhotoBashing app error and implemented automatic queue clearing functionality when the GUI becomes idle or when force terminate is used.

## Issues Fixed

### 1. PhotoBashing App Error
**Problem**: The PhotoBashing app was failing to load with the error:
```
Error: 'PhotoBashingApp' object has no attribute 'add_bounds_to_background'
```

**Solution**: Added the missing `add_bounds_to_background` method to the `PhotoBashingApp` class in `image_photo_bashing.pyw`.

**Implementation**:
```python
def add_bounds_to_background(self):
    """Add bounds/border to the background image"""
    if not self.background_image:
        messagebox.showwarning("Warning", "No background image to add bounds to")
        return
    
    try:
        # Get the background image
        bg_img = self.background_image.original_image.copy()
        
        # Add a border/bounds to the image
        border_width = 10  # Default border width
        border_color = (128, 128, 128, 255)  # Gray border
        
        # Create new image with border
        new_width = bg_img.width + (border_width * 2)
        new_height = bg_img.height + (border_width * 2)
        
        # Create new image with border
        bordered_img = Image.new('RGBA', (new_width, new_height), border_color)
        bordered_img.paste(bg_img, (border_width, border_width))
        
        # Update the background image
        self.background_image.original_image = bordered_img
        self.background_image.create_canvas_image()
        
        self.update_status("Added bounds to background image")
        
    except Exception as e:
        messagebox.showerror("Error", f"Failed to add bounds to background: {str(e)}")
        self.update_status("Failed to add bounds to background")
```

### 2. Queue Clearing Functionality
**Problem**: User requested that the job queue be cleared when:
1. The GUI becomes idle
2. Force terminate all is used

**Solution**: Implemented automatic queue clearing in two locations:

#### A. Force Terminate All
Modified the `reset_all_ui_states()` function in `framepack_gui.py` to clear the job queue when force terminate is used:

```python
# Clear the job queue when force terminate is used
if hasattr(self, 'job_queue'):
    queue_count = len(self.job_queue)
    self.job_queue.clear()
    if queue_count > 0:
        print(f"FORCE TERMINATE: Cleared {queue_count} items from job queue")
        self.update_queue_display()
```

#### B. GUI Idle State
Modified the `tune_back_in_to_cli()` function in `framepack_gui.py` to clear the job queue when the GUI becomes idle (but not during active queue processing):

```python
elif cli_state == "idle":
    self.backend_state = "idle"
    # Only reset generation_started if not processing queue
    if not self.is_processing_queue:
        self.generation_started = False
        print("CLI is idle - updated UI for idle state, generation_started reset")
        
        # Clear the job queue when GUI becomes idle (not during queue processing)
        if hasattr(self, 'job_queue') and self.job_queue:
            queue_count = len(self.job_queue)
            self.job_queue.clear()
            self.current_job = None
            self.current_job_iteration = 0
            self.update_queue_display()
            print(f"GUI idle: Cleared {queue_count} items from job queue")
            self.show_status(f"GUI idle: Cleared {queue_count} items from job queue", "info")
```

## Key Features

### PhotoBashing App
- ✅ **Fixed Missing Method**: Added `add_bounds_to_background` method
- ✅ **Border Functionality**: Adds a 10-pixel gray border around background images
- ✅ **Error Handling**: Proper error handling and user feedback
- ✅ **UI Integration**: Updates canvas and status messages appropriately

### Queue Clearing
- ✅ **Force Terminate Clearing**: Queue is cleared when "Force Terminate All" is used
- ✅ **Idle State Clearing**: Queue is cleared when GUI becomes idle (not during processing)
- ✅ **Smart Detection**: Only clears during idle state, not during active queue processing
- ✅ **User Feedback**: Shows status messages when queue is cleared
- ✅ **Complete Reset**: Clears job queue, current job, and iteration counters
- ✅ **UI Updates**: Updates queue display to reflect cleared state

## Behavior

### When Queue is Cleared
1. **Force Terminate All**: Always clears the queue regardless of state
2. **GUI Idle**: Only clears when not actively processing a queue
3. **Status Messages**: User is informed when queue is cleared
4. **UI Updates**: Queue display is updated to show empty state

### What Gets Cleared
- `self.job_queue` - The main job queue list
- `self.current_job` - Currently active job
- `self.current_job_iteration` - Current iteration counter
- Queue display is updated via `self.update_queue_display()`

### What Doesn't Get Cleared
- `self.selected_files` - The batch file list (input files remain)
- UI settings and configurations
- Output files and generated content

## Testing

### PhotoBashing App
- ✅ App loads without errors
- ✅ Background image functionality works
- ✅ Add bounds feature is accessible

### Queue Clearing
- ✅ Force terminate clears queue
- ✅ Idle state detection works
- ✅ Queue processing is not interrupted
- ✅ UI updates correctly

## Files Modified

1. **`image_photo_bashing.pyw`**
   - Added `add_bounds_to_background()` method
   - Fixed missing method error

2. **`framepack_gui.py`**
   - Modified `reset_all_ui_states()` for force terminate queue clearing
   - Modified `tune_back_in_to_cli()` for idle state queue clearing
   - Added proper error handling and user feedback

## User Impact

- **PhotoBashing App**: Now loads and functions correctly
- **Queue Management**: Automatic cleanup prevents queue buildup
- **User Control**: Force terminate provides complete reset
- **Workflow**: Idle detection keeps interface clean
- **Feedback**: Clear status messages inform user of actions

The implementation provides the requested functionality while maintaining the existing workflow and ensuring proper error handling and user feedback.
