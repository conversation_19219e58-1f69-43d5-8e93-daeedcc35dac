# FramePack Prompt Chain Implementation

## Overview
Successfully implemented a seamless batch video continuation scheme that integrates into the FramePack GUI. This allows users to create a series of prompts that will generate consecutively, with each prompt using the previous video's final frame as input.

## Key Features Implemented

### 1. GUI Layout Modifications
- **Compacted prompt settings**: Converted the large fallback prompt text area to a single-line entry to save vertical space
- **Added Prompt Chain section**: New collapsible section below existing prompt settings
- **Maintained 1920x1080 compatibility**: Carefully designed to fit within standard monitor resolution

### 2. Prompt Chain Management with Individual Settings
- **Enable/Disable Toggle**: Checkbox to enable prompt chain functionality
- **Chain List Display**: Listbox showing prompts with settings info (e.g., "[FramePack, 9.0s, seed:123]")
- **Settings Capture**: Each prompt saves current generation settings when added
- **Chain Management Buttons**:
  - Add Current: Adds current fallback prompt with current settings to chain
  - Add Custom: Opens dialog to add custom prompt with current settings
  - Load Settings: Loads settings from selected chain step into current UI
  - Remove: Removes selected prompt from chain
  - Clear All: Clears entire prompt chain with confirmation
  - Move Up/Down: Reorders prompts in the chain

### 3. Queue System Integration
- **Chain-aware Jobs**: Queue jobs now store prompt chains instead of single prompts
- **Iteration Handling**: Each iteration processes the entire prompt chain
- **Progress Tracking**: Shows current prompt in chain during processing
- **Queue Display**: Shows chain information (e.g., "Chain: 4 prompts")

### 4. Processing Logic
- **Sequential Processing**: Each prompt in the chain is processed in order
- **Frame Continuity**: Uses the `_end.png` file from the previous generation as input for the next
- **Chain Progress**: Tracks current position in the chain (e.g., "Step 2/4")
- **Iteration Support**: Entire chains can be repeated for multiple iterations

### 5. Backend Integration
- **Job Settings**: Extended job structure to include prompt chain data
- **State Management**: Tracks current chain index and prompt during processing
- **Status Messages**: Enhanced status display with chain progress information

## Technical Implementation Details

### New Variables Added
```python
# Prompt Chain system variables
self.use_prompt_chain = tk.BooleanVar(value=False)  # Enable/disable prompt chaining
self.prompt_chain = []  # List of dictionaries containing prompt and settings for each chain step
self.current_chain_index = 0  # Current prompt index in the chain during processing
self.chain_input_image = None  # Path to the current input image for chain processing
```

### Chain Step Data Structure
Each chain step is now a dictionary containing:
```python
{
    "prompt": "Your prompt text here",
    "settings": {
        "seed": 12345,
        "video_length": 15.0,
        "steps": 50,
        "cfg": 2.0,
        "distilled_cfg": 8.0,
        "model_type": "F1",
        "latent_window_size": 32,
        "use_teacache": False,
        "gpu_memory": 24.0,
        "mp4_crf": 16,
        "negative_prompt": "blurry, low quality",
        "use_image_prompt": True,
        "overwrite": True,
        "fix_encoding": True,
        "copy_to_input": False
    }
}
```

### Key Functions Added
- `get_current_generation_settings()`: Capture current UI settings
- `apply_generation_settings()`: Apply settings to UI
- `toggle_prompt_chain()`: Enable/disable chain controls
- `add_current_prompt_to_chain()`: Add current prompt with settings to chain
- `add_custom_prompt_to_chain()`: Add custom prompt with settings via dialog
- `load_chain_step_settings()`: Load settings from selected chain step into UI
- `remove_chain_prompt()`: Remove selected prompt
- `clear_chain_prompts()`: Clear all prompts with confirmation
- `move_chain_prompt_up/down()`: Reorder prompts
- `update_chain_display()`: Update chain listbox display with settings info
- `process_prompt_chain()`: Handle chain processing logic
- `complete_current_job()`: Helper for job completion

### Modified Functions
- `add_current_settings_to_queue()`: Now includes prompt chain settings
- `process_next_queue_item()`: Enhanced with chain processing logic
- `update_queue_display()`: Shows chain information
- `apply_job_settings()`: Applies chain settings to UI
- `save/load_default_settings()`: Includes chain settings in persistence

## Usage Workflow

### Setting Up a Prompt Chain
1. Enable "Enable Prompt Chain" checkbox
2. Enter prompts and add them to the chain using "Add Current" or "Add Custom"
3. Reorder prompts as needed using Move Up/Down buttons
4. Add the job to the queue with desired iterations

### Processing Behavior
1. **First Prompt**: Uses the original input image(s)
2. **Subsequent Prompts**: Each prompt uses the previous video's final frame (`_end.png`) as input
3. **Chain Completion**: After all prompts in chain are processed, moves to next iteration or next job
4. **Progress Display**: Shows current chain step and prompt being processed

### Queue Integration
- Each queue job can have its own prompt chain
- Iterations repeat the entire prompt chain
- Queue display shows chain information
- Progress tracking includes chain step information

## File Continuity System
The system leverages the existing `_end.png` file generation in both `batch.py` and `batch_f1.py`:
- Each video generation saves its final frame as `{job_id}_end.png`
- The next prompt in the chain uses this end frame as its input image
- This creates seamless video continuity between prompts

## Benefits
1. **Seamless Continuity**: Each video flows naturally into the next
2. **Creative Flexibility**: Users can create complex narrative sequences
3. **Queue Integration**: Works with existing iteration and queue systems
4. **User-Friendly**: Intuitive interface for managing prompt sequences
5. **Backward Compatible**: Existing functionality remains unchanged

## Future Enhancements
- Save/load prompt chain presets
- Import prompt chains from text files
- Visual preview of chain sequence
- Advanced chain branching options
- Integration with prompt templates

## Bug Fix: File Filtering Issue

### Problem Identified
The initial implementation failed because the batch scripts were filtering out files with "expanded" in their names, but prompt chain processing needs to use `_end.png` files from previous generations as input.

### Solution Implemented
1. **Modified File Filtering Logic**: Updated `should_exclude_file()` function in both `batch.py` and `batch_f1.py` to accept a `prompt_chain_mode` parameter
2. **Special Handling for End Frames**: In prompt chain mode, files ending with `_end` are allowed through the filter
3. **Command Line Arguments**: Added new arguments to both batch scripts:
   - `--prompt_chain_mode`: Enable prompt chain processing
   - `--chain_index`: Current step in the chain (0-based)
   - `--chain_total`: Total number of prompts in chain
   - `--use_chain_input`: Use previous video's end frame as input

### Chain Input Logic
- **First Prompt (index 0)**: Uses original input files
- **Subsequent Prompts**: Automatically finds the most recent `*_end.png` file in output directory
- **File Selection**: Sorts end frames by modification time to get the most recent

## Testing Recommendations
1. Test basic chain functionality with 2-3 prompts
2. Verify frame continuity between chain steps
3. Test with different iteration counts
4. Verify queue processing with multiple chain jobs
5. Test UI responsiveness and layout on 1920x1080 screens
6. Verify that `_end.png` files are properly used as input for subsequent chain steps
