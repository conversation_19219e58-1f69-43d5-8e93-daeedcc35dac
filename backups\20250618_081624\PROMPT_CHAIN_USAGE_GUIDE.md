# FramePack Prompt Chain Usage Guide

## Overview
The Prompt Chain feature allows you to create seamless video sequences where each prompt uses the previous video's final frame as input, creating continuous narrative flow.

## How to Use Prompt Chain

### 1. Setting Up a Prompt Chain with Individual Settings

1. **Enable Prompt Chain**: Check the "Enable Prompt Chain" checkbox in the GUI
2. **Configure Settings**: Set your desired generation settings (seed, video length, model, etc.)
3. **Add Prompts**: Use the following methods to build your chain:
   - **Add Current**: Adds the current fallback prompt WITH current settings to the chain
   - **Add Custom**: Opens a dialog to enter a custom prompt WITH current settings
4. **Manage Chain**: Use the management buttons:
   - **Load Settings**: Load settings from selected chain step into current UI
   - **Remove**: Remove selected prompt from chain
   - **Clear All**: Clear entire chain (with confirmation)
   - **Move Up/Down**: Reorder prompts in the chain

**Important**: Settings are captured when you add each prompt to the chain, so configure your settings before adding each step!

### 2. Example Workflow with Different Settings

Let's create a 3-step video sequence with different settings for each step:

1. **Step 1**:
   - Set: FramePack model, 9 seconds, seed 123, 30 steps
   - Add prompt: "A peaceful forest clearing at dawn"

2. **Step 2**:
   - Set: F1 model, 12 seconds, seed 456, 50 steps
   - Add prompt: "The same forest clearing as storm clouds gather"

3. **Step 3**:
   - Set: FramePack model, 6 seconds, seed 789, 40 steps
   - Add prompt: "The forest clearing during a dramatic thunderstorm"

Each step will use its own unique settings while maintaining visual continuity!

### 3. Processing Behavior

- **First Prompt**: Uses your original input image(s)
- **Second Prompt**: Uses the final frame from the first video as input
- **Third Prompt**: Uses the final frame from the second video as input
- **Result**: Three videos that flow seamlessly into each other

### 4. Queue Integration

- **Multiple Jobs**: Each queue job can have its own prompt chain
- **Iterations**: Set iterations to repeat the entire chain multiple times
- **Progress Tracking**: GUI shows current chain step during processing

### 5. Advanced Features

#### Individual Settings Per Step
- **Different Models**: Mix FramePack and F1 models in the same chain
- **Variable Lengths**: Short intro (6s) + long middle (15s) + quick ending (3s)
- **Seed Control**: Use specific seeds for reproducible results per step
- **Quality Settings**: High steps for important scenes, lower for transitions

#### Settings Management
- **Load Settings**: Click "Load Settings" to copy settings from any chain step to current UI
- **Modify Existing**: Load settings, modify them, then add as new step
- **Template Steps**: Create template steps with common settings, then modify prompts

#### Iterations with Chains
- Set iterations to 3 with a 2-prompt chain = 6 total videos (3 iterations × 2 prompts)
- Each iteration starts fresh with the original input image
- Each step uses its saved settings consistently across iterations

#### Multiple Queue Jobs
- Job 1: 3-prompt nature chain with FramePack settings, 2 iterations
- Job 2: 4-prompt urban chain with F1 settings, 1 iteration
- Job 3: 2-prompt abstract chain with mixed models, 3 iterations

## Technical Details

### File Naming
- Each video saves its final frame as `{job_id}_end.png`
- Chain processing automatically finds and uses these end frames
- Original input images remain unchanged

### Model Compatibility
- Works with both standard FramePack and F1 models
- Can mix different models within the same chain
- Each step uses its own saved model and settings
- Automatically handles model-specific requirements (CFG handling, etc.)

### Error Handling
- If no end frame is found, falls back to original input
- Graceful handling of missing files or processing errors
- Clear status messages for troubleshooting

## Best Practices

### 1. Prompt Design
- **Continuity**: Reference elements from previous prompts
- **Progression**: Build logical narrative or visual progression
- **Consistency**: Maintain consistent style/setting across chain

### 2. Settings Strategy
- **Plan Ahead**: Think about which settings each step needs before adding
- **Use Load Settings**: Copy settings from existing steps as starting points
- **Test Combinations**: Verify that different models/settings work well together
- **Document Chains**: Note which settings work well for different types of content

### 3. Technical Tips
- **Test Short Chains**: Start with 2-3 prompts to verify flow
- **Monitor Output**: Check end frames between steps
- **Backup Inputs**: Keep original images safe
- **Settings Preview**: Use "Load Settings" to preview what each step will use

### 3. Creative Applications

#### Storytelling
```
1. "A character walking towards a mysterious door"
2. "The character opening the glowing door"
3. "The character stepping through into a magical realm"
```

#### Time Progression
```
1. "A flower bud in early morning light"
2. "The flower beginning to bloom at midday"
3. "The flower in full bloom at sunset"
```

#### Style Evolution with Model Progression
```
1. "A realistic portrait of a person" [FramePack, 9s, seed:123]
2. "The same person in impressionist painting style" [F1, 12s, seed:456]
3. "The same person in abstract art style" [FramePack, 6s, seed:789]
```

#### Technical Progression
```
1. "A simple sketch of a building" [FramePack, 6s, 20 steps]
2. "The building with more detail and shading" [FramePack, 9s, 40 steps]
3. "The building fully rendered with lighting" [F1, 12s, 60 steps]
```

## Troubleshooting

### Common Issues

**Chain Not Processing**
- Verify "Enable Prompt Chain" is checked
- Ensure prompts are added to the chain list
- Check that queue job includes chain settings

**Missing Frame Continuity**
- Verify `_end.png` files are being generated
- Check output directory permissions
- Ensure sufficient disk space

**Unexpected Results**
- Review prompt wording for continuity
- Check model settings consistency
- Verify input image quality

### Debug Information
- Enable debug mode for detailed logging
- Check console output for chain processing messages
- Monitor temp and output directories

## Performance Considerations

- **Memory**: Each chain step processes independently
- **Storage**: End frames are saved for each step
- **Time**: Total time = (steps in chain) × (normal processing time)

## Limitations

- Chain processing is sequential (not parallel)
- Maximum practical chain length depends on available storage
- Each step must complete before the next begins

## Future Enhancements

Planned features for future versions:
- Save/load prompt chain presets
- Visual chain preview
- Branching chain support
- Batch chain import from files
