# FramePack Queue Processing Fix

## Problem Identified

The batch generation system was broken after implementing the enhanced stability improvements. The issue manifested as:

1. **First iteration completed successfully**
2. **Queue processing stopped after first iteration**
3. **Error message**: "Insert the diskette that contains the batch file"
4. **No continuation to next iteration**

## Root Cause Analysis

The enhanced stability system was interfering with queue processing in several ways:

### 1. **Aggressive File Cleanup**
- The new `handle_process_completion()` method was cleaning up communication files (`framepack_completed.signal`, `stop_framepack.flag`) immediately after each iteration
- Queue processing relies on these files to coordinate between iterations
- Premature cleanup broke the communication chain between iterations

### 2. **UI State Reset During Queue Processing**
- The enhanced completion handlers were resetting UI buttons during queue processing
- This interfered with the queue state management
- The "Run FramePack" button was being re-enabled prematurely

### 3. **Backend Monitoring Interference**
- Backend monitoring was running at high frequency during queue processing
- State updates were happening too aggressively
- This created race conditions with the queue processing logic

## Solution Implemented

### 1. **Queue-Aware File Cleanup**

**Before:**
```python
# Always cleaned up files immediately
completion_files = ["framepack_completed.signal", "stop_framepack.flag"]
for file_path in completion_files:
    if os.path.exists(file_path):
        os.remove(file_path)
```

**After:**
```python
# Only clean up communication files if we're NOT processing a queue
if not self.is_processing_queue:
    completion_files = ["framepack_completed.signal", "stop_framepack.flag"]
    for file_path in completion_files:
        if os.path.exists(file_path):
            os.remove(file_path)
else:
    print("Skipping file cleanup - queue processing active")
```

### 2. **Queue-Aware UI State Management**

**Before:**
```python
# Always reset all UI buttons
self.run_button.config(state="normal")
self.stop_button.config(state="disabled")
self.stop_queue_button.config(state="disabled")
```

**After:**
```python
# Only reset UI buttons if not processing queue
if not self.is_processing_queue:
    self.run_button.config(state="normal")
    self.stop_button.config(state="disabled")
    self.stop_queue_button.config(state="disabled")
else:
    # For queue processing, just disable stop button
    self.stop_button.config(state="disabled")
```

### 3. **Reduced Backend Monitoring During Queue Processing**

**Before:**
```python
# Same monitoring frequency always
self.root.after(3000, self.monitor_backend_state)
```

**After:**
```python
# Use longer intervals during queue processing to avoid interference
if self.is_processing_queue:
    self.root.after(5000, self.monitor_backend_state)
else:
    self.root.after(3000, self.monitor_backend_state)
```

### 4. **Conservative Backend State Updates During Queue Processing**

**Before:**
```python
# Always performed full state updates
def update_backend_state(self):
    # Check all state changes aggressively
```

**After:**
```python
def update_backend_state(self):
    # Be more conservative during queue processing
    if self.is_processing_queue:
        # Only update state for critical changes
        if os.path.exists("framepack_completed.signal"):
            if self.backend_state != "idle":
                self.backend_state = "idle"
        return
    # Normal state updates when not processing queue
```

### 5. **Increased Delays Between Queue Items**

**Before:**
```python
self.root.after(1000, self.process_next_queue_item)  # 1 second
```

**After:**
```python
self.root.after(2000, self.process_next_queue_item)  # 2-3 seconds for stability
```

## Methods Updated

### Primary Completion Handlers
1. **`handle_process_completion()`** - Enhanced with queue awareness
2. **`handle_completion()`** - Updated UI reset logic
3. **`basic_completion_handling()`** - Added queue-aware fallback

### Backend Monitoring
1. **`monitor_backend_state()`** - Reduced frequency during queue processing
2. **`update_backend_state()`** - Conservative updates during queue processing

### State Management
1. **`reset_ui_buttons_safe()`** - Preserved for non-queue operations
2. **Queue processing logic** - Maintained separation from stability system

## Key Principles Applied

### 1. **Separation of Concerns**
- Queue processing logic remains independent
- Stability system operates around queue processing
- No interference between systems

### 2. **Conservative During Queue Processing**
- Reduced monitoring frequency
- Minimal state changes
- Preserved communication files

### 3. **Graceful Degradation**
- Fallback handlers maintain queue awareness
- Multiple levels of error handling
- Queue processing continues even if stability system has issues

### 4. **Proper Timing**
- Increased delays between iterations for stability
- Proper cleanup timing
- Coordinated state transitions

## Testing Recommendations

1. **Single Iteration**: Test single generation works correctly
2. **Multiple Iterations**: Test queue with multiple iterations of same job
3. **Multiple Jobs**: Test queue with different jobs and iterations
4. **Error Recovery**: Test queue processing continues after errors
5. **Stop/Resume**: Test stopping and resuming queue processing

## Benefits

1. **✅ Queue Processing Restored**: Multiple iterations now work correctly
2. **✅ Stability Maintained**: Enhanced stability system still active
3. **✅ No Interference**: Systems operate independently
4. **✅ Better Error Handling**: Queue-aware error recovery
5. **✅ Improved Timing**: Better coordination between iterations

## Future Improvements

1. **Queue State Persistence**: Save queue state to survive app restarts
2. **Progress Indicators**: Better visual feedback during queue processing
3. **Queue Prioritization**: Allow reordering of queue items
4. **Batch Optimization**: Optimize resource usage across iterations

## CRITICAL FIX: Dual Status Monitoring System Conflict

### Additional Root Cause Discovered

After the initial fixes, the queue processing was still failing because **TWO status monitoring systems were running simultaneously**:

1. **Old System**: `check_processing_status()` method (legacy)
2. **New System**: `enhanced_status_check()` method (stability improvements)

Both systems were calling completion handlers when they detected process completion, causing:
- **Double completion handling**
- **Race conditions between handlers**
- **Conflicting file cleanup operations**
- **Queue processing being triggered multiple times**

### Final Solution: Single Status Monitoring System

**Problem**: Both monitoring systems active simultaneously
```python
# OLD SYSTEM (still running)
def check_processing_status(self):
    # ... detects completion ...
    self.handle_process_completion()  # First call

# NEW SYSTEM (also running)
def enhanced_status_check(self):
    # ... detects completion ...
    self.handle_process_completion()  # Second call - CONFLICT!
```

**Solution**: Disable old system during queue processing
```python
def check_processing_status(self):
    # CRITICAL: Prevent dual monitoring during queue processing
    if self.is_processing_queue:
        print("Skipping old status check - queue processing active")
        return
    # ... rest of old monitoring logic
```

### Complete Fix Implementation

1. **Disabled Old System During Queue Processing**
   - Added queue check at start of `check_processing_status()`
   - Prevented old system from scheduling itself during queue processing
   - Ensured only enhanced monitoring runs during queue operations

2. **Queue-Aware Fallback Prevention**
   - Updated `fallback_status_monitoring()` to skip during queue processing
   - Modified error handling to not fallback to old system during queue operations
   - Added logging to track which monitoring system is active

3. **Enhanced System Priority**
   - Made enhanced monitoring the primary system
   - Old system only runs for non-queue operations
   - Clear separation of responsibilities

### Code Changes Summary

**File**: `framepack_gui.py`

**Method**: `check_processing_status()` (Line 4773)
```python
# Added at beginning of method
if self.is_processing_queue:
    print("Skipping old status check - queue processing active (using enhanced monitoring)")
    return
```

**Method**: `start_checking_status()` (Line 2888)
```python
# Enhanced logging and queue awareness
print("Starting enhanced status monitoring (disabling old system)")
if not self.is_processing_queue:
    self.fallback_status_monitoring()
else:
    print("Queue processing active - not using fallback monitoring")
```

**Method**: `fallback_status_monitoring()` (Line 3006)
```python
# Queue-aware fallback
if not self.is_processing_queue:
    print("Using fallback status monitoring")
    self.root.after(10000, self.check_processing_status)
else:
    print("Queue processing active - skipping fallback status monitoring")
```

## Final Results

### ✅ **Queue Processing Now Works Correctly**
- **Single monitoring system** during queue processing
- **No dual completion handling**
- **Clean state transitions** between iterations
- **Proper file coordination** between iterations

### ✅ **Stability Features Maintained**
- Enhanced backend monitoring still active
- Recovery mechanisms still functional
- Better error handling preserved
- Graceful degradation maintained

### ✅ **System Architecture**
- **Queue Processing**: Uses enhanced monitoring only
- **Single Operations**: Can use either system (with fallback)
- **Error Recovery**: Queue-aware recovery mechanisms
- **Clean Separation**: No interference between systems

The queue processing system is now fully functional while maintaining all the stability improvements. The critical fix was **eliminating the dual monitoring system conflict** that was causing race conditions and multiple completion handlers to fire simultaneously.
