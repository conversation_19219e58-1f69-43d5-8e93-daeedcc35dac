# Technical Guide: Reverting Transparency/Text-to-Video Changes

## Overview
This guide provides exact technical steps to revert transparency detection and text-to-video changes that cause washed-out video output in FramePack. Use this if the original code is not available.

## Problem Identification
**Symptoms**: Videos appear washed out, desaturated, or have incorrect colors/brightness
**Root Cause**: Modified VAE encoding pipeline with transparency detection interfering with normal image processing

## Files to Modify

### 1. `diffusers_helper/utils.py`

**REMOVE** the entire `load_start_latent_with_t2v_support()` function if it exists.

**Function signature to look for**:
```python
def load_start_latent_with_t2v_support(image_path, device, H, W, T_frames, vae, seed=None, use_noise=False):
```

**Complete removal**: Delete the entire function including:
- All transparency detection logic using `getextrema()` or alpha channel checks
- Conditional noise generation based on transparency
- Channel expansion from 4 to 16 channels
- Any `use_noise` parameter handling
- Image resizing within the function
- Frame dimension manipulation (`unsqueeze(2)`)

**Result**: The file should only contain the original utility functions without any text-to-video support.

### 2. `batch_f1.py` (F1 Model Batch Script)

**FIND** the section that imports and uses `load_start_latent_with_t2v_support`

**REPLACE** this pattern:
```python
# Import the new text-to-video support function
from diffusers_helper.utils import load_start_latent_with_t2v_support

# VAE encoding with text-to-video support
print("VAE encoding with text-to-video support...")
if not high_vram:
    load_model_as_complete(vae, target_device=gpu)

# Use the new function that supports both I2V and T2V modes
start_latent, is_text_to_video = load_start_latent_with_t2v_support(
    image_path=image_path,
    device=gpu,
    H=height,
    W=width,
    T_frames=1,  # F1 model uses single frame start latent
    vae=vae,
    seed=seed,
    use_noise=use_noise  # or any variation
)

if is_text_to_video:
    print("🎬 Running in pure text-to-video mode with deterministic seed control")
    # For T2V mode, we don't need the original image processing
    input_image_np = np.zeros((height, width, 3), dtype=np.uint8)  # Create dummy image for CLIP
else:
    print("🖼️ Running in image-to-video mode")
    # Keep the original image processing for I2V mode
    input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1
    input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]
```

**WITH** this standard pattern:
```python
input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1
input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]

# VAE encoding
print("VAE encoding...")
if not high_vram:
    load_model_as_complete(vae, target_device=gpu)

start_latent = vae_encode(input_image_pt, vae)
```

**Key points**:
- Remove the import of `load_start_latent_with_t2v_support`
- Remove all conditional logic based on `is_text_to_video`
- Restore direct call to `vae_encode(input_image_pt, vae)`
- Ensure `input_image_pt` is always created from `input_image_np`

### 3. `batch.py` (Standard Model Batch Script)

**Apply the exact same changes as in `batch_f1.py`**:

**FIND** and **REPLACE** the same pattern:
- Remove `load_start_latent_with_t2v_support` import
- Remove conditional text-to-video logic
- Restore standard VAE encoding: `start_latent = vae_encode(input_image_pt, vae)`

## Critical Technical Details

### VAE Encoding Restoration
**Correct pattern**:
```python
# Standard image tensor preparation
input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1
input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]

# Standard VAE encoding
start_latent = vae_encode(input_image_pt, vae)
```

**Avoid these patterns**:
- Any function that returns `(start_latent, is_text_to_video)` tuple
- Channel expansion: `start_latent.repeat(1, 4, 1, 1, 1)`
- Conditional processing based on image transparency
- Frame dimension manipulation before VAE encoding
- Noise generation instead of image encoding

### Image Processing Pipeline
**Ensure this sequence**:
1. Load image: `input_image = np.array(Image.open(image_path).convert('RGB'))`
2. Resize/crop: `input_image_np = resize_and_center_crop(...)`
3. Convert to tensor: `input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1`
4. Add dimensions: `input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]`
5. VAE encode: `start_latent = vae_encode(input_image_pt, vae)`

## Verification Steps

### 1. Code Verification
- [ ] No `load_start_latent_with_t2v_support` function exists in `utils.py`
- [ ] No imports of `load_start_latent_with_t2v_support` in batch scripts
- [ ] All VAE encoding uses direct `vae_encode(input_image_pt, vae)` calls
- [ ] No conditional logic based on `is_text_to_video` variable
- [ ] No `use_noise` parameters in VAE encoding

### 2. Functional Verification
- [ ] Generate a test video from a regular image
- [ ] Check that colors are vibrant and not washed out
- [ ] Verify brightness levels are correct
- [ ] Test with different image types (JPG, PNG)

### 3. Error Checking
- [ ] No "load_start_latent_with_t2v_support" import errors
- [ ] No undefined variable errors for `is_text_to_video`
- [ ] VAE encoding completes without tensor shape errors

## Common Mistakes to Avoid

1. **Partial Removal**: Don't leave any transparency detection logic
2. **Import Cleanup**: Remove ALL imports of the removed function
3. **Variable References**: Remove ALL references to `is_text_to_video`
4. **Conditional Logic**: Remove ALL conditional processing based on transparency
5. **Parameter Cleanup**: Remove `use_noise` parameters from function calls

## Backup Strategy

Before making changes:
1. Create backup copies of all three files
2. Test with a simple image first
3. Compare video quality before/after changes
4. Keep the working version for reference

## Expected Outcome

After these changes:
- Videos should have proper color saturation
- Brightness levels should be correct
- No washed-out appearance
- Consistent quality across all image types
- Standard image-to-video pipeline restored

## Troubleshooting

**If videos are still washed out**:
1. Check for any remaining transparency detection code
2. Verify VAE encoding uses original `vae_encode()` function
3. Ensure no channel expansion (4→16) is happening
4. Look for any remaining conditional image processing

**If import errors occur**:
1. Remove ALL imports of `load_start_latent_with_t2v_support`
2. Check for any remaining function calls to the removed function
3. Verify all variable references are cleaned up

This guide provides the exact technical steps to restore the original, working video generation pipeline.

## Additional Technical Context

### Why These Changes Cause Washed-Out Videos

**Root Technical Issues**:

1. **Transparency Detection Interference**:
   ```python
   # PROBLEMATIC CODE (remove this pattern):
   alpha_extrema = img.getextrema()[3]  # Get alpha channel extrema
   if alpha_extrema == (0, 0):  # Both min and max alpha are 0 = fully transparent
       use_text_to_video = True
   ```
   - This logic was triggering even for normal images
   - Caused different processing paths for similar images
   - Led to inconsistent VAE encoding

2. **Modified VAE Input Pipeline**:
   ```python
   # PROBLEMATIC CODE (remove this pattern):
   img = img.resize((W, H), Image.LANCZOS)  # Resizing in wrong place
   tensor = tensor.unsqueeze(2)  # Adding frame dimension incorrectly
   ```
   - Image resizing was happening at wrong stage
   - Frame dimensions were being manipulated incorrectly
   - Tensor shapes were inconsistent with original pipeline

3. **Channel Expansion Issues**:
   ```python
   # PROBLEMATIC CODE (remove this pattern):
   if start_latent.shape[1] == 4:  # Standard VAE output has 4 channels
       start_latent = start_latent.repeat(1, 4, 1, 1, 1)  # [1, 4, T, H, W] -> [1, 16, T, H, W]
   ```
   - Artificial channel expansion was corrupting latent space
   - F1 model compatibility changes were affecting standard model
   - Latent tensor structure was being modified incorrectly

### Exact Function Signatures to Remove

**In `diffusers_helper/utils.py`**, remove any function matching these patterns:

```python
# Pattern 1: Full signature
def load_start_latent_with_t2v_support(image_path, device, H, W, T_frames, vae, seed=None, use_noise=False):

# Pattern 2: Without use_noise
def load_start_latent_with_t2v_support(image_path, device, H, W, T_frames, vae, seed=None):

# Pattern 3: Any variation with similar name
def load_start_latent_*(*args, **kwargs):  # If it contains transparency logic
```

### Exact Import Statements to Remove

**In both `batch.py` and `batch_f1.py`**, remove these import patterns:

```python
# Remove these exact lines:
from diffusers_helper.utils import load_start_latent_with_t2v_support

# Or if part of larger import:
from diffusers_helper.utils import save_bcthw_as_mp4, load_start_latent_with_t2v_support, other_functions
# Change to:
from diffusers_helper.utils import save_bcthw_as_mp4, other_functions
```

### Exact Code Blocks to Replace

**Pattern to find and replace in both batch scripts**:

```python
# FIND THIS ENTIRE BLOCK (may span 20-30 lines):
from diffusers_helper.utils import load_start_latent_with_t2v_support

# VAE encoding with text-to-video support
print("VAE encoding with text-to-video support...")
if not high_vram:
    load_model_as_complete(vae, target_device=gpu)

start_latent, is_text_to_video = load_start_latent_with_t2v_support(
    image_path=image_path,
    device=gpu,
    H=height,
    W=width,
    T_frames=1,
    vae=vae,
    seed=seed,
    use_noise=use_noise  # This parameter may or may not be present
)

if is_text_to_video:
    print("🎬 Running in pure text-to-video mode with deterministic seed control")
    input_image_np = np.zeros((height, width, 3), dtype=np.uint8)
else:
    print("🖼️ Running in image-to-video mode")
    input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1
    input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]

# REPLACE WITH THIS EXACT BLOCK (8 lines):
input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1
input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]

# VAE encoding
print("VAE encoding...")
if not high_vram:
    load_model_as_complete(vae, target_device=gpu)

start_latent = vae_encode(input_image_pt, vae)
```

### Critical Validation Points

**After making changes, verify these exact conditions**:

1. **No transparency detection**:
   ```bash
   grep -r "getextrema" diffusers_helper/
   grep -r "alpha_extrema" diffusers_helper/
   # Should return no results
   ```

2. **No conditional T2V logic**:
   ```bash
   grep -r "is_text_to_video" *.py
   grep -r "use_text_to_video" *.py
   # Should return no results in batch scripts
   ```

3. **Standard VAE encoding only**:
   ```bash
   grep -r "vae_encode(" *.py
   # Should show direct calls: vae_encode(input_image_pt, vae)
   ```

4. **No channel expansion**:
   ```bash
   grep -r "repeat(1, 4" diffusers_helper/
   grep -r "16.*channels" diffusers_helper/
   # Should return no results
   ```

### File Size Verification

**Expected file size changes after reversion**:
- `diffusers_helper/utils.py`: Should be ~90 lines smaller
- `batch_f1.py`: Should be ~25-30 lines smaller
- `batch.py`: Should be ~25-30 lines smaller

### Testing Protocol

**Exact test sequence**:
1. Use a standard JPG image (not PNG, not transparent)
2. Generate 5-second video with default settings
3. Check output video for:
   - Proper color saturation (not washed out)
   - Correct brightness levels
   - No color shifts or tinting
4. Compare with known good output if available

This comprehensive technical guide ensures complete and accurate reversion of the problematic transparency/text-to-video changes.
