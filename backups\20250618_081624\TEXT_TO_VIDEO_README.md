# FramePack Text-to-Video Integration

This document describes the new text-to-video functionality integrated into FramePack, which provides deterministic seed control and automatic transparent PNG detection for pure text-to-video generation.

## Overview

The integration implements the tip from the user about using transparent PNG detection and deterministic seed control to enable clean text-to-video generation without requiring an input image.

## Key Features

✅ **Automatic Transparent PNG Detection** - Uses `img.getextrema()[3] == (0, 0)` to detect fully transparent images  
✅ **Deterministic Seed Control** - Uses `torch.Generator(device).manual_seed(seed)` for reproducible results  
✅ **Pure Text-to-Video Mode** - No input image required, generates from noise  
✅ **Seamless Integration** - Works with existing batch scripts and all FramePack features  
✅ **Backward Compatibility** - Existing image-to-video workflows unchanged  

## How It Works

### Transparent PNG Detection
```python
img = Image.open(image_path).convert("RGBA")
if img.getextrema()[3] == (0, 0):  # Both min and max alpha are 0
    # Fully transparent = text-to-video mode
```

### Deterministic Noise Generation
```python
generator = torch.Generator(device=device).manual_seed(seed)
start_latent = torch.randn(
    1, 4, T_frames, H//8, W//8, 
    device=device, 
    dtype=torch.float16,
    generator=generator
)
```

## Usage

### Method 1: Use Transparent PNG (Recommended)

1. Create a transparent PNG:
```python
from PIL import Image
transparent_img = Image.new('RGBA', (512, 512), (0, 0, 0, 0))
transparent_img.save("transparent_t2v.png", format='PNG')
```

2. Use with batch scripts:
```bash
# Standard FramePack
python batch.py "transparent_t2v.png" --prompt "A beautiful sunset" --seed 42

# F1 Model
python batch_f1.py "transparent_t2v.png" --prompt "A cat walking" --seed 12345
```

### Method 2: Programmatic Usage

```python
from diffusers_helper.utils import load_start_latent_with_t2v_support

# Option A: Use None (no image needed)
start_latent, is_text_to_video = load_start_latent_with_t2v_support(
    image_path=None,
    device="cuda",
    H=512, W=512, T_frames=1,
    vae=vae_model,
    seed=42
)

# Option B: Use transparent PNG
start_latent, is_text_to_video = load_start_latent_with_t2v_support(
    image_path="transparent.png",
    device="cuda", 
    H=512, W=512, T_frames=1,
    vae=vae_model,
    seed=42
)

if is_text_to_video:
    print("🎬 Using text-to-video mode")
else:
    print("🖼️ Using image-to-video mode")
```

## Console Output

When text-to-video mode is activated, you'll see:
```
🎬 Running in pure text-to-video mode with deterministic seed control
Creating noise latents for T2V: shape [1, 4, 1, 64, 64]
Generated deterministic noise latents with seed 42
```

## Reproducible Results

Same seed + same prompt = identical video output:

```bash
# These will produce identical videos
python batch_f1.py "transparent.png" --prompt "sunset" --seed 42
python batch_f1.py "transparent.png" --prompt "sunset" --seed 42
```

## Integration Points

The feature is integrated into:
- ✅ `batch.py` (Standard FramePack model)
- ✅ `batch_f1.py` (F1 model)  
- ✅ `diffusers_helper/utils.py` (Core function)
- 🔄 `demo_gradio.py` (Future integration)
- 🔄 `demo_gradio_f1.py` (Future integration)

## Technical Details

### Function Signature
```python
def load_start_latent_with_t2v_support(
    image_path,      # Path to image or None
    device,          # Target device ('cuda' or 'cpu')
    H, W,           # Target height and width (divisible by 8)
    T_frames,       # Number of frames for latent tensor
    vae,            # VAE model for encoding (can be None for T2V)
    seed=None       # Seed for deterministic generation
):
    return start_latent, is_text_to_video
```

### Return Values
- `start_latent`: Tensor with shape `[1, channels, T_frames, H//8, W//8]`
- `is_text_to_video`: Boolean indicating if T2V mode was used

## Testing

Run the test script to verify functionality:
```bash
python test_text_to_video.py
```

View usage examples:
```bash
python example_text_to_video.py
```

## Best Practices

1. **Use specific seeds** for reproducible experiments
2. **Document your seeds** for future reference  
3. **Use transparent PNGs** for cleaner workflows than None paths
4. **Test with different seeds** to explore variations
5. **Combine with LoRAs** and custom models as needed

## Compatibility

- ✅ Works with both standard and F1 models
- ✅ Compatible with LoRA loading
- ✅ Supports custom models
- ✅ Works with all existing FramePack parameters
- ✅ Maintains backward compatibility

## Future Enhancements

- Integration with Gradio demo interfaces
- GUI controls for T2V mode selection
- Batch processing of multiple T2V prompts
- Advanced noise scheduling options

---

*This feature implements the user's suggestion for deterministic text-to-video generation using transparent PNG detection and torch.Generator seed control.*
