# FramePack GUI URL Drag and Drop Fix

This document explains the fixes applied to the FramePack GUI to enable URL drag and drop functionality.

## Issues Fixed

1. **Fixed References to `use_individual_files`**: The code was updated to use the new `input_mode` variable instead of the old `use_individual_files` boolean.

2. **Updated Settings Saving and Loading**: The settings saving and loading methods were updated to use the new `input_mode` variable.

3. **Fixed Batch File Generation**: The batch file generation code was updated to handle the three different modes (directory, files, and URLs).

## Changes Made

### 1. Updated save_default_settings Method

```python
def save_default_settings(self):
    settings = {
        # ... other settings ...
        "input_mode": self.input_mode.get(),  # Changed from use_individual_files
        "selected_files": self.selected_files
    }
    # ... rest of the method ...
```

### 2. Updated save_settings Method

```python
settings = {
    # ... other settings ...
    "input_mode": self.input_mode.get(),  # Changed from use_individual_files
    "selected_files": self.selected_files
}
```

### 3. Updated Batch File Generation

```python
# Write settings summary
current_mode = self.input_mode.get()
if current_mode == "directory":
    # Directory mode settings
    # ...
elif current_mode == "files":
    # Files mode settings
    # ...
else:  # URLs mode
    # URLs mode settings
    # ...
```

## How to Use

To use the fixed URL drag and drop functionality:

1. Run the FramePack GUI using `run_framepack_url_fixed.bat`
2. Select the "Process URLs" radio button
3. Drag and drop URLs from your browser into the file list area
4. Alternatively, click the "Add URLs..." button to manually enter URLs
5. Click "Run FramePack" to process the URLs

## Notes

- The batch.py script must support the `--url-list` parameter for this functionality to work correctly.
- URLs are displayed in the listbox with a shortened format if they are longer than 50 characters.
- The GUI automatically switches to the appropriate mode (files or URLs) based on what is dropped.
- No popup confirmation is shown after adding files or URLs, making the drag and drop experience smoother.

## Troubleshooting

If you encounter any issues:

1. Make sure tkinterdnd2 is installed in your virtual environment:
   ```
   venv\Scripts\activate
   pip install tkinterdnd2
   ```

2. Check the console for any error messages.

3. Try running the standalone test script to verify that drag and drop is working correctly:
   ```
   python standalone_dnd_test.py
   ```
