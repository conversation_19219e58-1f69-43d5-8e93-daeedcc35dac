# FramePack GUI URL Drag and Drop Implementation

This document explains the implementation of URL drag and drop functionality in the FramePack GUI.

## Overview

The FramePack GUI has been enhanced with the following features:

1. **URL Drag and Drop Support**: Users can now drag and drop URLs directly from their browser into the FramePack GUI.
2. **Mode Selection**: A new "Process URLs" radio button has been added alongside the existing "Process Directory" and "Process Files" options.
3. **Dynamic Button Labels**: The "Add Files..." button changes to "Add URLs..." when in URL mode.
4. **No Popup Confirmation**: The popup confirmation dialog after adding files/URLs has been removed for a smoother experience.

## Implementation Details

### Input Mode Variable

The implementation uses a string variable `input_mode` instead of the boolean `use_individual_files` to support three modes:

```python
self.input_mode = tk.StringVar(value="directory")  # Mode: "directory", "files", or "urls"
```

### Radio Buttons

Three radio buttons are now provided for mode selection:

```python
# Directory mode radio button
self.dir_mode_radio = ttk.Radiobutton(
    mode_frame,
    text="Process Directory",
    variable=self.input_mode,
    value="directory",
    command=self.toggle_input_mode
)

# Individual files mode radio button
self.files_mode_radio = ttk.Radiobutton(
    mode_frame,
    text="Process Files",
    variable=self.input_mode,
    value="files",
    command=self.toggle_input_mode
)

# URL mode radio button
self.url_mode_radio = ttk.Radiobutton(
    mode_frame,
    text="Process URLs",
    variable=self.input_mode,
    value="urls",
    command=self.toggle_input_mode
)
```

### Toggle Input Mode

The `toggle_input_mode` method has been updated to handle all three modes:

```python
def toggle_input_mode(self):
    """Toggle between directory, files, and URL modes"""
    current_mode = self.input_mode.get()
    
    # Hide all frames first
    self.dir_frame.grid_remove()
    self.files_frame.grid_remove()
    
    if current_mode == "directory":
        # Directory mode
        self.dir_frame.grid()
    elif current_mode == "files" or current_mode == "urls":
        # Files or URLs mode - both use the files_frame
        self.files_frame.grid()
        
        # Update buttons and labels based on mode
        if current_mode == "files":
            self.add_files_button.config(text="Add Files...")
            self.drag_drop_label.config(text="Drag and drop files here", foreground="blue")
        else:  # URLs mode
            self.add_files_button.config(text="Add URLs...")
            self.drag_drop_label.config(text="Drag and drop URLs here", foreground="blue")
```

### Drop Files Method

The `drop_files` method has been enhanced to handle both files and URLs:

```python
def drop_files(self, event):
    """Handle files or URLs dropped onto the widget"""
    # ...
    
    # Check if it's a URL
    if item.startswith(('http://', 'https://')):
        # Handle URL
        if self.input_mode.get() != "urls":
            self.input_mode.set("urls")
            self.toggle_input_mode()
        
        # Add URL to the list
        # ...
    else:
        # Handle file
        if self.input_mode.get() != "files":
            self.input_mode.set("files")
            self.toggle_input_mode()
        
        # Add file to the list
        # ...
    
    # Show a summary message in the status label (no popup)
    # ...
```

### Run FramePack Method

The `run_framepack` method has been updated to handle URL lists:

```python
def run_framepack(self):
    current_mode = self.input_mode.get()
    
    # ...
    
    # Add arguments based on mode
    if current_mode == "directory":
        # Directory mode
        cmd.extend(["--input_dir", self.input_dir.get()])
    else:
        # Files or URLs mode - create a temporary file with paths/URLs
        # ...
        
        # Add the appropriate parameter based on mode
        if current_mode == "files":
            cmd.extend(["--file-list", temp_file_list])
        else:  # URLs mode
            cmd.extend(["--url-list", temp_file_list])
```

## Usage

To use the URL drag and drop functionality:

1. Run the FramePack GUI using `run_framepack_with_url.bat`
2. Select the "Process URLs" radio button
3. Drag and drop URLs from your browser into the file list area
4. Alternatively, click the "Add URLs..." button to manually enter URLs
5. Click "Run FramePack" to process the URLs

## Notes

- The batch.py script must support the `--url-list` parameter for this functionality to work correctly.
- URLs are displayed in the listbox with a shortened format if they are longer than 50 characters.
- The GUI automatically switches to the appropriate mode (files or URLs) based on what is dropped.
- No popup confirmation is shown after adding files or URLs, making the drag and drop experience smoother.
