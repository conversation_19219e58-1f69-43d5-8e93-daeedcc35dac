# FramePack Video Preview System

This document provides instructions for testing and using the dual video preview system in FramePack.

## Overview

The FramePack application now features a sophisticated dual video preview system that provides real-time feedback during the video generation process:

1. **Latent Preview Animation** - A low-resolution, real-time preview showing the generation process as it happens
2. **Final Output Preview** - A high-quality display of the most recently completed video

## Testing the Video Preview System

To test the video preview system, follow these steps:

1. **Install Dependencies**:
   - Make sure you have Python installed
   - Install the required packages: `pip install tkvideoplayer pillow`
   - Ensure ffmpeg is installed and in your PATH (required for generating sample videos)

2. **Generate Sample Preview Videos**:
   - Run `generate_sample_previews.bat` to create sample latent preview and output videos
   - This will create videos in the `latent_previews` and `outputs` directories

3. **Test the Video Preview System**:
   - Run `test_video_previews.bat` to launch the test application
   - The application will display both latent preview and output preview players
   - Click on a video to pause/resume playback

4. **Test in the Main Application**:
   - Launch the main FramePack application
   - The video preview system should automatically detect and display videos in the `latent_previews` and `outputs` directories

## Features

- **Latent Preview Animation**:
  - Low-resolution preview (typically 64x96 pixels)
  - Updates in real-time during generation
  - Plays at 10fps for smooth playback
  - Displayed at 350px on longest side

- **Final Output Preview**:
  - High-quality display of completed videos
  - Automatically shows the most recent video in the outputs directory
  - Displayed at 600px on longest side

- **Common Features**:
  - Click to pause/resume playback
  - Automatic looping
  - Thread-safe loading to prevent UI freezing

## Troubleshooting

If you encounter issues with the video preview system:

1. **Missing Dependencies**:
   - Ensure you have installed the `tkvideoplayer` package: `pip install tkvideoplayer`
   - Ensure you have installed the `pillow` package: `pip install pillow`
   - The application will display a message if any packages are missing

2. **Video Not Playing**:
   - Check that the video files exist in the correct directories
   - Ensure the video format is compatible (MP4 is recommended for output videos, GIF for latent previews)
   - Try generating new sample videos with `generate_sample_previews.bat`

3. **Latent Preview Not Animating**:
   - The application now uses a custom GIF player for latent previews, which is more reliable than MP4
   - Run `convert_to_gif.bat` to convert all latent preview MP4 files to GIF format
   - The application will automatically convert MP4 files to GIF format when using the GIF player
   - You can test the GIF player separately with `test_gif_player.bat`

4. **Application Freezing or Crashing**:
   - The video preview system now uses a more robust approach with a custom GIF player for latent previews
   - If the application still crashes, try running `convert_to_gif.bat` to pre-convert all MP4 files to GIF format
   - If issues persist, try running `test_gif_player.bat` to test the GIF player separately

## Implementation Details

The video preview system is implemented in the following files:

- `framepack_gui.py` - Main application with video preview integration
- `tk_gif_player.py` - Custom GIF player for latent previews
- `test_video_previews.py` - Standalone test application
- `test_gif_player.bat` - Batch file to test the GIF player
- `generate_sample_previews.py` - Script to generate sample preview videos
- `generate_gif_preview.py` - Script to convert MP4 latent previews to GIF format
- `convert_to_gif.bat` - Batch file to run the GIF converter

The system uses two different approaches for video playback:

1. **Latent Previews**: Uses a custom `TkGifPlayer` class that displays animated GIFs using Tkinter's native capabilities. This provides better compatibility and stability for the small, low-resolution latent previews.

2. **Output Videos**: Uses the `TkinterVideo` library for playing the final output videos, which provides a simple interface for playing MP4 videos in Tkinter applications.

The dual approach ensures maximum compatibility and stability, with each player optimized for its specific use case.

## Future Enhancements

Planned enhancements for the video preview system include:

- Fullscreen mode with double-click to maximize
- Playback controls (speed, scrubbing)
- Display of original input image alongside video previews
