import os
import argparse
import torch
import numpy as np
import traceback
import shutil
import glob
from pathlib import Path
import random
from tqdm import tqdm
from PIL import Image, PngImagePlugin
import subprocess
import json
import cv2
import requests
import tempfile
import urllib.parse

# Import auto sorter functionality
from auto_sorter import auto_sort_after_generation

# Set environment variable for HuggingFace models cache
os.environ['HF_HOME'] = os.path.abspath(os.path.realpath(os.path.join(os.path.dirname(__file__), './hf_download')))

import torch
import einops

from diffusers import AutoencoderKLHunyuanVideo
from transformers import LlamaModel, CLIPTextModel, LlamaTokenizerFast, CLIPTokenizer
from diffusers_helper.hunyuan import encode_prompt_conds, vae_decode, vae_encode, vae_decode_fake
from diffusers_helper.utils import save_bcthw_as_mp4, crop_or_pad_yield_mask, soft_append_bcthw, resize_and_center_crop, generate_timestamp
from diffusers_helper.models.hunyuan_video_packed import HunyuanVideoTransformer3DModelPacked
from diffusers_helper.pipelines.k_diffusion_hunyuan import sample_hunyuan
from diffusers_helper.memory import cpu, gpu, get_cuda_free_memory_gb, move_model_to_device_with_memory_preservation, offload_model_from_device_for_memory_preservation, fake_diffusers_current_device, DynamicSwapInstaller, unload_complete_models, load_model_as_complete
from transformers import SiglipImageProcessor, SiglipVisionModel
from diffusers_helper.clip_vision import hf_clip_vision_encode
from diffusers_helper.bucket_tools import find_nearest_bucket

# Prompt selection order:
# 1. prompt_list.txt (if use_prompt_list is True). One prompt per line in this .txt-file
# 2. Per-image .txt file (if exists). The .txt-file should share name with the image-file.
# 3. Image metadata (if use_image_prompt is True)
# 4. fallback_prompt. The same will be used for each generation

prompt_list_file   = 'prompt_list.txt'  # File with one prompt per line for batch processing
use_prompt_list_file = False            # Enable to use prompt_list_file for prompts
use_image_prompt   = True               # Use image metadata as prompt if available
fallback_prompt    = ""                 # Fallback prompt if no other prompt source is found

# Other settings
input_dir          = 'input'            # Directory containing input images
output_dir         = 'output'           # Directory to save output videos
seed               = -1                 # Random seed; -1 means random each run
use_teacache       = True               # Use TeaCache for faster processing (may affect hand quality)
video_length       = 5                  # Video length in seconds (range: 1-120)
steps              = 25                 # Number of sampling steps per video
distilled_cfg      = 10.0               # Distilled CFG scale for model guidance
gpu_memory         = 6.0                # GPU memory to preserve (GB)
mp4_crf            = 16                 # MP4 compression quality (0-51, lower is better, 16 recommended)
randomize_order    = False              # Randomize the order of image processing
clear_processed_list = True             # Clear processed files list after completion
overwrite          = False              # Overwrite existing output files if True
allow_duplicates   = False              # Allow duplicate files to be processed
fix_encoding       = True               # Re-encode video for web compatibility
copy_to_input      = True               # Copy final video to input folder
debug_mode         = True               # Enable debug mode for more verbose logging

def download_image_from_url(url, temp_dir=None):
    """Download an image from a URL and save it to a temporary file"""
    try:
        # Create a temporary directory if none is provided
        if temp_dir is None:
            temp_dir = os.path.join(os.path.dirname(__file__), 'temp')
            os.makedirs(temp_dir, exist_ok=True)

        # Extract filename from URL or use a random name
        parsed_url = urllib.parse.urlparse(url)
        url_path = parsed_url.path
        filename = os.path.basename(url_path)

        # If no filename or it doesn't have an extension, use a random name with .jpg extension
        if not filename or '.' not in filename:
            filename = f"url_image_{random.randint(10000, 99999)}.jpg"

        # Ensure the filename has a valid image extension
        valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
        if not any(filename.lower().endswith(ext) for ext in valid_extensions):
            filename = f"{filename}.jpg"

        # Create the full path for the downloaded image
        temp_file_path = os.path.join(temp_dir, filename)

        # Download the image
        print(f"Downloading image from {url}...")
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()  # Raise an exception for HTTP errors

        # Save the image to the temporary file
        with open(temp_file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        # Verify that the file is a valid image
        try:
            with Image.open(temp_file_path) as img:
                # Force load the image to verify it's valid
                img.verify()
            print(f"Successfully downloaded image to {temp_file_path}")
            return temp_file_path
        except Exception as e:
            print(f"Downloaded file is not a valid image: {e}")
            os.remove(temp_file_path)
            return None

    except Exception as e:
        print(f"Error downloading image from {url}: {e}")
        return None

def get_image_files(directory):
    """Get all image files from directory"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
    image_files = []

    for ext in image_extensions:
        image_files.extend([f for f in Path(directory).glob(f'*{ext}') if f.is_file()])
        image_files.extend([f for f in Path(directory).glob(f'*{ext.upper()}') if f.is_file()])

    # Filter out any non-image files that might have been caught
    image_files = [f for f in image_files if f.suffix.lower() in image_extensions]

    # Filter out files with "expanded" or "rotated" in their names
    excluded_keywords = ["expanded", "rotated"]
    excluded_files = [f for f in image_files if any(keyword.lower() in f.stem.lower() for keyword in excluded_keywords)]
    filtered_files = [f for f in image_files if not any(keyword.lower() in f.stem.lower() for keyword in excluded_keywords)]

    if excluded_files:
        print(f"\nAutomatically excluding {len(excluded_files)} files with 'expanded' or 'rotated' in their names:")
        for i, img in enumerate(excluded_files):
            print(f"  {i+1}. {img.name}")

    # Remove duplicates while preserving order
    seen = set()
    unique_image_files = []
    for f in filtered_files:
        if f not in seen:
            seen.add(f)
            unique_image_files.append(f)

    return sorted(unique_image_files)

def get_image_prompt(image_path):
    """Extract the prompt from an image's metadata"""
    try:
        with Image.open(image_path) as img:
            exif_data = img.info
            if not exif_data:
                return None

            # Look for parameters in different possible metadata fields
            prompt = None

            # Check standard 'parameters' field (common in SD outputs)
            if 'parameters' in exif_data:
                params = exif_data['parameters']
                # Extract just positive prompt if there's a negative prompt section
                positive_end = params.find('Negative prompt:')
                if positive_end != -1:
                    prompt = params[:positive_end].strip()
                else:
                    prompt = params.strip()

            # Check for other common metadata fields if parameters wasn't found
            elif 'prompt' in exif_data:
                prompt = exif_data['prompt']
            elif 'Comment' in exif_data:
                # Some tools store in Comment field
                prompt = exif_data['Comment']

            # Handle case where metadata exists but prompt is empty
            if prompt and len(prompt.strip()) == 0:
                return None

            return prompt

    except Exception as e:
        print(f"Warning: Error extracting metadata from {image_path}: {e}")
        return None

def save_image_with_metadata(image_array, filepath, metadata_dict):
    """
    Save an image with metadata embedded in the PNG file.

    Args:
        image_array: Numpy array containing the image data
        filepath: Path where the image will be saved
        metadata_dict: Dictionary containing metadata to embed
    """
    # Convert numpy array to PIL Image
    img = Image.fromarray(image_array)

    # Convert metadata to JSON string
    metadata_json = json.dumps(metadata_dict)

    # Create PngInfo object
    png_info = PngImagePlugin.PngInfo()

    # Add metadata to the PngInfo object
    png_info.add_text("FramePack", metadata_json)

    # Save the image with metadata
    img.save(filepath, format="PNG", pnginfo=png_info)

def update_processed_files_txt(file_path, input_dir, debug=False):
    """
    Directly update the processed_files.txt file with a new entry.
    This function adds the file path to the processed_files.txt file in the input directory,
    but only if the file path doesn't already exist in the file.

    Args:
        file_path (str): The path of the file to add to the processed_files.txt
        input_dir (str): The directory where the processed_files.txt file is located
        debug (bool): Whether to print debug information

    Returns:
        bool: True if the file was updated successfully or already exists, False otherwise
    """
    try:
        # Ensure the input directory exists
        os.makedirs(input_dir, exist_ok=True)

        # Define the path to the processed_files.txt file
        processed_files_path = os.path.join(input_dir, "processed_files.txt")

        if debug:
            print(f"DEBUG: Checking processed_files.txt at {processed_files_path}")
            print(f"DEBUG: Checking for file: {file_path}")

        # First check if the file already exists in processed_files.txt
        file_already_exists = False
        if os.path.exists(processed_files_path):
            with open(processed_files_path, 'r') as f:
                content = f.read().splitlines()
                if file_path in content:
                    file_already_exists = True
                    if debug:
                        print(f"DEBUG: File path already exists in processed_files.txt, skipping append")
                    return True

        # Only append if the file doesn't already exist in the list
        if not file_already_exists:
            if debug:
                print(f"DEBUG: File path not found in processed_files.txt, appending")

            # Append the file path to the processed_files.txt file
            with open(processed_files_path, 'a') as f:
                f.write(f"{file_path}\n")
                # Flush the file to ensure it's written immediately
                f.flush()
                os.fsync(f.fileno())

            # Verify the file was updated correctly
            if os.path.exists(processed_files_path):
                with open(processed_files_path, 'r') as f:
                    content = f.read()
                    if file_path in content:
                        if debug:
                            print(f"DEBUG: Verified file path was successfully added to processed_files.txt")
                        return True
                    else:
                        print(f"Warning: File path was not found in processed_files.txt after update")
                        return False
            else:
                print(f"Warning: processed_files.txt does not exist after update attempt")
                return False

        return True
    except Exception as e:
        print(f"Error updating processed_files.txt: {e}")
        traceback.print_exc()
        return False

def fix_video_encoding(input_path):
    """Re-encode video to ensure web compatibility with minimal quality loss using FFmpeg"""
    try:
        input_path = Path(input_path)
        output_path = input_path.with_stem(input_path.stem + "_fixed")

        ffmpeg_cmd = [
            "ffmpeg",
            "-i", str(input_path),
            "-c:v", "libx264",
            "-preset", "fast",
            "-crf", "17",  # Lower CRF for high quality
            "-c:a", "aac",
            "-b:a", "192k",
            "-movflags", "+faststart",
            "-pix_fmt", "yuv420p",
            "-y",
            str(output_path)
        ]

        subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)
        print(f"Successfully fixed encoding: {input_path} -> {output_path}")
        return output_path

    except subprocess.CalledProcessError as e:
        print(f"Error fixing encoding for {input_path}: {e.stderr}")
        return None
    except FileNotFoundError:
        print("Error: FFmpeg is not installed or not found in PATH. Please install FFmpeg.")
        return None
    except Exception as e:
        print(f"Unexpected error fixing encoding for {input_path}: {str(e)}")
        return None

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Batch process images to generate videos")

    parser.add_argument("--input_dir", type=str, default=input_dir,
                        help=f"Directory containing input images (default: {input_dir})")
    parser.add_argument("--output_dir", type=str, default=output_dir,
                        help=f"Directory to save output videos (default: {output_dir})")
    parser.add_argument("--prompt", type=str, default=fallback_prompt,
                        help=f"Prompt to guide the generation (fallback: '{fallback_prompt}')")
    parser.add_argument("--seed", type=int, default=seed,
                        help=f"Random seed, -1 for random (default: {seed})")
    parser.add_argument("--use_teacache", action="store_true", default=use_teacache,
                        help=f"Use TeaCache - faster but may affect hand quality (default: {use_teacache})")
    parser.add_argument("--video_length", type=float, default=video_length,
                        help=f"Total video length in seconds, range 1-120 (default: {video_length})")
    parser.add_argument("--steps", type=int, default=steps,
                        help=f"Number of sampling steps, range 1-100 (default: {steps})")
    parser.add_argument("--distilled_cfg", type=float, default=distilled_cfg,
                        help=f"Distilled CFG scale, range 1.0-32.0 (default: {distilled_cfg})")
    parser.add_argument("--gpu_memory", type=float, default=gpu_memory,
                        help=f"GPU memory preservation in GB, range 6-128 (default: {gpu_memory})")
    parser.add_argument("--mp4_crf", type=int, default=mp4_crf,
                        help=f"MP4 compression quality, range 0-51, lower is better (default: {mp4_crf})")
    parser.add_argument("--randomize_order", action="store_true", default=randomize_order,
                        help=f"Randomize the order of image processing (default: {randomize_order})")
    parser.add_argument("--clear_processed_list", action="store_true", default=clear_processed_list,
                        help=f"Clear processed files list after completion (default: {clear_processed_list})")
    parser.add_argument("--use_image_prompt", action="store_true", default=use_image_prompt,
                        help="Use image metadata for prompt if available")
    parser.add_argument("--no_image_prompt", action="store_false", dest="use_image_prompt",
                        help="Do not use image metadata for prompt")
    parser.add_argument("--overwrite", action="store_true", default=overwrite,
                        help=f"Whether to overwrite existing output files (default: {overwrite})")
    parser.add_argument("--allow_duplicates", action="store_true", default=allow_duplicates,
                        help=f"Allow duplicate files to be processed and ignore tracking (default: {allow_duplicates})")
    parser.add_argument("--fix_encoding", action="store_true", dest="fix_encoding", default=fix_encoding,
                        help=f"Fix video encoding for web compatibility (default: {fix_encoding})")
    parser.add_argument("--no_fix_encoding", action="store_false", dest="fix_encoding",
                        help="Do not fix video encoding")
    parser.add_argument("--use_prompt_list_file", action="store_true", default=use_prompt_list_file,
                        help=f"Use prompt list file (default: {use_prompt_list_file})")
    parser.add_argument("--prompt_list_file", type=str, default=prompt_list_file,
                        help=f"Path to prompt list file (default: '{prompt_list_file}')")
    parser.add_argument("--copy_to_input", action="store_true", dest="copy_to_input", default=copy_to_input,
                        help=f"Copy final video to input folder (default: {copy_to_input})")
    parser.add_argument("--no_copy_to_input", action="store_false", dest="copy_to_input",
                        help="Do not copy final video to input folder")
    parser.add_argument("--files", nargs="+", type=str, default=None,
                        help="List of specific image files to process (full or relative paths). If provided, input_dir is ignored.")
    parser.add_argument("--file-list", type=str, default=None,
                        help="Path to a text file containing a list of image files to process (one file path per line). If provided, input_dir is ignored.")
    parser.add_argument("--url-list", type=str, default=None,
                        help="Path to a text file containing a list of image URLs to process (one URL per line). If provided, input_dir is ignored.")
    parser.add_argument("--combined-list", type=str, default=None,
                        help="Path to a text file containing a mixed list of image files and URLs to process (one item per line). If provided, input_dir is ignored.")
    parser.add_argument("--unified-list", type=str, default=None,
                        help="Path to a text file containing a unified list of directories, image files, and URLs to process (one item per line). If provided, input_dir is ignored.")
    parser.add_argument("--apply_all_prompts", action="store_true", default=False,
                        help="Apply each prompt from prompt list to each image (creates multiple outputs per image)")
    parser.add_argument("--debug", action="store_true", default=debug_mode,
                        help=f"Enable debug mode for more verbose logging (default: {debug_mode})")

    return parser.parse_args()

@torch.no_grad()
def process_single_image(image_path, output_dir, prompt="", n_prompt="", seed=-1,
                         video_length=5.0, steps=25, gs=10.0, gpu_memory=6.0,
                         use_teacache=True, high_vram=False,
                         text_encoder=None, text_encoder_2=None, tokenizer=None, tokenizer_2=None,
                         vae=None, feature_extractor=None, image_encoder=None, transformer=None,
                         fix_encoding=True, copy_to_input=True, mp4_crf=16):
    """Process a single image to generate a video"""

    # Generate a unique timestamp ID for this job
    job_id = generate_timestamp()

    # Use random seed if seed is -1
    if seed == -1:
        seed = random.randint(0, 2**32 - 1)
        print(f"Using random seed: {seed}")

    # Calculate total latent sections based on video length
    latent_window_size = 9  # Fixed parameter
    total_latent_sections = (video_length * 30) / (latent_window_size * 4)
    total_latent_sections = int(max(round(total_latent_sections), 1))

    try:
        # Clean GPU
        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )

        # Text encoding
        print("Text encoding...")
        if not high_vram:
            fake_diffusers_current_device(text_encoder, gpu)
            load_model_as_complete(text_encoder_2, target_device=gpu)

        llama_vec, clip_l_pooler = encode_prompt_conds(prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)

        # Fixed CFG parameter
        cfg = 1.0
        if cfg == 1:
            llama_vec_n, clip_l_pooler_n = torch.zeros_like(llama_vec), torch.zeros_like(clip_l_pooler)
        else:
            llama_vec_n, clip_l_pooler_n = encode_prompt_conds(n_prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)

        llama_vec, llama_attention_mask = crop_or_pad_yield_mask(llama_vec, length=512)
        llama_vec_n, llama_attention_mask_n = crop_or_pad_yield_mask(llama_vec_n, length=512)

        # Processing input image
        print("Image processing...")
        input_image = np.array(Image.open(image_path).convert('RGB'))
        H, W, C = input_image.shape
        height, width = find_nearest_bucket(H, W, resolution=640)
        input_image_np = resize_and_center_crop(input_image, target_width=width, target_height=height)

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Store the start frame for later use
        temp_start_image = input_image_np.copy()

        # Define start frame path for later use and monitoring deletion
        start_frame_path = os.path.join(output_dir, f'{job_id}_start.png')
        reference_image_path = start_frame_path  # For compatibility with existing code

        # Create start frame metadata
        start_frame_metadata = {
            "prompt": prompt,
            "negative_prompt": n_prompt,
            "seed": seed,
            "steps": steps,
            "cfg_scale": 1.0,  # Fixed CFG parameter
            "distilled_cfg_scale": gs,
            "cfg_rescale": 0.0,  # Fixed guidance_rescale parameter
            "use_teacache": use_teacache,
            "total_video_length": f"{video_length} seconds",
            "total_sections": total_latent_sections,
            "frame_type": "start_frame",
            "job_id": job_id
        }

        input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1
        input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]

        # VAE encoding
        print("VAE encoding...")
        if not high_vram:
            load_model_as_complete(vae, target_device=gpu)

        start_latent = vae_encode(input_image_pt, vae)

        # CLIP Vision encoding
        print("CLIP Vision encoding...")
        if not high_vram:
            load_model_as_complete(image_encoder, target_device=gpu)

        image_encoder_output = hf_clip_vision_encode(input_image_np, feature_extractor, image_encoder)
        image_encoder_last_hidden_state = image_encoder_output.last_hidden_state

        # Convert tensors to appropriate dtype
        llama_vec = llama_vec.to(transformer.dtype)
        llama_vec_n = llama_vec_n.to(transformer.dtype)
        clip_l_pooler = clip_l_pooler.to(transformer.dtype)
        clip_l_pooler_n = clip_l_pooler_n.to(transformer.dtype)
        image_encoder_last_hidden_state = image_encoder_last_hidden_state.to(transformer.dtype)

        # Sampling
        print("Starting sampling...")
        rnd = torch.Generator("cpu").manual_seed(seed)
        num_frames = latent_window_size * 4 - 3

        history_latents = torch.zeros(size=(1, 16, 1 + 2 + 16, height // 8, width // 8), dtype=torch.float32).cpu()
        history_pixels = None
        total_generated_latent_frames = 0

        # Convert to list immediately to avoid consuming the iterator
        latent_paddings = list(reversed(range(total_latent_sections)))

        if total_latent_sections > 4:
            # In theory the latent_paddings should follow the above sequence, but it seems that duplicating some
            # items looks better than expanding it when total_latent_sections > 4
            latent_paddings = [3] + [2] * (total_latent_sections - 3) + [1, 0]

        # Save the start frame with metadata before generating any video sections
        print(f"Saving start frame with metadata before video generation")
        save_image_with_metadata(temp_start_image, start_frame_path, start_frame_metadata)
        print(f"Saved start frame to {start_frame_path}")

        for latent_padding in tqdm(latent_paddings, desc=f"Processing {Path(image_path).name}"):
            is_last_section = latent_padding == 0
            latent_padding_size = latent_padding * latent_window_size

            print(f'latent_padding_size = {latent_padding_size}, is_last_section = {is_last_section}')

            indices = torch.arange(0, sum([1, latent_padding_size, latent_window_size, 1, 2, 16])).unsqueeze(0)
            clean_latent_indices_pre, blank_indices, latent_indices, clean_latent_indices_post, clean_latent_2x_indices, clean_latent_4x_indices = indices.split([1, latent_padding_size, latent_window_size, 1, 2, 16], dim=1)
            clean_latent_indices = torch.cat([clean_latent_indices_pre, clean_latent_indices_post], dim=1)

            clean_latents_pre = start_latent.to(history_latents)
            clean_latents_post, clean_latents_2x, clean_latents_4x = history_latents[:, :, :1 + 2 + 16, :, :].split([1, 2, 16], dim=2)
            clean_latents = torch.cat([clean_latents_pre, clean_latents_post], dim=2)

            if not high_vram:
                unload_complete_models()
                move_model_to_device_with_memory_preservation(transformer, target_device=gpu, preserved_memory_gb=gpu_memory)

            if use_teacache:
                transformer.initialize_teacache(enable_teacache=True, num_steps=steps)
            else:
                transformer.initialize_teacache(enable_teacache=False)

            # Fixed guidance_rescale parameter
            rs = 0.0

            # Custom exceptions for stopping generation
            class ReferenceImageDeletedException(Exception):
                pass

            class StopGenerationRequestedException(Exception):
                pass

            def callback(d):
                current_step = d['i'] + 1
                if current_step % 5 == 0:  # Show progress every 5 steps
                    print(f"Step {current_step}/{steps} - Total frames: {int(max(0, total_generated_latent_frames * 4 - 3))}")

                    # Check if start frame image still exists
                    if not os.path.exists(reference_image_path):
                        print(f"\n⚠️ Start frame image {reference_image_path} has been deleted. Stopping generation...")
                        raise ReferenceImageDeletedException("Start frame image was deleted during generation")

                    # Check for stop flag file
                    stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
                    if os.path.exists(stop_flag_path):
                        print(f"\n⚠️ Stop generation flag detected. Stopping generation gracefully...")
                        raise StopGenerationRequestedException("Stop generation requested by user")
                return

            generated_latents = sample_hunyuan(
                transformer=transformer,
                sampler='unipc',
                width=width,
                height=height,
                frames=num_frames,
                real_guidance_scale=cfg,
                distilled_guidance_scale=gs,
                guidance_rescale=rs,
                num_inference_steps=steps,
                generator=rnd,
                prompt_embeds=llama_vec,
                prompt_embeds_mask=llama_attention_mask,
                prompt_poolers=clip_l_pooler,
                negative_prompt_embeds=llama_vec_n,
                negative_prompt_embeds_mask=llama_attention_mask_n,
                negative_prompt_poolers=clip_l_pooler_n,
                device=gpu,
                dtype=torch.bfloat16,
                image_embeddings=image_encoder_last_hidden_state,
                latent_indices=latent_indices,
                clean_latents=clean_latents,
                clean_latent_indices=clean_latent_indices,
                clean_latents_2x=clean_latents_2x,
                clean_latent_2x_indices=clean_latent_2x_indices,
                clean_latents_4x=clean_latents_4x,
                clean_latent_4x_indices=clean_latent_4x_indices,
                callback=callback,
            )

            if is_last_section:
                generated_latents = torch.cat([start_latent.to(generated_latents), generated_latents], dim=2)

            total_generated_latent_frames += int(generated_latents.shape[2])
            history_latents = torch.cat([generated_latents.to(history_latents), history_latents], dim=2)

            if not high_vram:
                offload_model_from_device_for_memory_preservation(transformer, target_device=gpu, preserved_memory_gb=8)
                load_model_as_complete(vae, target_device=gpu)

            real_history_latents = history_latents[:, :, :total_generated_latent_frames, :, :]

            if history_pixels is None:
                history_pixels = vae_decode(real_history_latents, vae).cpu()
            else:
                section_latent_frames = (latent_window_size * 2 + 1) if is_last_section else (latent_window_size * 2)
                overlapped_frames = latent_window_size * 4 - 3

                current_pixels = vae_decode(real_history_latents[:, :, :section_latent_frames], vae).cpu()
                history_pixels = soft_append_bcthw(current_pixels, history_pixels, overlapped_frames)

            if not high_vram:
                unload_complete_models()

            temp_output_filename = os.path.join(output_dir, f'{job_id}_{total_generated_latent_frames}.mp4')
            # Use the specified CRF value to fix black video output issue (0 is uncompressed and can cause problems)
            save_bcthw_as_mp4(history_pixels, temp_output_filename, fps=30, crf=mp4_crf)

            print(f'Decoded. Current latent shape {real_history_latents.shape}; pixel shape {history_pixels.shape}')

            # Start frame is already saved before any video sections are generated

            if is_last_section:
                break

        # Use the timestamp-based filename as the final output
        # Add duration to the temp filename for better identification
        duration_seconds = int(video_length)  # Truncate to whole number
        final_output_filename = os.path.join(output_dir, f'{job_id}_{total_generated_latent_frames}_{duration_seconds}s.mp4')

        # Rename the temp file to include the duration
        os.rename(temp_output_filename, final_output_filename)

        # Save the end frame with metadata
        try:
            # Extract the last frame from the video
            video = cv2.VideoCapture(final_output_filename)
            total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
            video.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)
            success, last_frame = video.read()
            video.release()

            if success:
                # Convert from BGR to RGB
                last_frame_rgb = cv2.cvtColor(last_frame, cv2.COLOR_BGR2RGB)

                # Create end frame metadata
                end_frame_metadata = {
                    "prompt": prompt,
                    "negative_prompt": n_prompt,
                    "seed": seed,
                    "steps": steps,
                    "cfg_scale": 1.0,  # Fixed CFG parameter
                    "distilled_cfg_scale": gs,
                    "cfg_rescale": 0.0,  # Fixed guidance_rescale parameter
                    "use_teacache": use_teacache,
                    "total_video_length": f"{video_length} seconds",
                    "total_sections": total_latent_sections,
                    "frame_type": "end_frame",
                    "job_id": job_id
                }

                # Save the end frame with metadata
                end_frame_path = os.path.join(output_dir, f'{job_id}_end.png')
                save_image_with_metadata(last_frame_rgb, end_frame_path, end_frame_metadata)
                print(f"Saved end frame to {end_frame_path} (extracted from video)")
            else:
                print("Warning: Could not extract last frame from video for end frame")
        except Exception as e:
            print(f"Error saving end frame: {e}")

        # Handle encoding fix and copying to input folder
        input_dir = str(Path(image_path).parent)
        # Use the timestamp-based filename for the input folder copy as well
        input_output_filename = os.path.join(input_dir, f'{job_id}_{total_generated_latent_frames}_{duration_seconds}s.mp4')

        if copy_to_input:
            try:
                # Check if file exists and is locked
                if os.path.exists(input_output_filename):
                    try:
                        with open(input_output_filename, 'a'):
                            pass
                    except:
                        print(f"Warning: Output file {input_output_filename} is locked or in use. Skipping copy to input folder.")
                        return final_output_filename

                # Fix encoding if enabled
                if fix_encoding:
                    fixed_output = fix_video_encoding(final_output_filename)
                    if fixed_output:
                        # Use the fixed video for copying
                        shutil.copy2(fixed_output, input_output_filename)
                        print(f"✅ Successfully processed and fixed {image_path} -> {input_output_filename}")
                        # Remove the fixed temporary file
                        os.remove(fixed_output)
                    else:
                        print(f"Warning: Encoding fix failed. Copying original video to {input_output_filename}")
                        shutil.copy2(final_output_filename, input_output_filename)
                        print(f"✅ Successfully processed {image_path} -> {input_output_filename}")
                else:
                    shutil.copy2(final_output_filename, input_output_filename)
                    print(f"✅ Successfully processed {image_path} -> {input_output_filename}")

            except PermissionError:
                print(f"Warning: Could not copy to {input_output_filename} due to permission error. Output is still available at {final_output_filename}")
            except Exception as e:
                print(f"Warning: Could not copy to input folder: {e}. Output is still available at {final_output_filename}")

        else:
            if fix_encoding:
                fixed_output = fix_video_encoding(final_output_filename)
                if fixed_output:
                    # Replace the original output with the fixed version
                    shutil.move(fixed_output, final_output_filename)
                    print(f"✅ Successfully processed and fixed {image_path} -> {final_output_filename}")
                else:
                    print(f"Warning: Encoding fix failed. Keeping original video at {final_output_filename}")
                    print(f"✅ Successfully processed {image_path} -> {final_output_filename}")
            else:
                print(f"✅ Successfully processed {image_path} -> {final_output_filename}")

        # Run the auto sorter to copy the largest file to the sorted folder and deduplicate
        print("\nRunning automatic sorting and deduplication...")
        try:
            # Get the filename of the video we just created
            recent_file = os.path.basename(final_output_filename)
            print(f"Processing recent file: {recent_file}")

            # Get the input directory (parent directory of the input image)
            input_dir = str(Path(image_path).parent)

            # Pass the input directory and original input file to the auto sorter
            auto_sort_after_generation(
                outputs_folder=output_dir,
                recent_file=recent_file,
                input_dir=input_dir,
                original_input_file=str(image_path)
            )
        except Exception as sort_error:
            print(f"Error during auto-sorting: {sort_error}")
            traceback.print_exc()

        return final_output_filename

    except (ReferenceImageDeletedException, StopGenerationRequestedException) as e:
        print(f"⚠️ Stopping generation for {image_path}: {e}")

        # Clean up any intermediate files
        print("Cleaning up intermediate files...")
        try:
            # Find and remove all temporary MP4 files for this job
            temp_files = glob.glob(os.path.join(output_dir, f'{job_id}_*.mp4'))
            for temp_file in temp_files:
                try:
                    os.remove(temp_file)
                    print(f"Removed temporary file: {temp_file}")
                except Exception as cleanup_error:
                    print(f"Error removing temporary file {temp_file}: {cleanup_error}")

            # Also remove the start frame image if it hasn't been deleted yet
            if os.path.exists(reference_image_path):
                os.remove(reference_image_path)
                print(f"Removed start frame image: {reference_image_path}")

            # Remove the stop flag file if this was a user-requested stop
            if isinstance(e, StopGenerationRequestedException):
                stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
                if os.path.exists(stop_flag_path):
                    os.remove(stop_flag_path)
                    print(f"Removed stop flag file: {stop_flag_path}")
        except Exception as cleanup_error:
            print(f"Error during cleanup: {cleanup_error}")

        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )
        return None

    except Exception as e:
        print(f"❌ Error processing {image_path}: {e}")
        traceback.print_exc()

        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )
        return None

def main():
    args = parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Process specific files if provided via --files, --file-list, --url-list, --combined-list, or --unified-list
    if args.files or args.file_list or args.url_list or args.combined_list or args.unified_list:
        image_files = []
        downloaded_files = []  # Track downloaded files for cleanup later

        # For URL, file list, combined list, and unified list modes, we'll use appropriate directories for processed_files.txt
        if args.url_list or args.combined_list or args.unified_list:
            # For URL-based processing, use the temp directory
            temp_dir = os.path.join(os.path.dirname(__file__), 'temp')
            os.makedirs(temp_dir, exist_ok=True)
            args.input_dir = temp_dir
        elif args.file_list:
            # For file list processing, use the directory of the file list as the input directory
            # This allows tracking processed files across multiple runs with the same file list
            file_list_dir = os.path.dirname(os.path.abspath(args.file_list))
            if file_list_dir:
                args.input_dir = file_list_dir
        elif args.files:
            # For individual files mode, we'll use the directory of the first file as the input directory
            # This is only relevant if the files are in the same directory
            if len(args.files) > 0:
                first_file_dir = os.path.dirname(os.path.abspath(args.files[0]))
                if first_file_dir and os.path.isdir(first_file_dir):
                    args.input_dir = first_file_dir

        # Process files from --files parameter
        if args.files:
            for file_path in args.files:
                # Handle both absolute and relative paths
                if os.path.isabs(file_path):
                    path = Path(file_path)
                else:
                    path = Path(os.path.abspath(file_path))

                if path.exists() and path.is_file():
                    # Check if it's an image file
                    if path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
                        image_files.append(path)
                    else:
                        print(f"Warning: Skipping {path} - not a supported image file")
                else:
                    print(f"Warning: File not found or not a file: {path}")

        # Process files from --file-list parameter
        if args.file_list:
            if os.path.exists(args.file_list):
                print(f"Reading file list from {args.file_list}")
                try:
                    with open(args.file_list, 'r', encoding='utf-8') as f:
                        for line in f:
                            file_path = line.strip()
                            if file_path:
                                # Handle both absolute and relative paths
                                if os.path.isabs(file_path):
                                    path = Path(file_path)
                                else:
                                    path = Path(os.path.abspath(file_path))

                                if path.exists() and path.is_file():
                                    # Check if it's an image file
                                    if path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
                                        image_files.append(path)
                                    else:
                                        print(f"Warning: Skipping {path} - not a supported image file")
                                else:
                                    print(f"Warning: File not found or not a file: {path}")
                except Exception as e:
                    print(f"Error reading file list: {e}")
            else:
                print(f"Warning: File list not found: {args.file_list}")

        # Process URLs from --url-list parameter
        if args.url_list:
            if os.path.exists(args.url_list):
                print(f"Reading URL list from {args.url_list}")
                try:
                    # Create a temporary directory for downloaded images
                    temp_dir = os.path.join(os.path.dirname(__file__), 'temp')
                    os.makedirs(temp_dir, exist_ok=True)

                    with open(args.url_list, 'r', encoding='utf-8') as f:
                        for line in f:
                            url = line.strip()
                            if url:
                                # Download the image from the URL
                                downloaded_file = download_image_from_url(url, temp_dir)
                                if downloaded_file:
                                    image_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                                else:
                                    print(f"Warning: Failed to download image from URL: {url}")
                except Exception as e:
                    print(f"Error processing URL list: {e}")
            else:
                print(f"Warning: URL list not found: {args.url_list}")

        # Process combined list (files and URLs) from --combined-list parameter
        if args.combined_list:
            if os.path.exists(args.combined_list):
                print(f"Reading combined list from {args.combined_list}")
                try:
                    # Create a temporary directory for downloaded images
                    temp_dir = os.path.join(os.path.dirname(__file__), 'temp')
                    os.makedirs(temp_dir, exist_ok=True)

                    with open(args.combined_list, 'r', encoding='utf-8') as f:
                        for line in f:
                            item = line.strip()
                            if not item:
                                continue

                            # Check if it's a URL or a file path
                            if item.startswith(('http://', 'https://')):
                                # It's a URL, download it
                                print(f"Processing URL from combined list: {item}")
                                downloaded_file = download_image_from_url(item, temp_dir)
                                if downloaded_file:
                                    image_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                                else:
                                    print(f"Warning: Failed to download image from URL: {item}")
                            else:
                                # It's a file path, check if it exists
                                print(f"Processing file path from combined list: {item}")
                                # Handle both absolute and relative paths
                                if os.path.isabs(item):
                                    path = Path(item)
                                else:
                                    path = Path(os.path.abspath(item))

                                if path.exists() and path.is_file():
                                    # Check if it's an image file
                                    if path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
                                        image_files.append(path)
                                    else:
                                        print(f"Warning: Skipping {path} - not a supported image file")
                                else:
                                    print(f"Warning: File not found or not a file: {path}")
                except Exception as e:
                    print(f"Error processing combined list: {e}")
            else:
                print(f"Warning: Combined list not found: {args.combined_list}")

        # Process unified list (directories, files, and URLs) from --unified-list parameter
        if args.unified_list:
            if os.path.exists(args.unified_list):
                print(f"Reading unified list from {args.unified_list}")
                try:
                    # Create a temporary directory for downloaded images
                    temp_dir = os.path.join(os.path.dirname(__file__), 'temp')
                    os.makedirs(temp_dir, exist_ok=True)

                    with open(args.unified_list, 'r', encoding='utf-8') as f:
                        for line in f:
                            item = line.strip()
                            if not item:
                                continue

                            # Check if it's a directory, URL, or a file path
                            if item.startswith('DIR:'):
                                # It's a directory, extract the path and process all images in it
                                dir_path = item[4:]  # Remove the DIR: prefix
                                print(f"Processing directory from unified list: {dir_path}")

                                if os.path.exists(dir_path) and os.path.isdir(dir_path):
                                    # Get all image files from the directory
                                    dir_image_files = get_image_files(dir_path)
                                    if dir_image_files:
                                        print(f"Found {len(dir_image_files)} image files in directory {dir_path}")
                                        image_files.extend(dir_image_files)
                                    else:
                                        print(f"Warning: No image files found in directory: {dir_path}")
                                else:
                                    print(f"Warning: Directory not found: {dir_path}")
                            elif item.startswith(('http://', 'https://')):
                                # It's a URL, download it
                                print(f"Processing URL from unified list: {item}")
                                downloaded_file = download_image_from_url(item, temp_dir)
                                if downloaded_file:
                                    image_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                                else:
                                    print(f"Warning: Failed to download image from URL: {item}")
                            else:
                                # It's a file path, check if it exists
                                print(f"Processing file path from unified list: {item}")
                                # Handle both absolute and relative paths
                                if os.path.isabs(item):
                                    path = Path(item)
                                else:
                                    path = Path(os.path.abspath(item))

                                if path.exists() and path.is_file():
                                    # Check if it's an image file
                                    if path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
                                        image_files.append(path)
                                    else:
                                        print(f"Warning: Skipping {path} - not a supported image file")
                                else:
                                    print(f"Warning: File not found or not a file: {path}")
                except Exception as e:
                    print(f"Error processing unified list: {e}")
                    traceback.print_exc()
            else:
                print(f"Warning: Unified list not found: {args.unified_list}")

        if not image_files:
            print("No valid image files found in the provided file/URL list")
            return
    else:
        # Check if input directory exists
        if not os.path.isdir(args.input_dir):
            print(f"Input directory {args.input_dir} does not exist. Creating...")
            os.makedirs(args.input_dir, exist_ok=True)
            print(f"Please add images to {args.input_dir} and run again.")
            return

        # Get all image files from input directory
        image_files = get_image_files(args.input_dir)
        if not image_files:
            print(f"No image files found in {args.input_dir}")
            return

    print(f"Found {len(image_files)} image files:")
    for i, img in enumerate(image_files):
        print(f"  {i+1}. {img.name}")

    # Create a processed files tracking file (skip for individual files mode)
    processed_files = set()
    if not args.files and not args.allow_duplicates:
        # Store processed_files.txt in the input directory instead of output directory
        processed_files_path = os.path.join(args.input_dir, "processed_files.txt")

        # Load previously processed files if the file exists
        if os.path.exists(processed_files_path):
            try:
                with open(processed_files_path, 'r') as f:
                    processed_files = set(line.strip() for line in f.readlines())
                print(f"Loaded {len(processed_files)} previously processed files from tracking file in input folder")
            except Exception as e:
                print(f"Error loading processed files tracking: {e}")

    # Check for existing output files if overwrite is disabled and duplicates are not allowed
    if not args.overwrite and not args.files and not args.allow_duplicates:
        skipped_files = []
        files_to_process = []

        for img_path in image_files:
            # Check if file has already been processed according to tracking
            if str(img_path) in processed_files:
                skipped_files.append(img_path)
            else:
                files_to_process.append(img_path)

        if skipped_files:
            print(f"\nSkipping {len(skipped_files)} files that have already been processed:")
            for i, img in enumerate(skipped_files):
                print(f"  {i+1}. {img.name}")

        image_files = files_to_process

        if not image_files:
            print(f"\nNo files to process. All images have already been processed.")
            print(f"Use --overwrite to regenerate videos for existing files.")
            print(f"Or use --allow_duplicates to process files multiple times.")
            return

    # Randomize order if requested (now works for all modes)
    if args.randomize_order:
        import random
        random.shuffle(image_files)
        print("Image processing order has been randomized")

    # Print batch processing settings
    print("\nProcessing Settings:")
    if args.unified_list:
        print(f"  Mode: Unified (Directories, Files, and URLs)")
        print(f"  Number of Items: {len(image_files)}")
    elif args.combined_list:
        print(f"  Mode: Combined (Files and URLs)")
        print(f"  Number of Items: {len(image_files)}")
    elif args.url_list:
        print(f"  Mode: URL List")
        print(f"  Number of URLs: {len(image_files)}")
    elif args.files or args.file_list:
        print(f"  Mode: Individual Files")
        print(f"  Number of Files: {len(image_files)}")
    else:
        print(f"  Mode: Batch Directory")
        print(f"  Input Directory: {args.input_dir}")
    print(f"  Output Directory: {args.output_dir}")
    # Determine prompt source for settings printout (accurate to per-image .txt, prompt list, image metadata, or fallback)
    prompt_desc = None
    per_image_txt_exists = all(os.path.exists(str(img.with_suffix('.txt'))) for img in image_files)
    if per_image_txt_exists and len(image_files) > 0:
        prompt_desc = "(Using per-image .txt files)"
    elif args.use_prompt_list_file and os.path.exists(args.prompt_list_file):
        prompt_desc = f"(Using prompt list: {args.prompt_list_file})"
    elif args.use_image_prompt:
        prompt_desc = "(Using image metadata)"
    elif args.prompt:
        prompt_desc = args.prompt
    else:
        prompt_desc = f"(Fallback: '{fallback_prompt}')"
    print(f"  Prompt: {prompt_desc}")
    print(f"  Video Length: {args.video_length} seconds")
    print(f"  Steps: {args.steps}")
    print(f"  Seed: {args.seed if args.seed != -1 else 'Random'}")
    print(f"  Distilled CFG: {args.distilled_cfg}")
    print(f"  TeaCache: {args.use_teacache}")
    print(f"  GPU Memory: {args.gpu_memory} GB")
    print(f"  MP4 Compression (CRF): {args.mp4_crf} (0-51, lower is better)")
    print(f"  Randomize Order: {args.randomize_order}")
    print(f"  Clear Processed List: {args.clear_processed_list if not args.files else 'N/A (Individual Files)'}")
    print(f"  Overwrite Existing: {args.overwrite}")
    print(f"  Allow Duplicates: {args.allow_duplicates}")
    print(f"  Fix Encoding: {args.fix_encoding}")
    print(f"  Copy to Input: {args.copy_to_input}")

    print(f"\nProcessing {len(image_files)} images...")

    # Check VRAM and set high_vram mode
    free_mem_gb = get_cuda_free_memory_gb(gpu)
    high_vram = free_mem_gb > 60

    print(f'Free VRAM {free_mem_gb} GB')
    print(f'High-VRAM Mode: {high_vram}')

    # Load models - following the same pattern as demo_gradio.py
    print("Loading models...")
    text_encoder = LlamaModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder', torch_dtype=torch.float16).cpu()
    text_encoder_2 = CLIPTextModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder_2', torch_dtype=torch.float16).cpu()
    tokenizer = LlamaTokenizerFast.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer')
    tokenizer_2 = CLIPTokenizer.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer_2')
    vae = AutoencoderKLHunyuanVideo.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='vae', torch_dtype=torch.float16).cpu()

    feature_extractor = SiglipImageProcessor.from_pretrained("lllyasviel/flux_redux_bfl", subfolder='feature_extractor')
    image_encoder = SiglipVisionModel.from_pretrained("lllyasviel/flux_redux_bfl", subfolder='image_encoder', torch_dtype=torch.float16).cpu()

    transformer = HunyuanVideoTransformer3DModelPacked.from_pretrained('lllyasviel/FramePackI2V_HY', torch_dtype=torch.bfloat16).cpu()

    # Set models to evaluation mode
    vae.eval()
    text_encoder.eval()
    text_encoder_2.eval()
    image_encoder.eval()
    transformer.eval()

    # Configure models for low VRAM mode
    if not high_vram:
        vae.enable_slicing()
        vae.enable_tiling()

    # Set high quality output for transformer
    transformer.high_quality_fp32_output_for_inference = True
    print('transformer.high_quality_fp32_output_for_inference = True')

    # Set appropriate data types for models
    transformer.to(dtype=torch.bfloat16)
    vae.to(dtype=torch.float16)
    image_encoder.to(dtype=torch.float16)
    text_encoder.to(dtype=torch.float16)
    text_encoder_2.to(dtype=torch.float16)

    # Disable gradient calculation for all models
    vae.requires_grad_(False)
    text_encoder.requires_grad_(False)
    text_encoder_2.requires_grad_(False)
    image_encoder.requires_grad_(False)
    transformer.requires_grad_(False)

    # Install DynamicSwap for models in low VRAM mode
    if not high_vram:
        DynamicSwapInstaller.install_model(transformer, device=gpu)
        DynamicSwapInstaller.install_model(text_encoder, device=gpu)
    else:
        # Load all models to GPU for high VRAM mode
        text_encoder.to(gpu)
        text_encoder_2.to(gpu)
        image_encoder.to(gpu)
        vae.to(gpu)
        transformer.to(gpu)

    # Priority 1: Prompt list (prompt_list.txt, if use_prompt_list and prompt_list_file exists)
    prompt_list = None
    prompt_list_path = None
    if args.use_prompt_list_file and os.path.exists(args.prompt_list_file):
        prompt_list_path = args.prompt_list_file
    if prompt_list_path is not None:
        with open(prompt_list_path, 'r', encoding='utf-8') as f:
            prompt_list = [line.strip() for line in f if line.strip()]
        print(f"Loaded {len(prompt_list)} prompts from prompts.txt (project root)")

    if args.apply_all_prompts and prompt_list is not None:
        print(f"Apply all prompts mode: Each image will be processed with all {len(prompt_list)} prompts")

        # Create a new list of image-prompt pairs
        image_prompt_pairs = []
        for image_path in image_files:
            for prompt_idx, prompt in enumerate(prompt_list):
                image_prompt_pairs.append((image_path, prompt, prompt_idx))

        # Replace the original image list with our pairs
        total_jobs = len(image_prompt_pairs)
        print(f"Total jobs to process: {total_jobs}")

        # Process each image-prompt pair
        for i, (image_path, prompt, prompt_idx) in enumerate(image_prompt_pairs):
            print(f"\n[{i+1}/{total_jobs}] Processing {image_path} with prompt #{prompt_idx+1}")
            print(f"Prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")

            # Process the image with the specific prompt
            try:
                process_single_image(
                    image_path=image_path,
                    output_dir=args.output_dir,
                    prompt=prompt,
                    n_prompt="",
                    seed=args.seed,
                    video_length=args.video_length,
                    steps=args.steps,
                    gs=args.distilled_cfg,
                    gpu_memory=args.gpu_memory,
                    use_teacache=args.use_teacache,
                    high_vram=high_vram,
                    text_encoder=text_encoder,
                    text_encoder_2=text_encoder_2,
                    tokenizer=tokenizer,
                    tokenizer_2=tokenizer_2,
                    vae=vae,
                    feature_extractor=feature_extractor,
                    image_encoder=image_encoder,
                    transformer=transformer,
                    fix_encoding=args.fix_encoding,
                    copy_to_input=args.copy_to_input,
                    mp4_crf=args.mp4_crf
                )

                # Add to processed files and update tracking file (skip for individual files mode)
                if not args.files:
                    processed_files.add(str(image_path))

                    # Update the processed files tracking file in the input directory
                    try:
                        # First, append the new file to the processed_files.txt
                        with open(processed_files_path, 'a') as f:
                            # Only write the current file path
                            f.write(f"{str(image_path)}\n")
                            # Flush the file to ensure it's written immediately
                            f.flush()
                            os.fsync(f.fileno())
                        print(f"Updated processed files tracking - appended new entry to list: {str(image_path)}")

                        # Verify the file was updated correctly
                        if os.path.exists(processed_files_path):
                            with open(processed_files_path, 'r') as f:
                                content = f.read()
                                if str(image_path) in content:
                                    print(f"Verified: File path was successfully added to processed_files.txt")
                                else:
                                    print(f"Warning: File path was not found in processed_files.txt after update")
                    except Exception as e:
                        print(f"Error updating processed files tracking: {e}")
                        traceback.print_exc()

            except Exception as e:
                print(f"Error processing {image_path} with prompt #{prompt_idx+1}: {e}")
                traceback.print_exc()
    else:
        # Original processing logic for one-to-one mapping
        for i, image_path in enumerate(image_files):
            # We already filtered out processed files earlier if overwrite is disabled and duplicates are not allowed
            # This check is only needed if overwrite is enabled or we're allowing duplicates
            if not args.overwrite and not args.files and not args.allow_duplicates and str(image_path) in processed_files:
                print(f"\n[{i+1}/{len(image_files)}] Skipping {image_path} - already processed according to tracking file")
                continue

            # Check for stop flag before processing each image
            stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
            if os.path.exists(stop_flag_path):
                print(f"\n⚠️ Stop generation flag detected. Stopping batch processing...")
                # Remove the stop flag file
                try:
                    os.remove(stop_flag_path)
                    print(f"Removed stop flag file: {stop_flag_path}")
                except Exception as e:
                    print(f"Error removing stop flag file: {e}")
                break

            print(f"\n[{i+1}/{len(image_files)}] Processing {image_path}")
            actual_prompt = None
            prompt_source = None

            # Priority 1: Project-wide prompts.txt
            if prompt_list is not None:
                if i < len(prompt_list):
                    actual_prompt = prompt_list[i]
                    prompt_source = f"project prompts.txt line {i+1}"
                else:
                    actual_prompt = ""
                    prompt_source = "project prompts.txt (no line, using empty prompt)"
            else:
                # Priority 2: Per-image .txt file
                image_txt_path = image_path.with_suffix('.txt')
                if image_txt_path.exists():
                    with open(image_txt_path, 'r', encoding='utf-8') as f:
                        actual_prompt = f.read().strip()
                    prompt_source = f"per-image .txt ({image_txt_path.name})"
                # Priority 3: Image metadata
                if actual_prompt is None and args.use_image_prompt:
                    image_prompt = get_image_prompt(image_path)
                    if image_prompt:
                        actual_prompt = image_prompt
                        prompt_source = "image metadata"
                # Priority 4: Fallback prompt
                if actual_prompt is None:
                    actual_prompt = args.prompt  # Use the prompt from command line arguments
                    if actual_prompt:
                        prompt_source = "fallback prompt"
                    else:
                        prompt_source = "empty prompt"

            print(f"Using prompt from: {prompt_source}")
            if actual_prompt:
                print(f"Prompt: {actual_prompt[:100]}{'...' if len(actual_prompt) > 100 else ''}")
            else:
                print("Prompt: (empty)")

            # Process the image
            try:
                process_single_image(
                    image_path=image_path,
                    output_dir=args.output_dir,
                    prompt=actual_prompt,
                    n_prompt="",
                    seed=args.seed,
                    video_length=args.video_length,
                    steps=args.steps,
                    gs=args.distilled_cfg,
                    gpu_memory=args.gpu_memory,
                    use_teacache=args.use_teacache,
                    high_vram=high_vram,
                    text_encoder=text_encoder,
                    text_encoder_2=text_encoder_2,
                    tokenizer=tokenizer,
                    tokenizer_2=tokenizer_2,
                    vae=vae,
                    feature_extractor=feature_extractor,
                    image_encoder=image_encoder,
                    transformer=transformer,
                    fix_encoding=args.fix_encoding,
                    copy_to_input=args.copy_to_input,
                    mp4_crf=args.mp4_crf
                )

                # Add to processed files and update tracking file (skip for individual files mode)
                if not args.files:
                    processed_files.add(str(image_path))

                    # Use the direct file update function to ensure the processed_files.txt is updated correctly
                    if update_processed_files_txt(str(image_path), args.input_dir, debug=True):
                        print(f"Successfully updated processed_files.txt with: {str(image_path)}")
                    else:
                        print(f"Failed to update processed_files.txt with: {str(image_path)}")

            except Exception as e:
                print(f"Error processing {image_path}: {e}")
                # Don't add to processed files so we can retry later

    print("\nAll images processed!")

    # Clear the processed files tracking file if enabled and not processing individual files
    # Only clear if there are no more files to process (all files in input directory have been processed)
    if args.clear_processed_list and not args.files:
        # Check if all files in the input directory have been processed
        all_input_files = get_image_files(args.input_dir)
        unprocessed_files = [f for f in all_input_files if str(f) not in processed_files]

        if not unprocessed_files:  # Only clear if all files have been processed
            processed_files_path = os.path.join(args.input_dir, "processed_files.txt")
            if os.path.exists(processed_files_path):
                try:
                    # Create an empty file to clear the list
                    with open(processed_files_path, 'w') as f:
                        f.write("")
                    print("All files processed. Processed files tracking has been cleared for next batch.")
                except Exception as e:
                    print(f"Error clearing processed files tracking: {e}")
        else:
            print(f"Processed files tracking preserved - {len(unprocessed_files)} files still need processing.")
    elif not args.files:
        print("Processed files tracking has been preserved for incremental processing.")

    # Exit message for individual file processing
    if args.files or args.url_list or args.combined_list or args.unified_list:
        print("\nFinished processing specified files. Exiting.")

    # Clean up downloaded files if any
    if 'downloaded_files' in locals() and downloaded_files:
        print("\nCleaning up downloaded temporary files...")
        for file_path in downloaded_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"Removed temporary file: {file_path}")
            except Exception as e:
                print(f"Error removing temporary file {file_path}: {e}")

if __name__ == "__main__":
    main()


