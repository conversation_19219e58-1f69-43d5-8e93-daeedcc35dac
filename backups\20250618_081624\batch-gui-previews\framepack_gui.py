import os
import sys
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import json
import shutil
import time
import glob
import pyperclip  # For clipboard operations
import threading  # For thread locking
# Import TkinterVideo for latent preview (optional)
try:
    from tkVideoPlayer import TkinterVideo
    TKVIDEO_AVAILABLE = True
    print("TkinterVideo loaded successfully. Video preview is enabled.")
except ImportError:
    print("TkinterVideo not available. Video preview will be disabled.")
    print("To enable video preview, install tkVideoPlayer: pip install tkvideoplayer")
    TKVIDEO_AVAILABLE = False
    TkinterVideo = None
# add the extra constant
from tkinterdnd2 import TkinterDnD, DND_FILES, DND_TEXT


# Import TkinterDnD for drag and drop support
try:
    # Import the required components from tkinterdnd2
    from tkinterdnd2 import TkinterDnD, DND_FILES
    # Also import PRIVATE constant which may be needed for proper drag and drop operation
    from tkinterdnd2 import PRIVATE
    TKDND_AVAILABLE = True
    print("TkinterDnD2 loaded successfully. Drag and drop is enabled.")
except ImportError:
    print("TkinterDnD2 not available. Drag and drop will be disabled.")
    print("To enable drag and drop, install tkinterdnd2: pip install tkinterdnd2")
    TKDND_AVAILABLE = False

# Function to load quick prompts from JSON file
def load_quick_prompts_from_json():
    # Default prompts to use if JSON file doesn't exist or has issues
    default_prompts = [
        'The girl dances gracefully, with clear movements, full of charm.',
        'A character doing some simple body movements.',
    ]

    json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "quick_list.json")

    try:
        # Check if the JSON file exists
        if os.path.exists(json_path):
            print(f"Loading quick prompts from {json_path}")
            with open(json_path, 'r', encoding='utf-8') as f:
                try:
                    prompts_data = json.load(f)

                    # Check if it's a list format
                    if isinstance(prompts_data, list):
                        print(f"Loaded {len(prompts_data)} prompts from JSON file (list format)")
                        return prompts_data

                    # Check if it's a dict with a "prompts" field
                    if isinstance(prompts_data, dict) and "prompts" in prompts_data and isinstance(prompts_data["prompts"], list):
                        result = prompts_data["prompts"]
                        print(f"Loaded {len(result)} prompts from JSON file (prompts field format)")
                        return result

                    print(f"JSON format not recognized, using default prompts")
                except json.JSONDecodeError:
                    print(f"JSON file is invalid, using default prompts")

                    # Try to backup the corrupted file
                    try:
                        backup_path = json_path + ".bak"
                        shutil.copy2(json_path, backup_path)
                        print(f"Backed up corrupted JSON file to {backup_path}")
                    except Exception as backup_error:
                        print(f"Failed to backup corrupted JSON file: {backup_error}")
        else:
            print(f"Quick prompts JSON file not found at {json_path}, using default prompts")

            # Create a sample JSON file for the user
            sample_data = {
                "prompts": default_prompts
            }
            try:
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(sample_data, f, indent=2, ensure_ascii=False)
                print(f"Created sample quick_list.json file at {json_path}")
            except Exception as e:
                print(f"Failed to create sample JSON file: {e}")

        return default_prompts
    except Exception as e:
        print(f"Error loading quick prompts from JSON: {e}")
        return default_prompts

# Function to save a prompt to the quick list JSON file
def save_prompt_to_quick_list(prompt_text):
    if not prompt_text or prompt_text.strip() == "":
        print("Cannot save empty prompt to quick list")
        return False

    json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "quick_list.json")

    try:
        # Load existing prompts
        existing_prompts = load_quick_prompts_from_json()

        # Check if the prompt already exists (case-sensitive comparison)
        if prompt_text not in existing_prompts:
            # Add the new prompt to the list
            existing_prompts.append(prompt_text)

            # Save the updated list back to the JSON file
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump({"prompts": existing_prompts}, f, indent=2, ensure_ascii=False)

            print(f"Added prompt to quick list: {prompt_text}")
            return True
        else:
            print(f"Prompt already exists in quick list: {prompt_text}")
            return False
    except Exception as e:
        print(f"Error saving prompt to quick list: {e}")
        return False

class FramePackGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("FramePack Batch GUI")
        self.root.geometry("935x447")  # Updated size requested by user
        self.root.resizable(True, True)

        # Set icon if available
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # Create main frame with padding
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create a frame for the canvas and scrollbar
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        # Create a canvas with scrollbar for scrolling
        canvas = tk.Canvas(canvas_frame, width=915, height=427)  # Adjusted for the new window size
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        # Configure the scrollregion when the frame size changes
        def configure_scroll_region(_):
            canvas.configure(scrollregion=canvas.bbox("all"))

        scrollable_frame.bind("<Configure>", configure_scroll_region)

        # Mousewheel scrolling disabled as requested
        # def _on_mousewheel(e):
        #     canvas.yview_scroll(int(-1*(e.delta/120)), "units")
        #
        # canvas.bind_all("<MouseWheel>", _on_mousewheel)

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        self.content_frame = scrollable_frame

        # Initialize variables
        self.init_variables()

        # Create UI elements
        self.create_ui_elements()

        # Load default settings if available
        self.load_default_settings()

        # Initialize prompt entry state
        self.toggle_prompt_entry()

        # Add keyboard binding for Ctrl+V to paste URL from clipboard
        self.root.bind("<Control-v>", self.paste_url_from_clipboard)
        # Also bind Ctrl+V to the listbox and its frame for better usability
        self.files_listbox.bind("<Control-v>", self.paste_url_from_clipboard)
        self.listbox_frame.bind("<Control-v>", self.paste_url_from_clipboard)

    def init_variables(self):
        # Basic settings
        self.input_dir = tk.StringVar(value="input")
        self.output_dir = tk.StringVar(value="output")
        self.temp_dir = tk.StringVar(value="temp")  # Directory for temporary files
        self.fallback_prompt = tk.StringVar(value="")
        self.seed = tk.IntVar(value=-1)
        self.video_length = tk.DoubleVar(value=5.0)
        self.steps = tk.IntVar(value=25)
        self.distilled_cfg = tk.DoubleVar(value=10.0)

        # File selection
        self.selected_files = []  # List to store selected file paths
        self.input_mode = tk.StringVar(value="directory")  # Mode: "directory", "files", "urls", or "combined"

        # Quick prompts
        self.quick_prompts = load_quick_prompts_from_json()  # Load quick prompts from JSON file

        # Advanced settings
        self.use_teacache = tk.BooleanVar(value=True)
        self.gpu_memory = tk.DoubleVar(value=6.0)
        self.mp4_crf = tk.IntVar(value=16)  # MP4 compression quality (0-51, lower is better)
        self.randomize_order = tk.BooleanVar(value=False)  # Randomize processing order
        self.clear_processed_list = tk.BooleanVar(value=True)  # Clear processed files list after completion
        self.use_image_prompt = tk.BooleanVar(value=False)  # Changed to False by default
        self.overwrite = tk.BooleanVar(value=False)
        self.fix_encoding = tk.BooleanVar(value=True)
        self.use_prompt_list_file = tk.BooleanVar(value=False)
        self.prompt_list_file = tk.StringVar(value="prompt_list.txt")
        self.copy_to_input = tk.BooleanVar(value=True)
        self.allow_duplicates = tk.BooleanVar(value=False)  # Allow duplicate files to be processed
        self.apply_all_prompts = tk.BooleanVar(value=False)  # Apply each prompt to each image
        self.show_latent_preview = tk.BooleanVar(value=True)  # Show latent preview animation

        # Collapsible section state variables
        self.input_settings_collapsed = tk.BooleanVar(value=False)
        self.prompt_settings_collapsed = tk.BooleanVar(value=False)
        self.prompt_chain_collapsed = tk.BooleanVar(value=False)
        self.latent_preview_collapsed = tk.BooleanVar(value=False)
        self.video_settings_collapsed = tk.BooleanVar(value=False)
        self.performance_settings_collapsed = tk.BooleanVar(value=False)
        self.output_settings_collapsed = tk.BooleanVar(value=False)

        # ETA tracking variables
        self.eta_start_time = None
        self.eta_generation_started = False
        self.eta_current_section = 0
        self.eta_total_sections = 0
        self.eta_current_step = 0
        self.eta_total_steps = 0
        self.eta_section_start_time = None

        # Prompt chain variables
        self.use_prompt_chain = tk.BooleanVar(value=False)
        self.prompt_chain_data = []  # List of prompt chain items

        # Video preview variables
        self.video_load_lock = threading.Lock()  # Lock for video loading operations
        self.preview_loading = False  # Flag to track if a preview is currently being loaded
        self.current_preview = None  # Path to the currently playing preview
        self._restarting_preview = False  # Flag to prevent multiple restart attempts

    def create_ui_elements(self):
        # Create a main frame with padding
        main_frame = ttk.Frame(self.content_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create a two-column layout
        left_column = ttk.Frame(main_frame)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        right_column = ttk.Frame(main_frame)
        right_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # Create frames for each section
        frame = left_column  # For backward compatibility with existing code

        # Input settings (collapsible)
        input_container, input_frame = self.create_collapsible_labelframe(frame, "Input Settings", self.input_settings_collapsed)
        input_container.pack(fill=tk.X, pady=5)

        # Mode selection
        mode_frame = ttk.Frame(input_frame)
        mode_frame.grid(row=0, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        # Directory mode radio button
        self.dir_mode_radio = ttk.Radiobutton(
            mode_frame,
            text="Process Directory",
            variable=self.input_mode,
            value="directory",
            command=self.toggle_input_mode
        )
        self.dir_mode_radio.pack(side=tk.LEFT, padx=(0, 10))

        # Individual files mode radio button
        self.files_mode_radio = ttk.Radiobutton(
            mode_frame,
            text="Process Files",
            variable=self.input_mode,
            value="files",
            command=self.toggle_input_mode
        )
        self.files_mode_radio.pack(side=tk.LEFT, padx=(0, 10))

        # URL mode radio button
        self.url_mode_radio = ttk.Radiobutton(
            mode_frame,
            text="Process URLs",
            variable=self.input_mode,
            value="urls",
            command=self.toggle_input_mode
        )
        self.url_mode_radio.pack(side=tk.LEFT, padx=(0, 10))

        # Combined mode radio button
        self.combined_mode_radio = ttk.Radiobutton(
            mode_frame,
            text="Combined Mode",
            variable=self.input_mode,
            value="combined",
            command=self.toggle_input_mode
        )
        self.combined_mode_radio.pack(side=tk.LEFT)

        # Directory input
        self.dir_frame = ttk.Frame(input_frame)
        self.dir_frame.grid(row=1, column=0, columnspan=3, sticky=tk.W+tk.E, padx=5, pady=5)

        # Input directory
        ttk.Label(self.dir_frame, text="Input Directory:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.input_dir_entry = ttk.Entry(self.dir_frame, textvariable=self.input_dir, width=50)
        self.input_dir_entry.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        self.input_dir_button = ttk.Button(self.dir_frame, text="Browse...", command=lambda: self.browse_directory(self.input_dir))
        self.input_dir_button.grid(row=0, column=2, padx=5, pady=5)

        # Files input
        self.files_frame = ttk.Frame(input_frame)
        self.files_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W+tk.E, padx=5, pady=5)

        # Selected files label
        ttk.Label(self.files_frame, text="Selected Files:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        # Files listbox with scrollbar - using a similar structure to the working test script
        files_list_frame = ttk.Frame(self.files_frame)
        files_list_frame.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)

        # Create a frame with a visible border for the listbox (similar to test_listbox_dnd.py)
        self.listbox_frame = ttk.Frame(files_list_frame, borderwidth=2, relief="solid")
        self.listbox_frame.pack(fill=tk.BOTH, expand=True)

        # Create the listbox
        self.files_listbox = tk.Listbox(self.listbox_frame, width=50, height=5)
        self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)

        # Add a scrollbar
        files_scrollbar = ttk.Scrollbar(self.listbox_frame, orient="vertical", command=self.files_listbox.yview)
        files_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.files_listbox.configure(yscrollcommand=files_scrollbar.set)

        # Add a status label for drag and drop
        self.drag_drop_label = ttk.Label(self.files_frame, text="Ready for drag and drop", foreground="gray")
        self.drag_drop_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=(0, 5))

        # Add drag and drop support if TkinterDnD is available
        if TKDND_AVAILABLE:
            # Register the listbox as a drop target
            self.files_listbox.drop_target_register(DND_FILES, DND_TEXT)
            self.files_listbox.dnd_bind('<<Drop>>', self.drop_files)
            self.files_listbox.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.files_listbox.dnd_bind('<<DragLeave>>', self.drag_leave)

            # Also register the listbox frame
            self.listbox_frame.drop_target_register(DND_FILES, DND_TEXT)
            self.listbox_frame.dnd_bind('<<Drop>>', self.drop_files)
            self.listbox_frame.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.listbox_frame.dnd_bind('<<DragLeave>>', self.drag_leave)

            # Update the label text to indicate drag and drop is available
            self.drag_drop_label.config(text="Drag and drop files here", foreground="blue")
        else:
            # Indicate that drag and drop is not available
            self.drag_drop_label.config(text="Drag and drop not available", foreground="red")

        # Files buttons
        files_buttons_frame = ttk.Frame(self.files_frame)
        files_buttons_frame.grid(row=0, column=2, sticky=tk.N+tk.S, padx=5, pady=5)

        self.add_files_button = ttk.Button(files_buttons_frame, text="Add Files...", command=self.browse_files)
        self.add_files_button.pack(fill=tk.X, pady=(0, 5))

        # Add Paste URL button with tooltip
        self.paste_url_button = ttk.Button(files_buttons_frame, text="Paste URL", command=self.paste_url_from_clipboard)
        self.paste_url_button.pack(fill=tk.X, pady=(0, 5))

        # Create tooltip for the paste URL button
        paste_tooltip = tk.Label(self.root, text="Paste URL from clipboard (Ctrl+V)",
                                background="#ffffe0", relief="solid", borderwidth=1)
        paste_tooltip.pack_forget()  # Hide initially

        # Functions to show/hide the tooltip
        def show_paste_tooltip(event):
            x, y, _, _ = self.paste_url_button.bbox("insert")
            x += self.paste_url_button.winfo_rootx() + 25
            y += self.paste_url_button.winfo_rooty() + 25
            paste_tooltip.lift()
            paste_tooltip.place(x=x, y=y)

        def hide_paste_tooltip(event):
            paste_tooltip.place_forget()

        # Bind tooltip events to the paste URL button
        self.paste_url_button.bind("<Enter>", show_paste_tooltip)
        self.paste_url_button.bind("<Leave>", hide_paste_tooltip)

        self.remove_file_button = ttk.Button(files_buttons_frame, text="Remove", command=self.remove_selected_file)
        self.remove_file_button.pack(fill=tk.X, pady=(0, 5))

        self.clear_files_button = ttk.Button(files_buttons_frame, text="Clear All", command=self.clear_files)
        self.clear_files_button.pack(fill=tk.X, pady=(0, 5))

        # Output directory
        output_frame = ttk.Frame(input_frame)
        output_frame.grid(row=3, column=0, columnspan=3, sticky=tk.W+tk.E, padx=5, pady=5)

        ttk.Label(output_frame, text="Output Directory:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(output_frame, textvariable=self.output_dir, width=50).grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        ttk.Button(output_frame, text="Browse...", command=lambda: self.browse_directory(self.output_dir)).grid(row=0, column=2, padx=5, pady=5)

        # Initialize the input mode
        self.toggle_input_mode()

        # Prompt settings - Grouped together (collapsible)
        prompt_container, prompt_frame = self.create_collapsible_labelframe(frame, "Prompt Settings", self.prompt_settings_collapsed)
        prompt_container.pack(fill=tk.X, pady=5)

        # Create a header frame for the title and info icon
        header_frame = ttk.Frame(prompt_frame)
        header_frame.grid(row=0, column=0, columnspan=4, sticky=tk.W, padx=5, pady=5)

        # Create a label for "Prompt Priority Order" with an info icon
        priority_label = ttk.Label(header_frame, text="Prompt Priority Order", font=("", 9, "bold"))
        priority_label.pack(side=tk.LEFT)

        # Create an info icon (using a label with a character)
        info_icon = ttk.Label(header_frame, text="ⓘ", font=("", 10), foreground="blue")
        info_icon.pack(side=tk.LEFT, padx=(5, 0))

        # Create tooltip text
        tooltip_text = "Priority Order:\n1. Prompt list file (if enabled)\n2. Per-image .txt file (if exists)\n3. Image metadata (if enabled)\n4. Fallback prompt (below)"

        # Create tooltip functionality
        def show_tooltip(event):
            tooltip = tk.Toplevel(self.root)
            tooltip.wm_overrideredirect(True)
            tooltip.geometry(f"+{event.x_root+10}+{event.y_root+10}")
            tooltip_label = ttk.Label(tooltip, text=tooltip_text, background="#FFFFCC", relief="solid", borderwidth=1, padding=5, justify=tk.LEFT)
            tooltip_label.pack()
            self.active_tooltip = tooltip

        def hide_tooltip(_):  # Using _ to indicate unused parameter
            if hasattr(self, 'active_tooltip') and self.active_tooltip:
                self.active_tooltip.destroy()
                self.active_tooltip = None

        # Bind tooltip events to the info icon
        info_icon.bind("<Enter>", show_tooltip)
        info_icon.bind("<Leave>", hide_tooltip)

        # Create a frame for the image metadata checkbox
        metadata_frame = ttk.Frame(prompt_frame)
        metadata_frame.grid(row=1, column=0, columnspan=4, sticky=tk.W, padx=5, pady=5)

        # Use image prompt checkbox
        ttk.Checkbutton(
            metadata_frame,
            text="Use image metadata for prompt",
            variable=self.use_image_prompt
        ).pack(anchor=tk.W)

        # Prompt list file row
        file_frame = ttk.Frame(prompt_frame)
        file_frame.grid(row=2, column=0, columnspan=4, sticky=tk.W+tk.E, padx=5, pady=5)

        ttk.Label(file_frame, text="Prompt List File:").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Entry(file_frame, textvariable=self.prompt_list_file, width=50).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(
            file_frame,
            text="Browse...",
            command=lambda: self.browse_file(self.prompt_list_file, [("Text Files", "*.txt"), ("All Files", "*.*")])
        ).pack(side=tk.LEFT)

        # Edit button for prompt list file
        ttk.Button(
            file_frame,
            text="Edit...",
            command=self.edit_prompt_list_file
        ).pack(side=tk.LEFT, padx=(5, 0))

        # Prompt list options frame
        prompt_list_options_frame = ttk.Frame(prompt_frame)
        prompt_list_options_frame.grid(row=3, column=0, columnspan=4, sticky=tk.W, padx=5, pady=5)

        # Use prompt list file checkbox
        self.prompt_list_checkbox = ttk.Checkbutton(
            prompt_list_options_frame,
            text="Use prompt list file",
            variable=self.use_prompt_list_file,
            command=self.toggle_prompt_entry
        )
        self.prompt_list_checkbox.pack(side=tk.LEFT, padx=(0, 15))

        # Apply each prompt to each image checkbox
        ttk.Checkbutton(
            prompt_list_options_frame,
            text="Apply each prompt to each image",
            variable=self.apply_all_prompts
        ).pack(side=tk.LEFT)

        # Fallback prompt row
        fallback_frame = ttk.Frame(prompt_frame)
        fallback_frame.grid(row=4, column=0, columnspan=4, sticky=tk.W+tk.E, padx=5, pady=5)

        ttk.Label(fallback_frame, text="Fallback Prompt:").pack(side=tk.LEFT, padx=(0, 5))

        # Create combobox with quick prompts
        self.fallback_prompt_combobox = ttk.Combobox(
            fallback_frame,
            textvariable=self.fallback_prompt,
            values=self.quick_prompts,
            width=55
        )
        self.fallback_prompt_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Add buttons for quick prompt management
        buttons_frame = ttk.Frame(prompt_frame)
        buttons_frame.grid(row=5, column=0, columnspan=4, sticky=tk.E, padx=5, pady=5)

        # Save to quick list button
        self.save_prompt_button = ttk.Button(
            buttons_frame,
            text="Save to Quick List",
            command=self.save_current_prompt,
            width=15
        )
        self.save_prompt_button.pack(side=tk.RIGHT, padx=5)

        # Refresh quick list button
        self.refresh_prompts_button = ttk.Button(
            buttons_frame,
            text="Refresh Quick List",
            command=self.refresh_quick_prompts,
            width=15
        )
        self.refresh_prompts_button.pack(side=tk.RIGHT, padx=5)

        # Add a spacer to push the action buttons to the bottom of the left column
        spacer_frame = ttk.Frame(frame)
        spacer_frame.pack(fill=tk.BOTH, expand=True)

        # Prompt Chain section (collapsible)
        prompt_chain_container, prompt_chain_frame = self.create_collapsible_labelframe(frame, "Prompt Chain", self.prompt_chain_collapsed)
        prompt_chain_container.pack(fill=tk.X, pady=5)

        # Prompt chain checkbox
        ttk.Checkbutton(
            prompt_chain_frame,
            text="Use prompt chain",
            variable=self.use_prompt_chain,
            command=self.toggle_prompt_chain
        ).pack(anchor=tk.W, padx=5, pady=5)

        # Prompt chain listbox and controls
        self.prompt_chain_controls_frame = ttk.Frame(prompt_chain_frame)
        self.prompt_chain_controls_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Listbox for prompt chain items
        chain_list_frame = ttk.Frame(self.prompt_chain_controls_frame)
        chain_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        self.prompt_chain_listbox = tk.Listbox(chain_list_frame, height=4)
        self.prompt_chain_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        chain_scrollbar = ttk.Scrollbar(chain_list_frame, orient=tk.VERTICAL, command=self.prompt_chain_listbox.yview)
        chain_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.prompt_chain_listbox.configure(yscrollcommand=chain_scrollbar.set)

        # Prompt chain buttons
        chain_buttons_frame = ttk.Frame(self.prompt_chain_controls_frame)
        chain_buttons_frame.pack(fill=tk.X)

        ttk.Button(chain_buttons_frame, text="Add Current", command=self.add_current_to_chain, width=12).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(chain_buttons_frame, text="Remove", command=self.remove_from_chain, width=12).pack(side=tk.LEFT, padx=2)
        ttk.Button(chain_buttons_frame, text="Clear All", command=self.clear_chain, width=12).pack(side=tk.LEFT, padx=2)

        # Initially hide prompt chain controls if not enabled
        self.toggle_prompt_chain()

        # Add a frame for the latent preview (collapsible)
        latent_preview_container, latent_preview_frame = self.create_collapsible_labelframe(frame, "Latent Preview", self.latent_preview_collapsed)
        latent_preview_container.pack(fill=tk.X, pady=5)

        # Create a frame to hold the video player
        self.video_frame = ttk.Frame(latent_preview_frame)
        self.video_frame.pack(fill=tk.X, padx=5, pady=5, expand=True)

        # Initialize the video player with a try-except block to handle potential errors
        try:
            if TKVIDEO_AVAILABLE and TkinterVideo:
                # Create a TkinterVideo widget to display the latent preview
                self.latent_preview_player = TkinterVideo(master=self.video_frame, scaled=True, keep_aspect=True)
                self.latent_preview_player.pack(fill=tk.X, expand=True)

                # Set a minimum height for the video player (4x the original size)
                self.latent_preview_player.config(height=240)  # Default height x 4

                # Bind click event to pause/resume the video
                self.latent_preview_player.bind("<Button-1>", self.toggle_preview_playback)
            else:
                # TkinterVideo not available
                self.latent_preview_player = None
                ttk.Label(self.video_frame, text="Video preview not available - TkinterVideo not installed").pack(pady=10)
        except Exception as e:
            print(f"Error initializing video player: {e}")
            # Create a fallback label if video player fails to initialize
            self.latent_preview_player = None
            ttk.Label(self.video_frame, text="Video player initialization failed. Please restart the application.").pack(pady=10)

        # Create a label to show status
        self.latent_preview_label = ttk.Label(latent_preview_frame, text="No preview available - Start generation to see preview")
        self.latent_preview_label.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Add a checkbox to enable/disable latent preview
        ttk.Checkbutton(
            latent_preview_frame,
            text="Show latent preview animation",
            variable=self.show_latent_preview
        ).pack(anchor=tk.W, padx=5, pady=5)

        # ETA display frame
        eta_frame = ttk.Frame(frame)
        eta_frame.pack(fill=tk.X, pady=(5, 0))

        # ETA display labels
        self.eta_current_label = ttk.Label(eta_frame, text="", foreground="blue")
        self.eta_current_label.pack(anchor=tk.W, padx=5)

        self.eta_total_label = ttk.Label(eta_frame, text="", foreground="green")
        self.eta_total_label.pack(anchor=tk.W, padx=5)

        # Add action buttons at the bottom of the left column
        action_frame = ttk.Frame(frame)
        action_frame.pack(fill=tk.X, pady=10)

        # Run button
        self.run_button = ttk.Button(
            action_frame,
            text="Run FramePack",
            command=self.run_framepack,
            width=15
        )
        self.run_button.pack(side=tk.RIGHT, padx=5)

        # Stop button (initially disabled)
        self.stop_button = ttk.Button(
            action_frame,
            text="Stop Generation",
            command=self.stop_framepack,
            width=15,
            state="disabled"
        )
        self.stop_button.pack(side=tk.RIGHT, padx=5)

        # Save settings button
        save_button = ttk.Button(
            action_frame,
            text="Save Settings",
            command=self.save_settings,
            width=15
        )
        save_button.pack(side=tk.RIGHT, padx=5)

        # Load settings button
        load_button = ttk.Button(
            action_frame,
            text="Load Settings",
            command=self.load_settings,
            width=15
        )
        load_button.pack(side=tk.RIGHT, padx=5)

        # Save as default button
        save_default_button = ttk.Button(
            action_frame,
            text="Save as Default",
            command=self.save_as_default_with_feedback,
            width=15
        )
        save_default_button.pack(side=tk.RIGHT, padx=5)

        # ===== RIGHT COLUMN SETTINGS =====

        # Video settings (collapsible)
        video_container, video_frame = self.create_collapsible_labelframe(right_column, "Video Generation Settings", self.video_settings_collapsed)
        video_container.pack(fill=tk.X, pady=5)

        # Create a more compact grid layout for video settings
        settings_grid = ttk.Frame(video_frame)
        settings_grid.pack(fill=tk.X, padx=5, pady=5)

        # Row 0: Seed
        ttk.Label(settings_grid, text="Seed (-1 for random):").grid(row=0, column=0, sticky=tk.W, padx=2, pady=2)
        seed_frame = ttk.Frame(settings_grid)
        seed_frame.grid(row=0, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Entry(seed_frame, textvariable=self.seed, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(seed_frame, text="Random", command=lambda: self.seed.set(-1), width=8).pack(side=tk.LEFT)

        # Row 1: Video Length
        ttk.Label(settings_grid, text="Video Length (sec):").grid(row=1, column=0, sticky=tk.W, padx=2, pady=2)
        length_frame = ttk.Frame(settings_grid)
        length_frame.grid(row=1, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Spinbox(length_frame, from_=1, to=120, textvariable=self.video_length, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(length_frame, text="(1-120)").pack(side=tk.LEFT)

        # Row 2: Steps
        ttk.Label(settings_grid, text="Steps:").grid(row=2, column=0, sticky=tk.W, padx=2, pady=2)
        steps_frame = ttk.Frame(settings_grid)
        steps_frame.grid(row=2, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Spinbox(steps_frame, from_=1, to=100, textvariable=self.steps, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(steps_frame, text="(1-100)").pack(side=tk.LEFT)

        # Row 3: Distilled CFG
        ttk.Label(settings_grid, text="Distilled CFG:").grid(row=3, column=0, sticky=tk.W, padx=2, pady=2)
        cfg_frame = ttk.Frame(settings_grid)
        cfg_frame.grid(row=3, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Spinbox(cfg_frame, from_=1.0, to=32.0, increment=0.5, textvariable=self.distilled_cfg, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(cfg_frame, text="(1.0-32.0)").pack(side=tk.LEFT)

        # Performance settings (collapsible)
        perf_container, perf_frame = self.create_collapsible_labelframe(right_column, "Performance Settings", self.performance_settings_collapsed)
        perf_container.pack(fill=tk.X, pady=5)

        # Create a more compact layout for performance settings
        perf_grid = ttk.Frame(perf_frame)
        perf_grid.pack(fill=tk.X, padx=5, pady=5)

        # Left column of checkboxes
        perf_left = ttk.Frame(perf_grid)
        perf_left.grid(row=0, column=0, sticky=tk.NW)

        # TeaCache
        ttk.Checkbutton(perf_left, text="Use TeaCache", variable=self.use_teacache).pack(anchor=tk.W, pady=1)

        # Randomize Order
        ttk.Checkbutton(perf_left, text="Randomize order", variable=self.randomize_order).pack(anchor=tk.W, pady=1)

        # Right column of checkboxes
        perf_right = ttk.Frame(perf_grid)
        perf_right.grid(row=0, column=1, sticky=tk.NW)

        # Clear Processed List
        ttk.Checkbutton(perf_right, text="Clear processed list", variable=self.clear_processed_list).pack(anchor=tk.W, pady=1)

        # GPU and CRF settings
        perf_settings = ttk.Frame(perf_frame)
        perf_settings.pack(fill=tk.X, padx=5, pady=(0, 5))

        # GPU Memory
        gpu_frame = ttk.Frame(perf_settings)
        gpu_frame.pack(fill=tk.X, pady=1)
        ttk.Label(gpu_frame, text="GPU Memory (GB):").pack(side=tk.LEFT, padx=(0, 2))
        ttk.Spinbox(gpu_frame, from_=6.0, to=128.0, increment=0.5, textvariable=self.gpu_memory, width=6).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(gpu_frame, text="(6-128)").pack(side=tk.LEFT)

        # MP4 Compression (CRF)
        crf_frame = ttk.Frame(perf_settings)
        crf_frame.pack(fill=tk.X, pady=1)
        ttk.Label(crf_frame, text="MP4 Compression:").pack(side=tk.LEFT, padx=(0, 2))
        ttk.Spinbox(crf_frame, from_=0, to=51, increment=1, textvariable=self.mp4_crf, width=6).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(crf_frame, text="(0-51, lower is better)").pack(side=tk.LEFT)

        # Output settings (collapsible)
        output_container, output_frame = self.create_collapsible_labelframe(right_column, "Output Settings", self.output_settings_collapsed)
        output_container.pack(fill=tk.X, pady=5)

        # Create a more compact layout with two columns of checkboxes
        output_grid = ttk.Frame(output_frame)
        output_grid.pack(fill=tk.X, padx=5, pady=5)

        # Left column
        output_left = ttk.Frame(output_grid)
        output_left.grid(row=0, column=0, sticky=tk.NW)

        # Overwrite
        ttk.Checkbutton(output_left, text="Overwrite existing files", variable=self.overwrite).pack(anchor=tk.W, pady=1)

        # Allow duplicates
        ttk.Checkbutton(output_left, text="Allow duplicate processing", variable=self.allow_duplicates,
                       command=self.update_duplicate_tooltip).pack(anchor=tk.W, pady=1)

        # Create tooltip for the allow duplicates checkbox
        self.duplicate_tooltip = tk.Label(self.root, text="Process the same file multiple times\nand ignore the processed files tracking.\nOnly applies to directory processing.\nIndividual files and lists always allow duplicates.",
                                background="#ffffe0", relief="solid", borderwidth=1, justify=tk.LEFT)
        self.duplicate_tooltip.pack_forget()  # Hide initially

        # Right column
        output_right = ttk.Frame(output_grid)
        output_right.grid(row=0, column=1, sticky=tk.NW)

        # Fix encoding
        ttk.Checkbutton(output_right, text="Fix video encoding", variable=self.fix_encoding).pack(anchor=tk.W, pady=1)

        # Copy to input
        ttk.Checkbutton(output_right, text="Copy to input folder", variable=self.copy_to_input).pack(anchor=tk.W, pady=1)

        # Initialize the prompt entry state based on the checkbox
        self.toggle_prompt_entry()

    def create_collapsible_labelframe(self, parent, text, collapsed_var):
        """Create a collapsible LabelFrame with a checkbox in the header"""
        # Create a frame to hold the header and content
        container_frame = ttk.Frame(parent)

        # Create header frame with checkbox and label
        header_frame = ttk.Frame(container_frame)
        header_frame.pack(fill=tk.X, pady=(5, 0))

        # Checkbox to show/hide content (checked = visible, unchecked = hidden)
        show_var = tk.BooleanVar(value=not collapsed_var.get())  # Invert logic: checked = visible
        checkbox = ttk.Checkbutton(
            header_frame,
            text=text,
            variable=show_var,
            command=lambda: self.toggle_section_checkbox(collapsed_var, show_var, content_frame)
        )
        checkbox.pack(side=tk.LEFT, anchor=tk.W)

        # Content frame (this will be hidden/shown)
        content_frame = ttk.Frame(container_frame)
        if not collapsed_var.get():  # If not collapsed, show content
            content_frame.pack(fill=tk.BOTH, expand=True, padx=(20, 0), pady=(5, 0))

        return container_frame, content_frame

    def toggle_section_checkbox(self, collapsed_var, show_var, content_frame):
        """Toggle the visibility of a collapsible section using checkbox"""
        # Update collapsed_var based on checkbox state (inverted logic)
        collapsed_var.set(not show_var.get())

        if show_var.get():
            # Show content when checkbox is checked
            content_frame.pack(fill=tk.BOTH, expand=True, padx=(20, 0), pady=(5, 0))
        else:
            # Hide content when checkbox is unchecked
            content_frame.pack_forget()

        # Auto-save the state
        self.save_default_settings()

    def restore_collapsible_states(self):
        """Restore the collapsible section states after loading settings"""
        # Schedule a delayed update to ensure all UI elements are ready
        try:
            self.root.after(100, self._update_all_section_states)
        except Exception as e:
            print(f"Error restoring collapsible states: {e}")

    def _update_all_section_states(self):
        """Internal method to update all section states"""
        # This method will be called after a delay to ensure UI is ready
        # The sections are created with the correct initial state based on the loaded variables
        # No additional action needed since the checkboxes are created with the correct state
        pass

    def browse_directory(self, var):
        directory = filedialog.askdirectory(initialdir=var.get())
        if directory:
            var.set(directory)

    def browse_file(self, var, filetypes):
        filename = filedialog.askopenfilename(initialdir=os.path.dirname(var.get()), filetypes=filetypes)
        if filename:
            var.set(filename)

    def edit_prompt_list_file(self):
        """Open the prompt list file in Notepad for editing"""
        file_path = self.prompt_list_file.get()
        if not file_path:
            messagebox.showwarning("No File Selected", "Please select a prompt list file first.")
            return

        # Check if the file exists, create it if it doesn't
        if not os.path.exists(file_path):
            try:
                # Create an empty file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("# Enter one prompt per line in this file\n")
                print(f"Created new prompt list file: {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to create prompt list file: {e}")
                return

        try:
            # Launch Notepad to edit the file
            subprocess.Popen(['notepad.exe', file_path])
            print(f"Opened {file_path} in Notepad")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open Notepad: {e}")
            print(f"Error opening Notepad: {e}")

    def browse_files(self):
        """Browse for files or prompt for URLs based on the current mode"""
        current_mode = self.input_mode.get()

        if current_mode == "files" or current_mode == "combined":
            # Browse for image files
            filetypes = [
                ("Image Files", "*.jpg *.jpeg *.png *.bmp *.webp"),
                ("JPEG Files", "*.jpg *.jpeg"),
                ("PNG Files", "*.png"),
                ("BMP Files", "*.bmp"),
                ("WebP Files", "*.webp"),
                ("All Files", "*.*")
            ]

            # Get initial directory from input_dir if no files selected yet, otherwise use directory of first file
            if self.selected_files and os.path.exists(self.selected_files[0]):
                initialdir = os.path.dirname(self.selected_files[0])
            else:
                initialdir = self.input_dir.get()

            filenames = filedialog.askopenfilenames(
                initialdir=initialdir,
                filetypes=filetypes,
                title="Select Image Files to Process"
            )

            if filenames:
                # Add files to the list (allowing duplicates)
                for filename in filenames:
                    self.selected_files.append(filename)
                    self.files_listbox.insert(tk.END, os.path.basename(filename))

        elif current_mode == "urls":
            # Prompt for URLs
            url = simpledialog.askstring("Enter URL", "Enter image URL:")
            if url:
                # Add the URL to the list
                if url not in self.selected_files:
                    self.selected_files.append(url)
                    # Display the URL or a shortened version if it's too long
                    display_name = url
                    if len(url) > 50:
                        display_name = url[:47] + "..."
                    self.files_listbox.insert(tk.END, display_name)

        # For combined mode, also offer URL input after file selection
        if current_mode == "combined" and filenames:
            # Ask if user wants to add URLs as well
            if messagebox.askyesno("Add URLs", "Would you like to add URLs as well?"):
                url = simpledialog.askstring("Enter URL", "Enter image URL:")
                if url:
                    # Add the URL to the list
                    if url not in self.selected_files:
                        self.selected_files.append(url)
                        # Display the URL or a shortened version if it's too long
                        display_name = url
                        if len(url) > 50:
                            display_name = url[:47] + "..."
                        self.files_listbox.insert(tk.END, display_name)

    def remove_selected_file(self):
        """Remove the selected file from the list"""
        selected_indices = self.files_listbox.curselection()
        if not selected_indices:
            return

        # Remove in reverse order to avoid index shifting
        for index in sorted(selected_indices, reverse=True):
            del self.selected_files[index]
            self.files_listbox.delete(index)

    def clear_files(self):
        """Clear all files from the list"""
        self.selected_files = []
        self.files_listbox.delete(0, tk.END)

    def paste_url_from_clipboard(self, event=None):
        """Paste URL from clipboard and add it to the list if it's valid"""
        try:
            # Get text from clipboard
            clipboard_text = pyperclip.paste().strip()

            if not clipboard_text:
                print("Clipboard is empty")
                return

            print(f"Clipboard content: {clipboard_text}")

            # Check if it's a URL
            if clipboard_text.startswith(('http://', 'https://')):
                # Switch to URL mode if not already in URLs or combined mode
                if self.input_mode.get() not in ["urls", "combined"]:
                    self.input_mode.set("urls")
                    self.toggle_input_mode()

                # Add the URL to the list (allowing duplicates)
                self.selected_files.append(clipboard_text)

                # Display the URL or a shortened version if it's too long
                display_name = clipboard_text
                if len(clipboard_text) > 50:
                    display_name = clipboard_text[:47] + "..."

                self.files_listbox.insert(tk.END, display_name)
                print(f"Added URL from clipboard: {clipboard_text}")

                # Flash the listbox to indicate success
                self.files_listbox.config(background="#e0ffe0")  # Light green
                self.root.after(200, lambda: self.files_listbox.config(background="white"))
            else:
                print(f"Clipboard content is not a URL: {clipboard_text}")
                messagebox.showinfo("Invalid URL", "The clipboard content does not appear to be a valid URL.\n\nURLs must start with http:// or https://")
        except Exception as e:
            print(f"Error pasting from clipboard: {e}")
            messagebox.showerror("Error", f"Failed to paste from clipboard: {e}")

    def drop_files(self, event):
        """Handle files or URLs dropped onto the widget"""
        if not TKDND_AVAILABLE:
            return

        try:
            # Get the dropped data
            dropped_data = self.root.tk.splitlist(event.data)
            print(f"Received drop event with data: {event.data}")

            current_mode = self.input_mode.get()

            # If we're in directory mode, switch to the appropriate mode based on the dropped data
            if current_mode == "directory":
                # Check if the dropped data contains both URLs and files
                has_urls = any(data.startswith(('http://', 'https://')) for data in dropped_data)
                has_files = any(not data.startswith(('http://', 'https://')) for data in dropped_data)

                if has_urls and has_files:
                    self.input_mode.set("combined")
                elif has_urls:
                    self.input_mode.set("urls")
                else:
                    self.input_mode.set("files")
                self.toggle_input_mode()

            # Count how many new items we add
            added_count = 0
            skipped_count = 0

            # Valid image extensions for file mode
            valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']

            # Process each dropped item
            for item in dropped_data:
                # Remove quotes if present (Windows sometimes adds them)
                if item.startswith('"') and item.endswith('"'):
                    item = item[1:-1]

                # Check if it's a URL
                if item.startswith(('http://', 'https://')):
                    # Handle URL
                    if self.input_mode.get() not in ["urls", "combined"]:
                        # If we're dropping a URL and we're not in URLs or combined mode, switch to URLs mode
                        self.input_mode.set("urls")
                        self.toggle_input_mode()

                    # Allow duplicate URLs
                    self.selected_files.append(item)
                    # Display the URL or a shortened version if it's too long
                    display_name = item
                    if len(item) > 50:
                        display_name = item[:47] + "..."
                    self.files_listbox.insert(tk.END, display_name)
                    added_count += 1
                    print(f"Added URL: {item}")
                else:
                    # Handle file
                    if self.input_mode.get() not in ["files", "combined"]:
                        # If we're dropping a file and we're not in files or combined mode, switch to files mode
                        self.input_mode.set("files")
                        self.toggle_input_mode()

                    # Normalize path
                    file_path = os.path.normpath(item)
                    print(f"Processing dropped file: {file_path}")

                    if os.path.exists(file_path) and os.path.isfile(file_path):
                        ext = os.path.splitext(file_path)[1].lower()
                        if ext in valid_extensions:
                            # Allow duplicate files
                            self.selected_files.append(file_path)
                            self.files_listbox.insert(tk.END, os.path.basename(file_path))
                            added_count += 1
                            print(f"Added file: {file_path}")
                        else:
                            print(f"Warning: Skipping {file_path} - not a supported image file")
                            skipped_count += 1
                    else:
                        print(f"Warning: File not found or not a file: {file_path}")
                        skipped_count += 1

            # Show a summary message in the status label (no popup)
            if added_count > 0:
                self.drag_drop_label.config(text=f"Added {added_count} items", foreground="green")
            else:
                self.drag_drop_label.config(text="No new items were added", foreground="red")
        except Exception as e:
            print(f"Error during drop: {str(e)}")
            import traceback
            traceback.print_exc()
            self.drag_drop_label.config(text=f"Error: {str(e)}", foreground="red")
        finally:
            # Restore normal appearance
            self.files_listbox.config(background="white")
            self.listbox_frame.config(relief="solid", borderwidth=2)

    def drag_enter(self, event):
        """Handle drag enter event"""
        if not TKDND_AVAILABLE:
            return

        try:
            print(f"Drag enter event on {event.widget}")

            # Change appearance to indicate it's a drop target
            self.files_listbox.config(background="#e0f0ff")  # Light blue background
            self.listbox_frame.config(relief="groove", borderwidth=3)

            # Check if the data being dragged looks like a URL
            # This is a best-effort check since we can't reliably get the data type during drag
            current_mode = self.input_mode.get()

            # Update the label based on the current mode
            if current_mode == "urls":
                self.drag_drop_label.config(text="Drop URLs here!", foreground="#0066cc")
            elif current_mode == "combined":
                self.drag_drop_label.config(text="Drop files or URLs here!", foreground="#0066cc")
            else:
                self.drag_drop_label.config(text="Drop files here!", foreground="#0066cc")
        except Exception as e:
            print(f"Error in drag_enter: {str(e)}")

    def drag_leave(self, event):
        """Handle drag leave event"""
        if not TKDND_AVAILABLE:
            return

        try:
            print(f"Drag leave event on {event.widget}")

            # Restore normal appearance
            self.files_listbox.config(background="white")
            self.listbox_frame.config(relief="solid", borderwidth=2)

            # Update the label based on the current mode
            current_mode = self.input_mode.get()
            if current_mode == "urls":
                self.drag_drop_label.config(text="Drag and drop URLs here", foreground="blue")
            elif current_mode == "combined":
                self.drag_drop_label.config(text="Drag and drop files or URLs here", foreground="blue")
            else:
                self.drag_drop_label.config(text="Drag and drop files here", foreground="blue")
        except Exception as e:
            print(f"Error in drag_leave: {str(e)}")

    def toggle_input_mode(self):
        """Toggle between directory, files, URL, and combined modes"""
        current_mode = self.input_mode.get()

        # Hide all frames first
        self.dir_frame.grid_remove()
        self.files_frame.grid_remove()

        if current_mode == "directory":
            # Directory mode
            self.dir_frame.grid()
            # Randomize order is allowed in directory mode (don't reset it)
        elif current_mode in ["files", "urls", "combined"]:
            # Files, URLs, or Combined mode - all use the files_frame
            self.files_frame.grid()
            # Disable randomize order in these modes since it's not applicable
            self.randomize_order.set(False)

            # Update buttons and labels based on mode
            if current_mode == "files":
                self.add_files_button.config(text="Add Files...")
                self.drag_drop_label.config(text="Drag and drop files here", foreground="blue")
                # Hide the paste URL button in files mode
                self.paste_url_button.pack_forget()
            elif current_mode == "urls":
                self.add_files_button.config(text="Add URLs...")
                self.drag_drop_label.config(text="Drag and drop URLs here", foreground="blue")
                # Show the paste URL button in URLs mode
                self.paste_url_button.pack(fill=tk.X, pady=(0, 5), after=self.add_files_button)
            else:  # Combined mode
                self.add_files_button.config(text="Add Files...")
                self.drag_drop_label.config(text="Drag and drop files or URLs here", foreground="blue")
                # Show the paste URL button in combined mode
                self.paste_url_button.pack(fill=tk.X, pady=(0, 5), after=self.add_files_button)

    def toggle_prompt_entry(self):
        """Enable or disable the fallback prompt entry based on the use_prompt_list_file checkbox"""
        if self.use_prompt_list_file.get():
            # Disable fallback prompt controls when using prompt list file
            self.fallback_prompt_combobox.configure(state="disabled")
            self.save_prompt_button.configure(state="disabled")
            self.refresh_prompts_button.configure(state="disabled")
        else:
            # Enable fallback prompt controls when not using prompt list file
            self.fallback_prompt_combobox.configure(state="normal")
            self.save_prompt_button.configure(state="normal")
            self.refresh_prompts_button.configure(state="normal")

    def update_duplicate_tooltip(self):
        """Set up tooltip for the allow duplicates checkbox"""
        # Find all checkbuttons in the UI
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Checkbutton) and "Allow duplicate processing" in str(child.cget("text")):
                        # Found the checkbox, bind events
                        child.bind("<Enter>", self.show_duplicate_tooltip)
                        child.bind("<Leave>", self.hide_duplicate_tooltip)
                        return

    def show_duplicate_tooltip(self, event):
        """Show the tooltip for the allow duplicates checkbox"""
        x = event.widget.winfo_rootx() + 20
        y = event.widget.winfo_rooty() + 20
        self.duplicate_tooltip.lift()
        self.duplicate_tooltip.place(x=x, y=y)

    def hide_duplicate_tooltip(self, event):
        """Hide the tooltip for the allow duplicates checkbox"""
        self.duplicate_tooltip.place_forget()

    def save_current_prompt(self):
        """Save the current prompt to the quick list"""
        current_prompt = self.fallback_prompt.get().strip()

        if not current_prompt:
            messagebox.showwarning("Empty Prompt", "Cannot save an empty prompt to the quick list.")
            return

        if save_prompt_to_quick_list(current_prompt):
            # Refresh the combobox values
            self.refresh_quick_prompts()
            messagebox.showinfo("Success", f"Prompt added to quick list:\n{current_prompt[:50]}{'...' if len(current_prompt) > 50 else ''}")
        else:
            # If the prompt already exists or there was an error
            messagebox.showinfo("Note", "Prompt already exists in the quick list or there was an error saving it.")

    def refresh_quick_prompts(self):
        """Reload the quick prompts from the JSON file"""
        # Reload the prompts
        self.quick_prompts = load_quick_prompts_from_json()

        # Update the combobox values
        self.fallback_prompt_combobox['values'] = self.quick_prompts

        # Show a message
        print(f"Quick prompts refreshed. Loaded {len(self.quick_prompts)} prompts.")

    def save_settings(self):
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")],
            initialdir=".",
            title="Save Settings"
        )

        if not filename:
            return

        settings = {
            "input_dir": self.input_dir.get(),
            "output_dir": self.output_dir.get(),
            "temp_dir": self.temp_dir.get(),
            "fallback_prompt": self.fallback_prompt.get(),
            "seed": self.seed.get(),
            "video_length": self.video_length.get(),
            "steps": self.steps.get(),
            "distilled_cfg": self.distilled_cfg.get(),
            "use_teacache": self.use_teacache.get(),
            "gpu_memory": self.gpu_memory.get(),
            "mp4_crf": self.mp4_crf.get(),
            "randomize_order": self.randomize_order.get(),
            "clear_processed_list": self.clear_processed_list.get(),
            "use_image_prompt": self.use_image_prompt.get(),
            "overwrite": self.overwrite.get(),
            "fix_encoding": self.fix_encoding.get(),
            "use_prompt_list_file": self.use_prompt_list_file.get(),
            "prompt_list_file": self.prompt_list_file.get(),
            "copy_to_input": self.copy_to_input.get(),
            "allow_duplicates": self.allow_duplicates.get(),
            "apply_all_prompts": self.apply_all_prompts.get(),
            "show_latent_preview": self.show_latent_preview.get(),
            "input_mode": self.input_mode.get(),
            "selected_files": self.selected_files,
            # Collapsible section states
            "input_settings_collapsed": self.input_settings_collapsed.get(),
            "prompt_settings_collapsed": self.prompt_settings_collapsed.get(),
            "prompt_chain_collapsed": self.prompt_chain_collapsed.get(),
            "latent_preview_collapsed": self.latent_preview_collapsed.get(),
            "video_settings_collapsed": self.video_settings_collapsed.get(),
            "performance_settings_collapsed": self.performance_settings_collapsed.get(),
            "output_settings_collapsed": self.output_settings_collapsed.get(),
            # Prompt chain settings
            "use_prompt_chain": self.use_prompt_chain.get(),
            "prompt_chain_data": self.prompt_chain_data
        }

        try:
            with open(filename, 'w') as f:
                json.dump(settings, f, indent=4)
            messagebox.showinfo("Success", f"Settings saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")

    def load_settings(self):
        filename = filedialog.askopenfilename(
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")],
            initialdir=".",
            title="Load Settings"
        )

        if not filename:
            return

        try:
            with open(filename, 'r') as f:
                settings = json.load(f)

            # Apply settings
            self.input_dir.set(settings.get("input_dir", "input"))
            self.output_dir.set(settings.get("output_dir", "output"))
            self.temp_dir.set(settings.get("temp_dir", "temp"))
            self.fallback_prompt.set(settings.get("fallback_prompt", ""))
            self.seed.set(settings.get("seed", -1))
            self.video_length.set(settings.get("video_length", 5.0))
            self.steps.set(settings.get("steps", 25))
            self.distilled_cfg.set(settings.get("distilled_cfg", 10.0))
            self.use_teacache.set(settings.get("use_teacache", True))
            self.gpu_memory.set(settings.get("gpu_memory", 6.0))
            self.mp4_crf.set(settings.get("mp4_crf", 16))
            self.randomize_order.set(settings.get("randomize_order", False))
            self.clear_processed_list.set(settings.get("clear_processed_list", True))
            self.use_image_prompt.set(settings.get("use_image_prompt", True))
            self.overwrite.set(settings.get("overwrite", False))
            self.fix_encoding.set(settings.get("fix_encoding", True))
            self.use_prompt_list_file.set(settings.get("use_prompt_list_file", False))
            self.prompt_list_file.set(settings.get("prompt_list_file", "prompt_list.txt"))
            self.copy_to_input.set(settings.get("copy_to_input", True))
            self.allow_duplicates.set(settings.get("allow_duplicates", False))
            self.apply_all_prompts.set(settings.get("apply_all_prompts", False))
            self.show_latent_preview.set(settings.get("show_latent_preview", True))

            # Load collapsible section states
            self.input_settings_collapsed.set(settings.get("input_settings_collapsed", False))
            self.prompt_settings_collapsed.set(settings.get("prompt_settings_collapsed", False))
            self.prompt_chain_collapsed.set(settings.get("prompt_chain_collapsed", False))
            self.latent_preview_collapsed.set(settings.get("latent_preview_collapsed", False))
            self.video_settings_collapsed.set(settings.get("video_settings_collapsed", False))
            self.performance_settings_collapsed.set(settings.get("performance_settings_collapsed", False))
            self.output_settings_collapsed.set(settings.get("output_settings_collapsed", False))

            # Load prompt chain settings
            self.use_prompt_chain.set(settings.get("use_prompt_chain", False))
            self.prompt_chain_data = settings.get("prompt_chain_data", [])

            # Update prompt chain display (even if hidden)
            self.update_prompt_chain_display()
            self.toggle_prompt_chain()  # Update visibility based on loaded state

            # Load input mode setting (convert from old format if needed)
            if "input_mode" in settings:
                self.input_mode.set(settings.get("input_mode", "directory"))
            else:
                # Convert from old format (use_individual_files boolean)
                old_mode = settings.get("use_individual_files", False)
                self.input_mode.set("files" if old_mode else "directory")

            # Load selected files
            self.selected_files = settings.get("selected_files", [])

            # Update the files listbox
            self.files_listbox.delete(0, tk.END)
            for file_path in self.selected_files:
                # Check if the file still exists
                if os.path.exists(file_path):
                    self.files_listbox.insert(tk.END, os.path.basename(file_path))
                else:
                    print(f"Warning: File {file_path} no longer exists")

            # Update UI based on mode
            self.toggle_input_mode()

            # Restore collapsible section states
            self.restore_collapsible_states()

            messagebox.showinfo("Success", f"Settings loaded from {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load settings: {str(e)}")

    def load_default_settings(self):
        default_settings_path = "framepack_default_settings.json"
        if os.path.exists(default_settings_path):
            try:
                with open(default_settings_path, 'r') as f:
                    settings = json.load(f)

                # Apply settings
                self.input_dir.set(settings.get("input_dir", "input"))
                self.output_dir.set(settings.get("output_dir", "output"))
                self.fallback_prompt.set(settings.get("fallback_prompt", ""))
                self.seed.set(settings.get("seed", -1))
                self.video_length.set(settings.get("video_length", 5.0))
                self.steps.set(settings.get("steps", 25))
                self.distilled_cfg.set(settings.get("distilled_cfg", 10.0))
                self.use_teacache.set(settings.get("use_teacache", True))
                self.gpu_memory.set(settings.get("gpu_memory", 6.0))
                self.mp4_crf.set(settings.get("mp4_crf", 16))
                self.randomize_order.set(settings.get("randomize_order", False))
                self.clear_processed_list.set(settings.get("clear_processed_list", True))
                self.use_image_prompt.set(settings.get("use_image_prompt", True))
                self.overwrite.set(settings.get("overwrite", False))
                self.fix_encoding.set(settings.get("fix_encoding", True))
                self.use_prompt_list_file.set(settings.get("use_prompt_list_file", False))
                self.prompt_list_file.set(settings.get("prompt_list_file", "prompt_list.txt"))
                self.copy_to_input.set(settings.get("copy_to_input", True))
                self.allow_duplicates.set(settings.get("allow_duplicates", False))
                self.apply_all_prompts.set(settings.get("apply_all_prompts", False))

                # Load collapsible section states
                self.input_settings_collapsed.set(settings.get("input_settings_collapsed", False))
                self.prompt_settings_collapsed.set(settings.get("prompt_settings_collapsed", False))
                self.prompt_chain_collapsed.set(settings.get("prompt_chain_collapsed", False))
                self.latent_preview_collapsed.set(settings.get("latent_preview_collapsed", False))
                self.video_settings_collapsed.set(settings.get("video_settings_collapsed", False))
                self.performance_settings_collapsed.set(settings.get("performance_settings_collapsed", False))
                self.output_settings_collapsed.set(settings.get("output_settings_collapsed", False))

                # Load prompt chain settings
                self.use_prompt_chain.set(settings.get("use_prompt_chain", False))
                self.prompt_chain_data = settings.get("prompt_chain_data", [])

                # Update prompt chain display (even if hidden)
                self.update_prompt_chain_display()
                self.toggle_prompt_chain()  # Update visibility based on loaded state

                # Load input mode setting (convert from old format if needed)
                if "input_mode" in settings:
                    self.input_mode.set(settings.get("input_mode", "directory"))
                else:
                    # Convert from old format (use_individual_files boolean)
                    old_mode = settings.get("use_individual_files", False)
                    self.input_mode.set("files" if old_mode else "directory")

                # Load selected files
                self.selected_files = settings.get("selected_files", [])

                # Update the files listbox
                self.files_listbox.delete(0, tk.END)
                for file_path in self.selected_files:
                    # Check if the file still exists
                    if os.path.exists(file_path):
                        self.files_listbox.insert(tk.END, os.path.basename(file_path))
                    else:
                        # Remove non-existent files from the list
                        self.selected_files.remove(file_path)
                        print(f"Warning: File {file_path} no longer exists and was removed from the list")

                # Update UI based on mode
                self.toggle_input_mode()

                # Restore collapsible section states
                self.restore_collapsible_states()
            except Exception as e:
                print(f"Failed to load default settings: {str(e)}")

    def stop_framepack(self):
        """Stop the currently running FramePack process by creating a stop flag file"""
        try:
            # Create a stop flag file that batch.py will check for
            stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
            with open(stop_flag_path, 'w') as f:
                f.write(f"Stop requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")

            # Update button states
            self.stop_button.config(state="disabled")
            messagebox.showinfo("Stop Requested", "Stop signal sent. Processing will stop after the current frame completes.")

            # Schedule a check to re-enable the run button once processing is complete
            self.start_checking_status()
        except Exception as e:
            print(f"Error stopping FramePack: {e}")
            messagebox.showerror("Error", f"Failed to stop FramePack: {e}")

    def start_checking_status(self):
        """Start checking for processing status and latent preview updates"""
        # Start checking processing status
        self.root.after(2000, self.check_processing_status)

        # Start updating latent preview
        self.update_latent_preview()

    def check_processing_status(self):
        """Check if processing has stopped and update button states accordingly"""
        stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")

        # If the stop flag still exists, check again later
        if os.path.exists(stop_flag_path):
            # Continue checking for processing status
            self.root.after(2000, self.check_processing_status)
        else:
            # Processing has completed, reset button states
            self.run_button.config(state="normal")
            print("Processing has completed or was stopped.")

    def toggle_preview_playback(self, event=None):
        """Toggle play/pause of the latent preview video when clicked"""
        try:
            if hasattr(self, 'latent_preview_player') and self.latent_preview_player is not None:
                try:
                    if self.latent_preview_player.is_paused():
                        self.latent_preview_player.play()
                        self.latent_preview_label.config(text="Playing preview...")
                    else:
                        self.latent_preview_player.pause()
                        self.latent_preview_label.config(text="Paused - Click to resume")
                except Exception as e:
                    print(f"Error toggling video playback: {e}")
        except Exception as e:
            print(f"Unexpected error in toggle_preview_playback: {e}")

    def restart_preview(self, event=None):
        """Restart the video when it ends to create a loop effect"""
        try:
            if not hasattr(self, 'latent_preview_player') or self.latent_preview_player is None:
                return

            if not hasattr(self, 'current_preview') or not self.current_preview:
                return

            # Check if we're already in the process of restarting
            if hasattr(self, '_restarting_preview') and self._restarting_preview:
                return

            # Set flag to prevent multiple restarts
            self._restarting_preview = True

            # Add a small delay before restarting to prevent rapid flashing
            def delayed_restart():
                try:
                    # Check if the video player is still available
                    if not hasattr(self, 'latent_preview_player') or self.latent_preview_player is None:
                        return

                    # Seek to the beginning of the video
                    self.latent_preview_player.seek(0)
                    # Play the video again
                    self.latent_preview_player.play()
                except Exception as e:
                    print(f"Error restarting video: {e}")
                finally:
                    # Reset the flag
                    self._restarting_preview = False

            # Get the restart delay (default to 100ms if not set)
            delay = getattr(self, '_restart_delay', 100)

            # Schedule the restart after the appropriate delay
            self.root.after(delay, delayed_restart)
        except Exception as e:
            print(f"Unexpected error in restart_preview: {e}")
            # Reset the flag
            self._restarting_preview = False

    def update_latent_preview(self):
        """Check for and display the latest latent preview animation"""
        try:
            # Check if the video player is available
            if not hasattr(self, 'latent_preview_player') or self.latent_preview_player is None:
                # If the video player is not available, just schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            if not self.show_latent_preview.get():
                # If preview is disabled, hide any existing preview
                self.hide_preview()
                return

            # If we're already in the process of loading a video, don't try to load another one
            if self.preview_loading:
                # Schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            # Get the temp directory
            temp_dir = self.temp_dir.get()
            if not os.path.exists(temp_dir):
                # Schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            # Look for the latest latent preview file (mp4 format)
            try:
                preview_files = glob.glob(os.path.join(temp_dir, "*latent_preview_*.mp4"))
                if not preview_files:
                    # Also check for webm format as fallback
                    preview_files = glob.glob(os.path.join(temp_dir, "*latent_preview_*.webm"))
            except Exception as e:
                print(f"Error searching for preview files: {e}")
                # Schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            # If no preview files found, hide the preview and return
            if not preview_files:
                self.hide_preview()
                # Schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            # Check if any preview files were modified in the last 60 seconds
            # This is a more reliable way to detect if generation is running
            current_time = time.time()
            recent_files = False
            most_recent_time = 0
            most_recent_file = ""

            for file_path in preview_files:
                try:
                    # Get the modification time of the file
                    mod_time = os.path.getmtime(file_path)
                    # Track the most recent file
                    if mod_time > most_recent_time:
                        most_recent_time = mod_time
                        most_recent_file = file_path
                    # If the file was modified in the last 60 seconds, consider generation as running
                    if current_time - mod_time < 60:
                        recent_files = True
                        print(f"Found recent preview file: {os.path.basename(file_path)}, modified {int(current_time - mod_time)} seconds ago")
                        break
                except Exception as e:
                    print(f"Error checking file time: {e}")

            # Only show preview if recent files were found (indicating active generation)
            if not recent_files:
                # Print debug info about the most recent file
                if most_recent_file:
                    time_diff = int(current_time - most_recent_time)
                    print(f"Most recent preview file: {os.path.basename(most_recent_file)}, modified {time_diff} seconds ago (too old)")

                # Hide the preview if no recent files found
                self.hide_preview()
                # Schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            # We already have the preview_files from above

            if preview_files:
                try:
                    # Sort by modification time to get the latest
                    latest_preview = max(preview_files, key=os.path.getmtime)

                    # Check if this is a new preview (different from what we're currently showing)
                    current_preview = getattr(self, 'current_preview', None)
                    if current_preview != latest_preview:
                        # Use a lock to prevent multiple video loading operations
                        if not self.video_load_lock.acquire(blocking=False):
                            # If we can't acquire the lock, it means another thread is already loading a video
                            # Schedule the next update and return
                            self.root.after(1000, self.update_latent_preview)
                            return

                        try:
                            # Set the loading flag
                            self.preview_loading = True

                            # Store the current preview path
                            self.current_preview = latest_preview

                            # Initialize the restarting flag if not already set
                            self._restarting_preview = False

                            # Safely stop and unload any existing video to prevent errors
                            try:
                                if hasattr(self, 'latent_preview_player') and self.latent_preview_player is not None:
                                    self.latent_preview_player.pause()
                                    self.latent_preview_player.unbind("<<Ended>>")
                            except Exception as e:
                                print(f"Error pausing video: {e}")

                            # Add a small delay to ensure the previous video is fully unloaded
                            def load_new_video():
                                try:
                                    # Check if the video player is still available
                                    if not hasattr(self, 'latent_preview_player') or self.latent_preview_player is None:
                                        print("Video player is no longer available")
                                        return

                                    # Check if the file still exists
                                    if not os.path.exists(latest_preview):
                                        print(f"Preview file no longer exists: {latest_preview}")
                                        return

                                    # Load the video into the TkinterVideo widget
                                    self.latent_preview_player.load(latest_preview)

                                    # Get video info to set appropriate playback settings
                                    try:
                                        video_info = self.latent_preview_player.video_info()
                                        # If the video is very short, increase the delay before restart
                                        if video_info.get("duration", 0) < 1.0:
                                            self._restart_delay = 300  # longer delay for short videos
                                        else:
                                            self._restart_delay = 200  # normal delay
                                    except Exception as e:
                                        print(f"Error getting video info: {e}")
                                        self._restart_delay = 200  # default delay

                                    # Set up a video ended event handler to restart the video (looping)
                                    self.latent_preview_player.bind("<<Ended>>", self.restart_preview)

                                    # Play the video
                                    self.latent_preview_player.play()

                                    # Update the label
                                    self.latent_preview_label.config(text=f"Playing: {os.path.basename(latest_preview)}")
                                except Exception as e:
                                    print(f"Error loading new preview: {e}")
                                    self.latent_preview_label.config(text=f"Error loading preview: {str(e)}")
                                finally:
                                    # Reset the loading flag
                                    self.preview_loading = False
                                    # Release the lock
                                    self.video_load_lock.release()

                            # Schedule the video loading with a delay
                            self.root.after(500, load_new_video)  # Increased delay for better stability

                        except Exception as e:
                            print(f"Error displaying latent preview: {e}")
                            self.latent_preview_label.config(text=f"Error loading preview: {str(e)}")
                            # Reset the loading flag
                            self.preview_loading = False
                            # Release the lock
                            self.video_load_lock.release()
                except Exception as e:
                    print(f"Error processing preview files: {e}")

            # Schedule the next update
            self.root.after(1000, self.update_latent_preview)

        except Exception as e:
            print(f"Unexpected error in update_latent_preview: {e}")
            # Schedule the next update even if an error occurred
            self.root.after(1000, self.update_latent_preview)

    def hide_preview(self):
        """Hide the preview when no generation is running"""
        try:
            # Check if the video player is available
            if not hasattr(self, 'latent_preview_player') or self.latent_preview_player is None:
                # If the video player is not available, just return
                return

            # Try to acquire the lock to prevent conflicts with video loading
            if not self.video_load_lock.acquire(blocking=False):
                # If we can't acquire the lock, schedule another attempt later
                self.root.after(500, self.hide_preview)
                return

            try:
                # Set the loading flag to prevent other operations
                self.preview_loading = True

                if hasattr(self, 'latent_preview_player') and self.latent_preview_player is not None:
                    # Stop any playing video
                    try:
                        self.latent_preview_player.pause()
                    except Exception as e:
                        print(f"Error pausing video: {e}")

                    try:
                        self.latent_preview_player.unbind("<<Ended>>")
                    except Exception as e:
                        print(f"Error unbinding event: {e}")

                    # Clear the current preview
                    if hasattr(self, 'current_preview'):
                        delattr(self, 'current_preview')

                    # Reset the restarting flag
                    self._restarting_preview = False

                    # Update the label
                    self.latent_preview_label.config(text="No preview available - Start generation to see preview")
            except Exception as e:
                print(f"Error hiding preview: {e}")
            finally:
                # Reset the loading flag
                self.preview_loading = False
                # Release the lock
                self.video_load_lock.release()
        except Exception as e:
            print(f"Unexpected error in hide_preview: {e}")

    def run_framepack(self):
        current_mode = self.input_mode.get()

        # Check if we have files/URLs selected in files, URLs, or combined mode
        if (current_mode == "files" or current_mode == "urls" or current_mode == "combined") and not self.selected_files:
            messagebox.showerror("Error", "No items selected. Please select at least one item to process.")
            return

        # Remove any existing stop flag file
        stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
        if os.path.exists(stop_flag_path):
            try:
                os.remove(stop_flag_path)
                print(f"Removed existing stop flag file: {stop_flag_path}")
            except Exception as e:
                print(f"Warning: Could not remove existing stop flag file: {e}")

        # Build command
        cmd = [sys.executable, "batch.py"]

        # Add arguments based on mode
        if current_mode == "directory":
            # Directory mode
            cmd.extend(["--input_dir", self.input_dir.get()])
        else:
            # Files, URLs, or Combined mode - create temporary file(s) with paths/URLs
            if current_mode == "combined":
                # For combined mode, we need to separate files and URLs
                file_list = []
                url_list = []

                for item in self.selected_files:
                    if item.startswith(('http://', 'https://')):
                        url_list.append(item)
                    else:
                        file_list.append(item)

                # Create a combined list file
                temp_file_list = "temp_combined_list.txt"
                try:
                    with open(temp_file_list, 'w', encoding='utf-8') as f:
                        for item in self.selected_files:
                            f.write(f"{item}\n")

                    # Add the combined-list parameter
                    cmd.extend(["--combined-list", temp_file_list])
                except Exception as e:
                    print(f"Error creating combined list: {e}")
                    messagebox.showerror("Error", f"Failed to create combined list: {e}")
                    return
            else:
                # Files or URLs mode - create a temporary file with paths/URLs
                temp_file_list = "temp_file_list.txt"
                try:
                    with open(temp_file_list, 'w', encoding='utf-8') as f:
                        for item in self.selected_files:
                            f.write(f"{item}\n")

                    # Add the appropriate parameter based on mode
                    if current_mode == "files":
                        cmd.extend(["--file-list", temp_file_list])
                    else:  # URLs mode
                        cmd.extend(["--url-list", temp_file_list])
                except Exception as e:
                    print(f"Error creating temporary list: {e}")
                    messagebox.showerror("Error", f"Failed to create temporary list: {e}")
                    return

        # Output directory is always needed
        cmd.extend(["--output_dir", self.output_dir.get()])

        # Handle prompt - pass it directly as a command line argument
        # The batch file will add quotes around it
        if self.fallback_prompt.get():
            cmd.extend(["--prompt", self.fallback_prompt.get()])

        cmd.extend(["--seed", str(self.seed.get())])
        cmd.extend(["--video_length", str(self.video_length.get())])
        cmd.extend(["--steps", str(self.steps.get())])
        cmd.extend(["--distilled_cfg", str(self.distilled_cfg.get())])
        cmd.extend(["--gpu_memory", str(self.gpu_memory.get())])
        cmd.extend(["--mp4_crf", str(self.mp4_crf.get())])

        if self.use_teacache.get():
            cmd.append("--use_teacache")

        # Only add randomize_order in directory mode
        if current_mode == "directory" and self.randomize_order.get():
            cmd.append("--randomize_order")

        # Only add clear_processed_list in directory mode
        if current_mode == "directory" and self.clear_processed_list.get():
            cmd.append("--clear_processed_list")

        # Handle image prompt option
        if self.use_image_prompt.get():
            cmd.append("--use_image_prompt")
        else:
            # Explicitly disable image prompt extraction when not checked
            # This ensures the fallback prompt is used instead
            cmd.append("--no_image_prompt")

        if self.overwrite.get():
            cmd.append("--overwrite")

        if self.allow_duplicates.get():
            cmd.append("--allow_duplicates")

        if self.fix_encoding.get():
            cmd.append("--fix_encoding")
        else:
            cmd.append("--no_fix_encoding")

        if self.use_prompt_list_file.get():
            cmd.append("--use_prompt_list_file")
            cmd.extend(["--prompt_list_file", self.prompt_list_file.get()])

        if self.copy_to_input.get():
            cmd.append("--copy_to_input")
        else:
            cmd.append("--no_copy_to_input")

        if self.apply_all_prompts.get():
            cmd.append("--apply_all_prompts")

        # Add temp directory parameter
        cmd.extend(["--temp_dir", self.temp_dir.get()])

        # Add latent preview parameter
        if self.show_latent_preview.get():
            cmd.append("--show_latent_preview")
        else:
            cmd.append("--no_latent_preview")

        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir.get(), exist_ok=True)

        # Create temp directory if it doesn't exist
        os.makedirs(self.temp_dir.get(), exist_ok=True)

        # Create input directory if in directory mode and it doesn't exist
        if current_mode == "directory":
            os.makedirs(self.input_dir.get(), exist_ok=True)

        # Save current settings as default
        self.save_default_settings()

        # Show command that will be executed
        command_str = " ".join(cmd)
        print(f"Executing: {command_str}")

        # Create a temporary batch file to run the command and keep the window open
        temp_batch_file = "temp_run_framepack.bat"
        with open(temp_batch_file, 'w') as f:
            f.write('@echo off\n')
            f.write('title FramePack Batch Processing\n')
            f.write('color 0A\n')  # Green text on black background for better visibility
            f.write('echo ===============================================\n')
            f.write('echo             FRAMEPACK BATCH PROCESSING         \n')
            f.write('echo ===============================================\n')
            f.write('echo.\n')
            f.write('echo Running FramePack with the following settings:\n')
            f.write('echo.\n')

            # Write settings summary
            current_mode = self.input_mode.get()
            if current_mode == "directory":
                f.write(f'echo Mode:                Directory\n')
                f.write(f'echo Input Directory:    {self.input_dir.get()}\n')
                f.write(f'echo Randomize Order:    {"Yes" if self.randomize_order.get() else "No"}\n')
                f.write(f'echo Clear Processed:    {"Yes" if self.clear_processed_list.get() else "No"}\n')
            elif current_mode == "files":
                f.write(f'echo Mode:                Individual Files\n')
                f.write(f'echo Number of Files:     {len(self.selected_files)}\n')

                # List the first few files (up to 5)
                max_files_to_show = min(5, len(self.selected_files))
                for i in range(max_files_to_show):
                    f.write(f'echo File {i+1}:             {os.path.basename(self.selected_files[i])}\n')

                # Indicate if there are more files
                if len(self.selected_files) > max_files_to_show:
                    f.write(f'echo                    ... and {len(self.selected_files) - max_files_to_show} more files\n')
            elif current_mode == "urls":
                f.write(f'echo Mode:                URLs\n')
                f.write(f'echo Number of URLs:      {len(self.selected_files)}\n')

                # List the first few URLs (up to 5)
                max_urls_to_show = min(5, len(self.selected_files))
                for i in range(max_urls_to_show):
                    # Truncate long URLs for display
                    url = self.selected_files[i]
                    if len(url) > 50:
                        display_url = url[:47] + "..."
                    else:
                        display_url = url
                    f.write(f'echo URL {i+1}:              {display_url}\n')

                # Indicate if there are more URLs
                if len(self.selected_files) > max_urls_to_show:
                    f.write(f'echo                    ... and {len(self.selected_files) - max_urls_to_show} more URLs\n')
            else:  # Combined mode
                # Count files and URLs
                file_count = sum(1 for item in self.selected_files if not item.startswith(('http://', 'https://')))
                url_count = sum(1 for item in self.selected_files if item.startswith(('http://', 'https://')))

                f.write(f'echo Mode:                Combined (Files and URLs)\n')
                f.write(f'echo Number of Items:     {len(self.selected_files)} ({file_count} files, {url_count} URLs)\n')

                # List the first few items (up to 5)
                max_items_to_show = min(5, len(self.selected_files))
                for i in range(max_items_to_show):
                    item = self.selected_files[i]
                    if item.startswith(('http://', 'https://')):
                        # It's a URL, truncate if needed
                        if len(item) > 50:
                            display_item = item[:47] + "..."
                        else:
                            display_item = item
                        f.write(f'echo URL {i+1}:              {display_item}\n')
                    else:
                        # It's a file
                        f.write(f'echo File {i+1}:             {os.path.basename(item)}\n')

                # Indicate if there are more items
                if len(self.selected_files) > max_items_to_show:
                    f.write(f'echo                    ... and {len(self.selected_files) - max_items_to_show} more items\n')

            f.write(f'echo Output Directory:   {self.output_dir.get()}\n')
            f.write(f'echo Fallback Prompt:    {self.fallback_prompt.get() if self.fallback_prompt.get() else "(None)"}\n')
            f.write(f'echo Seed:               {self.seed.get()} {"(Random)" if self.seed.get() == -1 else ""}\n')
            f.write(f'echo Video Length:       {self.video_length.get()} seconds\n')
            f.write(f'echo Steps:              {self.steps.get()}\n')
            f.write(f'echo Distilled CFG:      {self.distilled_cfg.get()}\n')
            f.write(f'echo GPU Memory:         {self.gpu_memory.get()} GB\n')
            f.write(f'echo MP4 Compression:    {self.mp4_crf.get()} (0-51, lower is better)\n')
            f.write(f'echo Use TeaCache:       {"Yes" if self.use_teacache.get() else "No"}\n')
            f.write(f'echo Use Image Prompt:   {"Yes" if self.use_image_prompt.get() else "No"}\n')
            f.write(f'echo Use Prompt List:    {"Yes - " + self.prompt_list_file.get() if self.use_prompt_list_file.get() else "No"}\n')
            f.write(f'echo Fix Encoding:       {"Yes" if self.fix_encoding.get() else "No"}\n')
            f.write(f'echo Copy to Input:      {"Yes" if self.copy_to_input.get() else "No"}\n')
            f.write(f'echo Overwrite Existing: {"Yes" if self.overwrite.get() else "No"}\n')
            f.write(f'echo Allow Duplicates:   {"Yes" if self.allow_duplicates.get() else "No"}\n')
            f.write(f'echo Apply All Prompts:  {"Yes" if self.apply_all_prompts.get() else "No"}\n')
            f.write('echo.\n')
            f.write('echo ===============================================\n')
            f.write('echo.\n')

            # Activate virtual environment and run the command
            f.write('echo Activating virtual environment...\n')
            f.write('call venv\\Scripts\\activate.bat\n')
            f.write('if %ERRORLEVEL% NEQ 0 (\n')
            f.write('    color 0C\n')
            f.write('    echo ERROR: Failed to activate virtual environment.\n')
            f.write('    echo Please make sure the venv directory exists and is properly set up.\n')
            f.write('    echo.\n')
            f.write('    pause\n')
            f.write('    exit /b 1\n')
            f.write(')\n')
            f.write('echo Virtual environment activated successfully.\n')
            f.write('echo.\n')
            # Format the command with proper quoting for the batch file
            formatted_cmd = []
            i = 0
            while i < len(cmd):
                if i + 1 < len(cmd) and cmd[i] in ["--prompt", "--lora_file_1", "--lora_file_2", "--lora_file_3", "--lora_file", "--custom_model_path", "--input_dir", "--output_dir", "--temp_dir", "--file-list", "--url-list", "--combined-list", "--unified-list", "--prompt_list_file"]:
                    # Add quotes around values that might contain spaces
                    formatted_cmd.append(f'{cmd[i]} "{cmd[i+1]}"')
                    i += 2  # Skip the next item since we've included it
                else:
                    formatted_cmd.append(cmd[i])
                    i += 1

            formatted_cmd_str = " ".join(formatted_cmd)
            f.write('echo Running command: ' + formatted_cmd_str + '\n')
            f.write('echo.\n')
            f.write(formatted_cmd_str + '\n')

            # Keep the window open after the command finishes
            f.write('echo.\n')
            f.write('if %ERRORLEVEL% NEQ 0 (\n')
            f.write('    color 0C\n')  # Red text for errors
            f.write('    echo ===============================================\n')
            f.write('    echo ERROR: Processing failed with error code %ERRORLEVEL%\n')
            f.write('    echo ===============================================\n')
            f.write('    echo.\n')
            f.write('    echo Please check the error messages above for details.\n')
            f.write('    echo.\n')
            f.write('    pause\n')
            f.write(') else (\n')
            f.write('    echo ===============================================\n')
            f.write('    echo Processing completed successfully!\n')
            f.write('    echo ===============================================\n')
            f.write('    echo.\n')
            f.write('    pause\n')
            f.write(')\n')

            # Add cleanup for the temporary file list if in files, URLs, or combined mode
            if current_mode != "directory":
                f.write('\n')
                f.write('REM Clean up the temporary file list\n')
                f.write(f'if exist "{temp_file_list}" del "{temp_file_list}"\n')

        # Run the batch file
        subprocess.Popen(temp_batch_file, shell=True)

        # Update button states
        self.run_button.config(state="disabled")
        self.stop_button.config(state="normal")

        # Start checking for processing status and latent preview updates
        self.start_checking_status()

        # Initialize ETA tracking
        self.reset_eta_tracking()

        # Start monitoring log for ETA
        self.monitor_log_for_eta()

    def reset_eta_tracking(self):
        """Reset ETA tracking variables"""
        self.eta_start_time = None
        self.eta_generation_started = False
        self.eta_current_section = 0
        self.eta_total_sections = 0
        self.eta_current_step = 0
        self.eta_total_steps = 0
        self.eta_section_start_time = None

        # Clear ETA display
        self.eta_current_label.config(text="")
        self.eta_total_label.config(text="")

    def monitor_log_for_eta(self):
        """Monitor log output for ETA calculation triggers"""
        try:
            # Check for temp batch file output or log file
            log_sources = [
                "framepack_progress.txt",
                "temp_run_framepack.log",
                "framepack_output.log"
            ]

            for log_file in log_sources:
                if os.path.exists(log_file):
                    try:
                        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                            lines = f.readlines()

                        # Process recent lines for ETA triggers
                        for line in lines[-10:]:  # Check last 10 lines
                            line = line.strip()
                            self.process_log_line_for_eta(line)

                    except Exception as e:
                        print(f"Error reading log file {log_file}: {e}")

        except Exception as e:
            print(f"Error monitoring log for ETA: {e}")

        # Schedule next check if generation is active
        if hasattr(self, 'run_button') and self.run_button['state'] == 'disabled':
            self.root.after(2000, self.monitor_log_for_eta)

    def process_log_line_for_eta(self, line):
        """Process a log line to update ETA calculations"""
        import re
        import time

        try:
            # Check for "New section detected: Section X/Y" pattern
            section_match = re.search(r'New section detected: Section (\d+)/(\d+)', line)
            if section_match:
                current_section = int(section_match.group(1))
                total_sections = int(section_match.group(2))

                # Start ETA tracking only when we see Section 1/X
                if current_section == 1 and not self.eta_generation_started:
                    self.eta_start_time = time.time()
                    self.eta_generation_started = True
                    self.eta_section_start_time = time.time()
                    print(f"ETA tracking started at section {current_section}/{total_sections}")

                # Update section tracking
                if self.eta_generation_started:
                    self.eta_current_section = current_section
                    self.eta_total_sections = total_sections
                    self.eta_section_start_time = time.time()

            # Check for step progress patterns
            step_match = re.search(r'Step (\d+)/(\d+)', line)
            if step_match and self.eta_generation_started:
                current_step = int(step_match.group(1))
                total_steps = int(step_match.group(2))

                self.eta_current_step = current_step
                self.eta_total_steps = total_steps

                # Update ETA display
                self.update_eta_display()

        except Exception as e:
            print(f"Error processing log line for ETA: {e}")

    def update_eta_display(self):
        """Update the ETA display labels"""
        try:
            if not self.eta_generation_started or not self.eta_start_time:
                return

            import time

            current_time = time.time()
            elapsed_total = current_time - self.eta_start_time

            # Calculate current section progress
            if self.eta_total_steps > 0 and self.eta_current_step > 0:
                section_progress = self.eta_current_step / self.eta_total_steps

                # Calculate section ETA
                if self.eta_section_start_time and section_progress > 0:
                    section_elapsed = current_time - self.eta_section_start_time
                    section_eta = (section_elapsed / section_progress) - section_elapsed

                    # Format section ETA
                    section_eta_str = self.format_time(section_eta)
                    section_percent = int(section_progress * 100)

                    current_text = f"Current: {section_eta_str} remaining ({section_percent}%)"
                    self.eta_current_label.config(text=current_text)

                # Calculate total ETA
                if self.eta_total_sections > 0 and self.eta_current_section > 0:
                    # Estimate based on current section progress and total sections
                    total_progress = (self.eta_current_section - 1 + section_progress) / self.eta_total_sections

                    if total_progress > 0:
                        total_eta = (elapsed_total / total_progress) - elapsed_total
                        total_eta_str = self.format_time(total_eta)

                        total_text = f"Total: {total_eta_str} remaining (est.)"
                        self.eta_total_label.config(text=total_text)

        except Exception as e:
            print(f"Error updating ETA display: {e}")

    def format_time(self, seconds):
        """Format time in seconds to a readable string"""
        try:
            if seconds < 0:
                return "calculating..."

            if seconds < 60:
                return f"{int(seconds)}s"
            elif seconds < 3600:
                minutes = int(seconds // 60)
                secs = int(seconds % 60)
                return f"{minutes}m {secs}s"
            else:
                hours = int(seconds // 3600)
                minutes = int((seconds % 3600) // 60)
                return f"{hours}h {minutes}m"
        except:
            return "calculating..."

    def toggle_prompt_chain(self):
        """Toggle the visibility of prompt chain controls"""
        if self.use_prompt_chain.get():
            self.prompt_chain_controls_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        else:
            self.prompt_chain_controls_frame.pack_forget()

    def add_current_to_chain(self):
        """Add current settings to the prompt chain"""
        try:
            # Create a prompt chain item with current settings
            chain_item = {
                "prompt": self.fallback_prompt.get(),
                "seed": self.seed.get(),
                "video_length": self.video_length.get(),
                "steps": self.steps.get(),
                "distilled_cfg": self.distilled_cfg.get()
            }

            self.prompt_chain_data.append(chain_item)

            # Update the listbox display
            self.update_prompt_chain_display()

            print(f"Added prompt chain item: {chain_item['prompt'][:50]}...")
        except Exception as e:
            print(f"Error adding to prompt chain: {e}")

    def remove_from_chain(self):
        """Remove selected item from the prompt chain"""
        try:
            selected_indices = self.prompt_chain_listbox.curselection()
            if not selected_indices:
                return

            # Remove in reverse order to avoid index shifting
            for index in sorted(selected_indices, reverse=True):
                if 0 <= index < len(self.prompt_chain_data):
                    removed_item = self.prompt_chain_data.pop(index)
                    print(f"Removed prompt chain item: {removed_item['prompt'][:50]}...")

            # Update the listbox display
            self.update_prompt_chain_display()
        except Exception as e:
            print(f"Error removing from prompt chain: {e}")

    def clear_chain(self):
        """Clear all items from the prompt chain"""
        try:
            self.prompt_chain_data = []
            self.update_prompt_chain_display()
            print("Cleared all prompt chain items")
        except Exception as e:
            print(f"Error clearing prompt chain: {e}")

    def update_prompt_chain_display(self):
        """Update the prompt chain listbox display"""
        try:
            self.prompt_chain_listbox.delete(0, tk.END)

            for i, item in enumerate(self.prompt_chain_data):
                # Create a display string for the item
                prompt_preview = item['prompt'][:40] + "..." if len(item['prompt']) > 40 else item['prompt']
                display_text = f"{i+1}. {prompt_preview} (seed:{item['seed']}, len:{item['video_length']}s)"
                self.prompt_chain_listbox.insert(tk.END, display_text)
        except Exception as e:
            print(f"Error updating prompt chain display: {e}")

    def save_default_settings(self):
        settings = {
            "input_dir": self.input_dir.get(),
            "output_dir": self.output_dir.get(),
            "fallback_prompt": self.fallback_prompt.get(),
            "seed": self.seed.get(),
            "video_length": self.video_length.get(),
            "steps": self.steps.get(),
            "distilled_cfg": self.distilled_cfg.get(),
            "use_teacache": self.use_teacache.get(),
            "gpu_memory": self.gpu_memory.get(),
            "mp4_crf": self.mp4_crf.get(),
            "randomize_order": self.randomize_order.get(),
            "clear_processed_list": self.clear_processed_list.get(),
            "use_image_prompt": self.use_image_prompt.get(),
            "overwrite": self.overwrite.get(),
            "fix_encoding": self.fix_encoding.get(),
            "use_prompt_list_file": self.use_prompt_list_file.get(),
            "prompt_list_file": self.prompt_list_file.get(),
            "copy_to_input": self.copy_to_input.get(),
            "allow_duplicates": self.allow_duplicates.get(),
            "apply_all_prompts": self.apply_all_prompts.get(),
            "input_mode": self.input_mode.get(),
            "selected_files": self.selected_files,
            # Collapsible section states
            "input_settings_collapsed": self.input_settings_collapsed.get(),
            "prompt_settings_collapsed": self.prompt_settings_collapsed.get(),
            "prompt_chain_collapsed": self.prompt_chain_collapsed.get(),
            "latent_preview_collapsed": self.latent_preview_collapsed.get(),
            "video_settings_collapsed": self.video_settings_collapsed.get(),
            "performance_settings_collapsed": self.performance_settings_collapsed.get(),
            "output_settings_collapsed": self.output_settings_collapsed.get(),
            # Prompt chain settings
            "use_prompt_chain": self.use_prompt_chain.get(),
            "prompt_chain_data": self.prompt_chain_data
        }

        try:
            with open("framepack_default_settings.json", 'w') as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            print(f"Failed to save default settings: {str(e)}")

    def save_as_default_with_feedback(self):
        """Save current settings as default with user feedback"""
        try:
            self.save_default_settings()
            messagebox.showinfo("Success", "Current settings saved as default!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save default settings: {str(e)}")

def report_callback_exception(root, exc, val, tb):
    """Custom exception handler to prevent the application from closing on unhandled exceptions"""
    import traceback
    print("Exception in Tkinter callback:")
    print(f"Exception type: {exc}")
    print(f"Exception value: {val}")
    print("Traceback:")
    traceback.print_tb(tb)

    # Show error message to user
    error_message = f"An error occurred: {val}\n\nThe application will continue running, but some features may not work correctly."
    messagebox.showerror("Error", error_message)

def main():
    # Parse command line arguments for files
    import argparse
    parser = argparse.ArgumentParser(description="FramePack GUI")
    parser.add_argument("files", nargs="*", help="Image files to process")
    parser.add_argument("--file-list", help="Path to a text file containing a list of files to process")
    args = parser.parse_args()

    # Use TkinterDnD.Tk() if available, otherwise fallback to standard tk.Tk()
    if TKDND_AVAILABLE:
        root = TkinterDnD.Tk()
    else:
        root = tk.Tk()

    # Set custom exception handler to prevent application from closing on unhandled exceptions
    root.report_callback_exception = lambda exc, val, tb: report_callback_exception(root, exc, val, tb)

    app = FramePackGUI(root)  # Create the application

    # List to store file paths
    file_paths = []

    # If a file list was provided, read files from it
    if args.file_list and os.path.exists(args.file_list):
        print(f"Reading file list from {args.file_list}")
        try:
            with open(args.file_list, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        # Print the path for debugging
                        print(f"Found file path: {line}")
                        # Check if the file exists
                        if os.path.exists(line):
                            file_paths.append(line)
                        else:
                            print(f"Warning: File does not exist: {line}")
            print(f"Read {len(file_paths)} valid file paths from list file")
        except Exception as e:
            print(f"Error reading file list: {e}")

    # If files were provided as direct arguments, add them too
    if args.files and len(args.files) > 0:
        print(f"Processing {len(args.files)} files from command line arguments")
        for file_path in args.files:
            abs_path = os.path.abspath(file_path)
            print(f"Processing file: {abs_path}")
            if os.path.exists(abs_path):
                file_paths.append(abs_path)
            else:
                print(f"Warning: File does not exist: {abs_path}")

    # Process the file paths if we have any
    if file_paths:
        # Filter for valid image files
        valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
        valid_files = []

        # Clear existing files in the list
        app.selected_files = []
        app.files_listbox.delete(0, tk.END)
        print("Cleared existing files from the list")

        for file_path in file_paths:
            if os.path.exists(file_path) and os.path.isfile(file_path):
                ext = os.path.splitext(file_path)[1].lower()
                if ext in valid_extensions:
                    valid_files.append(file_path)
                    app.files_listbox.insert(tk.END, os.path.basename(file_path))
                else:
                    print(f"Warning: Skipping {file_path} - not a supported image file")
            else:
                print(f"Warning: File not found or not a file: {file_path}")

        # If we have valid files, switch to files mode
        if valid_files:
            app.selected_files = valid_files
            app.input_mode.set("files")
            app.toggle_input_mode()
            print(f"Loaded {len(valid_files)} files")

    root.mainloop()

if __name__ == "__main__":
    main()



