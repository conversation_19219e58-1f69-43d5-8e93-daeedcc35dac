import os
import argparse
import torch
import numpy as np
import traceback
import shutil
import glob
from pathlib import Path
import random
from tqdm import tqdm
from PIL import Image, PngImagePlugin
import subprocess
import json
import cv2
import requests
import tempfile
import urllib.parse
import sys
import time
import signal
import atexit

# Import auto sorter functionality
from auto_sorter import auto_sort_after_generation

# Global flag for graceful shutdown
shutdown_requested = False

def signal_handler(signum, frame):
    """Handle termination signals for immediate shutdown"""
    global shutdown_requested
    print(f"\n⚠️ Received termination signal {signum}. Initiating immediate shutdown...")
    shutdown_requested = True

    # Perform immediate cleanup
    try:
        # Clean up any temporary files
        temp_files = [
            "framepack_progress.txt",
            "stop_framepack.flag",
            "stop_queue.flag",
            "skip_generation.flag"
        ]
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    print(f"Cleaned up: {temp_file}")
                except Exception:
                    pass

        # Force CUDA cleanup if available
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                print("CUDA cache cleared during shutdown")
        except Exception:
            pass

    except Exception as e:
        print(f"Error during signal cleanup: {e}")

    print("Batch F1 process terminated by signal")
    sys.exit(1)

def cleanup_on_exit():
    """Cleanup function called on normal exit"""
    try:
        # Clean up temporary files
        temp_files = [
            "framepack_progress.txt"
        ]
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except Exception:
                    pass
    except Exception:
        pass

# Register signal handlers for immediate termination
if os.name == 'nt':  # Windows
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
else:  # Unix-like systems
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGQUIT, signal_handler)

# Register cleanup function for normal exit
atexit.register(cleanup_on_exit)

# Define exception classes
class MemoryExhaustionError(Exception):
    """Exception raised when memory is exhausted during processing."""
    pass

class StopGenerationRequestedException(Exception):
    """Exception raised when the user requests to stop generation."""
    pass

class ReferenceImageDeletedException(Exception):
    """Exception raised when the start frame image is deleted during generation."""
    pass

class SectionTimeoutException(Exception):
    """Exception raised when a section times out."""
    pass

# Check if sed_list command is available
def check_sed_list_command():
    """Check if the sed_list command is available in the system path"""
    try:
        # Try to run the sed_list command with no arguments
        result = subprocess.run(["sed_list"],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE,
                               text=True,
                               shell=True)
        return True
    except FileNotFoundError:
        print("Warning: sed_list command not found. Creating a placeholder.")
        # Create a simple batch file for sed_list if it doesn't exist
        if not os.path.exists("sed_list.bat"):
            try:
                with open("sed_list.bat", "w") as f:
                    f.write("@echo off\n")
                    f.write("REM This is a placeholder for the sed_list command\n")
                    f.write("echo sed_list command executed with arguments: %*\n")
                    f.write("exit /b 0\n")
                print("Created placeholder sed_list.bat file.")
                return True
            except Exception as e:
                print(f"Error creating sed_list.bat: {e}")
                return False
        return False
    except Exception as e:
        print(f"Error checking for sed_list command: {e}")
        return False

# Set environment variable for HuggingFace models cache
os.environ['HF_HOME'] = os.path.abspath(os.path.realpath(os.path.join(os.path.dirname(__file__), './hf_download')))

import torch
import einops

from diffusers import AutoencoderKLHunyuanVideo
from transformers import LlamaModel, CLIPTextModel, LlamaTokenizerFast, CLIPTokenizer
from diffusers_helper.hunyuan import encode_prompt_conds, vae_decode, vae_encode, vae_decode_fake
from diffusers_helper.utils import save_bcthw_as_mp4, crop_or_pad_yield_mask, soft_append_bcthw, resize_and_center_crop, generate_timestamp
from diffusers_helper.models.hunyuan_video_packed import HunyuanVideoTransformer3DModelPacked
from diffusers_helper.pipelines.k_diffusion_hunyuan import sample_hunyuan
from diffusers_helper.memory import cpu, gpu, get_cuda_free_memory_gb, move_model_to_device_with_memory_preservation, offload_model_from_device_for_memory_preservation, fake_diffusers_current_device, DynamicSwapInstaller, unload_complete_models, load_model_as_complete
from transformers import SiglipImageProcessor, SiglipVisionModel
from diffusers_helper.clip_vision import hf_clip_vision_encode
from diffusers_helper.bucket_tools import find_nearest_bucket
from utils.lora_utils import merge_lora_to_state_dict
from utils.fp8_optimization_utils import optimize_state_dict_with_fp8, apply_fp8_monkey_patch
import gc

# Prompt selection order:
# 1. prompt_list.txt (if use_prompt_list is True). One prompt per line in this .txt-file
# 2. Per-image .txt file (if exists). The .txt-file should share name with the image-file.
# 3. Image metadata (if use_image_prompt is True)
# 4. fallback_prompt. The same will be used for each generation

prompt_list_file   = 'prompt_list.txt'  # File with one prompt per line for batch processing
use_prompt_list_file = False            # Enable to use prompt_list_file for prompts
use_image_prompt   = True               # Use image metadata as prompt if available
fallback_prompt    = ""                 # Fallback prompt if no other prompt source is found

# Other settings
input_dir          = 'input'            # Directory containing input images
output_dir         = 'output'           # Directory to save output videos
temp_dir           = 'temp'             # Directory for temporary files
seed               = -1                 # Random seed; -1 means random each run
use_teacache       = True               # Use TeaCache for faster processing (may affect hand quality)
video_length       = 5                  # Video length in seconds (range: 1-120)
steps              = 25                 # Number of sampling steps per video
distilled_cfg      = 10.0               # Distilled CFG scale for model guidance
flow_shift         = 0.0                # Flow shift parameter (0.0 = auto-calculated, 1.0-10.0 = manual override)
gpu_memory         = 6.0                # GPU memory to preserve (GB)
mp4_crf            = 16                 # MP4 compression quality (0-51, lower is better, 16 recommended)
randomize_order    = False              # Randomize the order of image processing
clear_processed_list = True             # Clear processed files list after completion
overwrite          = False              # Overwrite existing output files if True
allow_duplicates   = False              # Allow duplicate files to be processed
fix_encoding       = True               # Re-encode video for web compatibility
copy_to_input      = True               # Copy final video to input folder
latent_window_size = 9                  # Latent window size (F1 default is 9)
show_latent_preview = True              # Show latent preview animation
debug_mode         = True               # Enable debug mode for more verbose logging

# LoRA and optimization settings (supports up to 3 LoRAs)
# Multiple LoRAs are merged sequentially in order: LoRA 1, then LoRA 2, then LoRA 3
# Each LoRA can have its own strength multiplier for fine-tuning the effect
lora_file_1        = None               # Path to first LoRA file (None to disable)
lora_multiplier_1  = 0.8                # First LoRA strength multiplier (0.0-1.0)
lora_file_2        = None               # Path to second LoRA file (None to disable)
lora_multiplier_2  = 0.8                # Second LoRA strength multiplier (0.0-1.0)
lora_file_3        = None               # Path to third LoRA file (None to disable)
lora_multiplier_3  = 0.8                # Third LoRA strength multiplier (0.0-1.0)

# Backward compatibility
lora_file          = None               # Path to LoRA file (backward compatibility, maps to lora_file_1)
lora_multiplier    = 0.8                # LoRA strength multiplier (backward compatibility, maps to lora_multiplier_1)
fp8_optimization   = False              # Enable FP8 optimization for memory efficiency

def download_image_from_url(url, temp_dir=None):
    """Download an image from a URL and save it to a temporary file with a timestamp in the filename"""
    try:
        # Create a temporary directory if none is provided
        if temp_dir is None:
            temp_dir = os.path.join(os.path.dirname(__file__), 'temp')

        # Ensure the temp directory exists
        os.makedirs(temp_dir, exist_ok=True)

        # Extract filename from URL or use a random name
        parsed_url = urllib.parse.urlparse(url)
        url_path = parsed_url.path
        filename = os.path.basename(url_path)

        # If no filename or it doesn't have an extension, use a random name with .jpg extension
        if not filename or '.' not in filename:
            filename = f"url_image_{random.randint(10000, 99999)}.jpg"

        # Ensure the filename has a valid image extension
        valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
        if not any(filename.lower().endswith(ext) for ext in valid_extensions):
            filename = f"{filename}.jpg"

        # Add timestamp to the filename to ensure uniqueness
        timestamp = int(time.time())
        name, ext = os.path.splitext(filename)
        unique_filename = f"{name}_{timestamp}{ext}"

        # Create the full path for the downloaded image
        temp_file_path = os.path.join(temp_dir, unique_filename)

        # Download the image
        print(f"Downloading image from {url}...")
        print(f"Will save as {unique_filename} with timestamp to prevent duplicates")
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()  # Raise an exception for HTTP errors

        # Save the image to the temporary file
        with open(temp_file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        # Verify that the file is a valid image
        try:
            with Image.open(temp_file_path) as img:
                # Force load the image to verify it's valid
                img.verify()
            print(f"Successfully downloaded image to {temp_file_path}")
            return temp_file_path
        except Exception as e:
            print(f"Downloaded file is not a valid image: {e}")
            os.remove(temp_file_path)
            return None

    except Exception as e:
        print(f"Error downloading image from {url}: {e}")
        return None

def should_exclude_file(file_path, prompt_chain_mode=False):
    """Check if a file should be excluded based on excluded keywords in its name"""
    excluded_keywords = ["expanded", "rotated"]

    # Convert Path object to string if needed
    if isinstance(file_path, Path):
        file_stem = file_path.stem.lower()
    else:
        # If it's a string, extract the stem (filename without extension)
        file_stem = Path(file_path).stem.lower()

    # In prompt chain mode, allow _end.png files to be processed
    if prompt_chain_mode and file_stem.endswith('_end'):
        print(f"Prompt chain mode: Allowing end frame file {file_path}")
        return False

    # Check if any excluded keyword is in the filename
    return any(keyword.lower() in file_stem for keyword in excluded_keywords)

def get_image_files(directory, prompt_chain_mode=False):
    """Get all image files from directory"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
    image_files = []

    for ext in image_extensions:
        image_files.extend([f for f in Path(directory).glob(f'*{ext}') if f.is_file()])
        image_files.extend([f for f in Path(directory).glob(f'*{ext.upper()}') if f.is_file()])

    # Filter out any non-image files that might have been caught
    image_files = [f for f in image_files if f.suffix.lower() in image_extensions]

    # Filter out files with "expanded" or "rotated" in their names
    # In prompt chain mode, allow _end.png files to be processed
    excluded_files = [f for f in image_files if should_exclude_file(f, prompt_chain_mode)]
    filtered_files = [f for f in image_files if not should_exclude_file(f, prompt_chain_mode)]

    if excluded_files:
        print(f"\nAutomatically excluding {len(excluded_files)} files with 'expanded' or 'rotated' in their names:")
        for i, img in enumerate(excluded_files):
            print(f"  {i+1}. {img.name}")

    # Remove duplicates while preserving order
    seen = set()
    unique_image_files = []
    for f in filtered_files:
        if f not in seen:
            seen.add(f)
            unique_image_files.append(f)

    return sorted(unique_image_files)

def get_image_prompt(image_path):
    """Extract the prompt from an image's metadata"""
    try:
        with Image.open(image_path) as img:
            exif_data = img.info
            if not exif_data:
                return None

            # Look for parameters in different possible metadata fields
            prompt = None

            # Check standard 'parameters' field (common in SD outputs)
            if 'parameters' in exif_data:
                params = exif_data['parameters']
                # Extract just positive prompt if there's a negative prompt section
                positive_end = params.find('Negative prompt:')
                if positive_end != -1:
                    prompt = params[:positive_end].strip()
                else:
                    prompt = params.strip()

            # Check for other common metadata fields if parameters wasn't found
            elif 'prompt' in exif_data:
                prompt = exif_data['prompt']
            elif 'Comment' in exif_data:
                # Some tools store in Comment field
                prompt = exif_data['Comment']

            # Handle case where metadata exists but prompt is empty
            if prompt and len(prompt.strip()) == 0:
                return None

            return prompt

    except Exception as e:
        print(f"Warning: Error extracting metadata from {image_path}: {e}")
        return None

def save_image_with_metadata(image_array, filepath, metadata_dict):
    """
    Save an image with metadata embedded in the PNG file.

    Args:
        image_array: Numpy array containing the image data
        filepath: Path where the image will be saved
        metadata_dict: Dictionary containing metadata to embed
    """
    # Convert numpy array to PIL Image
    img = Image.fromarray(image_array)

    # Convert metadata to JSON string
    metadata_json = json.dumps(metadata_dict)

    # Create PngInfo object
    png_info = PngImagePlugin.PngInfo()

    # Add metadata to the PngInfo object
    png_info.add_text("FramePack", metadata_json)

    # Save the image with metadata
    img.save(filepath, format="PNG", pnginfo=png_info)

def update_processed_files_txt(file_path, input_dir, debug=False):
    """
    Directly update the processed_files.txt file with a new entry.
    This function adds the file path to the processed_files.txt file in the input directory,
    but only if the file path doesn't already exist in the file.

    Args:
        file_path (str): The path of the file to add to the processed_files.txt
        input_dir (str): The directory where the processed_files.txt file is located
        debug (bool): Whether to print debug information

    Returns:
        bool: True if the file was updated successfully or already exists, False otherwise
    """
    try:
        # Ensure the input directory exists
        os.makedirs(input_dir, exist_ok=True)

        # Define the path to the processed_files.txt file
        processed_files_path = os.path.join(input_dir, "processed_files.txt")

        if debug:
            print(f"DEBUG: Checking processed_files.txt at {processed_files_path}")
            print(f"DEBUG: Checking for file: {file_path}")

        # First check if the file already exists in processed_files.txt
        file_already_exists = False
        if os.path.exists(processed_files_path):
            with open(processed_files_path, 'r') as f:
                content = f.read().splitlines()
                if file_path in content:
                    file_already_exists = True
                    if debug:
                        print(f"DEBUG: File path already exists in processed_files.txt, skipping append")
                    return True

        # Only append if the file doesn't already exist in the list
        if not file_already_exists:
            if debug:
                print(f"DEBUG: File path not found in processed_files.txt, appending")

            # Append the file path to the processed_files.txt file
            with open(processed_files_path, 'a') as f:
                f.write(f"{file_path}\n")
                # Flush the file to ensure it's written immediately
                f.flush()
                os.fsync(f.fileno())

            # Verify the file was updated correctly
            if os.path.exists(processed_files_path):
                with open(processed_files_path, 'r') as f:
                    content = f.read()
                    if file_path in content:
                        if debug:
                            print(f"DEBUG: Verified file path was successfully added to processed_files.txt")
                        return True
                    else:
                        print(f"Warning: File path was not found in processed_files.txt after update")
                        return False
            else:
                print(f"Warning: processed_files.txt does not exist after update attempt")
                return False

        return True
    except Exception as e:
        print(f"Error updating processed_files.txt: {e}")
        traceback.print_exc()
        return False

def fix_video_encoding(input_path):
    """Re-encode video to ensure web compatibility with minimal quality loss using FFmpeg"""
    try:
        input_path = Path(input_path)
        output_path = input_path.with_stem(input_path.stem + "_fixed")

        ffmpeg_cmd = [
            "ffmpeg",
            "-i", str(input_path),
            "-c:v", "libx264",
            "-preset", "fast",
            "-crf", "17",  # Lower CRF for high quality
            "-c:a", "aac",
            "-b:a", "192k",
            "-movflags", "+faststart",
            "-pix_fmt", "yuv420p",
            "-y",
            str(output_path)
        ]

        subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)
        print(f"Successfully fixed encoding: {input_path} -> {output_path}")
        return output_path

    except subprocess.CalledProcessError as e:
        print(f"Error fixing encoding for {input_path}: {e.stderr}")
        return None
    except FileNotFoundError:
        print("Error: FFmpeg is not installed or not found in PATH. Please install FFmpeg.")
        return None
    except Exception as e:
        print(f"Unexpected error fixing encoding for {input_path}: {str(e)}")
        return None

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Batch process images to generate videos with FramePack F1")

    parser.add_argument("--input_dir", type=str, default=input_dir,
                        help=f"Directory containing input images (default: {input_dir})")
    parser.add_argument("--output_dir", type=str, default=output_dir,
                        help=f"Directory to save output videos (default: {output_dir})")
    parser.add_argument("--prompt", type=str, default=fallback_prompt,
                        help=f"Prompt to guide the generation (fallback: '{fallback_prompt}')")
    parser.add_argument("--seed", type=int, default=seed,
                        help=f"Random seed, -1 for random (default: {seed})")
    parser.add_argument("--use_teacache", action="store_true", default=use_teacache,
                        help=f"Use TeaCache - faster but may affect hand quality (default: {use_teacache})")
    parser.add_argument("--video_length", type=float, default=video_length,
                        help=f"Total video length in seconds, range 1-120 (default: {video_length})")
    parser.add_argument("--steps", type=int, default=steps,
                        help=f"Number of sampling steps, range 1-100 (default: {steps})")
    parser.add_argument("--distilled_cfg", type=float, default=distilled_cfg,
                        help=f"Distilled CFG scale, range 1.0-32.0 (default: {distilled_cfg})")
    parser.add_argument("--flow_shift", type=float, default=flow_shift,
                        help=f"Flow shift parameter, 0.0=auto-calculated, 1.0-10.0=manual override (default: {flow_shift})")
    parser.add_argument("--gpu_memory", type=float, default=gpu_memory,
                        help=f"GPU memory preservation in GB, range 6-128 (default: {gpu_memory})")
    parser.add_argument("--mp4_crf", type=int, default=mp4_crf,
                        help=f"MP4 compression quality, range 0-51, lower is better (default: {mp4_crf})")
    parser.add_argument("--randomize_order", action="store_true", default=randomize_order,
                        help=f"Randomize the order of image processing (default: {randomize_order})")
    parser.add_argument("--clear_processed_list", action="store_true", default=clear_processed_list,
                        help=f"Clear processed files list after completion (default: {clear_processed_list})")
    parser.add_argument("--use_image_prompt", action="store_true", default=use_image_prompt,
                        help="Use image metadata for prompt if available")
    parser.add_argument("--no_image_prompt", action="store_false", dest="use_image_prompt",
                        help="Do not use image metadata for prompt")

    # Text-to-video arguments
    parser.add_argument("--use_noise", action="store_true", default=False,
                        help="Use noise method for text-to-video instead of transparent images (default: False)")
    parser.add_argument("--pixel_trick", action="store_true", default=False,
                        help="Add 1-pixel opaque dot to transparent images to avoid black-frame trap (default: False)")

    parser.add_argument("--overwrite", action="store_true", default=overwrite,
                        help=f"Whether to overwrite existing output files (default: {overwrite})")
    parser.add_argument("--allow_duplicates", action="store_true", default=allow_duplicates,
                        help=f"Allow duplicate files to be processed and ignore tracking (default: {allow_duplicates})")
    parser.add_argument("--fix_encoding", action="store_true", dest="fix_encoding", default=fix_encoding,
                        help=f"Fix video encoding for web compatibility (default: {fix_encoding})")
    parser.add_argument("--no_fix_encoding", action="store_false", dest="fix_encoding",
                        help="Do not fix video encoding")
    parser.add_argument("--use_prompt_list_file", action="store_true", default=use_prompt_list_file,
                        help=f"Use prompt list file (default: {use_prompt_list_file})")
    parser.add_argument("--prompt_list_file", type=str, default=prompt_list_file,
                        help=f"Path to prompt list file (default: '{prompt_list_file}')")
    parser.add_argument("--copy_to_input", action="store_true", dest="copy_to_input", default=copy_to_input,
                        help=f"Copy final video to input folder (default: {copy_to_input})")
    parser.add_argument("--no_copy_to_input", action="store_false", dest="copy_to_input",
                        help="Do not copy final video to input folder")
    parser.add_argument("--files", nargs="+", type=str, default=None,
                        help="List of specific image files to process (full or relative paths). If provided, input_dir is ignored.")
    parser.add_argument("--file-list", type=str, default=None,
                        help="Path to a text file containing a list of image files to process (one file path per line). If provided, input_dir is ignored.")
    parser.add_argument("--url-list", type=str, default=None,
                        help="Path to a text file containing a list of image URLs to process (one URL per line). If provided, input_dir is ignored.")
    parser.add_argument("--combined-list", type=str, default=None,
                        help="Path to a text file containing a mixed list of image files and URLs to process (one item per line). If provided, input_dir is ignored.")
    parser.add_argument("--unified-list", type=str, default=None,
                        help="Path to a text file containing a unified list of directories, files, and URLs to process (one item per line). If provided, input_dir is ignored.")
    parser.add_argument("--apply_all_prompts", action="store_true", default=False,
                        help="Apply each prompt from prompt list to each image (creates multiple outputs per image)")
    parser.add_argument("--latent_window_size", type=int, default=latent_window_size,
                        help=f"Latent window size (default: {latent_window_size})")
    parser.add_argument("--temp_dir", type=str, default=temp_dir,
                        help=f"Directory for temporary files (default: {temp_dir})")
    parser.add_argument("--show_latent_preview", action="store_true", dest="show_latent_preview", default=show_latent_preview,
                        help=f"Show latent preview animation (default: {show_latent_preview})")
    parser.add_argument("--no_latent_preview", action="store_false", dest="show_latent_preview",
                        help="Do not show latent preview animation")
    parser.add_argument("--debug", action="store_true", default=debug_mode,
                        help=f"Enable debug mode for more verbose logging (default: {debug_mode})")

    # Prompt chain arguments
    parser.add_argument("--prompt_chain_mode", action="store_true", default=False,
                        help="Enable prompt chain processing mode")
    parser.add_argument("--chain_index", type=int, default=0,
                        help="Current index in the prompt chain (0-based)")
    parser.add_argument("--chain_total", type=int, default=1,
                        help="Total number of prompts in the chain")
    parser.add_argument("--use_chain_input", action="store_true", default=False,
                        help="Use previous chain step's end frame as input")
    parser.add_argument("--job_id", type=str, default=None,
                        help="Job ID to use for consistent naming across chain steps")

    # LoRA and optimization arguments (supports up to 3 LoRAs)
    parser.add_argument("--lora_file_1", type=str, default=lora_file_1,
                        help=f"Path to first LoRA file (default: {lora_file_1})")
    parser.add_argument("--lora_multiplier_1", type=float, default=lora_multiplier_1,
                        help=f"First LoRA strength multiplier, range 0.0-1.0 (default: {lora_multiplier_1})")
    parser.add_argument("--lora_file_2", type=str, default=lora_file_2,
                        help=f"Path to second LoRA file (default: {lora_file_2})")
    parser.add_argument("--lora_multiplier_2", type=float, default=lora_multiplier_2,
                        help=f"Second LoRA strength multiplier, range 0.0-1.0 (default: {lora_multiplier_2})")
    parser.add_argument("--lora_file_3", type=str, default=lora_file_3,
                        help=f"Path to third LoRA file (default: {lora_file_3})")
    parser.add_argument("--lora_multiplier_3", type=float, default=lora_multiplier_3,
                        help=f"Third LoRA strength multiplier, range 0.0-1.0 (default: {lora_multiplier_3})")

    # Backward compatibility arguments
    parser.add_argument("--lora_file", type=str, default=lora_file,
                        help=f"Path to LoRA file (backward compatibility, maps to --lora_file_1) (default: {lora_file})")
    parser.add_argument("--lora_multiplier", type=float, default=lora_multiplier,
                        help=f"LoRA strength multiplier (backward compatibility, maps to --lora_multiplier_1) (default: {lora_multiplier})")
    parser.add_argument("--fp8_optimization", action="store_true", default=fp8_optimization,
                        help=f"Enable FP8 optimization for memory efficiency (default: {fp8_optimization})")

    parser.add_argument("--custom_model_path", type=str, default=None,
                        help="Path to custom merged model directory (overrides default F1 model)")

    return parser.parse_args()


def resolve_custom_model_path_f1(custom_model_path):
    """Resolve custom model path to handle both HuggingFace cache format and simple format"""
    if not custom_model_path:
        return None

    # Convert to absolute path
    if not os.path.isabs(custom_model_path):
        custom_model_path = os.path.abspath(custom_model_path)

    # Check if it's HuggingFace cache format (models--*)
    if os.path.basename(custom_model_path).startswith("models--"):
        snapshots_dir = os.path.join(custom_model_path, "snapshots")
        if os.path.exists(snapshots_dir) and os.path.isdir(snapshots_dir):
            # Find the latest snapshot (there should typically be only one)
            snapshot_dirs = [d for d in os.listdir(snapshots_dir) if os.path.isdir(os.path.join(snapshots_dir, d))]
            if snapshot_dirs:
                # Use the first (and typically only) snapshot
                snapshot_path = os.path.join(snapshots_dir, snapshot_dirs[0])
                if os.path.exists(os.path.join(snapshot_path, "config.json")):
                    return snapshot_path

    # Check if it's simple format (config.json directly in the directory)
    if os.path.exists(os.path.join(custom_model_path, "config.json")):
        return custom_model_path

    # If neither format is found, return the original path and let the error occur
    return custom_model_path

def load_transformer_with_lora_f1(lora_file_1=None, lora_multiplier_1=0.8,
                                  lora_file_2=None, lora_multiplier_2=0.8,
                                  lora_file_3=None, lora_multiplier_3=0.8,
                                  fp8_optimization=False, custom_model_path=None,
                                  # Backward compatibility parameters
                                  lora_file=None, lora_multiplier=0.8):
    """Load F1 transformer model with optional multiple LoRAs and FP8 optimizations"""
    print("Loading F1 transformer...")

    # Handle backward compatibility - if old parameters are used, map them to new ones
    if lora_file is not None and lora_file_1 is None:
        lora_file_1 = lora_file
        lora_multiplier_1 = lora_multiplier

    # Determine model path
    if custom_model_path:
        resolved_path = resolve_custom_model_path_f1(custom_model_path)
        print(f"Loading custom F1 model from: {custom_model_path}")
        if resolved_path != custom_model_path:
            print(f"Resolved to: {resolved_path}")
        transformer = HunyuanVideoTransformer3DModelPacked.from_pretrained(
            resolved_path, torch_dtype=torch.bfloat16
        ).cpu()
    else:
        print("Loading standard F1 model")
        transformer = HunyuanVideoTransformer3DModelPacked.from_pretrained(
            'lllyasviel/FramePack_F1_I2V_HY_20250503', torch_dtype=torch.bfloat16
        ).cpu()
    transformer.eval()
    transformer.high_quality_fp32_output_for_inference = True
    print("transformer.high_quality_fp32_output_for_inference = True")
    transformer.to(dtype=torch.bfloat16)
    transformer.requires_grad_(False)

    # Apply LoRAs and/or FP8 optimizations if requested
    lora_configs = [
        (lora_file_1, lora_multiplier_1, 1),
        (lora_file_2, lora_multiplier_2, 2),
        (lora_file_3, lora_multiplier_3, 3)
    ]

    has_loras = any(lora_file is not None for lora_file, _, _ in lora_configs)

    if has_loras or fp8_optimization:
        state_dict = transformer.state_dict()

        # Apply LoRAs sequentially (should be merged before fp8 optimization)
        for lora_file, lora_multiplier, lora_index in lora_configs:
            if lora_file is not None:
                print(f"Merging LoRA {lora_index}: {os.path.basename(lora_file)} with multiplier {lora_multiplier}...")
                state_dict = merge_lora_to_state_dict(state_dict, lora_file, lora_multiplier, device=gpu)
                gc.collect()

        if fp8_optimization:
            TARGET_KEYS = ["transformer_blocks", "single_transformer_blocks"]
            EXCLUDE_KEYS = ["norm"]  # Exclude norm layers (e.g., LayerNorm, RMSNorm) from FP8
            print("Optimizing for fp8...")
            state_dict = optimize_state_dict_with_fp8(
                state_dict, gpu, TARGET_KEYS, EXCLUDE_KEYS, move_to_device=False
            )
            # apply monkey patching
            apply_fp8_monkey_patch(transformer, state_dict, use_scaled_mm=False)
            gc.collect()

        info = transformer.load_state_dict(state_dict, strict=True, assign=True)
        print(f"LoRA and/or fp8 optimization applied: {info}")

    return transformer


@torch.no_grad()
def process_single_image(image_path, output_dir, prompt="", n_prompt="", seed=-1,
                         video_length=5.0, steps=25, gs=10.0, flow_shift=0.0, gpu_memory=6.0,
                         use_teacache=True, high_vram=False, latent_window_size=9,
                         text_encoder=None, text_encoder_2=None, tokenizer=None, tokenizer_2=None,
                         vae=None, feature_extractor=None, image_encoder=None, transformer=None,
                         fix_encoding=True, copy_to_input=True, mp4_crf=16, show_latent_preview=True,
                         temp_dir="temp", job_id=None, chain_index=None, chain_total=None, prompt_chain_mode=False,
                         # Text-to-video parameters
                         use_noise=False, pixel_trick=False,
                         lora_file=None, lora_multiplier=0.8, fp8_optimization=False):
    """Process a single image to generate a video using FramePack F1"""

    # Use provided job_id or generate a unique timestamp ID for this job
    if job_id is None:
        job_id = generate_timestamp()
    else:
        # If job_id is provided but we need to ensure uniqueness for iterations,
        # append a timestamp suffix to make each iteration unique
        # This prevents filename collisions when using iterations
        timestamp_suffix = generate_timestamp()
        job_id = f"{job_id}_{timestamp_suffix}"
        print(f"Using job ID with timestamp suffix for uniqueness: {job_id}")

    # Add chain step suffix if this is part of a prompt chain
    if chain_index is not None and chain_total is not None and chain_total > 1:
        job_id_with_chain = f"{job_id}_p{chain_index + 1}"
        print(f"Prompt chain step {chain_index + 1}/{chain_total}: Using job ID {job_id_with_chain}")
    else:
        job_id_with_chain = job_id

    # Use random seed if seed is -1
    if seed == -1:
        seed = random.randint(0, 2**32 - 1)
        print(f"Using random seed: {seed}")

    # Calculate total latent sections based on video length
    total_latent_sections = (video_length * 30) / (latent_window_size * 4)
    total_latent_sections = int(max(round(total_latent_sections), 1))

    try:
        # Clean GPU
        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )

        # Text encoding
        print("Text encoding...")
        if not high_vram:
            fake_diffusers_current_device(text_encoder, gpu)
            load_model_as_complete(text_encoder_2, target_device=gpu)

        llama_vec, clip_l_pooler = encode_prompt_conds(prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)

        # Fixed CFG parameter for F1
        cfg = 1.0
        if cfg == 1:
            llama_vec_n, clip_l_pooler_n = torch.zeros_like(llama_vec), torch.zeros_like(clip_l_pooler)
        else:
            llama_vec_n, clip_l_pooler_n = encode_prompt_conds(n_prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)

        llama_vec, llama_attention_mask = crop_or_pad_yield_mask(llama_vec, length=512)
        llama_vec_n, llama_attention_mask_n = crop_or_pad_yield_mask(llama_vec_n, length=512)

        # Processing input image
        print("Image processing...")
        input_image = np.array(Image.open(image_path).convert('RGB'))
        H, W, _ = input_image.shape  # _ for unused channel dimension
        height, width = find_nearest_bucket(H, W, resolution=640)
        input_image_np = resize_and_center_crop(input_image, target_width=width, target_height=height)

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Store the start frame for later use
        temp_start_image = input_image_np.copy()

        # Define start frame path for later use and monitoring deletion
        start_frame_path = os.path.join(output_dir, f'{job_id_with_chain}_start.png')
        reference_image_path = start_frame_path  # For compatibility with existing code

        # Create start frame metadata
        start_frame_metadata = {
            "prompt": prompt,
            "negative_prompt": n_prompt,
            "seed": seed,
            "steps": steps,
            "cfg_scale": 1.0,  # Fixed CFG parameter
            "distilled_cfg_scale": gs,
            "cfg_rescale": 0.0,  # Fixed guidance_rescale parameter
            "flow_shift": flow_shift,
            "use_teacache": use_teacache,
            "total_video_length": f"{video_length} seconds",
            "total_sections": total_latent_sections,
            "frame_type": "start_frame",
            "job_id": job_id_with_chain,
            # LoRA settings (F1 uses single LoRA parameters)
            "lora_file": lora_file if lora_file else None,
            "lora_multiplier": lora_multiplier if lora_file else None,
            # Model and optimization settings
            "fp8_optimization": fp8_optimization,
            "model_type": "f1"
        }

        input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1
        input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]

        # VAE encoding
        print("VAE encoding...")
        if not high_vram:
            load_model_as_complete(vae, target_device=gpu)

        start_latent = vae_encode(input_image_pt, vae)

        # CLIP Vision encoding
        print("CLIP Vision encoding...")
        if not high_vram:
            load_model_as_complete(image_encoder, target_device=gpu)

        image_encoder_output = hf_clip_vision_encode(input_image_np, feature_extractor, image_encoder)
        image_encoder_last_hidden_state = image_encoder_output.last_hidden_state

        # Convert tensors to appropriate dtype
        llama_vec = llama_vec.to(transformer.dtype)
        llama_vec_n = llama_vec_n.to(transformer.dtype)
        clip_l_pooler = clip_l_pooler.to(transformer.dtype)
        clip_l_pooler_n = clip_l_pooler_n.to(transformer.dtype)
        image_encoder_last_hidden_state = image_encoder_last_hidden_state.to(transformer.dtype)

        # Save the start frame with metadata before generating any video sections
        print(f"Saving start frame with metadata before video generation")
        save_image_with_metadata(temp_start_image, start_frame_path, start_frame_metadata)
        print(f"Saved start frame to {start_frame_path}")

        # F1 model uses different history_latents initialization
        history_latents = torch.zeros(size=(1, 16, 16 + 2 + 1, height // 8, width // 8), dtype=torch.float32).cpu()
        history_pixels = None

        # Add start latent to history
        history_latents = torch.cat([history_latents, start_latent.to(history_latents)], dim=2)
        total_generated_latent_frames = 1



        # Sampling
        print("Starting sampling...")
        rnd = torch.Generator("cpu").manual_seed(seed)

        for section_index in range(total_latent_sections):
            print(f'section_index = {section_index}, total_latent_sections = {total_latent_sections}')

            if not high_vram:
                unload_complete_models()
                move_model_to_device_with_memory_preservation(transformer, target_device=gpu, preserved_memory_gb=gpu_memory)

            if use_teacache:
                transformer.initialize_teacache(enable_teacache=True, num_steps=steps)
            else:
                transformer.initialize_teacache(enable_teacache=False)

            # Fixed guidance_rescale parameter
            rs = 0.0

            def callback(d):
                current_step = d['i'] + 1

                # Check for immediate shutdown signal first
                global shutdown_requested
                if shutdown_requested:
                    print(f"\n⚠️ Immediate shutdown requested. Terminating generation...")
                    raise StopGenerationRequestedException("Immediate shutdown requested")

                # Check for stop queue flag file (terminates entire batch)
                stop_queue_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_queue.flag")
                if os.path.exists(stop_queue_flag_path):
                    print(f"\n⚠️ Stop queue flag detected. Terminating entire batch process...")
                    raise StopGenerationRequestedException("Stop queue requested by user")

                # Check for skip generation flag file (skips current item, continues batch)
                skip_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "skip_generation.flag")
                if os.path.exists(skip_flag_path):
                    print(f"\n⚠️ Skip generation flag detected. Skipping current item...")
                    raise StopGenerationRequestedException("Skip generation requested by user")

                # Check for legacy stop flag file for backward compatibility
                stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
                if os.path.exists(stop_flag_path):
                    print(f"\n⚠️ Legacy stop generation flag detected. Stopping generation gracefully...")
                    raise StopGenerationRequestedException("Stop generation requested by user")

                # Show progress every step for more accurate ETA calculation
                progress_message = f"Step {current_step}/{steps} - Section {section_index + 1}/{total_latent_sections} - Total frames: {int(max(0, total_generated_latent_frames * 4 - 3))}"
                print(progress_message)

                # Write progress to file for GUI ETA display
                try:
                    with open("framepack_progress.txt", "w") as f:
                        f.write(progress_message)
                except Exception:
                    pass  # Don't let file write errors stop generation

                    # Check if start frame image still exists
                    if not os.path.exists(reference_image_path):
                        print(f"\n⚠️ Start frame image {reference_image_path} has been deleted. Stopping generation...")
                        raise ReferenceImageDeletedException("Start frame image was deleted during generation")



                # Process latent preview from denoised output
                if show_latent_preview and 'denoised' in d:
                    try:
                        # Get the denoised output from the sampler
                        preview = d['denoised']

                        # Decode the latents using vae_decode_fake (lightweight decoder)
                        # This creates a low-resolution preview without using the full VAE
                        preview = vae_decode_fake(preview)

                        # Convert to numpy array and normalize to 0-255 range
                        preview = (preview * 255.0).detach().cpu().numpy().clip(0, 255).astype(np.uint8)

                        # Store the original shape for individual frame extraction
                        original_shape = preview.shape  # Should be [b, c, t, h, w]

                        # Rearrange for display
                        preview = einops.rearrange(preview, 'b c t h w -> (b h) (t w) c')

                        # Upscale the preview to a more standard resolution for better compatibility
                        # The original is typically around 64x96, let's upscale by 4x to ~256x384
                        h, w, _ = preview.shape
                        preview = cv2.resize(preview, (w*4, h*4), interpolation=cv2.INTER_NEAREST)

                        # Create a timestamp for the preview folder
                        timestamp = int(time.time())

                        # Create a temporary folder for the frames with timestamp
                        os.makedirs(temp_dir, exist_ok=True)
                        latent_frames_dir = os.path.join(temp_dir, f"{timestamp}_{latent_window_size}_latent_frames")
                        os.makedirs(latent_frames_dir, exist_ok=True)

                        # Extract individual frames from the preview
                        # Calculate the number of frames in the preview
                        h, w, _ = preview.shape

                        # Calculate the width of each individual frame
                        # The number of frames depends on the latent window size
                        num_frames = original_shape[2]  # Get from original shape
                        frame_width = w // num_frames

                        # Extract each individual frame
                        new_frames = []
                        for j in range(num_frames):
                            frame = preview[:, j*frame_width:(j+1)*frame_width, :]
                            new_frames.append(frame)

                            # Save the frame as an image with a sequential number
                            frame_path = os.path.join(latent_frames_dir, f"frame_{j+1:04d}.png")
                            cv2.imwrite(frame_path, cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

                        # Create a timestamp for the preview file
                        file_timestamp = int(time.time() * 1000)

                        # Create latent_previews directory if it doesn't exist
                        latent_previews_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "latent_previews")
                        os.makedirs(latent_previews_dir, exist_ok=True)

                        # Create a new animation file in the temp directory
                        latent_preview_mp4 = os.path.join(temp_dir, f"latest_latent_preview_{file_timestamp}.mp4")

                        # Create a video writer for the MP4 file with improved parameters
                        h, w, _ = new_frames[0].shape
                        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                        fps = 10  # Use 10 fps for the latent preview
                        video_writer = cv2.VideoWriter(latent_preview_mp4, fourcc, fps, (w, h))

                        # Ensure the video writer was initialized correctly
                        if not video_writer.isOpened():
                            print(f"Warning: Could not initialize video writer with dimensions {w}x{h}")
                            # Try with different codec
                            fourcc = cv2.VideoWriter_fourcc(*'avc1')
                            video_writer = cv2.VideoWriter(latent_preview_mp4, fourcc, fps, (w, h))

                        # Add each frame to the video
                        for frame in new_frames:
                            video_writer.write(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

                        # Release the video writer
                        video_writer.release()

                        # Create MP4 file directly in the latent_previews directory using ffmpeg
                        # Always use a timestamped filename to avoid permission issues
                        timestamped_preview_mp4 = os.path.join(latent_previews_dir, f"latent_preview_{file_timestamp}.mp4")

                        # First, create a temporary directory for the frames
                        temp_frames_dir = os.path.join(temp_dir, f"temp_frames_{file_timestamp}")
                        os.makedirs(temp_frames_dir, exist_ok=True)

                        try:
                            # Save each frame as a PNG file
                            for i, frame in enumerate(new_frames):
                                frame_path = os.path.join(temp_frames_dir, f"frame_{i:04d}.png")
                                cv2.imwrite(frame_path, cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

                            # Create a temporary MP4 file first
                            temp_mp4 = os.path.join(temp_dir, f"temp_preview_{file_timestamp}.mp4")

                            # Use ffmpeg to create the MP4 file directly from the PNG files
                            # Using mpeg4 codec which is more compatible with TkVideoPlayer
                            ffmpeg_cmd = [
                                "ffmpeg",
                                "-framerate", "10",
                                "-i", os.path.join(temp_frames_dir, "frame_%04d.png"),
                                "-c:v", "mpeg4",
                                "-q:v", "1",
                                "-pix_fmt", "yuv420p",
                                "-y",
                                temp_mp4
                            ]

                            # Run ffmpeg command
                            subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)

                            # Copy the temp file to the final location with the timestamped name
                            shutil.copy2(temp_mp4, timestamped_preview_mp4)
                            print(f"Created latent preview: {timestamped_preview_mp4}")

                            # Also create a "latest.txt" file that contains the path to the most recent preview
                            # This will be used by the GUI to find the latest preview
                            latest_txt_path = os.path.join(latent_previews_dir, "latest.txt")
                            try:
                                with open(latest_txt_path, 'w') as f:
                                    f.write(timestamped_preview_mp4)
                            except Exception as txt_error:
                                print(f"Warning: Could not update latest.txt: {txt_error}")

                            # Clean up temporary files
                            os.remove(temp_mp4)

                        except Exception as e:
                            print(f"Warning: Could not create latent preview: {e}")
                        finally:
                            # Always clean up the temporary frame files
                            try:
                                for file in os.listdir(temp_frames_dir):
                                    os.remove(os.path.join(temp_frames_dir, file))
                                os.rmdir(temp_frames_dir)
                            except Exception as cleanup_error:
                                print(f"Warning: Error cleaning up temporary files: {cleanup_error}")
                    except Exception as e:
                        print(f"Warning: Could not create latent preview animation: {e}")

                return

            # F1 model uses different indices setup
            indices = torch.arange(0, sum([1, 16, 2, 1, latent_window_size])).unsqueeze(0)
            clean_latent_indices_start, clean_latent_4x_indices, clean_latent_2x_indices, clean_latent_1x_indices, latent_indices = indices.split([1, 16, 2, 1, latent_window_size], dim=1)
            clean_latent_indices = torch.cat([clean_latent_indices_start, clean_latent_1x_indices], dim=1)

            clean_latents_4x, clean_latents_2x, clean_latents_1x = history_latents[:, :, -sum([16, 2, 1]):, :, :].split([16, 2, 1], dim=2)
            clean_latents = torch.cat([start_latent.to(history_latents), clean_latents_1x], dim=2)

            generated_latents = sample_hunyuan(
                transformer=transformer,
                sampler='unipc',
                width=width,
                height=height,
                frames=latent_window_size * 4 - 3,
                real_guidance_scale=cfg,
                distilled_guidance_scale=gs,
                guidance_rescale=rs,
                shift=None if flow_shift == 0.0 else flow_shift,
                num_inference_steps=steps,
                generator=rnd,
                prompt_embeds=llama_vec,
                prompt_embeds_mask=llama_attention_mask,
                prompt_poolers=clip_l_pooler,
                negative_prompt_embeds=llama_vec_n,
                negative_prompt_embeds_mask=llama_attention_mask_n,
                negative_prompt_poolers=clip_l_pooler_n,
                device=gpu,
                dtype=torch.bfloat16,
                image_embeddings=image_encoder_last_hidden_state,
                latent_indices=latent_indices,
                clean_latents=clean_latents,
                clean_latent_indices=clean_latent_indices,
                clean_latents_2x=clean_latents_2x,
                clean_latent_2x_indices=clean_latent_2x_indices,
                clean_latents_4x=clean_latents_4x,
                clean_latent_4x_indices=clean_latent_4x_indices,
                callback=callback,
            )

            total_generated_latent_frames += int(generated_latents.shape[2])
            history_latents = torch.cat([history_latents, generated_latents.to(history_latents)], dim=2)

            if not high_vram:
                offload_model_from_device_for_memory_preservation(transformer, target_device=gpu, preserved_memory_gb=8)
                load_model_as_complete(vae, target_device=gpu)

            real_history_latents = history_latents[:, :, -total_generated_latent_frames:, :, :]

            if history_pixels is None:
                history_pixels = vae_decode(real_history_latents, vae).cpu()
            else:
                section_latent_frames = latent_window_size * 2
                overlapped_frames = latent_window_size * 4 - 3

                current_pixels = vae_decode(real_history_latents[:, :, -section_latent_frames:], vae).cpu()
                history_pixels = soft_append_bcthw(history_pixels, current_pixels, overlapped_frames)

            if not high_vram:
                unload_complete_models()

            temp_output_filename = os.path.join(output_dir, f'{job_id_with_chain}_{total_generated_latent_frames}.mp4')
            # Use the specified CRF value to fix black video output issue (0 is uncompressed and can cause problems)
            save_bcthw_as_mp4(history_pixels, temp_output_filename, fps=30, crf=mp4_crf)

            print(f'Decoded. Current latent shape {real_history_latents.shape}; pixel shape {history_pixels.shape}')

        # Use the timestamp-based filename as the final output
        # Add duration to the temp filename for better identification
        duration_seconds = int(video_length)  # Truncate to whole number
        final_output_filename = os.path.join(output_dir, f'{job_id_with_chain}_{total_generated_latent_frames}_{duration_seconds}s.mp4')

        # Rename the temp file to include the duration
        os.rename(temp_output_filename, final_output_filename)

        # Save the end frame with metadata
        try:
            # Extract the last frame from the video
            video = cv2.VideoCapture(final_output_filename)
            total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
            video.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)
            success, last_frame = video.read()
            video.release()

            if success:
                # Convert from BGR to RGB
                last_frame_rgb = cv2.cvtColor(last_frame, cv2.COLOR_BGR2RGB)

                # Create end frame metadata
                end_frame_metadata = {
                    "prompt": prompt,
                    "negative_prompt": n_prompt,
                    "seed": seed,
                    "steps": steps,
                    "cfg_scale": 1.0,  # Fixed CFG parameter
                    "distilled_cfg_scale": gs,
                    "cfg_rescale": 0.0,  # Fixed guidance_rescale parameter
                    "flow_shift": flow_shift,
                    "use_teacache": use_teacache,
                    "total_video_length": f"{video_length} seconds",
                    "total_sections": total_latent_sections,
                    "frame_type": "end_frame",
                    "job_id": job_id,
                    # LoRA settings (F1 uses single LoRA parameters)
                    "lora_file": lora_file if lora_file else None,
                    "lora_multiplier": lora_multiplier if lora_file else None,
                    # Model and optimization settings
                    "fp8_optimization": fp8_optimization,
                    "model_type": "f1"
                }

                # Save the end frame with metadata
                end_frame_path = os.path.join(output_dir, f'{job_id}_end.png')
                save_image_with_metadata(last_frame_rgb, end_frame_path, end_frame_metadata)
                print(f"Saved end frame to {end_frame_path} (extracted from video)")
            else:
                print("Warning: Could not extract last frame from video for end frame")
        except Exception as e:
            print(f"Error saving end frame: {e}")

        # Handle encoding fix and copying to input folder
        input_dir = str(Path(image_path).parent)
        # Use the timestamp-based filename for the input folder copy as well
        input_output_filename = os.path.join(input_dir, f'{job_id_with_chain}_{total_generated_latent_frames}_{duration_seconds}s.mp4')

        if copy_to_input:
            try:
                # Check if file exists and is locked
                if os.path.exists(input_output_filename):
                    try:
                        with open(input_output_filename, 'a'):
                            pass
                    except:
                        print(f"Warning: Output file {input_output_filename} is locked or in use. Skipping copy to input folder.")
                        return final_output_filename

                # Fix encoding if enabled
                if fix_encoding:
                    fixed_output = fix_video_encoding(final_output_filename)
                    if fixed_output:
                        # Use the fixed video for copying
                        shutil.copy2(fixed_output, input_output_filename)
                        print(f"✅ Successfully processed and fixed {image_path} -> {input_output_filename}")
                        # Remove the fixed temporary file
                        os.remove(fixed_output)
                    else:
                        print(f"Warning: Encoding fix failed. Copying original video to {input_output_filename}")
                        shutil.copy2(final_output_filename, input_output_filename)
                        print(f"✅ Successfully processed {image_path} -> {input_output_filename}")
                else:
                    shutil.copy2(final_output_filename, input_output_filename)
                    print(f"✅ Successfully processed {image_path} -> {input_output_filename}")

            except PermissionError:
                print(f"Warning: Could not copy to {input_output_filename} due to permission error. Output is still available at {final_output_filename}")
            except Exception as e:
                print(f"Warning: Could not copy to input folder: {e}. Output is still available at {final_output_filename}")

        else:
            if fix_encoding:
                fixed_output = fix_video_encoding(final_output_filename)
                if fixed_output:
                    # Replace the original output with the fixed version
                    shutil.move(fixed_output, final_output_filename)
                    print(f"✅ Successfully processed and fixed {image_path} -> {final_output_filename}")
                else:
                    print(f"Warning: Encoding fix failed. Keeping original video at {final_output_filename}")
                    print(f"✅ Successfully processed {image_path} -> {final_output_filename}")
            else:
                print(f"✅ Successfully processed {image_path} -> {final_output_filename}")

        # Skip auto-sorting for individual chain steps - only sort the final joined video
        if not prompt_chain_mode:
            # Run the auto sorter to copy the largest file to the sorted folder and deduplicate
            print("\nRunning automatic sorting and deduplication...")
            try:
                # Get the filename of the video we just created
                recent_file = os.path.basename(final_output_filename)
                print(f"Processing recent file: {recent_file}")

                # Get the input directory (parent directory of the input image)
                input_dir = str(Path(image_path).parent)

                # Pass the input directory and original input file to the auto sorter
                auto_sort_after_generation(
                    outputs_folder=output_dir,
                    recent_file=recent_file,
                    input_dir=input_dir,
                    original_input_file=str(image_path)
                )
            except Exception as sort_error:
                print(f"Error during auto-sorting: {sort_error}")
                traceback.print_exc()
        else:
            chain_info = f"{chain_index + 1}/{chain_total}" if chain_index is not None and chain_total is not None else "unknown"
            print(f"\nSkipping auto-sorting for prompt chain step {chain_info}")
            print("Individual chain step videos will remain in outputs folder")
            print("Only the final joined video will be placed in the sorted folder")

        return final_output_filename

    except (ReferenceImageDeletedException, StopGenerationRequestedException) as e:
        print(f"⚠️ Stopping generation for {image_path}: {e}")

        # Clean up any intermediate files
        print("Cleaning up intermediate files...")
        try:
            # Find and remove all temporary MP4 files for this job
            temp_files = glob.glob(os.path.join(output_dir, f'{job_id_with_chain}_*.mp4'))
            for temp_file in temp_files:
                try:
                    os.remove(temp_file)
                    print(f"Removed temporary file: {temp_file}")
                except Exception as cleanup_error:
                    print(f"Error removing temporary file {temp_file}: {cleanup_error}")

            # Also remove the start frame image if it hasn't been deleted yet
            if os.path.exists(reference_image_path):
                os.remove(reference_image_path)
                print(f"Removed start frame image: {reference_image_path}")

            # Determine the type of stop request and handle flag files accordingly
            if isinstance(e, StopGenerationRequestedException):
                stop_reason = str(e).lower()

                # For skip generation, only remove the skip flag but preserve others
                if "skip generation" in stop_reason:
                    skip_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "skip_generation.flag")
                    if os.path.exists(skip_flag_path):
                        os.remove(skip_flag_path)
                        print(f"Removed skip generation flag file: {skip_flag_path}")
                    # Return a special value to indicate this was a skip, not a stop
                    if not high_vram:
                        unload_complete_models(
                            text_encoder, text_encoder_2, image_encoder, vae, transformer
                        )
                    return "SKIPPED"
                else:
                    # For other stop types, remove all flag files
                    flag_files = [
                        "stop_queue.flag",
                        "skip_generation.flag",
                        "stop_framepack.flag"  # legacy
                    ]
                    for flag_file in flag_files:
                        flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), flag_file)
                        if os.path.exists(flag_path):
                            os.remove(flag_path)
                            print(f"Removed flag file: {flag_path}")

            # Clean up progress file for GUI ETA display
            try:
                progress_file = "framepack_progress.txt"
                if os.path.exists(progress_file):
                    os.remove(progress_file)
            except Exception:
                pass  # Don't let cleanup errors affect the main process
        except Exception as cleanup_error:
            print(f"Error during cleanup: {cleanup_error}")

        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )
        return None



    except Exception as e:
        print(f"❌ Error processing {image_path}: {e}")
        traceback.print_exc()

        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )
        return None

def main():
    # Check if sed_list command is available and create a placeholder if needed
    check_sed_list_command()

    args = parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Create temp directory if it doesn't exist
    os.makedirs(args.temp_dir, exist_ok=True)

    # Create latent_previews directory if it doesn't exist
    latent_previews_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "latent_previews")
    os.makedirs(latent_previews_dir, exist_ok=True)

    # Process specific files if provided via --files, --file-list, --url-list, --combined-list, or --unified-list
    if args.files or args.file_list or args.url_list or args.combined_list or args.unified_list:
        image_files = []
        downloaded_files = []  # Track downloaded files for cleanup later

        # For URL, file list, combined list, and unified list modes, we'll use appropriate directories for processed_files.txt
        if args.url_list or args.combined_list or args.unified_list:
            # For URL-based processing, use the temp directory
            os.makedirs(args.temp_dir, exist_ok=True)
            args.input_dir = args.temp_dir
        elif args.file_list:
            # For file list processing, use the directory of the file list as the input directory
            # This allows tracking processed files across multiple runs with the same file list
            file_list_dir = os.path.dirname(os.path.abspath(args.file_list))
            if file_list_dir:
                args.input_dir = file_list_dir
        elif args.files:
            # For individual files mode, we'll use the directory of the first file as the input directory
            # This is only relevant if the files are in the same directory
            if len(args.files) > 0:
                first_file_dir = os.path.dirname(os.path.abspath(args.files[0]))
                if first_file_dir and os.path.isdir(first_file_dir):
                    args.input_dir = first_file_dir

        # Process files from --files parameter
        if args.files:
            for file_path in args.files:
                # Handle both absolute and relative paths
                if os.path.isabs(file_path):
                    path = Path(file_path)
                else:
                    path = Path(os.path.abspath(file_path))

                if path.exists() and path.is_file():
                    # Check if it's an image file
                    if path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
                        image_files.append(path)
                    else:
                        print(f"Warning: Skipping {path} - not a supported image file")
                else:
                    print(f"Warning: File not found or not a file: {path}")

        # Process files from --file-list parameter
        if args.file_list:
            if os.path.exists(args.file_list):
                print(f"Reading file list from {args.file_list}")
                try:
                    with open(args.file_list, 'r', encoding='utf-8') as f:
                        for line in f:
                            file_path = line.strip()
                            if file_path:
                                # Handle both absolute and relative paths
                                if os.path.isabs(file_path):
                                    path = Path(file_path)
                                else:
                                    path = Path(os.path.abspath(file_path))

                                if path.exists() and path.is_file():
                                    # Check if it's an image file
                                    if path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
                                        image_files.append(path)
                                    else:
                                        print(f"Warning: Skipping {path} - not a supported image file")
                                else:
                                    print(f"Warning: File not found or not a file: {path}")
                except Exception as e:
                    print(f"Error reading file list: {e}")
            else:
                print(f"Warning: File list not found: {args.file_list}")

        # Process URLs from --url-list parameter
        if args.url_list:
            if os.path.exists(args.url_list):
                print(f"Reading URL list from {args.url_list}")
                try:
                    # Ensure the temp directory exists
                    os.makedirs(args.temp_dir, exist_ok=True)

                    with open(args.url_list, 'r', encoding='utf-8') as f:
                        for line in f:
                            url = line.strip()
                            if url:
                                # Download the image from the URL
                                downloaded_file = download_image_from_url(url, args.temp_dir)
                                if downloaded_file:
                                    image_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                                else:
                                    print(f"Warning: Failed to download image from URL: {url}")
                except Exception as e:
                    print(f"Error processing URL list: {e}")
            else:
                print(f"Warning: URL list not found: {args.url_list}")

        # Process combined list (files and URLs) from --combined-list parameter
        if args.combined_list:
            if os.path.exists(args.combined_list):
                print(f"Reading combined list from {args.combined_list}")
                try:
                    # Ensure the temp directory exists
                    os.makedirs(args.temp_dir, exist_ok=True)

                    with open(args.combined_list, 'r', encoding='utf-8') as f:
                        for line in f:
                            item = line.strip()
                            if not item:
                                continue

                            # Check if it's a URL, a URL with prefix, a directory, or a file path
                            if item.startswith('URL:'):
                                # It's a URL with the URL: prefix to handle special characters
                                url = item[4:]  # Remove the URL: prefix
                                print(f"Processing URL from combined list: {url}")
                                downloaded_file = download_image_from_url(url, args.temp_dir)
                                if downloaded_file:
                                    image_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                                else:
                                    print(f"Warning: Failed to download image from URL: {url}")
                            elif item.startswith(('http://', 'https://')):
                                # It's a URL without prefix (for backward compatibility)
                                print(f"Processing URL from combined list: {item}")
                                downloaded_file = download_image_from_url(item, args.temp_dir)
                                if downloaded_file:
                                    image_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                                else:
                                    print(f"Warning: Failed to download image from URL: {item}")
                            else:
                                # It's a file path, check if it exists
                                print(f"Processing file path from combined list: {item}")
                                # Handle both absolute and relative paths
                                if os.path.isabs(item):
                                    path = Path(item)
                                else:
                                    path = Path(os.path.abspath(item))

                                if path.exists() and path.is_file():
                                    # Check if it's an image file
                                    if path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
                                        image_files.append(path)
                                    else:
                                        print(f"Warning: Skipping {path} - not a supported image file")
                                else:
                                    print(f"Warning: File not found or not a file: {path}")
                except Exception as e:
                    print(f"Error processing combined list: {e}")
            else:
                print(f"Warning: Combined list not found: {args.combined_list}")

        # Process unified list (directories, files, and URLs) from --unified-list parameter
        if args.unified_list:
            if os.path.exists(args.unified_list):
                print(f"Reading unified list from {args.unified_list}")
                try:
                    # Ensure the temp directory exists
                    os.makedirs(args.temp_dir, exist_ok=True)

                    with open(args.unified_list, 'r', encoding='utf-8') as f:
                        for line in f:
                            item = line.strip()
                            if not item:
                                continue

                            # Check if it's a directory, URL with prefix, URL, or a file path
                            if item.startswith('DIR:'):
                                # It's a directory, extract the path and process all images in it
                                dir_path = item[4:]  # Remove the DIR: prefix
                                # Normalize the path to handle forward/backward slash differences
                                dir_path = os.path.normpath(dir_path)
                                print(f"Processing directory from unified list: {dir_path}")

                                if os.path.exists(dir_path) and os.path.isdir(dir_path):
                                    # Get all image files from the directory
                                    valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
                                    dir_image_files = []

                                    for ext in valid_extensions:
                                        dir_image_files.extend([f for f in Path(dir_path).glob(f'*{ext}') if f.is_file()])
                                        dir_image_files.extend([f for f in Path(dir_path).glob(f'*{ext.upper()}') if f.is_file()])

                                    if dir_image_files:
                                        print(f"Found {len(dir_image_files)} image files in directory {dir_path}")
                                        image_files.extend(dir_image_files)
                                    else:
                                        print(f"Warning: No image files found in directory: {dir_path}")
                                else:
                                    print(f"Warning: Directory not found: {dir_path}")
                            elif item.startswith('URL:'):
                                # It's a URL with the URL: prefix to handle special characters
                                url = item[4:]  # Remove the URL: prefix
                                print(f"Processing URL from unified list: {url}")
                                downloaded_file = download_image_from_url(url, args.temp_dir)
                                if downloaded_file:
                                    image_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                                else:
                                    print(f"Warning: Failed to download image from URL: {url}")
                            elif item.startswith(('http://', 'https://')):
                                # It's a URL without prefix (for backward compatibility)
                                print(f"Processing URL from unified list: {item}")
                                downloaded_file = download_image_from_url(item, args.temp_dir)
                                if downloaded_file:
                                    image_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                                else:
                                    print(f"Warning: Failed to download image from URL: {item}")
                            else:
                                # It's a file path, check if it exists
                                print(f"Processing file path from unified list: {item}")
                                # Handle both absolute and relative paths
                                if os.path.isabs(item):
                                    path = Path(item)
                                else:
                                    path = Path(os.path.abspath(item))

                                if path.exists() and path.is_file():
                                    # Check if it's an image file
                                    if path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
                                        image_files.append(path)
                                    else:
                                        print(f"Warning: Skipping {path} - not a supported image file")
                                else:
                                    print(f"Warning: File not found or not a file: {path}")
                except Exception as e:
                    print(f"Error processing unified list: {e}")
                    traceback.print_exc()
            else:
                print(f"Warning: Unified list not found: {args.unified_list}")

        if not image_files:
            print("No valid image files found in the provided file/URL list")
            return
    else:
        # Check if input directory exists
        if not os.path.isdir(args.input_dir):
            print(f"Input directory {args.input_dir} does not exist. Creating...")
            os.makedirs(args.input_dir, exist_ok=True)
            print(f"Please add images to {args.input_dir} and run again.")
            return

        # Get all image files from input directory
        image_files = get_image_files(args.input_dir, args.prompt_chain_mode)
        if not image_files:
            print(f"No image files found in {args.input_dir}")
            return

    print(f"Found {len(image_files)} image files:")
    for i, img in enumerate(image_files):
        print(f"  {i+1}. {img.name}")

    # Create a processed files tracking file
    processed_files = set()
    # Track newly processed files in this session to append them at the bottom
    newly_processed_files = set()

    # Always define processed_files_path to avoid issues with undefined variable
    # Default to the input directory for the processed_files.txt location
    processed_files_path = os.path.join(args.input_dir, "processed_files.txt")

    # Load and use the processed files list if duplicates are not allowed
    # Only apply duplicate checking for directory processing, not individual files or lists
    if not args.allow_duplicates and not args.files and not args.file_list and not args.url_list and not args.combined_list and not args.unified_list:
        # Use the input directory for the processed_files.txt location
        print(f"Using processed files tracking at: {processed_files_path}")

        # Load previously processed files if the file exists
        if os.path.exists(processed_files_path):
            try:
                with open(processed_files_path, 'r') as f:
                    processed_files = set(line.strip() for line in f.readlines() if line.strip())
                print(f"Loaded {len(processed_files)} previously processed files from tracking file")
            except Exception as e:
                print(f"Error loading processed files tracking: {e}")
        else:
            print(f"No existing processed files tracking file found. Creating an empty file.")
            try:
                # Create an empty file
                with open(processed_files_path, 'w') as f:
                    pass
                print(f"Created empty processed files tracking file at {processed_files_path}")
            except Exception as e:
                print(f"Error creating processed files tracking file: {e}")

        print(f"Total processed files loaded: {len(processed_files)}")
    elif args.files or args.file_list or args.url_list or args.combined_list or args.unified_list:
        print("Individual files/lists mode: Duplicate checking disabled (files will always be processed)")
        processed_files_path = None  # Disable processed files tracking for individual files and lists

    # Check for existing output files if overwrite is disabled and duplicates are not allowed
    # Only apply this check for directory processing, not individual files or lists
    if not args.overwrite and not args.allow_duplicates and not args.files and not args.file_list and not args.url_list and not args.combined_list and not args.unified_list:
        skipped_files = []
        files_to_process = []

        print(f"\nChecking for previously processed files...")
        print(f"Found {len(processed_files)} entries in the processed files tracking list")

        for img_path in image_files:
            # Check if file has already been processed according to tracking
            if str(img_path) in processed_files:
                skipped_files.append(img_path)
                print(f"  Skipping {img_path.name} - found in processed files list")
            else:
                files_to_process.append(img_path)
                print(f"  Will process {img_path.name} - not found in processed files list")

        if skipped_files:
            print(f"\nSkipping {len(skipped_files)} files that have already been processed:")
            for i, img in enumerate(skipped_files):
                print(f"  {i+1}. {img.name}")

        image_files = files_to_process

        if not image_files:
            print(f"\nNo files to process. All images have already been processed.")
            print(f"Use --overwrite to regenerate videos for existing files.")
            print(f"Or use --allow_duplicates to process files multiple times.")
            return

    # Randomize order if requested (now works for all modes)
    if args.randomize_order:
        import random
        random.shuffle(image_files)
        print("Image processing order has been randomized")

    # Print batch processing settings
    print("\nProcessing Settings:")
    if args.unified_list:
        print(f"  Mode: Unified (Directories, Files, and URLs)")
        print(f"  Number of Items: {len(image_files)}")
    elif args.combined_list:
        print(f"  Mode: Combined (Files and URLs)")
        print(f"  Number of Items: {len(image_files)}")
    elif args.url_list:
        print(f"  Mode: URL List")
        print(f"  Number of URLs: {len(image_files)}")
    elif args.files or args.file_list:
        print(f"  Mode: Individual Files")
        print(f"  Number of Files: {len(image_files)}")
    else:
        print(f"  Mode: Batch Directory")
        print(f"  Input Directory: {args.input_dir}")
    print(f"  Output Directory: {args.output_dir}")
    # Determine prompt source for settings printout (accurate to per-image .txt, prompt list, image metadata, or fallback)
    prompt_desc = None
    per_image_txt_exists = all(os.path.exists(str(img.with_suffix('.txt'))) for img in image_files)
    if per_image_txt_exists and len(image_files) > 0:
        prompt_desc = "(Using per-image .txt files)"
    elif args.use_prompt_list_file and os.path.exists(args.prompt_list_file):
        prompt_desc = f"(Using prompt list: {args.prompt_list_file})"
    elif args.use_image_prompt:
        prompt_desc = "(Using image metadata)"
    elif args.prompt:
        prompt_desc = args.prompt
    else:
        prompt_desc = f"(Fallback: '{fallback_prompt}')"
    print(f"  Prompt: {prompt_desc}")
    print(f"  Video Length: {args.video_length} seconds")
    print(f"  Steps: {args.steps}")
    print(f"  Seed: {args.seed if args.seed != -1 else 'Random'}")
    print(f"  Distilled CFG: {args.distilled_cfg}")
    print(f"  TeaCache: {args.use_teacache}")
    print(f"  GPU Memory: {args.gpu_memory} GB")
    print(f"  MP4 Compression (CRF): {args.mp4_crf} (0-51, lower is better)")
    print(f"  Randomize Order: {args.randomize_order}")
    print(f"  Clear Processed List: {args.clear_processed_list if not args.files else 'N/A (Individual Files)'}")
    print(f"  Overwrite Existing: {args.overwrite}")
    # Determine if we're in a mode that skips duplicate checking
    skip_duplicate_checking = args.files or args.file_list or args.url_list or args.combined_list or args.unified_list
    print(f"  Allow Duplicates: {args.allow_duplicates if not skip_duplicate_checking else 'N/A (Individual Files/Lists - duplicates always allowed)'}")
    print(f"  Fix Encoding: {args.fix_encoding}")
    print(f"  Copy to Input: {args.copy_to_input}")
    print(f"  Latent Window Size: {args.latent_window_size}")

    # Print prompt chain information if enabled
    if args.prompt_chain_mode:
        print(f"  Prompt Chain Mode: Enabled")
        print(f"  Chain Index: {args.chain_index + 1}/{args.chain_total}")
        if args.use_chain_input:
            print(f"  Using Chain Input: Previous video's end frame")

    # Handle prompt chain input - find the job-specific end frame if needed
    if args.prompt_chain_mode and args.use_chain_input:
        print("Prompt chain mode: Looking for previous video's end frame...")

        # Look for job-specific _end.png file in the output directory
        if args.job_id:
            # Look for the specific job's end frame first
            job_specific_end_frame = os.path.join(args.output_dir, f"{args.job_id}_end.png")

            if os.path.exists(job_specific_end_frame):
                print(f"Found job-specific end frame: {job_specific_end_frame}")
                image_files = [Path(job_specific_end_frame)]
                print(f"Using job-specific end frame as input for chain step {args.chain_index + 1}")
            else:
                print(f"Warning: Job-specific end frame not found: {job_specific_end_frame}")
                print("This might be the first step in the chain, proceeding with original input files")
        else:
            # Fallback to most recent end frame (legacy behavior)
            print("Warning: No job_id provided, falling back to most recent end frame")
            import glob
            end_frame_pattern = os.path.join(args.output_dir, "*_end.png")
            end_frame_files = glob.glob(end_frame_pattern)

            if end_frame_files:
                # Sort by modification time to get the most recent
                end_frame_files.sort(key=os.path.getmtime, reverse=True)
                most_recent_end_frame = end_frame_files[0]

                print(f"Found most recent end frame: {most_recent_end_frame}")
                image_files = [Path(most_recent_end_frame)]
                print(f"Using most recent end frame as input for chain step {args.chain_index + 1}")
            else:
                print("Warning: No end frame files found in output directory for chain processing")
                print("This might be the first step in the chain, proceeding with original input files")

    print(f"\nProcessing {len(image_files)} images...")

    # Check VRAM and set high_vram mode
    free_mem_gb = get_cuda_free_memory_gb(gpu)
    high_vram = free_mem_gb > 60

    print(f'Free VRAM {free_mem_gb} GB')
    print(f'High-VRAM Mode: {high_vram}')

    # Load models - following the same pattern as demo_gradio_f1.py
    print("Loading models...")
    text_encoder = LlamaModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder', torch_dtype=torch.float16).cpu()
    text_encoder_2 = CLIPTextModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder_2', torch_dtype=torch.float16).cpu()
    tokenizer = LlamaTokenizerFast.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer')
    tokenizer_2 = CLIPTokenizer.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer_2')
    vae = AutoencoderKLHunyuanVideo.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='vae', torch_dtype=torch.float16).cpu()

    feature_extractor = SiglipImageProcessor.from_pretrained("lllyasviel/flux_redux_bfl", subfolder='feature_extractor')
    image_encoder = SiglipVisionModel.from_pretrained("lllyasviel/flux_redux_bfl", subfolder='image_encoder', torch_dtype=torch.float16).cpu()

    # Use the F1 model with LoRA support instead of the regular FramePack model
    # Handle backward compatibility and new multi-LoRA parameters
    transformer = load_transformer_with_lora_f1(
        lora_file_1=args.lora_file_1 if hasattr(args, 'lora_file_1') else args.lora_file,
        lora_multiplier_1=args.lora_multiplier_1 if hasattr(args, 'lora_multiplier_1') else args.lora_multiplier,
        lora_file_2=args.lora_file_2 if hasattr(args, 'lora_file_2') else None,
        lora_multiplier_2=args.lora_multiplier_2 if hasattr(args, 'lora_multiplier_2') else 0.8,
        lora_file_3=args.lora_file_3 if hasattr(args, 'lora_file_3') else None,
        lora_multiplier_3=args.lora_multiplier_3 if hasattr(args, 'lora_multiplier_3') else 0.8,
        fp8_optimization=args.fp8_optimization,
        custom_model_path=args.custom_model_path
    )

    # Set models to evaluation mode
    vae.eval()
    text_encoder.eval()
    text_encoder_2.eval()
    image_encoder.eval()
    transformer.eval()

    # Configure models for low VRAM mode
    if not high_vram:
        vae.enable_slicing()
        vae.enable_tiling()

    # Set high quality output for transformer
    transformer.high_quality_fp32_output_for_inference = True
    print('transformer.high_quality_fp32_output_for_inference = True')

    # Set appropriate data types for models
    transformer.to(dtype=torch.bfloat16)
    vae.to(dtype=torch.float16)
    image_encoder.to(dtype=torch.float16)
    text_encoder.to(dtype=torch.float16)
    text_encoder_2.to(dtype=torch.float16)

    # Disable gradient calculation for all models
    vae.requires_grad_(False)
    text_encoder.requires_grad_(False)
    text_encoder_2.requires_grad_(False)
    image_encoder.requires_grad_(False)
    transformer.requires_grad_(False)

    # Install DynamicSwap for models in low VRAM mode
    if not high_vram:
        DynamicSwapInstaller.install_model(transformer, device=gpu)
        DynamicSwapInstaller.install_model(text_encoder, device=gpu)
    else:
        text_encoder.to(gpu)
        text_encoder_2.to(gpu)
        image_encoder.to(gpu)
        vae.to(gpu)
        transformer.to(gpu)

    # Process each image
    processed_count = 0
    failed_count = 0
    skipped_count = 0

    # Load prompts from prompt list file if enabled
    prompt_list = []
    if args.use_prompt_list_file and os.path.exists(args.prompt_list_file):
        try:
            with open(args.prompt_list_file, 'r', encoding='utf-8') as f:
                prompt_list = [line.strip() for line in f.readlines() if line.strip()]
            print(f"Loaded {len(prompt_list)} prompts from {args.prompt_list_file}")
        except Exception as e:
            print(f"Error loading prompt list: {e}")
            prompt_list = []

    # Process each image
    for img_idx, image_path in enumerate(image_files):
        print(f"\nProcessing image {img_idx + 1}/{len(image_files)}: {image_path}")

        # Check for stop queue flag before processing each image (terminates entire batch)
        stop_queue_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_queue.flag")
        if os.path.exists(stop_queue_flag_path):
            print(f"\n⚠️ Stop queue flag detected. Terminating entire batch process...")
            # Remove the stop queue flag file
            try:
                os.remove(stop_queue_flag_path)
                print(f"Removed stop queue flag file: {stop_queue_flag_path}")
            except Exception as e:
                print(f"Error removing stop queue flag file: {e}")
            break

        # Check for skip generation flag before processing each image (skips current item)
        skip_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "skip_generation.flag")
        if os.path.exists(skip_flag_path):
            print(f"\n⚠️ Skip generation flag detected. Skipping current item and continuing batch...")
            # Remove the skip flag file
            try:
                os.remove(skip_flag_path)
                print(f"Removed skip flag file: {skip_flag_path}")
            except Exception as e:
                print(f"Error removing skip flag file: {e}")
            skipped_count += 1
            continue  # Skip to next item in the batch

        # Check if we should apply all prompts from the prompt list to this image
        if args.use_prompt_list_file and prompt_list and args.apply_all_prompts:
            print(f"Applying {len(prompt_list)} prompts to this image")
            for prompt_idx, prompt in enumerate(prompt_list):
                print(f"\nApplying prompt {prompt_idx + 1}/{len(prompt_list)}: {prompt}")

                # Process the image with the specific prompt
                try:
                    process_single_image(
                        image_path=image_path,
                        output_dir=args.output_dir,
                        prompt=prompt,
                        n_prompt="",
                        seed=args.seed,
                        video_length=args.video_length,
                        steps=args.steps,
                        gs=args.distilled_cfg,
                        flow_shift=args.flow_shift,
                        gpu_memory=args.gpu_memory,
                        use_teacache=args.use_teacache,
                        high_vram=high_vram,
                        latent_window_size=args.latent_window_size,
                        text_encoder=text_encoder,
                        text_encoder_2=text_encoder_2,
                        tokenizer=tokenizer,
                        tokenizer_2=tokenizer_2,
                        vae=vae,
                        feature_extractor=feature_extractor,
                        image_encoder=image_encoder,
                        transformer=transformer,
                        fix_encoding=args.fix_encoding,
                        copy_to_input=args.copy_to_input,
                        mp4_crf=args.mp4_crf,
                        show_latent_preview=args.show_latent_preview,
                        temp_dir=args.temp_dir,
                        job_id=args.job_id,
                        chain_index=args.chain_index,
                        chain_total=args.chain_total,
                        prompt_chain_mode=args.prompt_chain_mode,
                        use_noise=args.use_noise,
                        pixel_trick=args.pixel_trick,
                        lora_file=args.lora_file,
                        lora_multiplier=args.lora_multiplier,
                        fp8_optimization=args.fp8_optimization
                    )
                    processed_count += 1
                except Exception as e:
                    print(f"Error processing image with prompt: {e}")
                    failed_count += 1

            # Add to processed files list (only for directory mode, not individual files or lists)
            if not args.allow_duplicates and not args.files and not args.file_list and not args.url_list and not args.combined_list and not args.unified_list and processed_files_path:
                file_path_str = str(image_path)
                # Only add to newly_processed_files if it's not already in processed_files
                if file_path_str not in processed_files:
                    newly_processed_files.add(file_path_str)
                processed_files.add(file_path_str)

                # Use the direct file update function to ensure the processed_files.txt is updated correctly
                if update_processed_files_txt(file_path_str, args.input_dir, debug=args.debug):
                    print(f"✅ Successfully updated processed_files.txt with: {file_path_str}")
                    print(f"   (in directory: {args.input_dir})")
                else:
                    print(f"❌ Failed to update processed_files.txt with: {file_path_str}")

                # Also add to the in-memory sets for backward compatibility
                if file_path_str not in processed_files:
                    newly_processed_files.add(file_path_str)
                processed_files.add(file_path_str)

            continue

        # Determine the prompt to use for this image
        actual_prompt = args.prompt

        # Check for per-image text file first
        txt_file = image_path.with_suffix('.txt')
        if txt_file.exists():
            try:
                with open(txt_file, 'r', encoding='utf-8') as f:
                    file_prompt = f.read().strip()
                if file_prompt:
                    actual_prompt = file_prompt
                    print(f"Using prompt from {txt_file.name}")
            except Exception as e:
                print(f"Error reading prompt from {txt_file}: {e}")

        # If no text file but prompt list is enabled, use the next prompt from the list
        elif args.use_prompt_list_file and prompt_list:
            # Use a rotating index based on the image index
            prompt_index = img_idx % len(prompt_list)
            actual_prompt = prompt_list[prompt_index]
            print(f"Using prompt {prompt_index + 1}/{len(prompt_list)} from prompt list")

        # If no text file or prompt list, check image metadata if enabled
        elif args.use_image_prompt:
            image_prompt = get_image_prompt(image_path)
            if image_prompt:
                actual_prompt = image_prompt
                print("Using prompt from image metadata")

        # If we still don't have a prompt, use the fallback
        if not actual_prompt:
            actual_prompt = fallback_prompt
            if fallback_prompt:
                print(f"Using fallback prompt: {fallback_prompt}")
            else:
                print("Warning: No prompt found. Using empty prompt.")

        # Process the image
        try:
            result = process_single_image(
                image_path=image_path,
                output_dir=args.output_dir,
                prompt=actual_prompt,
                n_prompt="",
                seed=args.seed,
                video_length=args.video_length,
                steps=args.steps,
                gs=args.distilled_cfg,
                flow_shift=args.flow_shift,
                gpu_memory=args.gpu_memory,
                use_teacache=args.use_teacache,
                high_vram=high_vram,
                latent_window_size=args.latent_window_size,
                text_encoder=text_encoder,
                text_encoder_2=text_encoder_2,
                tokenizer=tokenizer,
                tokenizer_2=tokenizer_2,
                vae=vae,
                feature_extractor=feature_extractor,
                image_encoder=image_encoder,
                transformer=transformer,
                fix_encoding=args.fix_encoding,
                copy_to_input=args.copy_to_input,
                mp4_crf=args.mp4_crf,
                show_latent_preview=args.show_latent_preview,
                temp_dir=args.temp_dir,
                job_id=args.job_id,
                chain_index=args.chain_index,
                chain_total=args.chain_total,
                prompt_chain_mode=args.prompt_chain_mode,
                use_noise=args.use_noise,
                pixel_trick=args.pixel_trick,
                lora_file=args.lora_file,
                lora_multiplier=args.lora_multiplier,
                fp8_optimization=args.fp8_optimization
            )

            # Check if the generation was skipped
            if result == "SKIPPED":
                print(f"⚠️ Skipped generation for {image_path} - continuing with next item")
                skipped_count += 1

                # Create completion signal for GUI to detect the skip
                try:
                    with open("framepack_completed.signal", "w") as f:
                        f.write("SKIPPED\n")
                    print("Created SKIPPED completion signal for GUI")
                except Exception as e:
                    print(f"Warning: Could not create skip completion signal: {e}")

                continue  # Skip to next item in the batch

            # Only count as processed and update tracking if generation was successful
            if result is not None:
                processed_count += 1

                # Add to processed files list (only for directory mode, not individual files or lists)
                if not args.allow_duplicates and not args.files and not args.file_list and not args.url_list and not args.combined_list and not args.unified_list and processed_files_path:
                    file_path_str = str(image_path)
                    # Only add to newly_processed_files if it's not already in processed_files
                    if file_path_str not in processed_files:
                        newly_processed_files.add(file_path_str)
                    processed_files.add(file_path_str)

                    # Use the direct file update function to ensure the processed_files.txt is updated correctly
                    if update_processed_files_txt(file_path_str, args.input_dir, debug=args.debug):
                        print(f"✅ Successfully updated processed_files.txt with: {file_path_str}")
                        print(f"   (in directory: {args.input_dir})")
                    else:
                        print(f"❌ Failed to update processed_files.txt with: {file_path_str}")

                    # Also add to the in-memory sets for backward compatibility
                    if file_path_str not in processed_files:
                        newly_processed_files.add(file_path_str)
                    processed_files.add(file_path_str)
        except Exception as e:
            print(f"Error processing image: {e}")
            traceback.print_exc()
            failed_count += 1

    # Handle clearing the processed files list if requested
    if not args.allow_duplicates and args.clear_processed_list and processed_count > 0:
        # Only clear the list if we successfully processed at least one file
        processed_files = set()
        newly_processed_files = set()
        print(f"Cleared processed files tracking as requested")

        # For individual files mode, we need to clear each file's directory processed_files.txt
        if args.files:
            # Get unique directories from processed files
            processed_dirs = set()
            for file_path in image_files:
                file_dir = os.path.dirname(os.path.abspath(str(file_path)))
                processed_dirs.add(file_dir)

            # Clear each directory's processed_files.txt
            for dir_path in processed_dirs:
                processed_files_path = os.path.join(dir_path, "processed_files.txt")
                try:
                    with open(processed_files_path, 'w') as f:
                        # Write nothing to create an empty file
                        pass
                    print(f"Updated processed files tracking in {dir_path} - list cleared")
                except Exception as e:
                    print(f"Error clearing processed files tracking in {dir_path}: {e}")
        else:
            # Save the empty list to disk
            try:
                # Make sure processed_files_path is defined
                if 'processed_files_path' in locals() or 'processed_files_path' in globals():
                    with open(processed_files_path, 'w') as f:
                        # Write nothing to create an empty file
                        pass
                    print(f"Updated processed files tracking - list cleared")
                else:
                    print(f"Warning: processed_files_path not defined, cannot save processed files list")
            except Exception as e:
                print(f"Error saving processed files tracking: {e}")

    # Print summary
    print("\nProcessing complete!")
    print(f"Processed: {processed_count}")
    print(f"Failed: {failed_count}")
    print(f"Skipped: {skipped_count}")
    print(f"Total: {len(image_files)}")

    # Clean up progress file for GUI ETA display
    try:
        progress_file = "framepack_progress.txt"
        if os.path.exists(progress_file):
            os.remove(progress_file)
    except Exception:
        pass  # Don't let cleanup errors affect the main process

if __name__ == "__main__":
    main()