@echo off
:: FramePack Filename Cleaner - Quick Mode
:: This batch file runs the framepack_filename_cleaner.py script with common options
:: Usage: clean_framepack_files.bat [folder] [filter] [--dry-run] [--auto-remove]
:: Example: clean_framepack_files.bat outputs cancelled --dry-run

setlocal enabledelayedexpansion

:: Default values
set "folder=outputs"
set "filter=all"
set "dry_run="
set "auto_remove="

:: Parse command line arguments
if not "%~1"=="" set "folder=%~1"
if not "%~2"=="" set "filter=%~2"

:: Check for flags in any position
for %%i in (%*) do (
    if "%%i"=="--dry-run" set "dry_run=--dry-run"
    if "%%i"=="--auto-remove" set "auto_remove=--auto-remove"
)

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Python is not installed or not in the PATH.
    echo Please install Python and make sure it's in your PATH.
    pause
    exit /b 1
)

:: Check if the cleaner script exists
if not exist framepack_filename_cleaner.py (
    echo ERROR: framepack_filename_cleaner.py not found in the current directory.
    echo Please make sure you're running this batch file from the same directory as the script.
    pause
    exit /b 1
)

:: Display settings
echo ===================================================
echo           FRAMEPACK FILENAME CLEANER
echo ===================================================
echo.
echo Running with the following settings:
echo   Folder: %folder%
echo   Filter: %filter%
if defined dry_run echo   Dry run: Enabled (files will NOT be deleted)
if not defined dry_run echo   Dry run: Disabled (files may be deleted)
if defined auto_remove echo   Auto remove: Enabled (no confirmation prompts)
if not defined auto_remove echo   Auto remove: Disabled (will prompt for confirmation)
echo.

:: Build the command
set "cmd=python framepack_filename_cleaner.py --folder "%folder%" --filter %filter%"
if defined dry_run set "cmd=!cmd! %dry_run%"
if defined auto_remove set "cmd=!cmd! %auto_remove%"

echo Running command: !cmd!
echo.
echo ===================================================
echo                  CLEANER OUTPUT
echo ===================================================
echo.

:: Run the command
!cmd!

echo.
echo ===================================================
echo                  CLEANER FINISHED
echo ===================================================
echo.
pause
endlocal
exit /b 0
