import re
import time
from tqdm.auto import tqdm, trange

# Global variable to store the latest progress information
latest_progress = {
    'percentage': 0,
    'current': 0,
    'total': 0,
    'elapsed': 0,
    'remaining': 0,
    'rate': 0,
    'bar': '',
    'description': '',
    'postfix': '',
    'last_update_time': 0,
    'callback': None
}

class TqdmToCallback(tqdm):
    """
    Redirects tqdm output to a callback function.
    """
    def __init__(self, *args, **kwargs):
        self.callback = kwargs.pop('callback', None)
        super().__init__(*args, **kwargs)

    def display(self, msg=None, pos=None):
        if self.callback:
            if msg is None:
                msg = self.__str__()
            self.callback(msg)
        else:
            super().display(msg, pos)

def trange_with_callback(*args, **kwargs):
    """
    A wrapper around trange that captures the output and sends it to a callback.
    """
    callback = kwargs.pop('callback', None)

    # Make sure we're not disabling the progress bar
    kwargs['disable'] = False

    if callback:
        kwargs['callback'] = callback
        return TqdmToCallback(range(*args), **kwargs)
    else:
        return trange(*args, **kwargs)

def parse_tqdm_output(output):
    """
    Parse the tqdm output string to extract progress information.
    """
    # Extract percentage
    percentage_match = re.search(r'(\d+)%', output)
    percentage = int(percentage_match.group(1)) if percentage_match else 0

    # Extract current/total
    progress_match = re.search(r'(\d+)/(\d+)', output)
    current = int(progress_match.group(1)) if progress_match else 0
    total = int(progress_match.group(2)) if progress_match else 0

    # Extract time information
    time_match = re.search(r'\[(.+)<(.+), (.+)it/s\]', output)
    elapsed = time_match.group(1).strip() if time_match else ''
    remaining = time_match.group(2).strip() if time_match else ''

    # Handle the case where rate is "?" (tqdm can't estimate the rate yet)
    if time_match and time_match.group(3):
        try:
            rate = float(time_match.group(3))
        except ValueError:
            # If conversion fails (e.g., "?"), set rate to 0
            rate = 0
    else:
        rate = 0

    # Extract progress bar
    bar_match = re.search(r'(\|[█▉▊▋▌▍▎▏ ]+\|)', output)
    bar = bar_match.group(1) if bar_match else ''

    # Create a more readable description
    if current > 0 and total > 0:
        eta_str = f" ETA: {remaining}" if remaining else ""
        description = f"Step {current}/{total} ({percentage}%){eta_str}"
    else:
        description = output

    return {
        'percentage': percentage,
        'current': current,
        'total': total,
        'elapsed': elapsed,
        'remaining': remaining,
        'rate': rate,
        'bar': bar,
        'description': description,
        'last_update_time': time.time()
    }

def update_progress(msg):
    """
    Update the global progress information and call the callback if set.
    """
    global latest_progress

    try:
        # Skip empty messages or non-string messages
        if not msg or not isinstance(msg, str):
            return

        # Skip messages that don't look like tqdm output
        if '%' not in msg and '/' not in msg:
            return

        parsed = parse_tqdm_output(msg)
        latest_progress.update(parsed)

        if latest_progress['callback']:
            try:
                # Only call the callback if we have valid progress information
                # This prevents the "Skipping progress update" messages
                if parsed['current'] > 0 or parsed['total'] > 0:
                    latest_progress['callback'](latest_progress)
            except Exception as e:
                print(f"Error in progress callback: {e}")
    except Exception as e:
        print(f"Error parsing tqdm output: {e}")
        # Don't crash on parsing errors, just continue

def set_progress_callback(callback):
    """
    Set a callback function to be called when progress is updated.
    """
    global latest_progress
    latest_progress['callback'] = callback

def get_latest_progress():
    """
    Get the latest progress information.
    """
    return latest_progress

# Monkey patch trange to use our version
tqdm.trange = trange_with_callback
