#!/usr/bin/env python
"""
This script fixes the batch_f1_video.py file to properly handle video files in the unified list.
"""

import re

def main():
    # Read the original file
    with open('batch_f1_video.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix the error message in the is_supported_file function
    content = content.replace(
        'message = f"Note: {file_path} is an image file. This script is for video processing only. To process images, use batch_f1.py or batch_f1_lock.py instead."',
        'message = f"Warning: Skipping {file_path} - not a supported video file. This script is for video processing only. To process images, use batch_f1.py or batch_f1_lock.py instead."'
    )
    
    # Fix the unified list processing
    pattern = re.compile(r'(\s+# It\'s a file path\n\s+path = Path\(os\.path\.abspath\(item\)\)\n\s+if path\.exists\(\) and path\.is_file\(\):\n\s+if path\.suffix\.lower\(\) in \[\'\.\w+\'\, \'\.\w+\'\, \'\.\w+\'\, \'\.\w+\'\, \'\.\w+\'\]:\n\s+video_files\.append\(path\)\n\s+else:\n\s+print\(f"Warning: Skipping \{path\} - not a supported video file"\)\n\s+else:\n\s+print\(f"Warning: File not found or not a file: \{path\}"\))')
    
    replacement = """                                # It's a file path
                                path = Path(os.path.abspath(item))
                                print(f"Processing file path from unified list: {path}")
                                if path.exists() and path.is_file():
                                    # Use the helper function to check if it's a supported file
                                    is_supported, message = is_supported_file(path)
                                    if is_supported:
                                        video_files.append(path)
                                    else:
                                        if message:
                                            print(message)
                                else:
                                    print(f"Warning: File not found or not a file: {path}")"""
    
    # Replace all occurrences
    content = pattern.sub(replacement, content)
    
    # Write the fixed file
    with open('batch_f1_video.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Fixed batch_f1_video.py successfully!")

if __name__ == "__main__":
    main()
