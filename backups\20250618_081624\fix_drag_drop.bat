@echo off
echo FramePack Drag and Drop Fix
echo =========================
echo.

echo This script will apply fixes to improve drag and drop functionality in FramePack.
echo.

echo Step 1: Checking Python installation...
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    goto :end
)
echo Python is installed.

echo Step 2: Checking tkinterdnd2 installation...
python -c "import tkinterdnd2" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo tkinterdnd2 is not installed. Installing it now...
    python -m pip install tkinterdnd2
    if %ERRORLEVEL% neq 0 (
        echo Error: Failed to install tkinterdnd2. Please install it manually:
        echo pip install tkinterdnd2
        goto :end
    )
    echo Successfully installed tkinterdnd2.
) else (
    echo tkinterdnd2 is already installed. Upgrading to the latest version...
    python -m pip install --upgrade tkinterdnd2
    echo Upgrade complete.
)

echo Step 3: Creating backup of framepack_gui.py...
if exist framepack_gui.py.bak (
    echo A backup already exists. Skipping backup creation.
) else (
    copy framepack_gui.py framepack_gui.py.bak
    echo Backup created as framepack_gui.py.bak
)

echo Step 4: Applying fixes to framepack_gui.py...
python framepack_dnd_fix.py
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to apply fixes. Please check the console for details.
    goto :end
)
echo Fixes applied successfully.

echo.
echo All fixes have been applied. You can now run FramePack with improved drag and drop functionality.
echo To test the drag and drop functionality, run run_framepack_gui_debug.bat
echo.
echo If you still experience issues, please refer to DRAG_DROP_FIX.md for more information.

:end
pause
