@echo off
setlocal enabledelayedexpansion

echo Converting HuggingFace model files to proper symlinks...
echo.

set "MODEL_DIR=%~1"
if "%MODEL_DIR%"=="" (
    echo Usage: fix_symlinks.bat "path\to\model\directory"
    echo.
    echo Example: fix_symlinks.bat "merged_models\models--lllyasviel--FramePack_F1_I2V_HY_20250503_pov-missionary-hunyuan-v1.0-vfx_ai_x0.8"
    pause
    exit /b 1
)

if not exist "%MODEL_DIR%" (
    echo Error: Model directory does not exist: %MODEL_DIR%
    pause
    exit /b 1
)

if not exist "%MODEL_DIR%\blobs" (
    echo Error: blobs directory not found in: %MODEL_DIR%
    pause
    exit /b 1
)

if not exist "%MODEL_DIR%\snapshots" (
    echo Error: snapshots directory not found in: %MODEL_DIR%
    pause
    exit /b 1
)

echo Processing model directory: %MODEL_DIR%
echo.

:: Change to the snapshots directory to make paths simpler
cd /d "%MODEL_DIR%\snapshots"

:: Process each safetensors file
for /r . %%f in (*.safetensors) do (
    set "snapshot_file=%%f"
    set "filename=%%~nxf"
    set "found_match=0"

    echo Processing: !filename!

    :: Get size of snapshot file
    for %%s in ("!snapshot_file!") do set "snapshot_size=%%~zs"

    :: Look for matching blob by size
    for %%b in ("%MODEL_DIR%\blobs\*") do (
        for %%s in ("%%b") do set "blob_size=%%~zs"
        set "blob_name=%%~nxb"

        if "!snapshot_size!"=="!blob_size!" (
            echo   Found matching blob: !blob_name!

            :: Calculate relative path to blob
            set "rel_path=..\..\blobs\!blob_name!"

            :: Delete the copied file
            del "!snapshot_file!" 2>nul

            :: Create symlink
            mklink "!snapshot_file!" "!rel_path!"

            if !errorlevel! equ 0 (
                echo   ✓ Created symlink successfully
            ) else (
                echo   ✗ Error creating symlink
            )
            set "found_match=1"
            goto :next_file
        )
    )

    if "!found_match!"=="0" (
        echo   Warning: No matching blob found
    )

    :next_file
    echo.
)

echo Symlink conversion complete!
echo.

:: Verify results
echo Verifying symlinks...
for /r . %%f in (*.safetensors) do (
    set "filename=%%~nxf"
    dir "%%f" | find "<SYMLINK>" >nul
    if !errorlevel! equ 0 (
        echo ✓ !filename! is now a symlink
    ) else (
        echo ✗ !filename! is still a regular file
    )
)

echo.
echo Done! You can now use this model with proper symlinks.
pause
