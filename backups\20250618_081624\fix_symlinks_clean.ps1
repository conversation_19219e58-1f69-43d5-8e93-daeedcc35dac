param(
    [string]$ModelDir
)

Write-Host "Converting HuggingFace model files to proper symlinks..." -ForegroundColor Green
Write-Host ""

# Prompt for model directory if not provided
if (-not $ModelDir) {
    Write-Host "Please enter the path to the model directory:" -ForegroundColor Yellow
    Write-Host "Example: merged_models\models--lllyasviel--FramePack_F1_I2V_HY_20250503_pov-missionary-hunyuan-v1.0-vfx_ai_x0.8" -ForegroundColor Gray
    $ModelDir = Read-Host "Model Directory"
    
    if (-not $ModelDir) {
        Write-Host "Error: No model directory specified" -ForegroundColor Red
        exit 1
    }
}

# Validate paths
if (-not (Test-Path $ModelDir)) {
    Write-Host "Error: Model directory does not exist: $ModelDir" -ForegroundColor Red
    exit 1
}

$blobsDir = Join-Path $ModelDir "blobs"
$snapshotsDir = Join-Path $ModelDir "snapshots"

if (-not (Test-Path $blobsDir)) {
    Write-Host "Error: blobs directory not found in: $ModelDir" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $snapshotsDir)) {
    Write-Host "Error: snapshots directory not found in: $ModelDir" -ForegroundColor Red
    exit 1
}

Write-Host "Processing model directory: $ModelDir"
Write-Host ""

# Get all safetensors files in snapshots directory
$safetensorsFiles = Get-ChildItem -Path $snapshotsDir -Recurse -Filter "*.safetensors"

foreach ($snapshotFile in $safetensorsFiles) {
    Write-Host "Processing: $($snapshotFile.Name)" -ForegroundColor Yellow
    
    # Get size of snapshot file
    $snapshotSize = $snapshotFile.Length
    
    # Find matching blob by size
    $matchingBlob = Get-ChildItem -Path $blobsDir | Where-Object { $_.Length -eq $snapshotSize }
    
    if ($matchingBlob) {
        Write-Host "  Found matching blob: $($matchingBlob.Name)" -ForegroundColor Cyan
        
        # Calculate relative path to blob
        $relativePath = "..\..\blobs\$($matchingBlob.Name)"
        
        # Remove the copied file
        Remove-Item $snapshotFile.FullName -Force
        
        # Create symlink
        try {
            New-Item -ItemType SymbolicLink -Path $snapshotFile.FullName -Target $relativePath -Force | Out-Null
            Write-Host "  ✓ Created symlink successfully" -ForegroundColor Green
        }
        catch {
            Write-Host "  ✗ Error creating symlink: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    else {
        Write-Host "  Warning: No matching blob found (size: $snapshotSize bytes)" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

Write-Host "Symlink conversion complete!" -ForegroundColor Green
Write-Host ""

# Verify results
Write-Host "Verifying symlinks..." -ForegroundColor Blue
$safetensorsFiles = Get-ChildItem -Path $snapshotsDir -Recurse -Filter "*.safetensors"

foreach ($file in $safetensorsFiles) {
    $item = Get-Item $file.FullName
    if ($item.Attributes -band [System.IO.FileAttributes]::ReparsePoint) {
        Write-Host "✓ $($file.Name) is now a symlink" -ForegroundColor Green
    }
    else {
        Write-Host "✗ $($file.Name) is still a regular file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Done! You can now use this model with proper symlinks." -ForegroundColor Green
