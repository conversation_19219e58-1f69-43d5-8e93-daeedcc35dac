@echo off
setlocal enabledelayedexpansion

echo FramePack Seconds Label Adder
echo =============================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    goto :end
)

:: Check if ffmpeg/ffprobe is installed
ffprobe -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: ffprobe is not installed or not in PATH. Please install ffmpeg.
    goto :end
)

:: Check if a directory was provided as an argument
set "target_dir=%~1"
if "%target_dir%"=="" (
    :: No directory provided, use current directory
    echo Running script to add seconds labels to video files in current directory...
    echo.
    python framepack_add_seconds_label.py
) else (
    :: Check if the argument is a directory or a file
    if exist "%target_dir%\" (
        :: It's a directory, pass it to the script
        echo Running script to add seconds labels to video files in "%target_dir%"...
        echo.
        python framepack_add_seconds_label.py "%target_dir%"
    ) else if exist "%target_dir%" (
        :: It's a file, use its parent directory
        echo The argument is a file. Using its parent directory instead.
        for %%F in ("%target_dir%") do set "parent_dir=%%~dpF"
        echo Running script to add seconds labels to video files in "!parent_dir!"...
        echo.
        python framepack_add_seconds_label.py "!parent_dir!"
    ) else (
        :: Neither a file nor a directory
        echo Error: "%target_dir%" is not a valid file or directory.
        goto :end
    )
)

echo.
echo Process complete.

:end
pause
