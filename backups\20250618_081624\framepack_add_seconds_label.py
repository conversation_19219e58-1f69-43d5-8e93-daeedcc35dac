#!/usr/bin/env python
"""
framepack_add_seconds_label.py

This script scans the current directory for MP4 files that match our naming schemes
but don't have the seconds label suffix. It then probes each file for its duration
and renames it to include the duration in seconds with the _XXs.mp4 format.

Naming schemes supported:
1. DATE_TIME_XXX_YYYY(_ZZ).mp4
2. DATE_TIME_XXX_YYYY(_ZZ_seed#######).mp4
3. image_name.mp4
4. image_name_seed#######.mp4

Files that already have the seconds label (e.g., _5s.mp4) will be skipped.
"""

import os
import re
import sys
import subprocess
import json
from pathlib import Path


def has_seconds_label(filename):
    """Check if the filename already has a seconds label (_XXs.mp4)."""
    return bool(re.search(r'_\d+s\.mp4$', filename.lower()))


def matches_naming_scheme(filename):
    """
    Check if the filename matches any of our supported naming schemes.
    Returns True if it matches, False otherwise.
    """
    # Skip files that already have seconds label
    if has_seconds_label(filename):
        return False

    # Only process MP4 files
    if not filename.lower().endswith('.mp4'):
        return False

    # Check for timestamp pattern (DATE_TIME_XXX_YYYY)
    timestamp_pattern = r'^\d+_\d+_\d+_\d+(?:_\d+)?(?:_seed\d+)?\.mp4$'
    if re.match(timestamp_pattern, filename):
        return True

    # Check for generic filename pattern (image_name)
    generic_pattern = r'^.+?(?:_seed\d+)?\.mp4$'
    if re.match(generic_pattern, filename):
        return True

    return False


def get_video_duration(file_path):
    """
    Get the duration of a video file in seconds using ffprobe.
    Returns the duration as an integer (truncated, not rounded).
    """
    try:
        # Use ffprobe to get the duration
        cmd = [
            'ffprobe',
            '-v', 'error',
            '-show_entries', 'format=duration',
            '-of', 'json',
            file_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        data = json.loads(result.stdout)

        # Get the duration in seconds and truncate to an integer
        duration_seconds = int(float(data['format']['duration']))

        # Also get the exact duration for display purposes
        exact_duration = float(data['format']['duration'])

        return duration_seconds, exact_duration

    except (subprocess.CalledProcessError, KeyError, json.JSONDecodeError, ValueError) as e:
        print(f"Error getting duration for {file_path}: {str(e)}")
        return None, None
    except FileNotFoundError:
        print("Error: ffprobe is not installed or not found in PATH. Please install ffmpeg.")
        return None, None


def rename_with_seconds_label(file_path, duration_seconds):
    """
    Rename a file to include the seconds label.
    For example: video.mp4 -> video_5s.mp4
    """
    path = Path(file_path)
    new_name = f"{path.stem}_{duration_seconds}s{path.suffix}"
    new_path = path.with_name(new_name)

    # Check if the destination file already exists
    if new_path.exists():
        print(f"Skipping {path.name}: Destination file {new_name} already exists")
        return False

    try:
        path.rename(new_path)
        return True
    except Exception as e:
        print(f"Error renaming {path.name}: {str(e)}")
        return False


def process_directory(directory="."):
    """
    Process all MP4 files in the specified directory.
    """
    print(f"Scanning directory: {os.path.abspath(directory)}")

    # Get all files in the directory
    files = [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]

    # Filter files that match our naming schemes but don't have seconds label
    files_to_process = [f for f in files if matches_naming_scheme(f)]

    if not files_to_process:
        print("No files found that need seconds labels.")
        return 0

    print(f"Found {len(files_to_process)} files to process:")
    for i, filename in enumerate(files_to_process):
        print(f"  {i+1}. {filename}")

    # Process each file
    processed_count = 0
    skipped_count = 0

    print("\nProcessing files...")
    for filename in files_to_process:
        file_path = os.path.join(directory, filename)

        # Get the video duration
        duration_seconds, exact_duration = get_video_duration(file_path)
        if duration_seconds is None:
            print(f"Skipping {filename}: Could not determine duration")
            skipped_count += 1
            continue

        # Display both truncated and exact duration
        print(f"File: {filename}")
        print(f"  Exact duration: {exact_duration:.2f} seconds")
        print(f"  Truncated duration for label: {duration_seconds} seconds")

        # Rename the file with the seconds label
        if rename_with_seconds_label(file_path, duration_seconds):
            processed_count += 1
            print(f"  Renamed to: {Path(file_path).stem}_{duration_seconds}s.mp4")
        else:
            skipped_count += 1

    # Print summary
    print(f"\nProcessed {processed_count} files, skipped {skipped_count} files.")
    return processed_count


def main():
    """
    Main function to process files in the current directory.
    """
    print("FramePack Seconds Label Adder")
    print("=============================")

    try:
        # Check if ffprobe is available
        subprocess.run(['ffprobe', '-version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Error: ffprobe is not installed or not found in PATH. Please install ffmpeg.")
        return 1

    try:
        # Get directory from command line argument if provided
        directory = "."
        if len(sys.argv) > 1 and not sys.argv[1].startswith('--'):
            directory = sys.argv[1]
            if not os.path.isdir(directory):
                print(f"Error: '{directory}' is not a valid directory.")
                return 1

        # Process the specified directory
        processed_count = process_directory(directory)

        if processed_count > 0:
            print("\nOperation completed successfully!")
        else:
            print("\nNo files were processed.")

        return 0
    except Exception as e:
        print(f"\nError: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
