@echo off
setlocal enabledelayedexpansion

title FramePack Headless Processing
color 0A

:: Check for settings file parameter
set SETTINGS_FILE=framepack_default_settings.json
if not "%~1"=="" (
    set SETTINGS_FILE=%~1
)

echo ===============================================
echo         FRAMEPACK HEADLESS PROCESSING
echo ===============================================
echo.
echo This script will run FramePack using the saved settings
echo without launching the GUI.
echo.
echo Using settings file: %SETTINGS_FILE%
echo.

:: Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if %ERRORLEVEL% NEQ 0 (
    color 0C
    echo ERROR: Failed to activate virtual environment.
    echo Please make sure the venv directory exists and is properly set up.
    echo.
    pause
    exit /b 1
)
echo Virtual environment activated successfully.
echo.

:: Read settings from JSON file using Python
echo Reading settings from %SETTINGS_FILE%...
echo.

:: Use Python to extract settings from JSON
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s['input_dir'])"') do set input_dir=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s['output_dir'])"') do set output_dir=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s['fallback_prompt'])"') do set fallback_prompt=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s['seed'])"') do set seed=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s['video_length'])"') do set video_length=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s['steps'])"') do set steps=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s['distilled_cfg'])"') do set distilled_cfg=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s['gpu_memory'])"') do set gpu_memory=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s['mp4_crf'])"') do set mp4_crf=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(str(s['use_teacache']).lower())"') do set use_teacache=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(str(s['randomize_order']).lower())"') do set randomize_order=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(str(s['clear_processed_list']).lower())"') do set clear_processed_list=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(str(s['use_image_prompt']).lower())"') do set use_image_prompt=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(str(s['overwrite']).lower())"') do set overwrite=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(str(s['fix_encoding']).lower())"') do set fix_encoding=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(str(s['use_prompt_list_file']).lower())"') do set use_prompt_list_file=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s['prompt_list_file'])"') do set prompt_list_file=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(str(s['copy_to_input']).lower())"') do set copy_to_input=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(str(s.get('allow_duplicates', False)).lower())"') do set allow_duplicates=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(str(s.get('apply_all_prompts', False)).lower())"') do set apply_all_prompts=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(s.get('input_mode', 'directory'))"') do set input_mode=%%a
for /f "tokens=*" %%a in ('python -c "import json; f=open('%SETTINGS_FILE%'); s=json.load(f); print(len(s.get('selected_files', [])))"') do set selected_files_count=%%a

:: Display settings
echo Mode:                !input_mode!
if !input_mode! EQU directory (
    echo Input Directory:    !input_dir!
) else (
    echo Selected Items:     !selected_files_count!
)
echo Output Directory:   !output_dir!
echo Fallback Prompt:    !fallback_prompt!
echo Seed:               !seed!
if !seed! EQU -1 echo (Random)
echo Video Length:       !video_length! seconds
echo Steps:              !steps!
echo Distilled CFG:      !distilled_cfg!
echo GPU Memory:         !gpu_memory! GB
echo MP4 Compression:    !mp4_crf! (0-51, lower is better)
echo Use TeaCache:       !use_teacache!
echo Randomize Order:    !randomize_order!
echo Clear Processed:    !clear_processed_list!
echo Use Image Prompt:   !use_image_prompt!
echo Use Prompt List:    !use_prompt_list_file!
if !use_prompt_list_file! EQU true echo Prompt List File:   !prompt_list_file!
echo Fix Encoding:       !fix_encoding!
echo Copy to Input:      !copy_to_input!
echo Overwrite Existing: !overwrite!
echo Allow Duplicates:   !allow_duplicates!
echo Apply All Prompts:  !apply_all_prompts!
echo.
echo ===============================================
echo.

:: Run the Python helper script to handle command execution
echo Running FramePack through Python helper script...
echo.

python run_framepack_helper.py "%SETTINGS_FILE%"

:: Save the error level
set LAST_ERROR=%ERRORLEVEL%

:: Keep the window open after the command finishes
echo.
if %LAST_ERROR% NEQ 0 (
    color 0C
    echo ===============================================
    echo ERROR: Processing failed with error code %LAST_ERROR%
    echo ===============================================
    echo.
    echo Please check the error messages above for details.
    echo.
    pause
    exit /b %LAST_ERROR%
) else (
    echo ===============================================
    echo Processing completed successfully!
    echo ===============================================
    echo.
    pause
    exit /b 0
)

endlocal
