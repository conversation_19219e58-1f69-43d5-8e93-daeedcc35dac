@echo off
setlocal enabledelayedexpansion
title FramePack F1 Batch Auto Processing
color 0A

:: Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo ===============================================
echo       FRAMEPACK F1 BATCH AUTO PROCESSING
echo ===============================================
echo.

:: Set default settings file
set "settings_file=framepack_default_settings.json"

:: Check if a settings file was provided as an argument
if not "%~1"=="" (
    set "settings_file=%~1"
)

:: Check if the settings file exists
if not exist "%settings_file%" (
    echo Settings file not found: %settings_file%
    echo Using default settings.
    set "settings_file=framepack_default_settings.json"

    :: Check if default settings file exists
    if not exist "%settings_file%" (
        echo Default settings file not found. Please create one or specify a valid settings file.
        pause
        exit /b 1
    )
)

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in your PATH.
    echo Please install Python 3.10 or newer and try again.
    pause
    exit /b 1
)

:: Check if virtual environment exists
if not exist "venv" (
    echo Virtual environment not found. Creating one...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo Failed to create virtual environment.
        pause
        exit /b 1
    )
    echo Virtual environment created successfully.
)

:: Activate virtual environment - make sure we're using the full path
echo Activating virtual environment...
call "%SCRIPT_DIR%venv\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo Failed to activate virtual environment.
    echo Current directory: %CD%
    echo Virtual environment path: %SCRIPT_DIR%venv\Scripts\activate.bat
    pause
    exit /b 1
)
echo Virtual environment activated successfully.

:: Run the helper script with the settings file
echo.
echo Running FramePack F1 batch processing with settings from: %settings_file%
echo.
python "%SCRIPT_DIR%run_framepack_helper.py" %settings_file% --use_f1

:: Deactivate virtual environment
call "%SCRIPT_DIR%venv\Scripts\deactivate.bat"

echo.
echo ===============================================
echo          FRAMEPACK F1 BATCH COMPLETED
echo ===============================================
echo.
pause
