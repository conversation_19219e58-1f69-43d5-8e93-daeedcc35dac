@echo off
setlocal enabledelayedexpansion
title FramePack F1 Batch Processing - Files
color 0A

:: Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo ===============================================
echo     FRAMEPACK F1 BATCH PROCESSING - FILES
echo ===============================================
echo.

:: Check if arguments were provided
if "%~1"=="" (
    echo No files specified. Please drag and drop image files onto this batch file.
    pause
    exit /b 1
)

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in your PATH.
    echo Please install Python 3.10 or newer and try again.
    pause
    exit /b 1
)

:: Check if virtual environment exists
if not exist "venv" (
    echo Virtual environment not found. Creating one...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo Failed to create virtual environment.
        pause
        exit /b 1
    )
    echo Virtual environment created successfully.
)

:: Activate virtual environment - make sure we're using the full path
echo Activating virtual environment...
call "%SCRIPT_DIR%venv\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo Failed to activate virtual environment.
    echo Current directory: %CD%
    echo Virtual environment path: %SCRIPT_DIR%venv\Scripts\activate.bat
    pause
    exit /b 1
)
echo Virtual environment activated successfully.

:: Build the command with all files
set "files_arg=--files"
:parse_args
if "%~1"=="" goto run_command
set "files_arg=%files_arg% "%~1""
shift
goto parse_args

:run_command
:: Run the batch script with the files
echo.
echo Running FramePack F1 batch processing with specified files...
echo.
python "%SCRIPT_DIR%batch_f1.py" %files_arg%

:: Deactivate virtual environment
call "%SCRIPT_DIR%venv\Scripts\deactivate.bat"

echo.
echo ===============================================
echo          FRAMEPACK F1 BATCH COMPLETED
echo ===============================================
echo.
pause
