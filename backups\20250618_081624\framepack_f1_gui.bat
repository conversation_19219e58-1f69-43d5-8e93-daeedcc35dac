@echo off
setlocal enabledelayedexpansion
title FramePack F1 GUI
color 0A

:: Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo ===============================================
echo             FRAMEPACK F1 GUI LAUNCHER
echo ===============================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in your PATH.
    echo Please install Python 3.10 or newer and try again.
    pause
    exit /b 1
)

:: Check if virtual environment exists
if not exist "venv" (
    echo Virtual environment not found. Creating one...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo Failed to create virtual environment.
        pause
        exit /b 1
    )
    echo Virtual environment created successfully.
)

:: Activate virtual environment - make sure we're using the full path
echo Activating virtual environment...
call "%SCRIPT_DIR%venv\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo Failed to activate virtual environment.
    echo Current directory: %CD%
    echo Virtual environment path: %SCRIPT_DIR%venv\Scripts\activate.bat
    pause
    exit /b 1
)
echo Virtual environment activated successfully.

:: Run the GUI
echo.
echo Starting FramePack F1 GUI...
echo.
python "%SCRIPT_DIR%demo_gradio_f1.py" --server 127.0.0.1 --inbrowser

:: Deactivate virtual environment
call "%SCRIPT_DIR%venv\Scripts\deactivate.bat"

pause
