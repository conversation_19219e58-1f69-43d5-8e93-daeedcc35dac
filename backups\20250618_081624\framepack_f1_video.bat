@echo off
setlocal enabledelayedexpansion
title FramePack F1 Video-to-Video Processor

:: Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo ===============================================
echo      FRAMEPACK F1 VIDEO-TO-VIDEO PROCESSOR
echo ===============================================
echo.
echo This script processes a single video file using the FramePack F1 model
echo to extend it with AI-generated content.
echo.

:: Check if a file was provided
if "%~1"=="" (
    echo Error: No video file provided.
    echo Usage: %~nx0 video_file.mp4
    goto :end
)

:: Check if the file exists
if not exist "%~1" (
    echo Error: File "%~1" not found.
    goto :end
)

:: Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found. Please make sure Python is installed and in your PATH.
    goto :end
)

:: Activate virtual environment - make sure we're using the full path
echo Activating virtual environment...
call "%SCRIPT_DIR%venv\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo Error: Failed to activate virtual environment. Please make sure it exists.
    echo Current directory: %CD%
    echo Virtual environment path: %SCRIPT_DIR%venv\Scripts\activate.bat
    goto :end
)

echo Virtual environment activated successfully.

:: Get the full path to the input file
set "INPUT_FILE=%~f1"

:: Run the Python script with the file
echo.
echo Running FramePack F1 Video-to-Video processing on %INPUT_FILE%...
echo.
python "%SCRIPT_DIR%batch_f1_video.py" "%INPUT_FILE%"

:: Deactivate virtual environment
call "%SCRIPT_DIR%venv\Scripts\deactivate.bat"

echo.
echo ===============================================
echo    FRAMEPACK F1 VIDEO-TO-VIDEO COMPLETED
echo ===============================================
echo.
pause

:end
