@echo off
setlocal enabledelayedexpansion

:: FramePack Filename Cleaner Batch File
:: This batch file provides a user-friendly interface to run the framepack_filename_cleaner.py script
:: with various options for cleaning up files that don't match FramePack naming conventions.

title FramePack Filename Cleaner

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Python is not installed or not in the PATH.
    echo Please install Python and make sure it's in your PATH.
    pause
    exit /b 1
)

:: Check if the cleaner script exists
if not exist framepack_filename_cleaner.py (
    echo ERROR: framepack_filename_cleaner.py not found in the current directory.
    echo Please make sure you're running this batch file from the same directory as the script.
    pause
    exit /b 1
)

:: Default values
set "folder=outputs"
set "filter=all"
set "dry_run="
set "auto_remove="

:menu
cls
echo ===================================================
echo           FRAMEPACK FILENAME CLEANER
echo ===================================================
echo.
echo This tool identifies and removes files that don't match 
echo the FramePack naming conventions.
echo.
echo Current Settings:
echo   Folder to scan: %folder%
echo   Filter: %filter%
echo   Dry run: %dry_run%
echo   Auto remove: %auto_remove%
echo.
echo Options:
echo   1. Set folder to scan (current: %folder%)
echo   2. Set filter type (current: %filter%)
echo   3. Toggle dry run mode (current: %dry_run%)
echo   4. Toggle auto remove mode (current: %auto_remove%)
echo   5. Run cleaner with current settings
echo   6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto set_folder
if "%choice%"=="2" goto set_filter
if "%choice%"=="3" goto toggle_dry_run
if "%choice%"=="4" goto toggle_auto_remove
if "%choice%"=="5" goto run_cleaner
if "%choice%"=="6" goto end
goto menu

:set_folder
cls
echo ===================================================
echo                  SET FOLDER TO SCAN
echo ===================================================
echo.
echo Enter the path to the folder you want to scan.
echo Default is "outputs" in the current directory.
echo.
set /p folder="Folder path: "
if "!folder!"=="" set "folder=outputs"
goto menu

:set_filter
cls
echo ===================================================
echo                  SET FILTER TYPE
echo ===================================================
echo.
echo Select the type of files to filter:
echo   1. All non-conforming files
echo   2. Only files with '_cancelled' suffix
echo   3. Only PNG files without '_start' or '_end' suffix
echo   4. Only non-conforming MP4 files
echo.
set /p filter_choice="Enter your choice (1-4): "

if "%filter_choice%"=="1" set "filter=all"
if "%filter_choice%"=="2" set "filter=cancelled"
if "%filter_choice%"=="3" set "filter=no-suffix"
if "%filter_choice%"=="4" set "filter=mp4"
goto menu

:toggle_dry_run
if "%dry_run%"=="--dry-run" (
    set "dry_run="
) else (
    set "dry_run=--dry-run"
)
goto menu

:toggle_auto_remove
if "%auto_remove%"=="--auto-remove" (
    set "auto_remove="
) else (
    set "auto_remove=--auto-remove"
)
goto menu

:run_cleaner
cls
echo ===================================================
echo               RUNNING FILENAME CLEANER
echo ===================================================
echo.
echo Running with the following settings:
echo   Folder: %folder%
echo   Filter: %filter%
if defined dry_run echo   Dry run: Enabled (files will NOT be deleted)
if not defined dry_run echo   Dry run: Disabled (files may be deleted)
if defined auto_remove echo   Auto remove: Enabled (no confirmation prompts)
if not defined auto_remove echo   Auto remove: Disabled (will prompt for confirmation)
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

:: Build the command
set "cmd=python framepack_filename_cleaner.py --folder "%folder%" --filter %filter%"
if defined dry_run set "cmd=!cmd! %dry_run%"
if defined auto_remove set "cmd=!cmd! %auto_remove%"

echo.
echo Running command: !cmd!
echo.
echo ===================================================
echo                  CLEANER OUTPUT
echo ===================================================
echo.

:: Run the command
!cmd!

echo.
echo ===================================================
echo                  CLEANER FINISHED
echo ===================================================
echo.
echo Press any key to return to the menu...
pause >nul
goto menu

:end
echo.
echo Thank you for using FramePack Filename Cleaner.
echo.
endlocal
exit /b 0
