import os
import sys
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import shutil

# Print Python version and path for debugging
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Current working directory: {os.getcwd()}")

# Import TkinterDnD for drag and drop support
try:
    print("Attempting to import tkinterdnd2...")
    # Import the required components from tkinterdnd2
    from tkinterdnd2 import TkinterDnD, DND_FILES
    # Also import PRIVATE constant which may be needed for proper drag and drop operation
    from tkinterdnd2 import PRIVATE
    TKDND_AVAILABLE = True
    print("TkinterDnD2 loaded successfully. Drag and drop is enabled.")
except ImportError as e:
    print(f"TkinterDnD2 import error: {e}")
    print("TkinterDnD2 not available. Drag and drop will be disabled.")
    print("To enable drag and drop, install tkinterdnd2: pip install tkinterdnd2")
    TKDND_AVAILABLE = False

# Import the rest of the framepack_gui.py file
with open("framepack_gui.py", "r") as f:
    code = f.read()

# Remove the import section and everything before the function definitions
code = code.split("# Function to load quick prompts from JSON file")[1]

# Execute the rest of the code
exec(code)
