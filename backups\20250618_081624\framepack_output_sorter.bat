@echo off
:: framepack_output_sorter.bat
:: Launcher for framepack_output_sorter.py
:: Processes all files in the current directory

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo <PERSON> is not installed or not in the PATH.
    echo Please install Python and try again.
    pause
    exit /b 1
)

:: Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"

:: Execute the Python script
python "%SCRIPT_DIR%framepack_output_sorter.py"

exit /b 0
