@echo off
setlocal enabledelayedexpansion

:: Set the Python executable path
set PYTHON_EXE=python

:: Check if Python is available
%PYTHON_EXE% --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Please make sure Python is installed and in your PATH.
    pause
    exit /b 1
)

:: Run the realignment script with the --clean option (using video duration by default)
echo Running sorted folder realignment with cleaning (using video duration)...
%PYTHON_EXE% framepack_realign_sorted.py --clean %*

:: Pause to see the results
pause
