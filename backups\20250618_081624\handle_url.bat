@echo off
setlocal enabledelayedexpansion

REM This script handles URLs passed to it, especially those with query parameters
REM It will download the image and return the path to the downloaded file

REM Get the URL from the command line
set "url=%*"

echo [URL_HANDLER] Processing URL: %url%

REM Create temp directory if it doesn't exist
set "temp_dir=%~dp0temp"
if not exist "%temp_dir%" (
    echo [URL_HANDLER] Creating temp directory
    mkdir "%temp_dir%"
)

REM Download the image from the URL
set "url_output=%temp_dir%\url_download_output.txt"
call venv\Scripts\python.exe download_url_image.py "%url%" > "%url_output%"

REM Check if download was successful
set "found_path="
for /f "tokens=1,* delims==" %%a in (%url_output%) do (
    if "%%a" == "DOWNLOADED_PATH" (
        echo [URL_HANDLER] Downloaded image to: %%b
        echo DOWNLOADED_PATH=%%b
        set "found_path=1"
    )
)

if not defined found_path (
    echo [URL_HANDLER] Error: Failed to download image from URL
    type "%url_output%"
    echo DOWNLOAD_FAILED
)

REM Clean up
if exist "%url_output%" del "%url_output%"
