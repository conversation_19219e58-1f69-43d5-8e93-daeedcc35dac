@echo off
setlocal enabledelayedexpansion

echo Image Editor with Proper Corner Scaling
echo =====================================

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    echo and make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

REM Check if PIL/Pillow is installed
python -c "import PIL" >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo PIL/Pillow is not installed.
    echo Installing Pillow...
    python -m pip install Pillow
    if %ERRORLEVEL% neq 0 (
        echo Failed to install Pillow. Please install it manually:
        echo python -m pip install Pillow
        pause
        exit /b 1
    )
)

REM Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"

REM If arguments are provided (drag and drop or send to)
if "%~1" neq "" (
    REM Process the first file
    python "%SCRIPT_DIR%image_editor.py" "%~1"
) else (
    REM No arguments, run the editor without a file
    python "%SCRIPT_DIR%image_editor.py"
)

REM Don't pause automatically - let the GUI handle the interaction
