#!/usr/bin/env python
"""
Simple Image Editor with Proper Corner Scaling

Demonstrates correct corner scaling behavior where:
- Dragging top-left corner scales from top-left
- Dragging bottom-right corner scales from bottom-right
- Canvas interactions work properly with bounding boxes

Usage:
    python image_editor.py [image_file]
"""

import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageTk
import os
import sys

class ImageEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("Image Editor - Proper Corner Scaling")
        self.root.geometry("800x600")
        
        # Variables
        self.image = None
        self.photo = None
        self.canvas_image_id = None
        self.image_x = 100
        self.image_y = 100
        self.image_width = 200
        self.image_height = 200
        
        # Scaling state
        self.scaling = False
        self.scale_corner = None  # 'tl', 'tr', 'bl', 'br'
        self.scale_start_x = 0
        self.scale_start_y = 0
        self.original_bounds = None
        
        # Handle size
        self.handle_size = 8
        self.handles = {}
        
        self.create_ui()
        
        # Load image if provided as argument
        if len(sys.argv) > 1 and os.path.exists(sys.argv[1]):
            self.load_image(sys.argv[1])
    
    def create_ui(self):
        """Create the user interface"""
        # Menu bar
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open Image", command=self.open_image)
        file_menu.add_command(label="Remove Background", command=self.remove_background)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Main frame
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Canvas
        self.canvas = tk.Canvas(main_frame, bg='white', relief=tk.SUNKEN, bd=2)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # Bind canvas events
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_canvas_release)
        self.canvas.bind("<Motion>", self.on_canvas_motion)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Load an image to start editing")
        status_bar = tk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def open_image(self):
        """Open an image file"""
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp *.webp"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.load_image(file_path)
    
    def load_image(self, file_path):
        """Load an image into the editor"""
        try:
            self.image = Image.open(file_path)
            
            # Resize image to fit canvas while maintaining aspect ratio
            canvas_width = self.canvas.winfo_width() or 600
            canvas_height = self.canvas.winfo_height() or 400
            
            # Calculate size to fit in canvas
            img_ratio = self.image.width / self.image.height
            canvas_ratio = canvas_width / canvas_height
            
            if img_ratio > canvas_ratio:
                # Image is wider than canvas ratio
                self.image_width = min(canvas_width - 200, self.image.width)
                self.image_height = int(self.image_width / img_ratio)
            else:
                # Image is taller than canvas ratio
                self.image_height = min(canvas_height - 200, self.image.height)
                self.image_width = int(self.image_height * img_ratio)
            
            # Center the image
            self.image_x = (canvas_width - self.image_width) // 2
            self.image_y = (canvas_height - self.image_height) // 2
            
            self.update_display()
            self.status_var.set(f"Loaded: {os.path.basename(file_path)} ({self.image.width}x{self.image.height})")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {str(e)}")
    
    def update_display(self):
        """Update the canvas display"""
        if not self.image:
            return
        
        # Clear canvas
        self.canvas.delete("all")
        
        # Resize image for display
        display_image = self.image.resize((self.image_width, self.image_height), Image.LANCZOS)
        self.photo = ImageTk.PhotoImage(display_image)
        
        # Draw image
        self.canvas_image_id = self.canvas.create_image(
            self.image_x, self.image_y, 
            image=self.photo, 
            anchor=tk.NW,
            tags="image"
        )
        
        # Draw bounding box and handles
        self.draw_bounding_box()
    
    def draw_bounding_box(self):
        """Draw bounding box and corner handles"""
        if not self.image:
            return
        
        x1, y1 = self.image_x, self.image_y
        x2, y2 = self.image_x + self.image_width, self.image_y + self.image_height
        
        # Draw bounding box
        self.canvas.create_rectangle(x1, y1, x2, y2, outline="blue", width=2, tags="bbox")
        
        # Draw corner handles with proper priority (drawn last = highest priority)
        handle_size = self.handle_size
        
        # Top-left handle
        self.handles['tl'] = self.canvas.create_rectangle(
            x1 - handle_size//2, y1 - handle_size//2,
            x1 + handle_size//2, y1 + handle_size//2,
            fill="blue", outline="white", width=2, tags="handle"
        )
        
        # Top-right handle
        self.handles['tr'] = self.canvas.create_rectangle(
            x2 - handle_size//2, y1 - handle_size//2,
            x2 + handle_size//2, y1 + handle_size//2,
            fill="blue", outline="white", width=2, tags="handle"
        )
        
        # Bottom-left handle
        self.handles['bl'] = self.canvas.create_rectangle(
            x1 - handle_size//2, y2 - handle_size//2,
            x1 + handle_size//2, y2 + handle_size//2,
            fill="blue", outline="white", width=2, tags="handle"
        )
        
        # Bottom-right handle
        self.handles['br'] = self.canvas.create_rectangle(
            x2 - handle_size//2, y2 - handle_size//2,
            x2 + handle_size//2, y2 + handle_size//2,
            fill="blue", outline="white", width=2, tags="handle"
        )
    
    def get_handle_at_point(self, x, y):
        """Get which handle is at the given point"""
        # Check handles in reverse order (last drawn = highest priority)
        for handle_name in ['br', 'bl', 'tr', 'tl']:
            handle_id = self.handles.get(handle_name)
            if handle_id:
                bbox = self.canvas.bbox(handle_id)
                if bbox and bbox[0] <= x <= bbox[2] and bbox[1] <= y <= bbox[3]:
                    return handle_name
        return None
    
    def on_canvas_click(self, event):
        """Handle canvas click events"""
        if not self.image:
            return
        
        # Check if clicking on a handle (handles have priority)
        handle = self.get_handle_at_point(event.x, event.y)
        if handle:
            self.scaling = True
            self.scale_corner = handle
            self.scale_start_x = event.x
            self.scale_start_y = event.y
            self.original_bounds = (self.image_x, self.image_y, self.image_width, self.image_height)
            self.canvas.config(cursor="sizing")
            self.status_var.set(f"Scaling from {handle.upper()} corner...")
            return
        
        # Check if clicking on image (for moving)
        if (self.image_x <= event.x <= self.image_x + self.image_width and
            self.image_y <= event.y <= self.image_y + self.image_height):
            self.status_var.set("Click and drag handles to resize image")
    
    def on_canvas_drag(self, event):
        """Handle canvas drag events"""
        if not self.scaling or not self.scale_corner:
            return
        
        # Calculate movement
        dx = event.x - self.scale_start_x
        dy = event.y - self.scale_start_y
        
        # Get original bounds
        orig_x, orig_y, orig_w, orig_h = self.original_bounds
        
        # Apply scaling based on corner - THIS IS THE CORRECT BEHAVIOR
        if self.scale_corner == 'tl':  # Top-left: scale from top-left
            new_x = orig_x + dx
            new_y = orig_y + dy
            new_w = orig_w - dx
            new_h = orig_h - dy
        elif self.scale_corner == 'tr':  # Top-right: scale from top-right
            new_x = orig_x
            new_y = orig_y + dy
            new_w = orig_w + dx
            new_h = orig_h - dy
        elif self.scale_corner == 'bl':  # Bottom-left: scale from bottom-left
            new_x = orig_x + dx
            new_y = orig_y
            new_w = orig_w - dx
            new_h = orig_h + dy
        elif self.scale_corner == 'br':  # Bottom-right: scale from bottom-right
            new_x = orig_x
            new_y = orig_y
            new_w = orig_w + dx
            new_h = orig_h + dy
        
        # Enforce minimum size
        min_size = 20
        if new_w >= min_size and new_h >= min_size:
            self.image_x = new_x
            self.image_y = new_y
            self.image_width = new_w
            self.image_height = new_h
            self.update_display()
    
    def on_canvas_release(self, event):
        """Handle canvas release events"""
        if self.scaling:
            self.scaling = False
            self.scale_corner = None
            self.canvas.config(cursor="")
            self.status_var.set("Scaling complete")
    
    def on_canvas_motion(self, event):
        """Handle canvas motion events for cursor changes"""
        if self.scaling:
            return
        
        handle = self.get_handle_at_point(event.x, event.y)
        if handle:
            # Set appropriate cursor for corner
            if handle in ['tl', 'br']:
                self.canvas.config(cursor="size_nw_se")
            else:  # tr, bl
                self.canvas.config(cursor="size_ne_sw")
        else:
            self.canvas.config(cursor="")
    
    def remove_background(self):
        """Remove background from the current image"""
        if not self.image:
            messagebox.showwarning("Warning", "Please load an image first")
            return
        
        try:
            # Try to import and use rembg
            from rembg import remove
            
            self.status_var.set("Removing background...")
            self.root.update()
            
            # Remove background
            result_image = remove(self.image)
            self.image = result_image
            self.update_display()
            self.status_var.set("Background removed successfully")
            
        except ImportError:
            messagebox.showerror("Error", 
                "rembg library not installed.\n\n"
                "Install it with: pip install rembg\n\n"
                "This will enable AI-powered background removal.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to remove background: {str(e)}")

def main():
    root = tk.Tk()
    app = ImageEditor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
