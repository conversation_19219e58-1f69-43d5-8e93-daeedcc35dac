#!/usr/bin/env python
"""
Face Cropper

Crops images to focus on detected faces, making the face fill approximately 60% of the frame
with the center of the face positioned at the center of the image.

Usage:
    python image_face_cropper.py [image_file1] [image_file2] ...
    python image_face_cropper.py --strength 7 [image_file1] [image_file2] ...
    python image_face_cropper.py --size 512 [image_file1] [image_file2] ...
    python image_face_cropper.py --fill 80 [image_file1] [image_file2] ...
    python image_face_cropper.py --list [list_file.txt]

    Options:
        --strength    Face detection strength (1-10, default: 5)
                     1-2: Very sensitive (may detect false positives)
                     3-4: Sensitive
                     5-6: Balanced (default)
                     7-8: Strict
                     9-10: Very strict (only clear faces)
        --size        Output image size in pixels (default: 512)
                     Creates square output images of size x size
        --fill        Face fill percentage (10-95, default: 60)
                     How much of the frame the face should fill
        --list        Enable list processing mode for text files with image paths

    If no files are provided, the script will prompt for input.
    For images with multiple faces, crops around the largest detected face.
"""
import os
import sys
import argparse
from pathlib import Path

# Check for required dependencies
try:
    import cv2
    from PIL import Image
except ImportError as e:
    print(f"Missing required dependency: {e}")
    print("Please install required packages with:")
    print("pip install opencv-python pillow")
    sys.exit(1)

def crop_face(input_path, output_path=None, strength=5, output_size=512, fill_percentage=60):
    """
    Crop image to focus on the detected face.

    Args:
        input_path (str): Path to the input image
        output_path (str, optional): Path to save the output image. If None, will use the original name with _cropped suffix.
        strength (int): Face detection strength (1-10). Higher values are more strict. Default: 5.
        output_size (int): Size of the output square image in pixels. Default: 512.
        fill_percentage (int): Percentage of frame the face should fill (10-95). Default: 60.

    Returns:
        str: Path to the output image if successful, None otherwise
    """
    try:
        # Generate output path if not provided
        if output_path is None:
            input_path_obj = Path(input_path)
            output_path = str(input_path_obj.parent / f"{input_path_obj.stem}_cropped{input_path_obj.suffix}")

        print(f"Processing: {os.path.basename(input_path)}")

        # Map strength to minNeighbors parameter for face detection
        strength = max(1, min(10, strength))  # Clamp to 1-10 range
        if strength <= 2:
            min_neighbors = 3
        elif strength <= 4:
            min_neighbors = 4
        elif strength <= 6:
            min_neighbors = 5
        elif strength <= 8:
            min_neighbors = 6 + (strength - 7)  # 6 or 7
        else:  # 9-10
            min_neighbors = 8 + (strength - 9)  # 8 or 9

        # Read the image
        image = cv2.imread(input_path)
        if image is None:
            print(f"Error: Could not read image: {input_path}")
            return None

        h, w = image.shape[:2]
        print(f"Original image size: {w}x{h}")

        # Load OpenCV's pre-trained face detection model
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

        # Convert to grayscale for face detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Detect faces using the strength-based minNeighbors parameter
        faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=min_neighbors, minSize=(30, 30))

        if len(faces) == 0:
            print("No faces detected in the image")
            return None

        print(f"Found {len(faces)} face(s)")

        # Find the largest face (by area)
        largest_face = None
        largest_area = 0
        for (x, y, w_face, h_face) in faces:
            area = w_face * h_face
            if area > largest_area:
                largest_area = area
                largest_face = (x, y, w_face, h_face)

        x, y, w_face, h_face = largest_face
        print(f"Using largest face at ({x}, {y}) with size {w_face}x{h_face}")

        # Calculate face center
        face_center_x = x + w_face // 2
        face_center_y = y + h_face // 2

        # Calculate crop size to make face fill the specified percentage of the frame
        # Face should be fill_percentage% of output, so crop size should be face_size / (fill_percentage/100)
        face_size = max(w_face, h_face)
        fill_ratio = fill_percentage / 100.0
        crop_size = int(face_size / fill_ratio)

        # Calculate crop boundaries centered on face
        crop_x1 = face_center_x - crop_size // 2
        crop_y1 = face_center_y - crop_size // 2
        crop_x2 = crop_x1 + crop_size
        crop_y2 = crop_y1 + crop_size

        # Adjust crop boundaries to stay within image bounds
        if crop_x1 < 0:
            crop_x2 -= crop_x1
            crop_x1 = 0
        if crop_y1 < 0:
            crop_y2 -= crop_y1
            crop_y1 = 0
        if crop_x2 > w:
            crop_x1 -= (crop_x2 - w)
            crop_x2 = w
        if crop_y2 > h:
            crop_y1 -= (crop_y2 - h)
            crop_y2 = h

        # Ensure we don't go negative after adjustments
        crop_x1 = max(0, crop_x1)
        crop_y1 = max(0, crop_y1)

        # If the crop area is still too large, adjust crop size
        actual_crop_w = crop_x2 - crop_x1
        actual_crop_h = crop_y2 - crop_y1
        actual_crop_size = min(actual_crop_w, actual_crop_h)

        if actual_crop_size < crop_size:
            # Recalculate centered crop with the maximum possible size
            crop_x1 = max(0, face_center_x - actual_crop_size // 2)
            crop_y1 = max(0, face_center_y - actual_crop_size // 2)
            crop_x2 = min(w, crop_x1 + actual_crop_size)
            crop_y2 = min(h, crop_y1 + actual_crop_size)

        print(f"Cropping region: ({crop_x1}, {crop_y1}) to ({crop_x2}, {crop_y2})")

        # Crop the image
        cropped_image = image[crop_y1:crop_y2, crop_x1:crop_x2]

        # Convert to PIL for better resizing
        cropped_pil = Image.fromarray(cv2.cvtColor(cropped_image, cv2.COLOR_BGR2RGB))
        
        # Resize to output size (square)
        cropped_pil = cropped_pil.resize((output_size, output_size), Image.Resampling.LANCZOS)
        
        # Save the result
        cropped_pil.save(output_path)

        print(f"Face cropping completed successfully! Saved to: {output_path}")
        print(f"Output size: {output_size}x{output_size}")
        return output_path

    except Exception as e:
        print(f"Error processing image: {e}")
        import traceback
        traceback.print_exc()
        return None

def read_image_list(list_file):
    """Read image paths from a text file."""
    try:
        with open(list_file, 'r', encoding='utf-8') as f:
            paths = [line.strip() for line in f if line.strip()]
        return paths
    except Exception as e:
        print(f"Error reading list file {list_file}: {e}")
        return []

def main():
    try:
        parser = argparse.ArgumentParser(description='Crop images to focus on detected faces')
        parser.add_argument('input', nargs='*', help='Input image files or list file')
        parser.add_argument('--strength', type=int, default=5, choices=range(1, 11),
                          help='Face detection strength (1-10, default: 5). Higher values are more strict.')
        parser.add_argument('--size', type=int, default=512,
                          help='Output image size in pixels (default: 512). Creates square images.')
        parser.add_argument('--fill', type=int, default=60, choices=range(10, 96),
                          help='Face fill percentage (10-95, default: 60). How much of the frame the face should fill.')
        parser.add_argument('--list', action='store_true',
                          help='Enable list processing mode for text files with image paths')

        args = parser.parse_args()
        strength = args.strength
        output_size = args.size
        fill_percentage = args.fill

        print(f"Face Cropper - Detection Strength: {strength}, Output Size: {output_size}x{output_size}, Face Fill: {fill_percentage}%")

        if not args.input:
            # Interactive mode
            print("\nNo input files provided. Please enter image file paths (press Enter with empty input to finish):")
            while True:
                user_input = input("Image file path: ").strip()
                if not user_input:
                    break

                if user_input and os.path.isfile(user_input):
                    crop_face(user_input, strength=strength, output_size=output_size, fill_percentage=fill_percentage)
                elif user_input:
                    print(f"Invalid file path: {user_input}")
                else:
                    print("No file path provided.")
        else:
            if args.list:
                # List processing mode
                for list_file in args.input:
                    if os.path.isfile(list_file):
                        print(f"Processing list file: {list_file}")
                        image_paths = read_image_list(list_file)
                        for image_path in image_paths:
                            if os.path.isfile(image_path):
                                crop_face(image_path, strength=strength, output_size=output_size, fill_percentage=fill_percentage)
                            else:
                                print(f"File not found: {image_path}")
                    else:
                        print(f"List file not found: {list_file}")
            else:
                # Process input files
                valid_inputs = [inp for inp in args.input if inp.strip()]

                if not valid_inputs:
                    print("No valid input files provided.")
                    return

                # Handle paths with spaces
                if len(valid_inputs) > 1:
                    full_path = ' '.join(valid_inputs)
                    if os.path.isfile(full_path):
                        crop_face(full_path, strength=strength, output_size=output_size, fill_percentage=fill_percentage)
                        return

                # Process each input separately
                for input_path in valid_inputs:
                    # Remove quotes if present
                    if (input_path.startswith('"') and input_path.endswith('"')) or \
                       (input_path.startswith("'") and input_path.endswith("'")):
                        input_path = input_path[1:-1]

                    if input_path.strip() and os.path.isfile(input_path):
                        crop_face(input_path, strength=strength, output_size=output_size, fill_percentage=fill_percentage)
                    elif input_path.strip():
                        print(f"Invalid file path: {input_path}")

    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        print(f"An error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
