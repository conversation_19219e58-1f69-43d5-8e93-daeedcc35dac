@echo off
echo Installing rembg for AI background removal...
echo.

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python from https://www.python.org/downloads/
    echo and make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

echo Installing rembg...
python -m pip install rembg

if %ERRORLEVEL% equ 0 (
    echo.
    echo rembg installed successfully!
    echo You can now use AI background removal in the image tools.
    echo.
    echo Usage:
    echo - Run image_blank.py to remove backgrounds from images
    echo - Run image_editor.py for interactive image editing with background removal
) else (
    echo.
    echo Failed to install rembg. Please try manually:
    echo python -m pip install rembg
)

echo.
pause
