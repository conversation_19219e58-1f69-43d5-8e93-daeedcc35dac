@echo off
setlocal enabledelayedexpansion
title FramePack GUI Launcher
color 0A

REM Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo ===============================================
echo           FRAMEPACK LAUNCHER
echo ===============================================
echo
echo Unified launcher that supports:
echo  - Image files (drag and drop)
echo  - Directories (drag and drop)
echo  - URLs (drag and drop or paste)
echo ===============================================
echo

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    color 0C
    echo.
    echo Error: Python is not installed or not in your PATH.
    echo Please install Python and make sure it's added to your PATH.
    echo.
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist venv\Scripts\activate.bat (
    echo Virtual environment not found. Checking if we can create it...

    REM Try to create virtual environment
    echo Creating virtual environment...
    python -m venv venv
    if %ERRORLEVEL% NEQ 0 (
        color 0C
        echo
        echo Error: Failed to create virtual environment.
        echo Please make sure you have the venv module installed.
        echo You may need to run: pip install virtualenv
        echo
        pause
        exit /b 1
    )

    echo Virtual environment created successfully.
    echo Installing required packages...

    REM Activate and install requirements
    call venv\Scripts\activate.bat
    pip install -r requirements.txt
    if %ERRORLEVEL% NEQ 0 (
        color 0C
        echo
        echo Error: Failed to install required packages.
        echo Please check your internet connection and try again.
        echo
        pause
        exit /b 1
    )

    echo Required packages installed successfully.
) else (
    echo Virtual environment found.
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Launch the GUI
echo Starting FramePack...

REM Check if arguments were provided (for drag and drop or send to)
if "%~1" == "" goto :no_args

REM Check if the first argument is a URL (contains http)
set "first_arg=%~1"
echo First argument: %first_arg%

REM Check if it's a URL by looking for "http" in the argument
echo %first_arg% | findstr /i "http" > nul
if %ERRORLEVEL% EQU 0 (
    echo Detected URL in arguments, redirecting to URL handler...
    call "%SCRIPT_DIR%url_to_framepack.bat" "%first_arg%"
    exit /b %ERRORLEVEL%
)

echo Files or directories were provided via drag and drop or command line...

REM Create temp directory if it doesn't exist
set "temp_dir=%SCRIPT_DIR%temp"
if not exist "%temp_dir%" (
    echo Creating temp directory
    mkdir "%temp_dir%"
    if %ERRORLEVEL% NEQ 0 (
        color 0C
        echo Error: Failed to create temp directory.
        echo Please check if you have write permissions to %SCRIPT_DIR%
        pause
        exit /b 1
    )
)

REM Create a temporary file to store the file paths
set "temp_file=%temp_dir%\framepack_temp_files.txt"
echo Creating temporary file list at: %temp_file%

REM Delete existing temp file if it exists
if exist "%temp_file%" del "%temp_file%"

REM Create an empty file
echo. 2>nul > "%temp_file%"
if not exist "%temp_file%" (
    color 0C
    echo Error: Failed to create temporary file list.
    echo Please check if you have write permissions to %temp_dir%
    pause
    exit /b 1
)

REM Process each argument
for %%i in (%*) do (
    set "arg=%%~i"
    set "full_path=%%~fi"
    echo Processing argument: !arg!
    echo Full path: !full_path!

    REM Check if it's a URL
    set "is_url=0"
    if "!arg:~0,7!" == "http://" set "is_url=1"
    if "!arg:~0,8!" == "https://" set "is_url=1"

    if "!is_url!" == "1" (
        echo Detected URL: !arg!

        REM Use handle_url.bat to process the URL (it handles query parameters properly)
        set "url_output=%temp_dir%\url_handler_output.txt"
        call "%SCRIPT_DIR%handle_url.bat" "!arg!" > "!url_output!"

        REM Check if download was successful
        set "found_path="
        for /f "tokens=1,* delims==" %%a in (!url_output!) do (
            if "%%a" == "DOWNLOADED_PATH" (
                echo Downloaded image to: %%b
                echo %%b>> "%temp_file%"
                set "found_path=1"
            )
        )

        if not defined found_path (
            echo Error: Failed to download image from URL: !arg!
            type "!url_output!"
        )

        if exist "!url_output!" del "!url_output!"
    ) else (
        REM Check if it's a directory
        if exist "!full_path!\*" (
            echo Detected directory: !full_path!

            REM Process image files in directory
            for %%j in ("!full_path!\*.jpg" "!full_path!\*.jpeg" "!full_path!\*.png" "!full_path!\*.bmp" "!full_path!\*.webp") do (
                if exist "%%~j" (
                    echo Adding file from directory: %%~nxj
                    echo %%~fj>> "%temp_file%"
                )
            )
        ) else (
            REM Check if it's a file
            if exist "!full_path!" (
                echo Adding file: %%~nxi
                echo !full_path!>> "%temp_file%"
            ) else (
                echo Warning: File not found: !full_path!

                REM Try as relative path
                if exist "%%~i" (
                    echo File exists as relative path: %%~i
                    echo %%~fi>> "%temp_file%"
                ) else (
                    echo Error: Could not find file: %%~i
                )
            )
        )
    )
)

REM Check if the temp file has content
set "has_content=0"
for /f "usebackq" %%A in ("%temp_file%") do (
    set "has_content=1"
    goto :check_done
)
:check_done

if "!has_content!" == "1" (
    echo Launching FramePack GUI with file list...
    call venv\Scripts\python.exe framepack_gui.py --file-list "%temp_file%"
) else (
    echo Warning: No valid files were found to process
    echo Launching GUI in normal mode...
    call venv\Scripts\python.exe framepack_gui.py
)

REM Clean up
if exist "%temp_file%" del "%temp_file%"
goto :eof

:no_args
echo Launching GUI in normal mode...
call venv\Scripts\python.exe framepack_gui.py

REM Check for errors
if %ERRORLEVEL% NEQ 0 (
    color 0C
    echo
    echo Error launching FramePack GUI. The error code was: %ERRORLEVEL%
    echo
    echo Please check if all required Python packages are installed.
    echo You may need to install tkinter with: pip install tk
    echo
    echo If you're having issues with drag and drop:
    echo 1. Make sure the file exists and is accessible
    echo 2. Try using the full path to the file
    echo 3. Check the temp directory permissions
    echo
    pause
)
exit /b %ERRORLEVEL%
