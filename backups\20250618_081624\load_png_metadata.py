import os
import sys
import json
from PIL import Image, PngImagePlugin

def load_png_metadata(file_path):
    """
    Load metadata from a PNG file.
    
    Args:
        file_path: Path to the PNG file
        
    Returns:
        Dictionary containing the extracted metadata, or None if no metadata is found
    """
    try:
        # Check if the file exists
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            return None
            
        # Open the image
        img = Image.open(file_path)
        
        # Print all metadata keys in the image
        print(f"Image metadata keys: {list(img.info.keys())}")
        
        # Get the metadata
        metadata_json = img.info.get('FramePack')
        if metadata_json:
            # Parse the JSON
            metadata = json.loads(metadata_json)
            print(f"Successfully extracted metadata: {metadata}")
            return metadata
        else:
            print("No FramePack metadata found in the image")
            return None
    except Exception as e:
        print(f"Error loading metadata: {e}")
        return None

if __name__ == "__main__":
    # Check if a file path was provided
    if len(sys.argv) < 2:
        print("Usage: python load_png_metadata.py <path_to_png_file>")
        sys.exit(1)
        
    # Load metadata from the specified file
    file_path = sys.argv[1]
    metadata = load_png_metadata(file_path)
    
    if metadata:
        print("\nMetadata found:")
        for key, value in metadata.items():
            print(f"  {key}: {value}")
    else:
        print("\nNo metadata found in the file.")
