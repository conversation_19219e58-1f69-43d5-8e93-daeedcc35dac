#!/usr/bin/env python3
"""
LoRA Merger Tool for FramePack Models

This tool merges LoRA weights into a copy of the original model weights,
creating a new model that doesn't require runtime LoRA loading.

Compatible with:
- Standard FramePack model (lllyasviel/FramePackI2V_HY)
- F1 FramePack model (lllyasviel/FramePack_F1_I2V_HY_20250503)
- Multiple LoRA formats (Musubi Tuner, Diffusion Pipeline, HunyuanVideo)

Usage:
    python lora_merger.py --model_name "lllyasviel/FramePackI2V_HY" --lora_file "my_lora.safetensors" --output_dir "merged_models" --multiplier 0.8
"""

import os
import sys
import argparse
import torch
import json
import shutil
import hashlib
import tempfile
from safetensors.torch import load_file, save_file
from tqdm import tqdm
from typing import Dict, List, Tuple

# Add utils to path for LoRA utilities
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
from lora_utils import merge_lora_to_state_dict

# Set HuggingFace cache directory
os.environ['HF_HOME'] = os.path.abspath(os.path.realpath(os.path.join(os.path.dirname(__file__), './hf_download')))

def find_merged_models(merged_models_dir: str = "merged_models") -> List[str]:
    """
    Find all merged models in the merged_models directory.

    Args:
        merged_models_dir: Directory containing merged models

    Returns:
        List of merged model paths
    """
    merged_models = []

    if not os.path.exists(merged_models_dir):
        return merged_models

    for item in os.listdir(merged_models_dir):
        item_path = os.path.join(merged_models_dir, item)
        if os.path.isdir(item_path):
            # Check if it has a config.json file (indicates it's a model)
            config_path = os.path.join(item_path, 'config.json')
            if os.path.exists(config_path):
                # Check if it has safetensors files
                has_safetensors = any(f.endswith('.safetensors') for f in os.listdir(item_path))
                if has_safetensors:
                    merged_models.append(item_path)

    return sorted(merged_models)


def get_available_models(merged_models_dir: str = "merged_models") -> List[str]:
    """
    Get all available models (both standard HuggingFace models and merged models).

    Args:
        merged_models_dir: Directory containing merged models

    Returns:
        List of all available model names/paths
    """
    models = []

    # Add standard models
    standard_models = [
        "lllyasviel/FramePackI2V_HY",
        "lllyasviel/FramePack_F1_I2V_HY_20250503"
    ]

    for model_name in standard_models:
        try:
            find_model_files(model_name)
            models.append(model_name)
        except FileNotFoundError:
            # Model not available, skip
            pass

    # Add merged models
    merged_models = find_merged_models(merged_models_dir)
    models.extend(merged_models)

    return models


def find_model_files(model_name: str) -> Tuple[str, List[str], str]:
    """
    Find the model files for a given model name.
    Supports both HuggingFace cache models and local merged models.

    Returns:
        Tuple of (model_path, safetensors_files, config_file)
    """
    # Check if this is a local path (merged model)
    if os.path.isdir(model_name):
        model_path = model_name

        # Find safetensors files
        safetensors_files = []
        config_file = None

        for file in os.listdir(model_path):
            if file.endswith('.safetensors') and not file.endswith('.index.json'):
                safetensors_files.append(os.path.join(model_path, file))
            elif file == 'config.json':
                config_file = os.path.join(model_path, file)

        if not safetensors_files:
            raise FileNotFoundError(f"No safetensors files found in {model_path}")

        if not config_file:
            raise FileNotFoundError(f"No config.json found in {model_path}")

        # Sort safetensors files to ensure consistent order
        safetensors_files.sort()

        return model_path, safetensors_files, config_file

    # Original HuggingFace cache logic
    # Convert model name to HuggingFace cache path format
    cache_name = model_name.replace('/', '--')
    cache_dir = os.path.join(os.environ['HF_HOME'], 'hub', f'models--{cache_name}')

    if not os.path.exists(cache_dir):
        raise FileNotFoundError(f"Model {model_name} not found in cache. Please run FramePack first to download the model.")

    # Find the latest snapshot
    snapshots_dir = os.path.join(cache_dir, 'snapshots')
    if not os.path.exists(snapshots_dir):
        raise FileNotFoundError(f"No snapshots found for model {model_name}")

    # Get the most recent snapshot (should be the only one)
    snapshot_dirs = [d for d in os.listdir(snapshots_dir) if os.path.isdir(os.path.join(snapshots_dir, d))]
    if not snapshot_dirs:
        raise FileNotFoundError(f"No snapshot directories found for model {model_name}")

    snapshot_path = os.path.join(snapshots_dir, snapshot_dirs[0])

    # Find safetensors files
    safetensors_files = []
    config_file = None

    for file in os.listdir(snapshot_path):
        if file.endswith('.safetensors') and not file.endswith('.index.json'):
            safetensors_files.append(os.path.join(snapshot_path, file))
        elif file == 'config.json':
            config_file = os.path.join(snapshot_path, file)

    if not safetensors_files:
        raise FileNotFoundError(f"No safetensors files found for model {model_name}")

    if not config_file:
        raise FileNotFoundError(f"No config.json found for model {model_name}")

    # Sort safetensors files to ensure consistent order
    safetensors_files.sort()

    return snapshot_path, safetensors_files, config_file

def load_model_state_dict(safetensors_files: List[str]) -> Dict[str, torch.Tensor]:
    """
    Load the complete model state dict from multiple safetensors files.
    """
    print(f"Loading model weights from {len(safetensors_files)} files...")
    
    complete_state_dict = {}
    
    for file_path in tqdm(safetensors_files, desc="Loading model files"):
        file_state_dict = load_file(file_path)
        complete_state_dict.update(file_state_dict)
        print(f"Loaded {len(file_state_dict)} tensors from {os.path.basename(file_path)}")
    
    print(f"Total model parameters loaded: {len(complete_state_dict)}")
    return complete_state_dict

def save_merged_model(state_dict: Dict[str, torch.Tensor], output_dir: str, model_name: str,
                     lora_info: str, config_file: str) -> str:
    """
    Save the merged model to the output directory in HuggingFace cache format.
    """
    # Create HuggingFace cache directory structure
    safe_model_name = model_name.replace('/', '--')
    model_cache_dir = os.path.join(output_dir, f"models--{safe_model_name}_{lora_info}")

    # Create directory structure
    blobs_dir = os.path.join(model_cache_dir, 'blobs')
    refs_dir = os.path.join(model_cache_dir, 'refs')
    snapshots_dir = os.path.join(model_cache_dir, 'snapshots')

    os.makedirs(blobs_dir, exist_ok=True)
    os.makedirs(refs_dir, exist_ok=True)
    os.makedirs(snapshots_dir, exist_ok=True)

    # Generate a commit hash for the snapshot
    commit_hash = hashlib.sha1(f"{model_name}_{lora_info}".encode()).hexdigest()
    snapshot_dir = os.path.join(snapshots_dir, commit_hash)
    os.makedirs(snapshot_dir, exist_ok=True)

    # Calculate model size and determine if we need to split
    total_params = sum(tensor.numel() for tensor in state_dict.values())
    approx_size_gb = (total_params * 2) / (1024**3)

    print(f"Model size: ~{approx_size_gb:.2f} GB ({total_params:,} parameters)")

    if approx_size_gb > 4.5:  # Split if larger than ~4.5GB to stay under 5GB per file
        print("Model is large, splitting into multiple files...")
        save_split_model_hf_format(state_dict, snapshot_dir, blobs_dir)
    else:
        print("Saving as single file...")
        save_single_model_hf_format(state_dict, snapshot_dir, blobs_dir)

    # Copy config file to snapshot and create blob
    config_blob_hash = save_file_as_blob(config_file, blobs_dir)
    config_symlink = os.path.join(snapshot_dir, 'config.json')
    create_blob_symlink(config_blob_hash, config_symlink, blobs_dir)

    # Create refs/main file pointing to the commit
    with open(os.path.join(refs_dir, 'main'), 'w') as f:
        f.write(commit_hash)

    # Create .no_exist directory (HuggingFace cache structure)
    no_exist_dir = os.path.join(model_cache_dir, '.no_exist', commit_hash)
    os.makedirs(no_exist_dir, exist_ok=True)

    print(f"Successfully saved merged model in HuggingFace cache format!")
    print(f"Output saved to: {model_cache_dir}")

    return model_cache_dir

def save_file_as_blob(file_path: str, blobs_dir: str) -> str:
    """
    Save a file as a blob and return its hash.
    """
    with open(file_path, 'rb') as f:
        content = f.read()

    # Calculate SHA256 hash
    blob_hash = hashlib.sha256(content).hexdigest()
    blob_path = os.path.join(blobs_dir, blob_hash)

    # Save blob if it doesn't exist
    if not os.path.exists(blob_path):
        with open(blob_path, 'wb') as f:
            f.write(content)

    return blob_hash

def create_blob_symlink(blob_hash: str, symlink_path: str, blobs_dir: str):
    """
    Create a symlink to a blob file (or copy on Windows).
    """
    blob_path = os.path.join(blobs_dir, blob_hash)

    # On Windows, create a copy instead of symlink for compatibility
    if os.name == 'nt':
        shutil.copy2(blob_path, symlink_path)
    else:
        # Create relative symlink
        rel_blob_path = os.path.relpath(blob_path, os.path.dirname(symlink_path))
        os.symlink(rel_blob_path, symlink_path)

def save_single_model_hf_format(state_dict: Dict[str, torch.Tensor], snapshot_dir: str, blobs_dir: str):
    """
    Save a single model file in HuggingFace format.
    """
    # Save model to temporary file first
    with tempfile.NamedTemporaryFile(suffix='.safetensors', delete=False) as tmp_file:
        tmp_path = tmp_file.name

    try:
        save_file(state_dict, tmp_path)

        # Save as blob
        blob_hash = save_file_as_blob(tmp_path, blobs_dir)

        # Create symlink in snapshot
        model_symlink = os.path.join(snapshot_dir, 'diffusion_pytorch_model.safetensors')
        create_blob_symlink(blob_hash, model_symlink, blobs_dir)

        print(f"Saved single model file as blob: {blob_hash}")

    finally:
        # Clean up temp file
        if os.path.exists(tmp_path):
            os.unlink(tmp_path)

def save_split_model_hf_format(state_dict: Dict[str, torch.Tensor], snapshot_dir: str, blobs_dir: str):
    """
    Save model split into multiple safetensors files in HuggingFace format.
    """
    # Group tensors by approximate size to balance files
    max_file_size = 4.5 * (1024**3)  # 4.5GB in bytes
    current_file_size = 0
    current_file_tensors = {}
    file_index = 1
    index_data = {"metadata": {"total_size": 0}, "weight_map": {}}

    total_size = 0
    temp_files = []

    try:
        for key, tensor in tqdm(state_dict.items(), desc="Splitting model"):
            tensor_size = tensor.numel() * tensor.element_size()
            total_size += tensor_size

            # Check if we need to start a new file
            if current_file_size + tensor_size > max_file_size and current_file_tensors:
                # Save current file to temp location
                with tempfile.NamedTemporaryFile(suffix='.safetensors', delete=False) as tmp_file:
                    tmp_path = tmp_file.name
                temp_files.append(tmp_path)

                filename = f"diffusion_pytorch_model-{file_index:05d}-of-XXXXX.safetensors"
                save_file(current_file_tensors, tmp_path)

                # Save as blob
                blob_hash = save_file_as_blob(tmp_path, blobs_dir)

                # Create symlink in snapshot (will be renamed later)
                model_symlink = os.path.join(snapshot_dir, filename)
                create_blob_symlink(blob_hash, model_symlink, blobs_dir)

                print(f"Saved file {file_index}: {filename} ({len(current_file_tensors)} tensors)")

                # Reset for next file
                current_file_tensors = {}
                current_file_size = 0
                file_index += 1

            # Add tensor to current file
            current_file_tensors[key] = tensor
            current_file_size += tensor_size
            index_data["weight_map"][key] = f"diffusion_pytorch_model-{file_index:05d}-of-XXXXX.safetensors"

        # Save the last file
        if current_file_tensors:
            with tempfile.NamedTemporaryFile(suffix='.safetensors', delete=False) as tmp_file:
                tmp_path = tmp_file.name
            temp_files.append(tmp_path)

            filename = f"diffusion_pytorch_model-{file_index:05d}-of-XXXXX.safetensors"
            save_file(current_file_tensors, tmp_path)

            # Save as blob
            blob_hash = save_file_as_blob(tmp_path, blobs_dir)

            # Create symlink in snapshot
            model_symlink = os.path.join(snapshot_dir, filename)
            create_blob_symlink(blob_hash, model_symlink, blobs_dir)

            print(f"Saved file {file_index}: {filename} ({len(current_file_tensors)} tensors)")

        # Update filenames with correct total count
        total_files = file_index
        for i in range(1, total_files + 1):
            old_filename = f"diffusion_pytorch_model-{i:05d}-of-XXXXX.safetensors"
            new_filename = f"diffusion_pytorch_model-{i:05d}-of-{total_files:05d}.safetensors"
            old_path = os.path.join(snapshot_dir, old_filename)
            new_path = os.path.join(snapshot_dir, new_filename)

            if os.path.exists(old_path):
                os.rename(old_path, new_path)

            # Update index
            for key in index_data["weight_map"]:
                if index_data["weight_map"][key] == old_filename:
                    index_data["weight_map"][key] = new_filename

        # Save index file as blob
        index_data["metadata"]["total_size"] = total_size
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as tmp_file:
            json.dump(index_data, tmp_file, indent=2)
            tmp_index_path = tmp_file.name
        temp_files.append(tmp_index_path)

        # Save index as blob
        index_blob_hash = save_file_as_blob(tmp_index_path, blobs_dir)
        index_symlink = os.path.join(snapshot_dir, "diffusion_pytorch_model.safetensors.index.json")
        create_blob_symlink(index_blob_hash, index_symlink, blobs_dir)

        print(f"Saved model split into {total_files} files with index")

    finally:
        # Clean up temp files
        for tmp_path in temp_files:
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)

def merge_single_lora(model_name: str, lora_file: str, multiplier: float, output_dir: str) -> str:
    """
    Merge a single LoRA into the base model.
    """
    print(f"Merging LoRA: {lora_file} (multiplier: {multiplier}) into {model_name}")
    
    # Find model files
    _, safetensors_files, config_file = find_model_files(model_name)
    
    # Load base model
    base_state_dict = load_model_state_dict(safetensors_files)
    
    # Merge LoRA
    print(f"Merging LoRA weights...")
    device = torch.device('cpu')  # Keep everything on CPU to save VRAM
    merged_state_dict = merge_lora_to_state_dict(base_state_dict, lora_file, multiplier, device)
    
    # Generate output info
    lora_name = os.path.splitext(os.path.basename(lora_file))[0]
    lora_info = f"{lora_name}_x{multiplier}"
    
    # Save merged model
    output_path = save_merged_model(merged_state_dict, output_dir, model_name, lora_info, config_file)
    
    print(f"Successfully merged LoRA into model!")
    print(f"Output saved to: {output_path}")
    
    return output_path

def merge_multiple_loras(model_name: str, lora_configs: List[Tuple[str, float]], output_dir: str) -> str:
    """
    Merge multiple LoRAs into the base model.

    Args:
        model_name: Name of the base model
        lora_configs: List of (lora_file, multiplier) tuples
        output_dir: Output directory for merged model
    """
    print(f"Merging {len(lora_configs)} LoRAs into {model_name}")

    # Find model files
    _, safetensors_files, config_file = find_model_files(model_name)

    # Load base model
    base_state_dict = load_model_state_dict(safetensors_files)

    # Merge LoRAs sequentially
    merged_state_dict = base_state_dict
    device = torch.device('cpu')  # Keep everything on CPU to save VRAM

    lora_names = []
    for i, (lora_file, multiplier) in enumerate(lora_configs, 1):
        print(f"Merging LoRA {i}/{len(lora_configs)}: {lora_file} (multiplier: {multiplier})")
        merged_state_dict = merge_lora_to_state_dict(merged_state_dict, lora_file, multiplier, device)

        lora_name = os.path.splitext(os.path.basename(lora_file))[0]
        lora_names.append(f"{lora_name}_x{multiplier}")

    # Generate output info
    lora_info = "_".join(lora_names)
    if len(lora_info) > 100:  # Truncate if too long
        lora_info = f"multi_lora_{len(lora_configs)}x"

    # Save merged model
    output_path = save_merged_model(merged_state_dict, output_dir, model_name, lora_info, config_file)

    print(f"Successfully merged {len(lora_configs)} LoRAs into model!")
    print(f"Output saved to: {output_path}")

    return output_path

def main():
    parser = argparse.ArgumentParser(description="Merge LoRA weights into FramePack model weights")
    parser.add_argument("--model_name", type=str,
                       help="Name or path of the base model to merge LoRA into. Can be a standard model name (lllyasviel/FramePackI2V_HY, lllyasviel/FramePack_F1_I2V_HY_20250503) or path to a merged model directory")
    parser.add_argument("--output_dir", type=str, default="merged_models",
                       help="Output directory for merged models (default: merged_models)")
    parser.add_argument("--list_models", action="store_true",
                       help="List all available models and exit")

    # Single LoRA options
    parser.add_argument("--lora_file", type=str,
                       help="Path to LoRA file (.safetensors)")
    parser.add_argument("--multiplier", type=float, default=0.8,
                       help="LoRA multiplier/strength (default: 0.8)")

    # Multiple LoRA options
    parser.add_argument("--lora_file_1", type=str, help="Path to first LoRA file")
    parser.add_argument("--multiplier_1", type=float, default=0.8, help="First LoRA multiplier")
    parser.add_argument("--lora_file_2", type=str, help="Path to second LoRA file")
    parser.add_argument("--multiplier_2", type=float, default=0.8, help="Second LoRA multiplier")
    parser.add_argument("--lora_file_3", type=str, help="Path to third LoRA file")
    parser.add_argument("--multiplier_3", type=float, default=0.8, help="Third LoRA multiplier")

    # Options
    parser.add_argument("--force", action="store_true",
                       help="Overwrite existing merged models")

    args = parser.parse_args()

    # Handle --list_models option
    if args.list_models:
        print("Available models:")
        print("\nStandard models:")
        standard_models = ["lllyasviel/FramePackI2V_HY", "lllyasviel/FramePack_F1_I2V_HY_20250503"]
        for model in standard_models:
            try:
                find_model_files(model)
                print(f"  ✓ {model}")
            except FileNotFoundError:
                print(f"  ✗ {model} (not downloaded)")

        print(f"\nMerged models in '{args.output_dir}':")
        merged_models = find_merged_models(args.output_dir)
        if merged_models:
            for model in merged_models:
                print(f"  ✓ {model}")
        else:
            print("  (none found)")

        return 0

    # Validate arguments
    if not args.model_name:
        print("Error: --model_name is required when not using --list_models")
        return 1

    single_lora = args.lora_file is not None
    multi_lora = any([args.lora_file_1, args.lora_file_2, args.lora_file_3])

    if not single_lora and not multi_lora:
        print("Error: Must specify either --lora_file or at least one of --lora_file_1/2/3")
        return 1

    if single_lora and multi_lora:
        print("Error: Cannot use both single LoRA (--lora_file) and multiple LoRA options")
        return 1

    # Validate model name/path
    try:
        find_model_files(args.model_name)
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("\nUse --list_models to see available models")
        return 1

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    try:
        if single_lora:
            # Single LoRA merge
            if not os.path.exists(args.lora_file):
                print(f"Error: LoRA file not found: {args.lora_file}")
                return 1

            output_path = merge_single_lora(args.model_name, args.lora_file, args.multiplier, args.output_dir)

        else:
            # Multiple LoRA merge
            lora_configs = []

            if args.lora_file_1:
                if not os.path.exists(args.lora_file_1):
                    print(f"Error: LoRA file 1 not found: {args.lora_file_1}")
                    return 1
                lora_configs.append((args.lora_file_1, args.multiplier_1))

            if args.lora_file_2:
                if not os.path.exists(args.lora_file_2):
                    print(f"Error: LoRA file 2 not found: {args.lora_file_2}")
                    return 1
                lora_configs.append((args.lora_file_2, args.multiplier_2))

            if args.lora_file_3:
                if not os.path.exists(args.lora_file_3):
                    print(f"Error: LoRA file 3 not found: {args.lora_file_3}")
                    return 1
                lora_configs.append((args.lora_file_3, args.multiplier_3))

            output_path = merge_multiple_loras(args.model_name, lora_configs, args.output_dir)

        print("\n" + "="*60)
        print("MERGE COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"Merged model saved to: {output_path}")
        print("\nThe merged model is saved in HuggingFace cache format and includes:")
        print("- Blob storage for efficient file management")
        print("- Proper snapshot and refs structure")
        print("- Compatible with HuggingFace model loading")
        print("- No runtime LoRA loading required")

        return 0

    except Exception as e:
        print(f"Error during merge: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
