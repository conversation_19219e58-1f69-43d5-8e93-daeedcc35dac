#!/usr/bin/env python
"""
Media Cropper - Trim media files with precise time control
Supports decimal seconds (e.g., 0.1, 1.5) for accurate trimming
"""
import os
import sys
import subprocess

def get_duration(file_path):
    """Get the duration of a media file using ffprobe"""
    cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration',
           '-of', 'default=noprint_wrappers=1:nokey=1', file_path]
    result = subprocess.run(cmd, capture_output=True, text=True)
    return float(result.stdout.strip())

def parse_time(time_str):
    """Parse time in seconds or M:SS format, supporting decimal values"""
    if not time_str:
        return 0.0

    if ':' in time_str:
        parts = time_str.split(':')
        minutes = float(parts[0])
        seconds = float(parts[1])
        return minutes * 60 + seconds
    else:
        return float(time_str)

def format_time_display(seconds):
    """Format seconds as M:SS for display"""
    minutes = int(seconds // 60)
    secs = seconds % 60
    return f"{minutes}:{int(secs):02d}"

def main():
    # Get input file
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        print("No file was provided.")
        print(f"Usage: {os.path.basename(__file__)} path/to/your/media/file.mp4")
        input_file = input("Please enter the full path to your media file: ").strip()

    # Check if file exists
    if not os.path.exists(input_file):
        print("The specified file does not exist.")
        input("Press Enter to exit...")
        return

    # Check file extension
    file_extension = os.path.splitext(input_file)[1].lower()
    supported_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.mp3', '.wav', '.m4a', '.aac', '.flac']

    if file_extension not in supported_extensions:
        print(f"Unsupported file type: {file_extension}")
        print(f"Supported formats: {', '.join(supported_extensions)}")
        input("Press Enter to exit...")
        return

    # Get media duration
    try:
        total_duration = get_duration(input_file)
        print(f"Total media duration: {format_time_display(total_duration)} ({total_duration:.6f} seconds)")
    except Exception as e:
        print(f"Error getting media duration: {e}")
        input("Press Enter to exit...")
        return

    # Get start time
    print("Enter time in seconds (e.g., 150 or 150.5) or m:ss format (e.g., 2:30 or 2:30.5) or start+duration format (e.g., 2:30.5+10.5)")
    print("Leave blank to start from the beginning (0 seconds)")
    time_input = input("Enter start time: ").strip()

    # Check if input contains a plus sign (start+duration format)
    if '+' in time_input:
        time_parts = time_input.split('+')
        start_time = parse_time(time_parts[0]) if time_parts[0].strip() else 0.0
        duration = parse_time(time_parts[1]) if time_parts[1].strip() else total_duration
        end_time = start_time + duration
    else:
        # Use 0 as default start time if nothing entered
        start_time = parse_time(time_input) if time_input else 0.0

        # Get end time
        print("Enter end time in seconds (e.g., 150 or 150.5) or m:ss format (e.g., 2:30 or 2:30.5):")
        print("Leave blank to use the end of the file")
        end_input = input("Enter the time to stop at: ").strip()

        # Use total duration as default end time if nothing entered
        end_time = parse_time(end_input) if end_input else total_duration
        duration = end_time - start_time

    # Validate and adjust times
    # Make sure start time is not negative
    if start_time < 0:
        print("Start time cannot be negative. Using 0 instead.")
        start_time = 0.0

    # Make sure start time is not beyond the file duration
    if start_time >= total_duration:
        print("Start time cannot be greater than or equal to the total duration.")
        print(f"Maximum allowed start time is {total_duration - 0.1:.1f} seconds.")
        input("Press Enter to exit...")
        return

    # Make sure end time is not beyond the file duration
    if end_time > total_duration:
        print(f"Warning: End time ({end_time:.2f}s) exceeds the file length ({total_duration:.2f}s).")
        print("Will trim until the end of the file.")
        end_time = total_duration

    # Make sure end time is after start time
    if end_time <= start_time:
        print(f"Error: End time ({end_time:.2f}s) must be greater than start time ({start_time:.2f}s).")
        input("Press Enter to exit...")
        return

    # Recalculate duration after all adjustments
    duration = end_time - start_time

    # Show the selected time range
    print(f"\nSelected time range: {start_time:.2f}s to {end_time:.2f}s (Duration: {duration:.2f}s)")

    # Generate output filename with clean decimal formatting
    base_name = os.path.splitext(input_file)[0]

    # Format start and end times with 1 decimal place
    start_str = f"{start_time:.1f}".rstrip('0').rstrip('.') if start_time == int(start_time) else f"{start_time:.1f}"
    end_str = f"{end_time:.1f}".rstrip('0').rstrip('.') if end_time == int(end_time) else f"{end_time:.1f}"

    output_file = f"{base_name}_{start_str}s-to-{end_str}s{file_extension}"

    # Show processing message
    print()
    print(f"Processing {input_file}...")
    print(f"Extracting from {start_time} seconds to {end_time} seconds (Duration: {duration} seconds)")
    print("This may take a moment...")
    print()

    # Use ffmpeg to crop the media file
    try:
        print("Using re-encoding method...")
        subprocess.run([
            'ffmpeg', '-ss', str(start_time), '-i', input_file,
            '-t', str(duration), '-c:v', 'libx264', '-c:a', 'aac',
            '-strict', 'experimental', output_file
        ])

        # Check if the operation was successful
        if os.path.exists(output_file):
            print()
            print("Media file successfully trimmed.")
            print(f"Extracted {duration} seconds starting from {start_time} seconds.")
            print(f"Output file: {output_file}")

            # Check if decimal precision was used
            if start_time != int(start_time) or end_time != int(end_time):
                print("Note: Decimal precision was used for accurate time selection.")
        else:
            print()
            print("An error occurred while trimming the media file.")
    except Exception as e:
        print(f"Error: {e}")

    input("Press Enter to exit...")

if __name__ == "__main__":
    print("Media Cropper starting...")
    print(f"Arguments: {sys.argv}")
    try:
        main()
    except Exception as e:
        print(f"Unhandled error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
