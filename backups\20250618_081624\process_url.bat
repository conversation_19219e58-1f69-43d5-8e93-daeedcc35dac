@echo off
setlocal enabledelayedexpansion
title FramePack URL Processor

REM This script is designed to handle URLs with query parameters
REM It should be called with the URL as a single quoted argument

REM Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

REM Create temp directory if it doesn't exist
if not exist "%SCRIPT_DIR%temp" (
    echo [URL_PROCESSOR] Creating temp directory
    mkdir "%SCRIPT_DIR%temp"
)

REM Check if URL was provided
if "%~1"=="" (
    echo [URL_PROCESSOR] No URL provided. Please provide a URL to an image.
    echo [URL_PROCESSOR] Usage: %~nx0 "https://example.com/image.jpg"
    exit /b 1
)

REM Get the URL from command line
set "url=%~1"

echo [URL_PROCESSOR] Processing URL: %url%

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Download the image
call venv\Scripts\python.exe download_url_image.py "%url%" > "%SCRIPT_DIR%temp\url_output.txt"

REM Check the result
set "found_path="
for /f "tokens=1,* delims==" %%a in (%SCRIPT_DIR%temp\url_output.txt) do (
    if "%%a"=="DOWNLOADED_PATH" (
        echo [URL_PROCESSOR] Downloaded image to: %%b
        set "downloaded_path=%%b"
        set "found_path=1"
    ) else (
        echo [URL_PROCESSOR] %%a %%b
    )
)

if not defined found_path (
    echo [URL_PROCESSOR] Error: Failed to download image from URL
    type "%SCRIPT_DIR%temp\url_output.txt"
    exit /b 1
)

REM Create a temporary file to store the file path
set "temp_file=%SCRIPT_DIR%temp\framepack_temp_files.txt"
if exist "%temp_file%" del "%temp_file%"
echo %downloaded_path%> "%temp_file%"

echo [URL_PROCESSOR] URL processing completed successfully!
exit /b 0
