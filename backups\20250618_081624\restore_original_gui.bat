@echo off
echo FramePack GUI Restore
echo ===================
echo.

echo This script will restore the original framepack_gui.py file from backup.
echo.

if not exist framepack_gui.py.bak (
    echo Error: Backup file framepack_gui.py.bak not found.
    echo Cannot restore the original file.
    goto :end
)

echo Restoring framepack_gui.py from backup...
copy framepack_gui.py.bak framepack_gui.py
if %ERRORLEVEL% neq 0 (
    echo Error: Failed to restore the original file.
    goto :end
)

echo Original file restored successfully.

:end
pause
