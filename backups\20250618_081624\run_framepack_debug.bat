@echo off
echo FramePack GUI Debug Version
echo =========================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    goto :end
)

echo Checking tkinterdnd2 installation...
python -c "import tkinterdnd2" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Warning: tkinterdnd2 is not installed. Attempting to install it...
    python -m pip install tkinterdnd2
    if %ERRORLEVEL% neq 0 (
        echo Error: Failed to install tkinterdnd2. Drag and drop will not work.
    ) else (
        echo Successfully installed tkinterdnd2.
    )
)

echo.
echo Starting FramePack GUI Debug Version...
echo (This version includes additional debugging information)
echo.

REM Run the debug version of the Python script
python framepack_gui_debug.py %*

:end
pause
