@echo off
echo FramePack GUI with URL Drag and Drop Support (Fixed)
echo ===============================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    goto :end
)

echo Checking tkinterdnd2 installation...
python -c "import tkinterdnd2" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Warning: tkinterdnd2 is not installed. Attempting to install it...
    
    REM Activate virtual environment if it exists
    if exist "venv\Scripts\activate.bat" (
        echo Activating virtual environment...
        call venv\Scripts\activate.bat
        
        echo Installing tkinterdnd2 in virtual environment...
        pip install tkinterdnd2
        
        if %ERRORLEVEL% neq 0 (
            echo Error: Failed to install tkinterdnd2 in virtual environment.
            echo Drag and drop will not work.
        ) else (
            echo Successfully installed tkinterdnd2 in virtual environment.
        )
    ) else (
        echo Installing tkinterdnd2 globally...
        python -m pip install tkinterdnd2
        
        if %ERRORLEVEL% neq 0 (
            echo Error: Failed to install tkinterdnd2 globally.
            echo Drag and drop will not work.
        ) else (
            echo Successfully installed tkinterdnd2 globally.
        )
    )
)

echo.
echo Starting FramePack GUI with URL drag and drop support...
echo.
echo You can now:
echo - Drag and drop image files from your file explorer
echo - Drag and drop image URLs from your browser
echo - Use the "Process URLs" radio button to switch to URL mode
echo.

REM Run the Python script
python framepack_gui.py %*

:end
pause
