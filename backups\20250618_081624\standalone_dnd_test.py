"""
Standalone drag and drop test for FramePack.
This script creates a minimal window with a listbox that supports drag and drop.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox

# Print Python version and path for debugging
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Current working directory: {os.getcwd()}")

try:
    print("Attempting to import tkinterdnd2...")
    from tkinterdnd2 import TkinterDnD, DND_FILES
    TKDND_AVAILABLE = True
    print("TkinterDnD2 loaded successfully. Drag and drop is enabled.")
except ImportError as e:
    print(f"TkinterDnD2 import error: {e}")
    print("TkinterDnD2 not available. Drag and drop will be disabled.")
    print("To enable drag and drop, install tkinterdnd2: pip install tkinterdnd2")
    TKDND_AVAILABLE = False

class StandaloneDnDTest:
    def __init__(self, root):
        self.root = root
        self.root.title("FramePack Standalone Drag & Drop Test")
        self.root.geometry("500x400")
        
        # Create main frame
        main_frame = ttk.Frame(root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create a label
        ttk.Label(main_frame, text="Drag and drop files onto the listbox:", font=("Arial", 12)).pack(pady=(0, 10))
        
        # Create a frame for the listbox with a visible border
        self.listbox_frame = ttk.Frame(main_frame, borderwidth=2, relief="solid")
        self.listbox_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create the listbox
        self.files_listbox = tk.Listbox(self.listbox_frame, width=50, height=10)
        self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)
        
        # Add a scrollbar
        scrollbar = ttk.Scrollbar(self.listbox_frame, orient="vertical", command=self.files_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.files_listbox.configure(yscrollcommand=scrollbar.set)
        
        # Add a status label
        self.status_label = ttk.Label(main_frame, text="Ready")
        self.status_label.pack(pady=10)
        
        # Add buttons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(buttons_frame, text="Clear List", command=self.clear_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Exit", command=root.destroy).pack(side=tk.RIGHT, padx=5)
        
        # Register drop targets if tkinterdnd2 is available
        if TKDND_AVAILABLE:
            # Register the listbox as a drop target
            self.files_listbox.drop_target_register(DND_FILES)
            self.files_listbox.dnd_bind('<<Drop>>', self.drop_files)
            self.files_listbox.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.files_listbox.dnd_bind('<<DragLeave>>', self.drag_leave)
            
            # Also register the listbox frame
            self.listbox_frame.drop_target_register(DND_FILES)
            self.listbox_frame.dnd_bind('<<Drop>>', self.drop_files)
            self.listbox_frame.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.listbox_frame.dnd_bind('<<DragLeave>>', self.drag_leave)
            
            # Update status label
            self.status_label.config(text="Drag and drop is ENABLED", foreground="green")
        else:
            # Update status label
            self.status_label.config(text="Drag and drop is DISABLED", foreground="red")
    
    def drop_files(self, event):
        """Handle files dropped onto the widget"""
        try:
            # Get the dropped data (file paths)
            file_paths = self.root.tk.splitlist(event.data)
            print(f"Received drop event with data: {event.data}")
            
            # Clear the listbox
            self.files_listbox.delete(0, tk.END)
            
            # Add each file to the listbox
            for file_path in file_paths:
                # Remove quotes if present
                if file_path.startswith('"') and file_path.endswith('"'):
                    file_path = file_path[1:-1]
                
                # Normalize path
                file_path = os.path.normpath(file_path)
                
                # Add to listbox
                self.files_listbox.insert(tk.END, os.path.basename(file_path))
                print(f"Added file: {file_path}")
            
            # Update status
            self.status_label.config(text=f"Added {len(file_paths)} files", foreground="green")
            
            # Show a message box
            messagebox.showinfo("Files Added", f"Added {len(file_paths)} files to the list")
        except Exception as e:
            print(f"Error during drop: {str(e)}")
            import traceback
            traceback.print_exc()
            self.status_label.config(text=f"Error: {str(e)}", foreground="red")
        finally:
            # Restore normal appearance
            self.files_listbox.config(background="white")
            self.listbox_frame.config(relief="solid", borderwidth=2)
    
    def drag_enter(self, event):
        """Handle drag enter event"""
        try:
            print(f"Drag enter event on {event.widget}")
            
            # Change appearance to indicate it's a drop target
            self.files_listbox.config(background="#e0f0ff")  # Light blue background
            self.listbox_frame.config(relief="groove", borderwidth=3)
            self.status_label.config(text="Drop files here!", foreground="blue")
        except Exception as e:
            print(f"Error in drag_enter: {str(e)}")
    
    def drag_leave(self, event):
        """Handle drag leave event"""
        try:
            print(f"Drag leave event on {event.widget}")
            
            # Restore normal appearance
            self.files_listbox.config(background="white")
            self.listbox_frame.config(relief="solid", borderwidth=2)
            self.status_label.config(text="Ready", foreground="green")
        except Exception as e:
            print(f"Error in drag_leave: {str(e)}")
    
    def clear_list(self):
        """Clear the listbox"""
        self.files_listbox.delete(0, tk.END)
        self.status_label.config(text="List cleared", foreground="black")

def main():
    # Use TkinterDnD.Tk() if available, otherwise fallback to standard tk.Tk()
    if TKDND_AVAILABLE:
        print("Creating TkinterDnD.Tk() window")
        root = TkinterDnD.Tk()
    else:
        print("Creating standard tk.Tk() window")
        root = tk.Tk()
    
    app = StandaloneDnDTest(root)
    root.mainloop()

if __name__ == "__main__":
    main()
