#!/usr/bin/env python
"""
Test script to verify the fix for the auto_sorter.py issue with seconds indicators.
This script creates test files with various sizes and naming patterns to simulate
the real-world scenario where videos with seconds indicators might be smaller.
"""

import os
import sys
import shutil
import tempfile
from auto_sorter import auto_sort_after_generation, get_largest_file, group_files

def create_test_files(directory, file_info):
    """
    Create test files in the specified directory.
    file_info is a list of tuples (filename, size)
    """
    os.makedirs(directory, exist_ok=True)
    
    for filename, size in file_info:
        file_path = os.path.join(directory, filename)
        # Create a file with the specified size
        with open(file_path, 'wb') as f:
            f.write(b'X' * size)
        
        print(f"Created {filename} with size {size} bytes")
    
    print(f"Created {len(file_info)} test files in {directory}")

def test_seconds_indicator_fix():
    """Test that the sorter correctly identifies the largest file even when seconds indicators are present."""
    print("\n=== Testing seconds indicator fix ===")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create outputs directory
        outputs_dir = os.path.join(temp_dir, "outputs")
        os.makedirs(outputs_dir, exist_ok=True)
        
        # Create test files in outputs directory with different sizes
        # Simulate the real-world scenario where files with seconds indicators are smaller
        test_files = [
            # Group 1: Same base_id, with and without seconds indicators
            ("250420_121919_242_3623_37.mp4", 5000),  # No seconds indicator, largest
            ("250420_121919_242_3623_38_4s.mp4", 3000),  # With seconds indicator, smaller
            ("250420_121919_242_3623_39.mp4", 4000),  # No seconds indicator, medium
            
            # Group 2: Same base_id with seed, with and without seconds indicators
            ("250421_121919_242_3623_37_seed123456.mp4", 6000),  # No seconds indicator, largest
            ("250421_121919_242_3623_38_seed123456_4s.mp4", 2000),  # With seconds indicator, smallest
            ("250421_121919_242_3623_39_seed123456.mp4", 4000),  # No seconds indicator, medium
            
            # Group 3: Generic filename
            ("my_video.mp4", 7000),  # No seconds indicator, largest
            ("my_video_4s.mp4", 3500),  # With seconds indicator, smaller
            
            # Group 4: Mixed case to test regex
            ("MixedCase_Video.mp4", 9000),  # No seconds indicator, largest
            ("MixedCase_Video_4s.mp4", 4500),  # With seconds indicator, smaller
        ]
        create_test_files(outputs_dir, test_files)
        
        # Run auto_sort_after_generation
        print("\nRunning auto_sort_after_generation:")
        num_copied, num_deleted = auto_sort_after_generation(outputs_dir)
        
        # Check the sorted directory
        sorted_dir = os.path.join(outputs_dir, "sorted")
        if os.path.exists(sorted_dir):
            sorted_files = os.listdir(sorted_dir)
            print(f"\nFiles in sorted directory: {len(sorted_files)}")
            
            # Expected files (largest from each group)
            expected_files = [
                "250420_121919_242_3623_37.mp4",  # Largest from Group 1
                "250421_121919_242_3623_37_seed123456.mp4",  # Largest from Group 2
                "my_video.mp4",  # Largest from Group 3
                "MixedCase_Video.mp4",  # Largest from Group 4
            ]
            
            # Check if we have the expected number of files
            if len(sorted_files) == len(expected_files):
                print(f"✅ Sorted directory has the expected number of files ({len(expected_files)})")
            else:
                print(f"❌ Sorted directory has {len(sorted_files)} files, expected {len(expected_files)}")
            
            # Check if the largest files from each group were copied
            for expected_file in expected_files:
                if expected_file in sorted_files:
                    print(f"✅ Found expected file in sorted directory: {expected_file}")
                else:
                    print(f"❌ Missing expected file in sorted directory: {expected_file}")
                    # Show what file was copied instead
                    for sorted_file in sorted_files:
                        if sorted_file.startswith(expected_file.split("_")[0]):
                            print(f"   Found instead: {sorted_file}")
            
            # Check if any unexpected files were copied
            for sorted_file in sorted_files:
                if sorted_file not in expected_files:
                    print(f"❌ Unexpected file in sorted directory: {sorted_file}")
        else:
            print("❌ Sorted directory was not created")

if __name__ == "__main__":
    test_seconds_indicator_fix()
    
    print("\nTest completed.")
