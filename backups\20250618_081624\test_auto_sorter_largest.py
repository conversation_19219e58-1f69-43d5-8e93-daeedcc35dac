#!/usr/bin/env python
"""
Test script for auto_sorter.py to verify that the largest file in each group is being copied.
This script tests that:
1. Files are correctly grouped by base_id and seed
2. The largest file in each group is copied to the sorted directory
"""

import os
import sys
import shutil
import tempfile
from auto_sorter import auto_sort_after_generation

def create_test_files(directory, file_info):
    """
    Create test files in the specified directory.
    file_info is a list of tuples (filename, size)
    """
    os.makedirs(directory, exist_ok=True)
    
    for filename, size in file_info:
        file_path = os.path.join(directory, filename)
        # Create a file with the specified size
        with open(file_path, 'wb') as f:
            f.write(b'X' * size)
        
        print(f"Created {filename} with size {size} bytes")
    
    print(f"Created {len(file_info)} test files in {directory}")

def test_sort_largest_file():
    """Test that auto_sort_after_generation copies the largest file in each group."""
    print("\n=== Testing that the largest file in each group is copied ===")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create outputs directory
        outputs_dir = os.path.join(temp_dir, "outputs")
        os.makedirs(outputs_dir, exist_ok=True)
        
        # Create test files in outputs directory with different sizes
        # Group 1: Files with the same base_id and no seed
        # Group 2: Files with the same base_id and seed
        test_files = [
            # Group 1: Same base_id (250420_121919_242_3623), different suffixes, no seed
            ("250420_121919_242_3623_37.mp4", 1000),  # Smallest
            ("250420_121919_242_3623_38.mp4", 2000),  # Medium
            ("250420_121919_242_3623_39.mp4", 3000),  # Largest - should be copied
            
            # Group 2: Same base_id (250421_121919_242_3623), different suffixes, same seed
            ("250421_121919_242_3623_37_seed123456.mp4", 4000),  # Largest - should be copied
            ("250421_121919_242_3623_38_seed123456.mp4", 2500),  # Medium
            ("250421_121919_242_3623_39_seed123456.mp4", 1500),  # Smallest
            
            # Group 3: Different base_id
            ("250422_121919_242_3623_37.mp4", 5000)  # Only one file - should be copied
        ]
        create_test_files(outputs_dir, test_files)
        
        # Run auto_sort_after_generation
        print("\nRunning auto_sort_after_generation:")
        num_copied, num_deleted = auto_sort_after_generation(outputs_dir)
        
        # Check the sorted directory
        sorted_dir = os.path.join(outputs_dir, "sorted")
        if os.path.exists(sorted_dir):
            sorted_files = os.listdir(sorted_dir)
            print(f"\nFiles in sorted directory: {len(sorted_files)}")
            
            # We should have 3 files in the sorted directory (one for each group)
            expected_count = 3
            if len(sorted_files) == expected_count:
                print(f"✅ Sorted directory has the expected number of files ({expected_count})")
            else:
                print(f"❌ Sorted directory has {len(sorted_files)} files, expected {expected_count}")
            
            # Check if the largest files from each group were copied
            expected_files = [
                "250420_121919_242_3623_39.mp4",  # Largest from Group 1
                "250421_121919_242_3623_37_seed123456.mp4",  # Largest from Group 2
                "250422_121919_242_3623_37.mp4"   # Only file from Group 3
            ]
            
            for expected_file in expected_files:
                if expected_file in sorted_files:
                    print(f"✅ Found expected file in sorted directory: {expected_file}")
                else:
                    print(f"❌ Missing expected file in sorted directory: {expected_file}")
            
            # Check if any unexpected files were copied
            for sorted_file in sorted_files:
                if sorted_file not in expected_files:
                    print(f"❌ Unexpected file in sorted directory: {sorted_file}")
        else:
            print("❌ Sorted directory was not created")

if __name__ == "__main__":
    test_sort_largest_file()
    
    print("\nAll tests completed.")
