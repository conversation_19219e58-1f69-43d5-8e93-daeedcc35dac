import os
import sys
import tkinter as tk
from tkVideoPlayer import TkinterVideo
import time
import cv2
import numpy as np

def create_test_avi(output_path, frames=10, width=256, height=256, fps=12.5):
    """Create a simple test AVI with numbered frames to verify playback"""
    # Create a video writer
    fourcc = cv2.VideoWriter_fourcc(*'MJPG')  # MJPG codec is widely compatible
    video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    if not video_writer.isOpened():
        print(f"Error: Could not initialize video writer for {output_path}")
        return None
    
    # Create frames with frame numbers
    for i in range(frames):
        # Create a blank frame
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add a colored rectangle that changes with each frame
        color = ((i * 25) % 255, (i * 50) % 255, (i * 100) % 255)
        cv2.rectangle(frame, (50, 50), (width-50, height-50), color, -1)
        
        # Add frame number text
        cv2.putText(frame, f"Frame {i+1}", (width//4, height//2), 
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Write the frame
        video_writer.write(frame)
    
    # Release the video writer
    video_writer.release()
    print(f"Created test AVI: {output_path}")
    return output_path

class VideoPlayerApp:
    def __init__(self, root, video_path):
        self.root = root
        self.root.title("AVI Player Test")
        self.root.geometry("800x600")
        
        # Create a frame for the video player
        self.video_frame = tk.Frame(root)
        self.video_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create the video player
        self.player = TkinterVideo(self.video_frame, scaled=True)
        self.player.pack(fill="both", expand=True)
        
        # Create control buttons
        self.control_frame = tk.Frame(root)
        self.control_frame.pack(fill="x", padx=10, pady=10)
        
        self.play_button = tk.Button(self.control_frame, text="Play", command=self.play)
        self.play_button.pack(side="left", padx=5)
        
        self.pause_button = tk.Button(self.control_frame, text="Pause", command=self.pause)
        self.pause_button.pack(side="left", padx=5)
        
        self.stop_button = tk.Button(self.control_frame, text="Stop", command=self.stop)
        self.stop_button.pack(side="left", padx=5)
        
        self.reload_button = tk.Button(self.control_frame, text="Reload", command=self.reload)
        self.reload_button.pack(side="left", padx=5)
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_label = tk.Label(root, textvariable=self.status_var, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side="bottom", fill="x")
        
        # Load the video
        self.video_path = video_path
        self.load_video()
        
        # Bind events
        self.player.bind("<<Loaded>>", self.on_loaded)
        self.player.bind("<<Ended>>", self.on_ended)
        
    def load_video(self):
        try:
            self.status_var.set(f"Loading video: {self.video_path}")
            self.player.load(self.video_path)
            self.status_var.set(f"Loaded video: {self.video_path}")
        except Exception as e:
            self.status_var.set(f"Error loading video: {e}")
    
    def play(self):
        try:
            self.player.play()
            self.status_var.set("Playing")
        except Exception as e:
            self.status_var.set(f"Error playing: {e}")
    
    def pause(self):
        try:
            self.player.pause()
            self.status_var.set("Paused")
        except Exception as e:
            self.status_var.set(f"Error pausing: {e}")
    
    def stop(self):
        try:
            self.player.stop()
            self.status_var.set("Stopped")
        except Exception as e:
            self.status_var.set(f"Error stopping: {e}")
    
    def reload(self):
        try:
            self.player.stop()
            time.sleep(0.5)  # Give it time to stop
            self.load_video()
            self.player.play()
            self.status_var.set("Reloaded and playing")
        except Exception as e:
            self.status_var.set(f"Error reloading: {e}")
    
    def on_loaded(self, event):
        info = self.player.video_info()
        self.status_var.set(f"Video loaded. Duration: {info.get('duration', 'unknown')}s, FPS: {info.get('framerate', 'unknown')}")
        # Auto-play when loaded
        self.player.play()
    
    def on_ended(self, event):
        self.status_var.set("Video ended")
        # Auto-replay
        self.player.play()

def main():
    # Create test AVI
    test_avi_path = "test_video.avi"
    test_avi_path = create_test_avi(test_avi_path)
    
    if not test_avi_path:
        print("Failed to create test AVI")
        return
    
    # Check if a specific video path was provided
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
    else:
        # Default to the test AVI
        video_path = test_avi_path
    
    # Create the Tkinter app
    root = tk.Tk()
    app = VideoPlayerApp(root, video_path)
    root.mainloop()

if __name__ == "__main__":
    main()
