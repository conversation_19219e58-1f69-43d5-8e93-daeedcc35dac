@echo off
setlocal enabledelayedexpansion

echo Testing framepack_batch_auto.bat compatibility with batch.py changes...
echo.

REM Create a backup of the current settings
echo Creating backup of framepack_default_settings.json...
copy framepack_default_settings.json framepack_default_settings.json.bak

REM Run the batch auto script
echo Running framepack_batch_auto.bat...
call framepack_batch_auto.bat

REM Restore the backup
echo Restoring backup of framepack_default_settings.json...
copy framepack_default_settings.json.bak framepack_default_settings.json
del framepack_default_settings.json.bak

echo.
echo Test completed.
pause
