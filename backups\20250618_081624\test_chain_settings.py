#!/usr/bin/env python3
"""
Test script to verify prompt chain settings functionality
"""

import json
import sys
import os
from pathlib import Path

def test_chain_settings_structure():
    """Test the new chain settings data structure"""
    
    print("Testing chain settings data structure...")
    
    # Simulate the new chain step format
    chain_step = {
        "prompt": "A beautiful sunset over mountains",
        "settings": {
            "seed": 12345,
            "video_length": 15.0,
            "steps": 50,
            "cfg": 2.0,
            "distilled_cfg": 8.0,
            "model_type": "F1",
            "latent_window_size": 32,
            "use_teacache": False,
            "gpu_memory": 24.0,
            "mp4_crf": 16,
            "negative_prompt": "blurry, low quality",
            "use_image_prompt": True,
            "overwrite": True,
            "fix_encoding": True,
            "copy_to_input": False
        }
    }
    
    # Test that we can access all the expected fields
    assert "prompt" in chain_step
    assert "settings" in chain_step
    assert isinstance(chain_step["settings"], dict)
    
    # Test specific settings
    settings = chain_step["settings"]
    assert settings["seed"] == 12345
    assert settings["video_length"] == 15.0
    assert settings["model_type"] == "F1"
    assert settings["use_teacache"] == False
    
    print("✅ Chain settings data structure is correct")
    return True

def test_chain_display_format():
    """Test the chain display formatting"""
    
    print("\nTesting chain display formatting...")
    
    # Simulate chain steps with different settings
    chain_steps = [
        {
            "prompt": "A peaceful forest clearing at dawn with soft morning light",
            "settings": {
                "seed": 123,
                "video_length": 9.0,
                "model_type": "FramePack"
            }
        },
        {
            "prompt": "The same forest clearing as storm clouds gather overhead",
            "settings": {
                "seed": 456,
                "video_length": 12.0,
                "model_type": "F1"
            }
        },
        {
            "prompt": "The forest clearing during a dramatic thunderstorm with lightning",
            "settings": {
                "seed": 789,
                "video_length": 6.0,
                "model_type": "FramePack"
            }
        }
    ]
    
    # Test display formatting
    for i, chain_step in enumerate(chain_steps):
        prompt = chain_step["prompt"]
        settings = chain_step["settings"]
        
        # Truncate long prompts for display (45 chars max)
        display_prompt = prompt if len(prompt) <= 45 else prompt[:42] + "..."
        
        # Add key settings info
        seed = settings.get("seed", -1)
        video_length = settings.get("video_length", 9.0)
        model = settings.get("model_type", "FramePack")
        
        # Create display string with settings info
        settings_info = f"[{model}, {video_length}s, seed:{seed}]"
        display_text = f"{i+1}. {display_prompt} {settings_info}"
        
        print(f"Step {i+1}: {display_text}")
        
        # Verify the display text contains expected elements
        assert f"{i+1}." in display_text
        assert model in display_text
        assert f"{video_length}s" in display_text
        assert f"seed:{seed}" in display_text
    
    print("✅ Chain display formatting works correctly")
    return True

def test_legacy_conversion():
    """Test conversion of legacy chain format to new format"""
    
    print("\nTesting legacy chain conversion...")
    
    # Simulate legacy chain (just strings)
    legacy_chain = [
        "A beautiful sunset",
        "The sunset turns to night",
        "Stars appear in the sky"
    ]
    
    # Simulate current settings for conversion
    current_settings = {
        "seed": -1,
        "video_length": 9.0,
        "steps": 30,
        "cfg": 1.0,
        "distilled_cfg": 7.0,
        "model_type": "FramePack",
        "latent_window_size": 16,
        "use_teacache": True,
        "gpu_memory": 24.0,
        "mp4_crf": 18,
        "negative_prompt": "",
        "use_image_prompt": True,
        "overwrite": False,
        "fix_encoding": True,
        "copy_to_input": False
    }
    
    # Convert legacy format to new format
    converted_chain = []
    for prompt in legacy_chain:
        chain_step = {
            "prompt": prompt,
            "settings": current_settings.copy()
        }
        converted_chain.append(chain_step)
    
    # Verify conversion
    assert len(converted_chain) == len(legacy_chain)
    for i, step in enumerate(converted_chain):
        assert "prompt" in step
        assert "settings" in step
        assert step["prompt"] == legacy_chain[i]
        assert isinstance(step["settings"], dict)
    
    print("✅ Legacy chain conversion works correctly")
    return True

def test_settings_serialization():
    """Test that chain settings can be serialized/deserialized"""
    
    print("\nTesting settings serialization...")
    
    # Create a chain with settings
    chain_with_settings = [
        {
            "prompt": "Test prompt 1",
            "settings": {
                "seed": 123,
                "video_length": 10.0,
                "model_type": "FramePack",
                "use_teacache": True
            }
        },
        {
            "prompt": "Test prompt 2", 
            "settings": {
                "seed": 456,
                "video_length": 15.0,
                "model_type": "F1",
                "use_teacache": False
            }
        }
    ]
    
    # Test JSON serialization
    try:
        json_str = json.dumps(chain_with_settings, indent=2)
        deserialized = json.loads(json_str)
        
        # Verify deserialized data matches original
        assert len(deserialized) == len(chain_with_settings)
        for i, step in enumerate(deserialized):
            original = chain_with_settings[i]
            assert step["prompt"] == original["prompt"]
            assert step["settings"]["seed"] == original["settings"]["seed"]
            assert step["settings"]["model_type"] == original["settings"]["model_type"]
        
        print("✅ Settings serialization works correctly")
        return True
        
    except Exception as e:
        print(f"❌ Settings serialization failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Prompt Chain Settings Test ===\n")
    
    success = True
    
    # Test chain settings structure
    if not test_chain_settings_structure():
        success = False
    
    # Test chain display formatting
    if not test_chain_display_format():
        success = False
    
    # Test legacy conversion
    if not test_legacy_conversion():
        success = False
    
    # Test settings serialization
    if not test_settings_serialization():
        success = False
    
    print("\n=== Test Results ===")
    if success:
        print("✅ All tests passed! Chain settings functionality is working.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
