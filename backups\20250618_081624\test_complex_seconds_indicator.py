#!/usr/bin/env python
"""
More complex test script to verify the issue with the seconds indicator in auto_sorter.py.
This script creates test files with various naming patterns and sizes to better
replicate real-world scenarios where the sorter might be failing.
"""

import os
import sys
import shutil
import tempfile
from auto_sorter import auto_sort_after_generation, get_largest_file, group_files, parse_filename

def create_test_files(directory, file_info):
    """
    Create test files in the specified directory.
    file_info is a list of tuples (filename, size)
    """
    os.makedirs(directory, exist_ok=True)
    
    for filename, size in file_info:
        file_path = os.path.join(directory, filename)
        # Create a file with the specified size
        with open(file_path, 'wb') as f:
            f.write(b'X' * size)
        
        print(f"Created {filename} with size {size} bytes")
    
    print(f"Created {len(file_info)} test files in {directory}")

def test_parse_filename_function():
    """Test the parse_filename function with various filename formats."""
    print("\n=== Testing parse_filename function ===")
    
    test_cases = [
        # Standard timestamp format
        ("250420_121919_242_3623_37.mp4", ("250420_121919_242_3623", None)),
        ("250420_121919_242_3623_37_24s.mp4", ("250420_121919_242_3623", None)),
        ("250426_085120_706_7278_19_seed357798872.mp4", ("250426_085120_706_7278", "357798872")),
        ("250426_085120_706_7278_19_seed357798872_24s.mp4", ("250426_085120_706_7278", "357798872")),
        
        # Generic filename format
        ("image_name.mp4", ("image_name", None)),
        ("image_name_5s.mp4", ("image_name", None)),
        ("image_name_seed123456.mp4", ("image_name", "123456")),
        ("image_name_seed123456_5s.mp4", ("image_name", "123456")),
        
        # Edge cases
        ("image_name_with_underscores.mp4", ("image_name_with_underscores", None)),
        ("image_name_with_underscores_5s.mp4", ("image_name_with_underscores", None)),
        ("image_name_with_underscores_seed123456.mp4", ("image_name_with_underscores", "123456")),
        ("image_name_with_underscores_seed123456_5s.mp4", ("image_name_with_underscores", "123456")),
        
        # Invalid formats
        ("image.jpg", None),
        ("invalid_format", None)
    ]
    
    for filename, expected in test_cases:
        result = parse_filename(filename)
        status = "✅" if result == expected else "❌"
        print(f"{status} {filename}: Got {result}, Expected {expected}")

def test_complex_seconds_indicator_issue():
    """Test with more complex filename patterns and sizes."""
    print("\n=== Testing complex seconds indicator issue ===")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create outputs directory
        outputs_dir = os.path.join(temp_dir, "outputs")
        os.makedirs(outputs_dir, exist_ok=True)
        
        # Create test files with various naming patterns and sizes
        test_files = [
            # Group 1: Standard timestamp format
            ("250420_121919_242_3623_37.mp4", 3000),  # Medium
            ("250420_121919_242_3623_38_4s.mp4", 5000),  # Largest with seconds
            ("250420_121919_242_3623_39.mp4", 1000),  # Small
            
            # Group 2: Timestamp with seed
            ("250421_121919_242_3623_37_seed123456.mp4", 2000),  # Medium
            ("250421_121919_242_3623_38_seed123456_4s.mp4", 6000),  # Largest with seconds
            ("250421_121919_242_3623_39_seed123456.mp4", 1500),  # Small
            
            # Group 3: Generic filename
            ("my_video.mp4", 4000),  # Largest
            ("my_video_4s.mp4", 2500),  # Medium with seconds
            
            # Group 4: Generic filename with seed
            ("another_video_seed987654.mp4", 7000),  # Largest
            ("another_video_seed987654_4s.mp4", 3500),  # Medium with seconds
            
            # Group 5: Filename with underscores
            ("my_long_video_name.mp4", 8000),  # Largest
            ("my_long_video_name_4s.mp4", 4000),  # Medium with seconds
            
            # Group 6: Mixed case to test regex
            ("MixedCase_Video.mp4", 9000),  # Largest
            ("MixedCase_Video_4s.mp4", 4500),  # Medium with seconds
        ]
        create_test_files(outputs_dir, test_files)
        
        # Test direct grouping and largest file selection
        print("\nTesting direct grouping and largest file selection:")
        file_paths = [os.path.join(outputs_dir, filename) for filename, _ in test_files]
        groups = group_files(file_paths)
        
        for group_key, files_in_group in groups.items():
            base_id, seed = group_key
            seed_info = f" (seed: {seed})" if seed else ""
            print(f"\nGroup: {base_id}{seed_info} - {len(files_in_group)} files")
            
            # Sort files by size (largest first) for display
            sorted_files = sorted(files_in_group, key=os.path.getsize, reverse=True)
            
            # Display files with their sizes
            for i, file_path in enumerate(sorted_files):
                size = os.path.getsize(file_path)
                filename = os.path.basename(file_path)
                status = "LARGEST" if i == 0 else "smaller"
                print(f"  {i+1}. {filename} - {size} bytes - {status}")
            
            # Get the largest file
            largest_file = get_largest_file(files_in_group)
            largest_filename = os.path.basename(largest_file)
            largest_size = os.path.getsize(largest_file)
            print(f"  Largest file: {largest_filename} - {largest_size} bytes")
            
            # Check if the largest file has a seconds indicator
            has_seconds = "_s.mp4" in largest_filename
            print(f"  Has seconds indicator: {has_seconds}")
            
            # Verify the largest file is actually the largest by size
            expected_largest = sorted_files[0]
            expected_largest_filename = os.path.basename(expected_largest)
            if largest_file == expected_largest:
                print(f"  ✅ Correct largest file selected: {largest_filename}")
            else:
                print(f"  ❌ Wrong file selected as largest: {largest_filename}")
                print(f"     Expected largest: {expected_largest_filename} - {os.path.getsize(expected_largest)} bytes")
        
        # Run auto_sort_after_generation
        print("\nRunning auto_sort_after_generation:")
        num_copied, num_deleted = auto_sort_after_generation(outputs_dir)
        
        # Check the sorted directory
        sorted_dir = os.path.join(outputs_dir, "sorted")
        if os.path.exists(sorted_dir):
            sorted_files = os.listdir(sorted_dir)
            print(f"\nFiles in sorted directory: {len(sorted_files)}")
            
            # Expected files (largest from each group)
            expected_files = [
                "250420_121919_242_3623_38_4s.mp4",  # Largest from Group 1
                "250421_121919_242_3623_38_seed123456_4s.mp4",  # Largest from Group 2
                "my_video.mp4",  # Largest from Group 3
                "another_video_seed987654.mp4",  # Largest from Group 4
                "my_long_video_name.mp4",  # Largest from Group 5
                "MixedCase_Video.mp4",  # Largest from Group 6
            ]
            
            # Check if we have the expected number of files
            if len(sorted_files) == len(expected_files):
                print(f"✅ Sorted directory has the expected number of files ({len(expected_files)})")
            else:
                print(f"❌ Sorted directory has {len(sorted_files)} files, expected {len(expected_files)}")
            
            # Check if the largest files from each group were copied
            for expected_file in expected_files:
                if expected_file in sorted_files:
                    print(f"✅ Found expected file in sorted directory: {expected_file}")
                else:
                    print(f"❌ Missing expected file in sorted directory: {expected_file}")
                    # Show what file was copied instead
                    for sorted_file in sorted_files:
                        if sorted_file.startswith(expected_file.split("_")[0]):
                            print(f"   Found instead: {sorted_file}")
            
            # Check if any unexpected files were copied
            for sorted_file in sorted_files:
                if sorted_file not in expected_files:
                    print(f"❌ Unexpected file in sorted directory: {sorted_file}")
        else:
            print("❌ Sorted directory was not created")

if __name__ == "__main__":
    test_parse_filename_function()
    test_complex_seconds_indicator_issue()
    
    print("\nAll tests completed.")
