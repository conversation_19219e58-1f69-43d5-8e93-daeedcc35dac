"""
Simple test script for drag and drop functionality.
This creates a minimal window with a drop target to test if tkinterdnd2 is working correctly.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

try:
    from tkinterdnd2 import TkinterDnD, DND_FILES
    TKDND_AVAILABLE = True
except ImportError:
    print("TkinterDnD2 not available. Drag and drop will be disabled.")
    print("To enable drag and drop, install tkinterdnd2: pip install tkinterdnd2")
    TKDND_AVAILABLE = False

class SimpleDnDTest:
    def __init__(self, root):
        self.root = root
        self.root.title("Simple Drag & Drop Test")
        self.root.geometry("400x300")
        
        # Create a frame with a visible border
        self.frame = ttk.Frame(root, borderwidth=2, relief="solid")
        self.frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Add a label
        self.label = ttk.Label(self.frame, text="Drag and drop files here", font=("Arial", 14))
        self.label.pack(pady=20)
        
        # Create a listbox to show dropped files
        self.listbox = tk.Listbox(self.frame, width=50, height=10)
        self.listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Register drop targets if tkinterdnd2 is available
        if TKDND_AVAILABLE:
            # Register the frame as a drop target
            self.frame.drop_target_register(DND_FILES)
            self.frame.dnd_bind('<<Drop>>', self.drop_files)
            self.frame.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.frame.dnd_bind('<<DragLeave>>', self.drag_leave)
            
            # Register the listbox as a drop target
            self.listbox.drop_target_register(DND_FILES)
            self.listbox.dnd_bind('<<Drop>>', self.drop_files)
            self.listbox.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.listbox.dnd_bind('<<DragLeave>>', self.drag_leave)
            
            # Register the label as a drop target
            self.label.drop_target_register(DND_FILES)
            self.label.dnd_bind('<<Drop>>', self.drop_files)
            self.label.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.label.dnd_bind('<<DragLeave>>', self.drag_leave)
            
            # Also register the root window
            self.root.drop_target_register(DND_FILES)
            self.root.dnd_bind('<<Drop>>', self.drop_files)
            self.root.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.root.dnd_bind('<<DragLeave>>', self.drag_leave)
        else:
            ttk.Label(self.frame, text="Drag and drop not available", foreground="red").pack(pady=10)
    
    def drop_files(self, event):
        """Handle files dropped onto the widget"""
        # Get the dropped data (file paths)
        file_paths = self.root.tk.splitlist(event.data)
        
        # Clear the listbox
        self.listbox.delete(0, tk.END)
        
        # Add each file to the listbox
        for file_path in file_paths:
            self.listbox.insert(tk.END, os.path.basename(file_path))
        
        # Restore normal appearance
        self.frame.config(relief="solid")
        self.label.config(foreground="black", text="Drag and drop files here")
        
        # Show success message
        print(f"Dropped {len(file_paths)} files")
    
    def drag_enter(self, event):
        """Handle drag enter event"""
        # Change appearance to indicate it's a drop target
        self.frame.config(relief="groove", borderwidth=3)
        self.label.config(foreground="blue", text="Drop files here!")
        print("Drag entered")
    
    def drag_leave(self, event):
        """Handle drag leave event"""
        # Restore normal appearance
        self.frame.config(relief="solid", borderwidth=2)
        self.label.config(foreground="black", text="Drag and drop files here")
        print("Drag left")

def main():
    # Use TkinterDnD.Tk() if available, otherwise fallback to standard tk.Tk()
    if TKDND_AVAILABLE:
        root = TkinterDnD.Tk()
    else:
        root = tk.Tk()
    
    app = SimpleDnDTest(root)
    root.mainloop()

if __name__ == "__main__":
    main()
