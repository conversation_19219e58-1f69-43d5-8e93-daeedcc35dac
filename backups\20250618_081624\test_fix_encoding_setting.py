#!/usr/bin/env python
"""
Test script to verify that the fix_encoding setting is correctly respected.
This script creates a temporary settings file with fix_encoding=False and
runs batch.py with those settings to check if the fix_encoding parameter
is correctly passed.
"""

import os
import json
import tempfile
import subprocess
import sys

def test_fix_encoding_setting():
    """Test that the fix_encoding setting is correctly respected."""
    print("\n=== Testing fix_encoding setting ===")
    
    # Create a temporary settings file
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as temp_file:
        settings = {
            "input_dir": "test_input",
            "output_dir": "test_output",
            "fallback_prompt": "Test prompt",
            "seed": -1,
            "video_length": 3.0,
            "steps": 20,
            "distilled_cfg": 8.0,
            "gpu_memory": 6.0,
            "mp4_crf": 16,
            "use_teacache": True,
            "randomize_order": False,
            "clear_processed_list": True,
            "use_image_prompt": True,
            "overwrite": False,
            "fix_encoding": False,  # Set to False to test
            "use_prompt_list_file": False,
            "prompt_list_file": "prompt_list.txt",
            "copy_to_input": True,
            "allow_duplicates": False,
            "apply_all_prompts": False,
            "input_mode": "directory",
            "selected_files": []
        }
        json.dump(settings, temp_file, indent=4)
        temp_file_path = temp_file.name
    
    print(f"Created temporary settings file: {temp_file_path}")
    
    # Run batch.py with --help to see the command line options
    print("\nRunning batch.py with --help to check command line options:")
    help_cmd = [sys.executable, "batch.py", "--help"]
    help_result = subprocess.run(help_cmd, capture_output=True, text=True)
    
    # Check if --no_fix_encoding is in the help output
    if "--no_fix_encoding" in help_result.stdout:
        print("✅ --no_fix_encoding option is available in the help output")
    else:
        print("❌ --no_fix_encoding option is NOT available in the help output")
    
    # Run the Python helper script with the temporary settings file
    print("\nRunning run_framepack_helper.py with fix_encoding=False:")
    cmd = [sys.executable, "run_framepack_helper.py", "--settings", temp_file_path]
    
    # Use subprocess.Popen to capture the command being executed
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    
    # Read the first few lines of output to see the command being executed
    command_line = ""
    for i in range(100):  # Read up to 100 lines
        line = process.stdout.readline()
        if not line:
            break
        print(line.strip())
        if "Running command:" in line:
            command_line = line.strip()
    
    # Kill the process since we don't need to run the full command
    process.terminate()
    
    # Check if --no_fix_encoding is in the command line
    if command_line and "--no_fix_encoding" in command_line:
        print("✅ --no_fix_encoding is correctly included in the command line")
    else:
        print("❌ --no_fix_encoding is NOT included in the command line")
    
    # Clean up the temporary file
    try:
        os.unlink(temp_file_path)
        print(f"Removed temporary settings file: {temp_file_path}")
    except Exception as e:
        print(f"Warning: Could not delete temporary file {temp_file_path}: {e}")
    
    print("\nTest completed.")

if __name__ == "__main__":
    test_fix_encoding_setting()
