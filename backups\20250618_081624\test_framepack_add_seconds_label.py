#!/usr/bin/env python
"""
Test script for framepack_add_seconds_label.py to verify it correctly identifies
files that need seconds labels and can parse different filename formats.
"""

import os
import sys
from framepack_add_seconds_label import has_seconds_label, matches_naming_scheme

def test_has_seconds_label():
    """Test the has_seconds_label function with various filename formats."""
    test_cases = [
        # Files with seconds label
        ("250420_121919_242_3623_37_5s.mp4", True),
        ("image_name_5s.mp4", True),
        ("image_name_seed123456_5s.mp4", True),
        
        # Files without seconds label
        ("250420_121919_242_3623_37.mp4", False),
        ("image_name.mp4", False),
        ("image_name_seed123456.mp4", False),
        
        # Invalid formats
        ("image.jpg", False),
        ("invalid_format", False)
    ]
    
    print("Testing has_seconds_label function:")
    for filename, expected in test_cases:
        result = has_seconds_label(filename)
        status = "✓" if result == expected else "✗"
        print(f"{status} {filename}: Got {result}, Expected {expected}")

def test_matches_naming_scheme():
    """Test the matches_naming_scheme function with various filename formats."""
    test_cases = [
        # Files that should match (and don't have seconds label)
        ("250420_121919_242_3623_37.mp4", True),
        ("250426_085120_706_7278_19_seed357798872.mp4", True),
        ("image_name.mp4", True),
        ("image_name_seed123456.mp4", True),
        
        # Files that should not match (already have seconds label)
        ("250420_121919_242_3623_37_5s.mp4", False),
        ("image_name_5s.mp4", False),
        ("image_name_seed123456_5s.mp4", False),
        
        # Invalid formats
        ("image.jpg", False),
        ("invalid_format", False)
    ]
    
    print("\nTesting matches_naming_scheme function:")
    for filename, expected in test_cases:
        result = matches_naming_scheme(filename)
        status = "✓" if result == expected else "✗"
        print(f"{status} {filename}: Got {result}, Expected {expected}")

if __name__ == "__main__":
    test_has_seconds_label()
    test_matches_naming_scheme()
