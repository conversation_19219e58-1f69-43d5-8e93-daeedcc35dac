#!/usr/bin/env python3
"""
Test script to verify GUI stability improvements
"""

import os
import time
import threading
import tkinter as tk
from framepack_gui import Frame<PERSON>ack<PERSON><PERSON>

def test_backend_monitoring():
    """Test the backend monitoring system"""
    print("Testing backend monitoring system...")
    
    # Create a test GUI instance
    root = tk.Tk()
    root.withdraw()  # Hide the window for testing
    
    try:
        app = FramePackGUI(root)
        
        # Test backend state tracking
        print(f"Initial backend state: {app.backend_state}")
        assert app.backend_state == "idle", "Backend should start in idle state"
        
        # Test recovery state
        print(f"Recovery state: {app.recovery_state}")
        assert not app.recovery_state['in_recovery'], "Should not be in recovery initially"
        
        # Test preview state
        print(f"Preview state: {app.preview_state}")
        assert not app.preview_state['latent_loading'], "Should not be loading initially"
        
        print("✓ Backend monitoring system initialized correctly")
        
    except Exception as e:
        print(f"✗ Backend monitoring test failed: {e}")
        return False
    finally:
        root.destroy()
    
    return True

def test_recovery_mechanisms():
    """Test the recovery mechanisms"""
    print("Testing recovery mechanisms...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        app = FramePackGUI(root)
        
        # Test should_attempt_recovery
        can_recover = app.should_attempt_recovery()
        print(f"Can attempt recovery: {can_recover}")
        assert can_recover, "Should be able to attempt recovery initially"
        
        # Test recovery state management
        app.recovery_state['recovery_count'] = 6  # Exceed limit
        can_recover = app.should_attempt_recovery()
        print(f"Can attempt recovery after limit: {can_recover}")
        assert not can_recover, "Should not be able to recover after exceeding limit"
        
        # Reset for next test
        app.recovery_state['recovery_count'] = 0
        app.recovery_state['last_recovery_time'] = time.time() - 7200  # 2 hours ago
        can_recover = app.should_attempt_recovery()
        print(f"Can attempt recovery after time reset: {can_recover}")
        assert can_recover, "Should be able to recover after time reset"
        
        print("✓ Recovery mechanisms working correctly")
        
    except Exception as e:
        print(f"✗ Recovery mechanisms test failed: {e}")
        return False
    finally:
        root.destroy()
    
    return True

def test_state_management():
    """Test the state management system"""
    print("Testing state management...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        app = FramePackGUI(root)
        
        # Test backend state updates
        app.backend_state = "running"
        print(f"Backend state set to: {app.backend_state}")
        assert app.backend_state == "running", "Backend state should be updatable"
        
        # Test preview state updates
        app.preview_state['latent_loading'] = True
        print(f"Preview loading state: {app.preview_state['latent_loading']}")
        assert app.preview_state['latent_loading'], "Preview state should be updatable"
        
        # Test safe UI reset
        app.reset_ui_buttons_safe()
        print("✓ Safe UI reset completed without errors")
        
        # Test preview state reset
        app.reset_preview_state_safe()
        print("✓ Preview state reset completed without errors")
        
        print("✓ State management working correctly")
        
    except Exception as e:
        print(f"✗ State management test failed: {e}")
        return False
    finally:
        root.destroy()
    
    return True

def test_error_handling():
    """Test error handling and fallback systems"""
    print("Testing error handling...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        app = FramePackGUI(root)
        
        # Test basic UI reset (fallback)
        app.basic_ui_reset()
        print("✓ Basic UI reset completed without errors")
        
        # Test emergency reset
        app.emergency_reset()
        print("✓ Emergency reset completed without errors")
        
        # Test safe state reset
        app.reset_to_safe_state()
        print("✓ Safe state reset completed without errors")
        
        print("✓ Error handling working correctly")
        
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        return False
    finally:
        root.destroy()
    
    return True

def test_file_operations():
    """Test file operation safety"""
    print("Testing file operations...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        app = FramePackGUI(root)
        
        # Test cleanup operations
        app.cleanup_heartbeat_files()
        print("✓ Heartbeat file cleanup completed without errors")
        
        # Test file finding operations
        latest_latent = app.find_latest_latent_preview()
        print(f"Latest latent preview: {latest_latent}")
        
        latest_output = app.find_latest_output_video()
        print(f"Latest output video: {latest_output}")
        
        latest_image = app.find_latest_start_image()
        print(f"Latest start image: {latest_image}")
        
        print("✓ File operations working correctly")
        
    except Exception as e:
        print(f"✗ File operations test failed: {e}")
        return False
    finally:
        root.destroy()
    
    return True

def main():
    """Run all stability tests"""
    print("=" * 50)
    print("FramePack GUI Stability Test Suite")
    print("=" * 50)
    
    tests = [
        test_backend_monitoring,
        test_recovery_mechanisms,
        test_state_management,
        test_error_handling,
        test_file_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All stability tests passed!")
        print("The GUI stability improvements are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
