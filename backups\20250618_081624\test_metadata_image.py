#!/usr/bin/env python3
"""
Test script to create a PNG image with embedded FramePack metadata
for testing the "Load from Image" button functionality.
"""

import json
from PIL import Image, PngImagePlugin
import numpy as np

def create_test_image_with_metadata():
    """Create a test PNG image with embedded FramePack metadata"""
    
    # Create a simple test image (100x100 pixels, solid color)
    width, height = 100, 100
    # Create a blue image
    image_array = np.full((height, width, 3), [100, 150, 255], dtype=np.uint8)
    img = Image.fromarray(image_array)
    
    # Create test metadata that matches what FramePack would embed
    test_metadata = {
        "prompt": "A beautiful landscape with mountains and a lake, cinematic lighting, highly detailed",
        "negative_prompt": "blurry, low quality, distorted",
        "seed": 12345,
        "steps": 30,
        "cfg_scale": 7.5,
        "distilled_cfg_scale": 12.0,
        "flow_shift": 1.5,
        "total_video_length": "8 seconds",
        "use_teacache": True,
        "model_type": "standard",
        "lora_file_1": "style_lora.safetensors",
        "lora_multiplier_1": 0.7,
        "timestamp": "2024-01-15 14:30:22"
    }
    
    # Convert metadata to JSON string
    metadata_json = json.dumps(test_metadata, indent=2)
    
    # Create PngInfo object and add metadata
    png_info = PngImagePlugin.PngInfo()
    png_info.add_text("FramePack", metadata_json)
    
    # Save the image with metadata
    output_path = "test_image_with_metadata.png"
    img.save(output_path, format="PNG", pnginfo=png_info)
    
    print(f"Created test image: {output_path}")
    print(f"Embedded metadata:")
    print(metadata_json)
    
    return output_path

def verify_metadata_extraction(image_path):
    """Verify that metadata can be extracted from the image"""
    try:
        # Open the image
        img = Image.open(image_path)
        
        # Get the metadata
        metadata_json = img.info.get('FramePack')
        if metadata_json:
            # Parse the JSON
            metadata = json.loads(metadata_json)
            print(f"\nSuccessfully extracted metadata from {image_path}:")
            print(json.dumps(metadata, indent=2))
            return metadata
        else:
            print(f"No FramePack metadata found in {image_path}")
            return None
    except Exception as e:
        print(f"Error extracting metadata from {image_path}: {e}")
        return None

if __name__ == "__main__":
    # Create test image with metadata
    image_path = create_test_image_with_metadata()
    
    # Verify the metadata can be extracted
    print("\n" + "="*50)
    print("VERIFICATION TEST")
    print("="*50)
    extracted_metadata = verify_metadata_extraction(image_path)
    
    if extracted_metadata:
        print("\n✅ SUCCESS: Metadata was successfully embedded and extracted!")
        print("\nYou can now test the 'Load from Image' button in the FramePack GUI")
        print(f"by selecting the file: {image_path}")
    else:
        print("\n❌ FAILED: Could not extract metadata from the image")
