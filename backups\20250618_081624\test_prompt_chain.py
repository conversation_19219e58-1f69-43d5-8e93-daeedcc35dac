#!/usr/bin/env python3
"""
Test script to verify prompt chain functionality
"""

import subprocess
import sys
import os
from pathlib import Path

def test_batch_script_args():
    """Test that batch scripts accept the new prompt chain arguments"""
    
    print("Testing batch.py with prompt chain arguments...")
    
    # Test batch.py with prompt chain arguments
    cmd = [
        sys.executable, "batch.py",
        "--prompt_chain_mode",
        "--chain_index", "1",
        "--chain_total", "3",
        "--use_chain_input",
        "--help"  # Just show help to verify args are accepted
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ batch.py accepts prompt chain arguments")
        else:
            print("❌ batch.py failed with prompt chain arguments")
            print("STDERR:", result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error testing batch.py: {e}")
        return False
    
    print("\nTesting batch_f1.py with prompt chain arguments...")
    
    # Test batch_f1.py with prompt chain arguments
    cmd = [
        sys.executable, "batch_f1.py",
        "--prompt_chain_mode",
        "--chain_index", "1", 
        "--chain_total", "3",
        "--use_chain_input",
        "--help"  # Just show help to verify args are accepted
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ batch_f1.py accepts prompt chain arguments")
        else:
            print("❌ batch_f1.py failed with prompt chain arguments")
            print("STDERR:", result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error testing batch_f1.py: {e}")
        return False
    
    return True

def test_should_exclude_file():
    """Test the should_exclude_file function with prompt chain mode"""
    
    print("\nTesting should_exclude_file function...")
    
    # Import the function from batch.py
    sys.path.insert(0, '.')
    try:
        from batch import should_exclude_file
        
        # Test normal exclusion
        assert should_exclude_file("test_expanded.png", False) == True
        assert should_exclude_file("test_rotated.png", False) == True
        assert should_exclude_file("normal_file.png", False) == False
        
        # Test prompt chain mode - should allow _end.png files
        assert should_exclude_file("video_123_end.png", True) == False
        assert should_exclude_file("test_expanded.png", True) == True  # Still exclude expanded
        assert should_exclude_file("normal_file.png", True) == False
        
        print("✅ should_exclude_file function works correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error testing should_exclude_file: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Prompt Chain Functionality Test ===\n")
    
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    success = True
    
    # Test batch script arguments
    if not test_batch_script_args():
        success = False
    
    # Test should_exclude_file function
    if not test_should_exclude_file():
        success = False
    
    print("\n=== Test Results ===")
    if success:
        print("✅ All tests passed! Prompt chain functionality is working.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
