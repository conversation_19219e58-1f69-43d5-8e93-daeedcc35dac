#!/usr/bin/env python3
"""
Test script to verify the prompt chain fix
"""

import os
import sys
from datetime import datetime

def test_job_structure():
    """Test the new job structure for prompt chains"""
    print("=== Testing Prompt Chain Job Structure ===\n")
    
    # Simulate the new job creation logic
    selected_files = ["image1.jpg", "image2.jpg", "image3.jpg"]
    prompt_chain = [
        {"prompt": "a beautiful landscape", "settings": {"video_length": 5.0}},
        {"prompt": "with dramatic lighting", "settings": {"video_length": 7.0}},
        {"prompt": "in cinematic style", "settings": {"video_length": 6.0}}
    ]
    
    base_job_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    jobs = []
    
    # Create separate jobs for each batch item (new approach)
    for i, file_item in enumerate(selected_files):
        job_id = f"{base_job_id}_{i+1:03d}"
        
        job = {
            "job_id": job_id,
            "selected_files": [file_item],  # Single file per job
            "use_prompt_chain": True,
            "prompt_chain": prompt_chain,
            "current_chain_index": 0,
            "batch_item_index": i + 1,
            "total_batch_items": len(selected_files),
            "iterations": 1
        }
        jobs.append(job)
    
    print(f"Created {len(jobs)} jobs for {len(selected_files)} batch items:")
    for i, job in enumerate(jobs):
        print(f"  Job {i+1}: {job['job_id']}")
        print(f"    File: {job['selected_files'][0]}")
        print(f"    Batch Item: {job['batch_item_index']}/{job['total_batch_items']}")
        print(f"    Chain Steps: {len(job['prompt_chain'])}")
        print()
    
    # Simulate processing sequence
    print("Expected Processing Sequence:")
    for job in jobs:
        file_name = os.path.basename(job['selected_files'][0])
        for step_idx, step in enumerate(job['prompt_chain']):
            step_num = step_idx + 1
            prompt = step['prompt'][:30] + "..." if len(step['prompt']) > 30 else step['prompt']
            print(f"  {job['job_id']}_p{step_num} - {file_name} - '{prompt}'")
    
    print("\nExpected Video Files:")
    for job in jobs:
        file_name = os.path.splitext(os.path.basename(job['selected_files'][0]))[0]
        for step_idx in range(len(job['prompt_chain'])):
            step_num = step_idx + 1
            video_name = f"{job['job_id']}_p{step_num}_1234_5s.mp4"
            print(f"  {video_name}")
    
    print("\nExpected Joined Videos:")
    for job in jobs:
        file_name = os.path.splitext(os.path.basename(job['selected_files'][0]))[0]
        joined_name = f"{job['job_id']}_chain_joined_{base_job_id}.mp4"
        print(f"  {joined_name}")
    
    return True

def test_video_joiner_pattern():
    """Test the video joiner pattern matching"""
    print("\n=== Testing Video Joiner Pattern Matching ===\n")
    
    try:
        from video_joiner import find_prompt_chain_videos
        
        # Test with a sample job ID
        job_id = "20241201_143022_001"
        
        print(f"Testing pattern matching for job ID: {job_id}")
        print("Expected pattern: ^20241201_143022_001_p\\d+_\\d+_\\d+s\\.mp4$")
        
        # Sample filenames that should match
        test_files = [
            "20241201_143022_001_p1_1234_5s.mp4",  # Should match
            "20241201_143022_001_p2_1234_7s.mp4",  # Should match
            "20241201_143022_001_p3_1234_6s.mp4",  # Should match
            "20241201_143022_002_p1_1234_5s.mp4",  # Should NOT match (different job)
            "20241201_143022_001_1234_5s.mp4",     # Should match (legacy format)
        ]
        
        import re
        pattern = re.compile(rf'^{re.escape(job_id)}_p\d+_\d+_\d+s\.mp4$')
        legacy_pattern = re.compile(rf'^{re.escape(job_id)}_\d+_\d+s\.mp4$')
        
        print("\nPattern matching results:")
        for filename in test_files:
            chain_match = pattern.match(filename)
            legacy_match = legacy_pattern.match(filename)
            match_result = "MATCH" if (chain_match or legacy_match) else "NO MATCH"
            match_type = "chain" if chain_match else "legacy" if legacy_match else "none"
            print(f"  {filename} - {match_result} ({match_type})")
        
        return True
        
    except ImportError:
        print("Could not import video_joiner module")
        return False
    except Exception as e:
        print(f"Error testing video joiner: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Prompt Chain Fix Verification ===\n")
    
    success = True
    
    # Test job structure
    if not test_job_structure():
        success = False
    
    # Test video joiner pattern
    if not test_video_joiner_pattern():
        success = False
    
    print("\n=== Test Results ===")
    if success:
        print("All tests passed! Prompt chain fix should work correctly.")
        print("\nKey Changes:")
        print("- Each batch item now gets its own job with unique job ID")
        print("- Jobs are processed sequentially: Item1_p1->Item1_p2->Item1_p3->Item2_p1->etc.")
        print("- Video files use chain suffixes: job_id_p1, job_id_p2, job_id_p3")
        print("- Auto-join works per batch item, creating one joined video per item")
    else:
        print("Some tests failed. Please check the implementation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
