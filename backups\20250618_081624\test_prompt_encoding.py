#!/usr/bin/env python
"""
test_prompt_encoding.py

This script tests the prompt encoding functionality in the FramePack codebase.
It loads the necessary models and tests the encode_prompt_conds function with various prompts.
"""

import os
import torch
from transformers import LlamaModel, CLIPTextModel, LlamaTokenizerFast, CLIPTokenizer
from diffusers_helper.hunyuan import encode_prompt_conds

# Set environment variable for HF_HOME
os.environ['HF_HOME'] = os.path.abspath(os.path.realpath(os.path.join(os.path.dirname(__file__), './hf_download')))

def test_prompt_encoding():
    """Test the prompt encoding functionality"""
    print("Loading models for testing...")
    
    # Load models on CPU to save memory
    text_encoder = LlamaModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder', torch_dtype=torch.float16).cpu()
    text_encoder_2 = CLIPTextModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder_2', torch_dtype=torch.float16).cpu()
    
    print("Loading tokenizers...")
    tokenizer = LlamaTokenizerFast.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer')
    tokenizer_2 = CLIPTokenizer.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer_2')
    
    # Set tokenizer padding side
    tokenizer.padding_side = "left"
    tokenizer_2.padding_side = "left"
    
    # Test prompts
    test_prompts = [
        "A beautiful sunset over the ocean",
        "A person dancing",
        "",  # Empty prompt
        "特殊字符和非英语文本测试",  # Non-English text
        "A" * 1000,  # Very long prompt
        123,  # Non-string input
        None,  # None input
    ]
    
    # Test each prompt
    for i, prompt in enumerate(test_prompts):
        print(f"\nTest {i+1}: Testing prompt: {prompt}")
        try:
            llama_vec, clip_l_pooler = encode_prompt_conds(prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)
            print(f"Success! Encoded prompt shape: {llama_vec.shape}, {clip_l_pooler.shape}")
        except Exception as e:
            print(f"Error encoding prompt: {e}")
    
    print("\nAll tests completed!")

if __name__ == "__main__":
    test_prompt_encoding()
