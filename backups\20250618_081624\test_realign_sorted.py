#!/usr/bin/env python
"""
Test script to verify that the framepack_realign_sorted.py script works correctly.
This script creates test files with various sizes and naming patterns to simulate
the real-world scenario where videos with seconds indicators might be smaller.
"""

import os
import sys
import shutil
import tempfile
import subprocess

def create_test_files(directory, file_info):
    """
    Create test files in the specified directory.
    file_info is a list of tuples (filename, size)
    """
    os.makedirs(directory, exist_ok=True)
    
    for filename, size in file_info:
        file_path = os.path.join(directory, filename)
        # Create a file with the specified size
        with open(file_path, 'wb') as f:
            f.write(b'X' * size)
        
        print(f"Created {filename} with size {size} bytes")
    
    print(f"Created {len(file_info)} test files in {directory}")

def test_realign_sorted():
    """Test that the realignment script works correctly."""
    print("\n=== Testing realignment script ===")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create outputs directory
        outputs_dir = os.path.join(temp_dir, "outputs")
        os.makedirs(outputs_dir, exist_ok=True)
        
        # Create sorted directory
        sorted_dir = os.path.join(outputs_dir, "sorted")
        os.makedirs(sorted_dir, exist_ok=True)
        
        # Create test files in outputs directory with different sizes
        # Simulate the real-world scenario where files with seconds indicators are smaller
        output_files = [
            # Group 1: Same base_id, with and without seconds indicators
            ("250420_121919_242_3623_37.mp4", 5000),  # No seconds indicator, largest
            ("250420_121919_242_3623_38_4s.mp4", 3000),  # With seconds indicator, smaller
            ("250420_121919_242_3623_39.mp4", 4000),  # No seconds indicator, medium
            
            # Group 2: Same base_id with seed, with and without seconds indicators
            ("250421_121919_242_3623_37_seed123456.mp4", 6000),  # No seconds indicator, largest
            ("250421_121919_242_3623_38_seed123456_4s.mp4", 2000),  # With seconds indicator, smallest
            ("250421_121919_242_3623_39_seed123456.mp4", 4000),  # No seconds indicator, medium
        ]
        create_test_files(outputs_dir, output_files)
        
        # Create test files in sorted directory with incorrect files
        sorted_files = [
            # Group 1: Wrong file (not the largest)
            ("250420_121919_242_3623_38_4s.mp4", 3000),  # With seconds indicator, smaller
            
            # Group 2: Wrong file (not the largest)
            ("250421_121919_242_3623_38_seed123456_4s.mp4", 2000),  # With seconds indicator, smallest
            
            # Group 3: File that doesn't exist in outputs
            ("250422_121919_242_3623_37.mp4", 7000),  # Should be kept
        ]
        create_test_files(sorted_dir, sorted_files)
        
        # Run the realignment script
        print("\nRunning realignment script without clean option:")
        cmd = [sys.executable, "framepack_realign_sorted.py", "--outputs-dir", outputs_dir]
        result = subprocess.run(cmd, capture_output=True, text=True)
        print(result.stdout)
        
        # Check the sorted directory after realignment
        print("\nChecking sorted directory after realignment:")
        sorted_files_after = os.listdir(sorted_dir)
        print(f"Files in sorted directory: {len(sorted_files_after)}")
        
        # Expected files (largest from each group + the existing file that doesn't exist in outputs)
        expected_files = [
            "250420_121919_242_3623_37.mp4",  # Largest from Group 1
            "250421_121919_242_3623_37_seed123456.mp4",  # Largest from Group 2
            "250422_121919_242_3623_37.mp4",  # Existing file that doesn't exist in outputs
        ]
        
        # Check if we have the expected files
        for expected_file in expected_files:
            if expected_file in sorted_files_after:
                print(f"✅ Found expected file in sorted directory: {expected_file}")
            else:
                print(f"❌ Missing expected file in sorted directory: {expected_file}")
        
        # Check if any unexpected files are present
        for sorted_file in sorted_files_after:
            if sorted_file not in expected_files:
                print(f"❌ Unexpected file in sorted directory: {sorted_file}")
        
        # Run the realignment script with clean option
        print("\nRunning realignment script with clean option:")
        cmd = [sys.executable, "framepack_realign_sorted.py", "--outputs-dir", outputs_dir, "--clean"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        print(result.stdout)
        
        # Check the sorted directory after realignment with clean
        print("\nChecking sorted directory after realignment with clean:")
        sorted_files_after_clean = os.listdir(sorted_dir)
        print(f"Files in sorted directory: {len(sorted_files_after_clean)}")
        
        # Expected files after clean (only the largest from each group)
        expected_files_after_clean = [
            "250420_121919_242_3623_37.mp4",  # Largest from Group 1
            "250421_121919_242_3623_37_seed123456.mp4",  # Largest from Group 2
        ]
        
        # Check if we have the expected files
        for expected_file in expected_files_after_clean:
            if expected_file in sorted_files_after_clean:
                print(f"✅ Found expected file in sorted directory: {expected_file}")
            else:
                print(f"❌ Missing expected file in sorted directory: {expected_file}")
        
        # Check if any unexpected files are present
        for sorted_file in sorted_files_after_clean:
            if sorted_file not in expected_files_after_clean:
                print(f"❌ Unexpected file in sorted directory: {sorted_file}")

if __name__ == "__main__":
    test_realign_sorted()
    
    print("\nTest completed.")
