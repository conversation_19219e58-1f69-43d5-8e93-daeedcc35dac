#!/usr/bin/env python
"""
Test script to verify the issue with the seconds indicator in auto_sorter.py.
This script creates test files with and without seconds indicators and checks
if the largest file is correctly identified regardless of the seconds indicator.
"""

import os
import sys
import shutil
import tempfile
from auto_sorter import auto_sort_after_generation, get_largest_file, group_files

def create_test_files(directory, file_info):
    """
    Create test files in the specified directory.
    file_info is a list of tuples (filename, size)
    """
    os.makedirs(directory, exist_ok=True)
    
    for filename, size in file_info:
        file_path = os.path.join(directory, filename)
        # Create a file with the specified size
        with open(file_path, 'wb') as f:
            f.write(b'X' * size)
        
        print(f"Created {filename} with size {size} bytes")
    
    print(f"Created {len(file_info)} test files in {directory}")

def test_seconds_indicator_issue():
    """Test that files with seconds indicators are correctly compared for size."""
    print("\n=== Testing seconds indicator issue ===")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create outputs directory
        outputs_dir = os.path.join(temp_dir, "outputs")
        os.makedirs(outputs_dir, exist_ok=True)
        
        # Create test files in outputs directory with different sizes
        # Group 1: Files with the same base_id, with and without seconds indicators
        test_files = [
            # Group 1: Same base_id, with and without seconds indicators
            ("250420_121919_242_3623_37.mp4", 1000),  # No seconds indicator, small
            ("250420_121919_242_3623_38_4s.mp4", 5000),  # With seconds indicator, largest
            ("250420_121919_242_3623_39.mp4", 3000),  # No seconds indicator, medium
            
            # Group 2: Same base_id with seed, with and without seconds indicators
            ("250421_121919_242_3623_37_seed123456.mp4", 2000),  # No seconds indicator, medium
            ("250421_121919_242_3623_38_seed123456_4s.mp4", 6000),  # With seconds indicator, largest
            ("250421_121919_242_3623_39_seed123456.mp4", 1500),  # No seconds indicator, small
        ]
        create_test_files(outputs_dir, test_files)
        
        # Test direct grouping and largest file selection
        print("\nTesting direct grouping and largest file selection:")
        file_paths = [os.path.join(outputs_dir, filename) for filename, _ in test_files]
        groups = group_files(file_paths)
        
        for group_key, files_in_group in groups.items():
            base_id, seed = group_key
            seed_info = f" (seed: {seed})" if seed else ""
            print(f"\nGroup: {base_id}{seed_info} - {len(files_in_group)} files")
            
            # Sort files by size (largest first) for display
            sorted_files = sorted(files_in_group, key=os.path.getsize, reverse=True)
            
            # Display files with their sizes
            for i, file_path in enumerate(sorted_files):
                size = os.path.getsize(file_path)
                filename = os.path.basename(file_path)
                status = "LARGEST" if i == 0 else "smaller"
                print(f"  {i+1}. {filename} - {size} bytes - {status}")
            
            # Get the largest file
            largest_file = get_largest_file(files_in_group)
            largest_filename = os.path.basename(largest_file)
            largest_size = os.path.getsize(largest_file)
            print(f"  Largest file: {largest_filename} - {largest_size} bytes")
            
            # Check if the largest file has a seconds indicator
            has_seconds = "_s.mp4" in largest_filename
            print(f"  Has seconds indicator: {has_seconds}")
            
            # Verify the largest file is actually the largest by size
            expected_largest = sorted_files[0]
            expected_largest_filename = os.path.basename(expected_largest)
            if largest_file == expected_largest:
                print(f"  ✅ Correct largest file selected: {largest_filename}")
            else:
                print(f"  ❌ Wrong file selected as largest: {largest_filename}")
                print(f"     Expected largest: {expected_largest_filename} - {os.path.getsize(expected_largest)} bytes")
        
        # Run auto_sort_after_generation
        print("\nRunning auto_sort_after_generation:")
        num_copied, num_deleted = auto_sort_after_generation(outputs_dir)
        
        # Check the sorted directory
        sorted_dir = os.path.join(outputs_dir, "sorted")
        if os.path.exists(sorted_dir):
            sorted_files = os.listdir(sorted_dir)
            print(f"\nFiles in sorted directory: {len(sorted_files)}")
            
            # We should have 2 files in the sorted directory (one for each group)
            expected_count = 2
            if len(sorted_files) == expected_count:
                print(f"✅ Sorted directory has the expected number of files ({expected_count})")
            else:
                print(f"❌ Sorted directory has {len(sorted_files)} files, expected {expected_count}")
            
            # Check if the largest files from each group were copied
            expected_files = [
                "250420_121919_242_3623_38_4s.mp4",  # Largest from Group 1 (with seconds indicator)
                "250421_121919_242_3623_38_seed123456_4s.mp4"  # Largest from Group 2 (with seconds indicator)
            ]
            
            for expected_file in expected_files:
                if expected_file in sorted_files:
                    print(f"✅ Found expected file in sorted directory: {expected_file}")
                else:
                    print(f"❌ Missing expected file in sorted directory: {expected_file}")
                    # Show what file was copied instead
                    for sorted_file in sorted_files:
                        if sorted_file.startswith(expected_file.split("_")[0]):
                            print(f"   Found instead: {sorted_file}")
            
            # Check if any unexpected files were copied
            for sorted_file in sorted_files:
                if sorted_file not in expected_files:
                    print(f"❌ Unexpected file in sorted directory: {sorted_file}")
        else:
            print("❌ Sorted directory was not created")

if __name__ == "__main__":
    test_seconds_indicator_issue()
    
    print("\nTest completed.")
