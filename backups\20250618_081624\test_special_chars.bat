@echo off
setlocal enabledelayedexpansion
echo This script tests drag and drop with filenames containing spaces and special characters

REM Get the current directory
set "CURRENT_DIR=%CD%"

REM Create test directory if it doesn't exist
if not exist test_files mkdir test_files

REM Create test files with spaces and special characters
echo Creating test files with special characters...
echo. > "test_files\File with spaces.jpg"
echo. > "test_files\Special_Chars_(1).png"
echo. > "test_files\Test, Comma & Ampersand.jpg"

REM Get absolute paths for the test files
set "file1=%CURRENT_DIR%\test_files\File with spaces.jpg"
set "file2=%CURRENT_DIR%\test_files\Special_Chars_(1).png"
set "file3=%CURRENT_DIR%\test_files\Test, Comma & Ampersand.jpg"

REM Call the launcher with the test files
echo.
echo Calling launch_framepack_gui.bat with special character files...
echo.
call launch_framepack_gui.bat "%file1%" "%file2%" "%file3%"

echo.
echo Test completed.
echo.
