#!/usr/bin/env python3
"""
Test script to verify the enhanced stop functionality works correctly.
This script simulates the GUI's process termination logic.
"""

import os
import sys
import time
import subprocess
import signal
import psutil

def test_process_termination():
    """Test the enhanced process termination functionality"""
    print("Testing enhanced stop functionality...")
    
    # Test 1: Create a stop flag file
    print("\n1. Testing stop flag file creation...")
    stop_flag_path = "stop_framepack.flag"
    with open(stop_flag_path, 'w') as f:
        f.write(f"Test stop requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    if os.path.exists(stop_flag_path):
        print("✓ Stop flag file created successfully")
        os.remove(stop_flag_path)
        print("✓ Stop flag file removed successfully")
    else:
        print("✗ Failed to create stop flag file")
        return False
    
    # Test 2: Test psutil availability for process termination
    print("\n2. Testing psutil availability...")
    try:
        import psutil
        current_pid = os.getpid()
        current_process = psutil.Process(current_pid)
        print(f"✓ psutil available - Current process PID: {current_pid}")
        print(f"✓ Process name: {current_process.name()}")
        print(f"✓ Process command line: {' '.join(current_process.cmdline())}")
    except ImportError:
        print("✗ psutil not available - will use fallback termination methods")
        return False
    except Exception as e:
        print(f"✗ Error testing psutil: {e}")
        return False
    
    # Test 3: Test signal handling
    print("\n3. Testing signal handling...")
    try:
        # Test if we can register signal handlers
        def test_signal_handler(signum, frame):
            print(f"✓ Signal {signum} handler called successfully")
        
        if os.name == 'nt':  # Windows
            signal.signal(signal.SIGTERM, test_signal_handler)
            signal.signal(signal.SIGINT, test_signal_handler)
            print("✓ Windows signal handlers registered")
        else:  # Unix-like systems
            signal.signal(signal.SIGTERM, test_signal_handler)
            signal.signal(signal.SIGINT, test_signal_handler)
            signal.signal(signal.SIGQUIT, test_signal_handler)
            print("✓ Unix signal handlers registered")
    except Exception as e:
        print(f"✗ Error testing signal handling: {e}")
        return False
    
    # Test 4: Test process enumeration
    print("\n4. Testing process enumeration...")
    try:
        framepack_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline'] or []
                cmdline_str = ' '.join(cmdline).lower()
                
                # Look for FramePack-related processes
                if any(script in cmdline_str for script in ['batch.py', 'batch_f1.py', 'batch_f1_video.py', 'framepack_gui.py']):
                    framepack_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline_str
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        print(f"✓ Found {len(framepack_processes)} FramePack-related processes")
        for proc in framepack_processes:
            print(f"  - PID {proc['pid']}: {proc['name']} - {proc['cmdline'][:100]}...")
    except Exception as e:
        print(f"✗ Error testing process enumeration: {e}")
        return False
    
    # Test 5: Test file cleanup
    print("\n5. Testing file cleanup...")
    try:
        test_files = [
            "test_temp_file.txt",
            "test_framepack_progress.txt",
            "test_stop_flag.flag"
        ]
        
        # Create test files
        for test_file in test_files:
            with open(test_file, 'w') as f:
                f.write("test content")
        
        # Clean up test files
        cleaned_files = 0
        for test_file in test_files:
            if os.path.exists(test_file):
                try:
                    os.remove(test_file)
                    cleaned_files += 1
                except Exception as e:
                    print(f"Warning: Could not remove {test_file}: {e}")
        
        print(f"✓ Successfully cleaned up {cleaned_files}/{len(test_files)} test files")
    except Exception as e:
        print(f"✗ Error testing file cleanup: {e}")
        return False
    
    print("\n✓ All tests passed! Enhanced stop functionality should work correctly.")
    return True

def simulate_gui_stop_queue():
    """Simulate the GUI's stop queue functionality"""
    print("\n" + "="*60)
    print("SIMULATING GUI STOP QUEUE FUNCTIONALITY")
    print("="*60)
    
    try:
        # Simulate creating stop flag
        print("1. Creating stop flag file...")
        stop_flag_path = "stop_framepack.flag"
        with open(stop_flag_path, 'w') as f:
            f.write(f"Stop queue requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("✓ Stop flag created")
        
        # Simulate process termination
        print("2. Simulating process termination...")
        terminated_processes = []
        
        try:
            import psutil
            current_pid = os.getpid()
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # Skip our own process
                    if proc.info['pid'] == current_pid:
                        continue
                    
                    cmdline = proc.info['cmdline'] or []
                    cmdline_str = ' '.join(cmdline).lower()
                    
                    # Look for batch processes (but not GUI)
                    should_terminate = False
                    if 'batch.py' in cmdline_str and 'framepack_gui.py' not in cmdline_str:
                        should_terminate = True
                    elif any(script in cmdline_str for script in ['batch_f1_lock.py', 'batch_f1_video.py']):
                        should_terminate = True
                    elif 'temp_run_framepack.bat' in cmdline_str:
                        should_terminate = True
                    
                    if should_terminate:
                        print(f"Would terminate: PID {proc.info['pid']} - {cmdline_str[:80]}...")
                        terminated_processes.append(proc.info['pid'])
                        # Note: Not actually terminating processes in this test
                
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            print(f"✓ Would terminate {len(terminated_processes)} generation processes")
        
        except ImportError:
            print("psutil not available - would use fallback termination")
        
        # Simulate cleanup
        print("3. Simulating cleanup...")
        cleanup_files = [
            "temp_run_framepack.bat",
            "framepack_completed.signal",
            "stop_framepack.flag",
            "temp_command_result.txt"
        ]
        
        cleaned_count = 0
        for file_path in cleanup_files:
            if os.path.exists(file_path):
                print(f"Would clean up: {file_path}")
                cleaned_count += 1
        
        # Clean up our test stop flag
        if os.path.exists(stop_flag_path):
            os.remove(stop_flag_path)
            print("✓ Test stop flag cleaned up")
        
        print(f"✓ Simulation complete - would clean up {cleaned_count} files")
        
    except Exception as e:
        print(f"✗ Error in simulation: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("FramePack Enhanced Stop Functionality Test")
    print("=" * 50)
    
    # Run basic tests
    if test_process_termination():
        # Run GUI simulation
        simulate_gui_stop_queue()
        print("\n🎉 All tests completed successfully!")
        print("\nThe enhanced stop functionality should now:")
        print("- Immediately terminate all batch generation processes")
        print("- Clean up temporary files and locks")
        print("- Handle both graceful stops and forced termination")
        print("- Preserve the GUI while stopping background processes")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        sys.exit(1)
