@echo off
setlocal enabledelayedexpansion

echo Testing URL download...

REM Create temp directory if it doesn't exist
if not exist "temp" mkdir "temp"

REM URL to test
set "url=https://snworksceo.imgix.net/bdh/df3cc68a-26bb-4fdd-adf5-27cac10d62a0.sized-1000x1000.png?w=1000"

echo Downloading from URL: %url%

REM Download the image
python download_url_image.py "%url%" > "temp\url_output.txt"

REM Check the result
set "found_path="
for /f "tokens=1,* delims==" %%a in (temp\url_output.txt) do (
    if "%%a"=="DOWNLOADED_PATH" (
        echo Downloaded image to: %%b
        set "found_path=1"
    ) else (
        echo %%a %%b
    )
)

if not defined found_path (
    echo Error: Failed to download image from URL
    type "temp\url_output.txt"
)

echo Test completed.
pause
