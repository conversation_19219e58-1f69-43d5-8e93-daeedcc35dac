#!/usr/bin/env python3
"""
Test script for the video joiner functionality
"""

import os
import sys
from pathlib import Path

def test_video_joiner_import():
    """Test that video joiner can be imported"""
    try:
        from video_joiner import join_prompt_chain_videos, find_prompt_chain_videos
        print("✅ Video joiner imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import video joiner: {e}")
        return False

def test_find_videos():
    """Test finding videos in outputs directory"""
    try:
        from video_joiner import find_prompt_chain_videos
        
        # Test with outputs directory
        output_dir = "outputs"
        if not os.path.exists(output_dir):
            print(f"⚠️  Output directory {output_dir} does not exist")
            return True  # Not a failure, just no videos to test with
        
        # Test finding recent videos
        videos = find_prompt_chain_videos(output_dir, job_id=None, recent_minutes=60)
        print(f"✅ Found {len(videos)} recent videos in {output_dir}")
        
        if videos:
            print("Recent videos found:")
            for video in videos[:5]:  # Show first 5
                print(f"  - {os.path.basename(video)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing find_videos: {e}")
        return False

def test_join_functionality():
    """Test the join functionality with dummy data"""
    try:
        from video_joiner import join_videos
        
        # This would normally require actual video files and ffmpeg
        # For now, just test that the function exists and can be called
        print("✅ join_videos function is available")
        return True
        
    except Exception as e:
        print(f"❌ Error testing join functionality: {e}")
        return False

def main():
    """Run all tests"""
    print("=== Video Joiner Integration Test ===\n")
    
    success = True
    
    # Test imports
    if not test_video_joiner_import():
        success = False
    
    # Test finding videos
    if not test_find_videos():
        success = False
    
    # Test join functionality
    if not test_join_functionality():
        success = False
    
    print("\n=== Test Results ===")
    if success:
        print("✅ All tests passed! Video joiner integration is ready.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
