@echo off
REM Test script for the dual video preview system in FramePack
REM This script installs the required dependencies and runs the test_video_previews.py script

echo Testing FramePack Video Preview System...

REM Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is not installed or not in the PATH.
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Install tkvideoplayer if not already installed
echo Checking for tkvideoplayer...
python -c "import tkVideoPlayer" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Installing tkvideoplayer...
    pip install tkvideoplayer
    if %ERRORLEVEL% NEQ 0 (
        echo Error: Failed to install tkvideoplayer.
        echo Please try installing it manually: pip install tkvideoplayer
        pause
        exit /b 1
    )
    echo tkvideoplayer installed successfully.
) else (
    echo tkvideoplayer is already installed.
)

REM Create latent_previews directory if it doesn't exist
if not exist "latent_previews" (
    echo Creating latent_previews directory...
    mkdir latent_previews
)

REM Create outputs directory if it doesn't exist
if not exist "outputs" (
    echo Creating outputs directory...
    mkdir outputs
)

REM Run the test script
echo Running test_video_previews.py...
python test_video_previews.py

echo Test completed.
pause
