"""
Test script for the dual video preview system in FramePack.
This script creates a simple GUI with both latent and output video players
to verify that the video preview functionality works correctly.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk
import threading
import time
import glob

# Import TkinterVideo
try:
    from tkVideoPlayer import TkinterVideo
    TKVIDEO_AVAILABLE = True
    print("TkinterVideo loaded successfully. Video preview is enabled.")
except ImportError:
    print("TkinterVideo not available. Video preview will be disabled.")
    print("To enable video preview, install tkvideoplayer: pip install tkvideoplayer")
    TKVIDEO_AVAILABLE = False

class TestVideoPreviewApp:
    def __init__(self, root):
        self.root = root
        self.root.title("FramePack Video Preview Test")
        self.root.geometry("1000x800")
        
        # Set custom exception handler
        self.root.report_callback_exception = self.report_callback_exception
        
        # Create variables
        self.video_load_lock = threading.Lock()
        self.output_video_load_lock = threading.Lock()
        self.preview_loading = False
        self.output_preview_loading = False
        self.current_preview = None
        self.current_output_preview = None
        self._restarting_preview = False
        self._restarting_output_preview = False
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create a two-column layout
        left_column = ttk.Frame(main_frame)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        right_column = ttk.Frame(main_frame)
        right_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # Create latent preview frame
        latent_preview_frame = ttk.LabelFrame(left_column, text="Latent Preview")
        latent_preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create a frame to hold the latent video player
        self.latent_video_frame = ttk.Frame(latent_preview_frame)
        self.latent_video_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Initialize the latent video player
        if TKVIDEO_AVAILABLE:
            try:
                self.latent_preview_player = TkinterVideo(master=self.latent_video_frame, scaled=True, keep_aspect=True)
                self.latent_preview_player.pack(fill=tk.BOTH, expand=True)
                
                # Set a minimum height for the video player
                self.latent_preview_player.config(height=240)
                
                # Bind click event to pause/resume the video
                self.latent_preview_player.bind("<Button-1>", self.toggle_latent_playback)
                
                # Bind ended event to restart the video
                self.latent_preview_player.bind("<<Ended>>", self.restart_latent_preview)
            except Exception as e:
                print(f"Error initializing latent preview player: {e}")
                self.latent_preview_player = None
                ttk.Label(self.latent_video_frame, text="Video player initialization failed.").pack(pady=10)
        else:
            self.latent_preview_player = None
            ttk.Label(self.latent_video_frame, text="Video preview not available. Install tkvideoplayer to enable.").pack(pady=10)
        
        # Create latent preview status label
        self.latent_preview_label = ttk.Label(latent_preview_frame, text="No latent preview available")
        self.latent_preview_label.pack(fill=tk.X, padx=5, pady=5)
        
        # Create output preview frame
        output_preview_frame = ttk.LabelFrame(right_column, text="Output Preview")
        output_preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create a frame to hold the output video player
        self.output_video_frame = ttk.Frame(output_preview_frame)
        self.output_video_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Initialize the output video player
        if TKVIDEO_AVAILABLE:
            try:
                self.output_preview_player = TkinterVideo(master=self.output_video_frame, scaled=True, keep_aspect=True)
                self.output_preview_player.pack(fill=tk.BOTH, expand=True)
                
                # Set a minimum height for the video player
                self.output_preview_player.config(height=350)
                
                # Bind click event to pause/resume the video
                self.output_preview_player.bind("<Button-1>", self.toggle_output_playback)
                
                # Bind ended event to restart the video
                self.output_preview_player.bind("<<Ended>>", self.restart_output_preview)
            except Exception as e:
                print(f"Error initializing output preview player: {e}")
                self.output_preview_player = None
                ttk.Label(self.output_video_frame, text="Video player initialization failed.").pack(pady=10)
        else:
            self.output_preview_player = None
            ttk.Label(self.output_video_frame, text="Video preview not available. Install tkvideoplayer to enable.").pack(pady=10)
        
        # Create output preview status label
        self.output_preview_label = ttk.Label(output_preview_frame, text="No output preview available")
        self.output_preview_label.pack(fill=tk.X, padx=5, pady=5)
        
        # Create control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=10)
        
        # Refresh button
        ttk.Button(control_frame, text="Refresh Previews", command=self.refresh_previews).pack(side=tk.LEFT, padx=5)
        
        # Start monitoring for video updates
        if TKVIDEO_AVAILABLE:
            self.root.after(1000, self.update_latent_preview)
            self.root.after(2000, self.update_output_preview)
    
    def report_callback_exception(self, exc, val, tb):
        """Custom exception handler to prevent application from closing on unhandled exceptions"""
        import traceback
        print(f"Exception in Tkinter callback: {val}")
        traceback.print_exception(exc, val, tb)
        # Show a message to the user
        ttk.Label(self.root, text=f"Error: {val}", foreground="red").pack(pady=10)
    
    def toggle_latent_playback(self, event=None):
        """Toggle play/pause of the latent preview video when clicked"""
        try:
            if self.latent_preview_player is not None:
                if self.latent_preview_player.is_paused():
                    self.latent_preview_player.play()
                    self.latent_preview_label.config(text="Playing latent preview...")
                else:
                    self.latent_preview_player.pause()
                    self.latent_preview_label.config(text="Paused - Click to resume")
        except Exception as e:
            print(f"Error toggling latent playback: {e}")
    
    def toggle_output_playback(self, event=None):
        """Toggle play/pause of the output preview video when clicked"""
        try:
            if self.output_preview_player is not None:
                if self.output_preview_player.is_paused():
                    self.output_preview_player.play()
                    self.output_preview_label.config(text="Playing output preview...")
                else:
                    self.output_preview_player.pause()
                    self.output_preview_label.config(text="Paused - Click to resume")
        except Exception as e:
            print(f"Error toggling output playback: {e}")
    
    def restart_latent_preview(self, event=None):
        """Restart the latent preview video when it ends"""
        try:
            if self._restarting_preview:
                return
            
            self._restarting_preview = True
            
            def delayed_restart():
                try:
                    if self.latent_preview_player is not None:
                        self.latent_preview_player.seek(0)
                        self.latent_preview_player.play()
                except Exception as e:
                    print(f"Error restarting latent preview: {e}")
                finally:
                    self._restarting_preview = False
            
            self.root.after(200, delayed_restart)
        except Exception as e:
            print(f"Error in restart_latent_preview: {e}")
            self._restarting_preview = False
    
    def restart_output_preview(self, event=None):
        """Restart the output preview video when it ends"""
        try:
            if self._restarting_output_preview:
                return
            
            self._restarting_output_preview = True
            
            def delayed_restart():
                try:
                    if self.output_preview_player is not None:
                        self.output_preview_player.seek(0)
                        self.output_preview_player.play()
                except Exception as e:
                    print(f"Error restarting output preview: {e}")
                finally:
                    self._restarting_output_preview = False
            
            self.root.after(200, delayed_restart)
        except Exception as e:
            print(f"Error in restart_output_preview: {e}")
            self._restarting_output_preview = False
    
    def refresh_previews(self):
        """Manually refresh both previews"""
        self.update_latent_preview(force=True)
        self.update_output_preview(force=True)
    
    def update_latent_preview(self, force=False):
        """Check for and display the latest latent preview animation"""
        try:
            # Check if the video player is available
            if self.latent_preview_player is None:
                self.root.after(1000, self.update_latent_preview)
                return
            
            # If we're already in the process of loading a video, don't try to load another one
            if self.preview_loading and not force:
                self.root.after(1000, self.update_latent_preview)
                return
            
            # Look for latent preview files
            latent_previews_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "latent_previews")
            temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
            
            preview_files = []
            
            # Check in latent_previews directory
            if os.path.exists(latent_previews_dir):
                preview_files.extend(glob.glob(os.path.join(latent_previews_dir, "*latent_preview_*.mp4")))
            
            # Check in temp directory
            if os.path.exists(temp_dir) and not preview_files:
                preview_files.extend(glob.glob(os.path.join(temp_dir, "*latent_preview_*.mp4")))
            
            # Check for webm format as fallback
            if not preview_files:
                if os.path.exists(latent_previews_dir):
                    preview_files.extend(glob.glob(os.path.join(latent_previews_dir, "*latent_preview_*.webm")))
                if os.path.exists(temp_dir):
                    preview_files.extend(glob.glob(os.path.join(temp_dir, "*latent_preview_*.webm")))
            
            if preview_files:
                # Sort by modification time (newest first)
                preview_files.sort(key=os.path.getmtime, reverse=True)
                latest_preview = preview_files[0]
                
                # Check if this is a new preview or if we're forcing an update
                if force or self.current_preview != latest_preview:
                    print(f"Loading latent preview: {latest_preview}")
                    
                    # Try to acquire the lock
                    if not self.video_load_lock.acquire(blocking=False):
                        self.root.after(1000, lambda: self.update_latent_preview(force))
                        return
                    
                    self.preview_loading = True
                    
                    def load_video():
                        try:
                            self.current_preview = latest_preview
                            
                            # Stop any currently playing video
                            try:
                                self.latent_preview_player.stop()
                            except Exception as e:
                                print(f"Error stopping current latent preview: {e}")
                            
                            # Load the video
                            self.latent_preview_player.load(latest_preview)
                            
                            # Set up restart on end
                            self.latent_preview_player.bind("<<Ended>>", self.restart_latent_preview)
                            
                            # Play the video
                            self.latent_preview_player.play()
                            
                            # Update the label
                            self.latent_preview_label.config(text=f"Playing: {os.path.basename(latest_preview)}")
                        except Exception as e:
                            print(f"Error loading latent preview: {e}")
                            self.latent_preview_label.config(text=f"Error: {str(e)}")
                        finally:
                            self.preview_loading = False
                            self.video_load_lock.release()
                    
                    self.root.after(500, load_video)
            
            # Schedule the next update
            self.root.after(1000, self.update_latent_preview)
        except Exception as e:
            print(f"Error in update_latent_preview: {e}")
            self.root.after(1000, self.update_latent_preview)
    
    def update_output_preview(self, force=False):
        """Check for and display the latest output video"""
        try:
            # Check if the video player is available
            if self.output_preview_player is None:
                self.root.after(2000, self.update_output_preview)
                return
            
            # If we're already in the process of loading a video, don't try to load another one
            if self.output_preview_loading and not force:
                self.root.after(2000, self.update_output_preview)
                return
            
            # Look for output video files
            outputs_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "outputs")
            
            output_files = []
            
            # Check in outputs directory
            if os.path.exists(outputs_dir):
                output_files.extend(glob.glob(os.path.join(outputs_dir, "*.mp4")))
                
                # Check for other formats if no mp4 files found
                if not output_files:
                    output_files.extend(glob.glob(os.path.join(outputs_dir, "*.webm")))
                    if not output_files:
                        output_files.extend(glob.glob(os.path.join(outputs_dir, "*.avi")))
                        if not output_files:
                            output_files.extend(glob.glob(os.path.join(outputs_dir, "*.mov")))
            
            if output_files:
                # Sort by modification time (newest first)
                output_files.sort(key=os.path.getmtime, reverse=True)
                latest_output = output_files[0]
                
                # Check if this is a new output or if we're forcing an update
                if force or self.current_output_preview != latest_output:
                    print(f"Loading output preview: {latest_output}")
                    
                    # Try to acquire the lock
                    if not self.output_video_load_lock.acquire(blocking=False):
                        self.root.after(2000, lambda: self.update_output_preview(force))
                        return
                    
                    self.output_preview_loading = True
                    
                    def load_video():
                        try:
                            self.current_output_preview = latest_output
                            
                            # Stop any currently playing video
                            try:
                                self.output_preview_player.stop()
                            except Exception as e:
                                print(f"Error stopping current output preview: {e}")
                            
                            # Load the video
                            self.output_preview_player.load(latest_output)
                            
                            # Set up restart on end
                            self.output_preview_player.bind("<<Ended>>", self.restart_output_preview)
                            
                            # Play the video
                            self.output_preview_player.play()
                            
                            # Update the label
                            self.output_preview_label.config(text=f"Playing: {os.path.basename(latest_output)}")
                        except Exception as e:
                            print(f"Error loading output preview: {e}")
                            self.output_preview_label.config(text=f"Error: {str(e)}")
                        finally:
                            self.output_preview_loading = False
                            self.output_video_load_lock.release()
                    
                    self.root.after(500, load_video)
            
            # Schedule the next update
            self.root.after(2000, self.update_output_preview)
        except Exception as e:
            print(f"Error in update_output_preview: {e}")
            self.root.after(2000, self.update_output_preview)

def main():
    root = tk.Tk()
    app = TestVideoPreviewApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
