"""
A simple GIF player for Tkinter that doesn't rely on external libraries.
This provides a more reliable way to display animated GIFs in Tkinter applications.
"""

import os
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk, ImageSequence

class TkGifPlayer(ttk.Frame):
    """A simple GIF player for Tkinter that uses PIL to display animated GIFs"""

    def __init__(self, master, width=350, height=240, **kwargs):
        """Initialize the GIF player

        Args:
            master: The parent widget
            width: The width of the player (default: 350)
            height: The height of the player (default: 240)
            **kwargs: Additional keyword arguments for the Frame
        """
        super().__init__(master, **kwargs)

        # Initialize variables
        self.master = master
        self.width = width
        self.height = height
        self.is_playing = False
        self.is_loaded = False
        self.current_frame = 0
        self.frames = []
        self.frame_count = 0
        self.delays = []
        self.original_delays = []  # Store original delays for playback rate adjustment
        self.loop = True
        self.after_id = None
        self.gif_path = None
        self.playback_rate = 1.0  # Default playback rate (100%)

        # Create a label to display the GIF
        self.label = ttk.Label(self)
        self.label.pack(fill=tk.BOTH, expand=True)

        # Create a default image
        self._create_default_image()

        # Bind events
        self.label.bind("<Button-1>", self.toggle_playback)

    def _create_default_image(self):
        """Create a default image to display when no GIF is loaded"""
        # Create a blank image
        image = Image.new("RGB", (self.width, self.height), color=(40, 40, 40))
        photo = ImageTk.PhotoImage(image)
        self.label.configure(image=photo)
        self.label.image = photo  # Keep a reference to prevent garbage collection

    def load(self, gif_path):
        """Load a GIF file

        Args:
            gif_path: Path to the GIF file

        Returns:
            True if the GIF was loaded successfully, False otherwise
        """
        # Stop any current playback
        self.stop()

        # Reset variables
        self.frames = []
        self.delays = []
        self.original_delays = []  # Reset original delays
        self.current_frame = 0
        self.is_loaded = False
        self.gif_path = None

        # Check if the file exists
        if not os.path.exists(gif_path):
            print(f"GIF file not found: {gif_path}")
            return False

        try:
            # Open the GIF file
            gif = Image.open(gif_path)

            # Check if it's an animated GIF
            if not getattr(gif, "is_animated", False):
                # It's a static image, create a single frame
                resized_img = self._resize_image(gif)
                photo = ImageTk.PhotoImage(resized_img)
                self.frames = [photo]
                self.delays = [100]  # Default delay of 100ms
                self.original_delays = [100]  # Store original delay
                self.frame_count = 1
            else:
                # It's an animated GIF, extract all frames
                for frame in ImageSequence.Iterator(gif):
                    # Convert to RGB to avoid transparency issues
                    frame = frame.convert("RGB")

                    # Resize the frame to fit the player
                    resized_frame = self._resize_image(frame)

                    # Convert to PhotoImage
                    photo = ImageTk.PhotoImage(resized_frame)

                    # Add to frames list
                    self.frames.append(photo)

                    # Get the delay for this frame (in milliseconds)
                    delay = frame.info.get("duration", 100)
                    self.original_delays.append(delay)  # Store original delay
                    # Apply playback rate to the delay
                    adjusted_delay = int(delay / self.playback_rate)
                    self.delays.append(adjusted_delay)

                self.frame_count = len(self.frames)

            # Set the first frame
            if self.frames:
                self.label.configure(image=self.frames[0])
                self.label.image = self.frames[0]  # Keep a reference
                self.is_loaded = True
                self.gif_path = gif_path
                return True
            else:
                print(f"No frames found in GIF: {gif_path}")
                self._create_default_image()
                return False

        except Exception as e:
            print(f"Error loading GIF {gif_path}: {e}")
            self._create_default_image()
            return False

    def _resize_image(self, image):
        """Resize an image to fit the player while maintaining aspect ratio"""
        # Get the original dimensions
        orig_width, orig_height = image.size

        # Calculate the scaling factor
        width_ratio = self.width / orig_width
        height_ratio = self.height / orig_height
        ratio = min(width_ratio, height_ratio)

        # Calculate the new dimensions
        new_width = int(orig_width * ratio)
        new_height = int(orig_height * ratio)

        # Resize the image
        return image.resize((new_width, new_height), Image.LANCZOS)

    def play(self):
        """Start playing the GIF"""
        if not self.is_loaded or self.is_playing:
            return

        self.is_playing = True
        self._show_next_frame()

    def _show_next_frame(self):
        """Show the next frame of the GIF"""
        if not self.is_playing or not self.is_loaded:
            return

        # Cancel any existing scheduled frame
        if self.after_id:
            self.after_id = None

        # Show the current frame
        self.label.configure(image=self.frames[self.current_frame])
        self.label.image = self.frames[self.current_frame]  # Keep a reference

        # Get the delay for this frame
        delay = self.delays[self.current_frame]

        # Increment the frame counter
        self.current_frame += 1

        # Check if we've reached the end
        if self.current_frame >= self.frame_count:
            if self.loop:
                # Loop back to the beginning
                self.current_frame = 0
                # Schedule the next frame
                self.after_id = self.after(delay, self._show_next_frame)
            else:
                # Stop at the end
                self.is_playing = False
                # Generate an event to notify that playback has ended
                self.event_generate("<<Ended>>", when="tail")
        else:
            # Schedule the next frame
            self.after_id = self.after(delay, self._show_next_frame)

    def pause(self):
        """Pause playback"""
        self.is_playing = False

        # Cancel any scheduled frame
        if self.after_id:
            self.after_id = None

    def stop(self):
        """Stop playback and reset to the first frame"""
        self.pause()
        self.current_frame = 0

        # Show the first frame if available
        if self.is_loaded and self.frames:
            self.label.configure(image=self.frames[0])
            self.label.image = self.frames[0]  # Keep a reference

    def toggle_playback(self, event=None):
        """Toggle between play and pause"""
        if self.is_playing:
            self.pause()
        else:
            self.play()

    def is_paused(self):
        """Check if playback is paused"""
        return self.is_loaded and not self.is_playing

    def seek(self, frame_index):
        """Seek to a specific frame"""
        if not self.is_loaded:
            return

        # Ensure the frame index is valid
        frame_index = max(0, min(frame_index, self.frame_count - 1))

        # Set the current frame
        self.current_frame = frame_index

        # Show the frame
        self.label.configure(image=self.frames[self.current_frame])
        self.label.image = self.frames[self.current_frame]  # Keep a reference

    def set_loop(self, loop):
        """Set whether to loop playback"""
        self.loop = loop

    def set_playback_rate(self, rate):
        """Set the playback rate as a percentage (0.05 to 2.0)

        Args:
            rate: Playback rate as a float (0.05 = 5%, 1.0 = 100%, 2.0 = 200%)
        """
        # Ensure rate is within valid range
        rate = max(0.05, min(2.0, rate))

        # Only update if the rate has changed
        if rate != self.playback_rate:
            # Store the new rate
            self.playback_rate = rate

            # Update the delays based on the new rate
            if self.is_loaded and self.original_delays:
                # Recalculate all delays based on original values
                self.delays = [int(original_delay / rate) for original_delay in self.original_delays]

                # If currently playing, restart with new rate
                if self.is_playing:
                    # Cancel any existing scheduled frame
                    if self.after_id:
                        self.after_cancel(self.after_id)
                        self.after_id = None

                    # Schedule the next frame with the new delay
                    delay = self.delays[self.current_frame]
                    self.after_id = self.after(delay, self._show_next_frame)

            # Return the actual rate used (may be clamped)
            return self.playback_rate

# Example usage
if __name__ == "__main__":
    root = tk.Tk()
    root.title("TkGifPlayer Example")

    # Create a GIF player
    player = TkGifPlayer(root, width=350, height=240)
    player.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Add some controls
    control_frame = ttk.Frame(root)
    control_frame.pack(fill=tk.X, padx=10, pady=5)

    # Play button
    play_button = ttk.Button(control_frame, text="Play", command=player.play)
    play_button.pack(side=tk.LEFT, padx=5)

    # Pause button
    pause_button = ttk.Button(control_frame, text="Pause", command=player.pause)
    pause_button.pack(side=tk.LEFT, padx=5)

    # Stop button
    stop_button = ttk.Button(control_frame, text="Stop", command=player.stop)
    stop_button.pack(side=tk.LEFT, padx=5)

    # Add a playback rate slider
    rate_frame = ttk.Frame(root)
    rate_frame.pack(fill=tk.X, padx=10, pady=5)

    # Playback rate label
    rate_label = ttk.Label(rate_frame, text="Playback Rate: 100%")
    rate_label.pack(side=tk.LEFT, padx=5)

    # Function to update playback rate
    def update_playback_rate(value):
        # Convert from percentage (5-200) to float (0.05-2.0)
        rate = float(value) / 100.0
        player.set_playback_rate(rate)
        # Update the label
        rate_label.config(text=f"Playback Rate: {int(float(value))}%")

    # Playback rate slider (5% to 200%)
    rate_slider = ttk.Scale(
        rate_frame,
        from_=5,
        to=200,
        orient=tk.HORIZONTAL,
        value=100,  # Default to 100%
        command=update_playback_rate
    )
    rate_slider.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

    # Load a GIF if specified
    import sys
    if len(sys.argv) > 1:
        gif_path = sys.argv[1]
        if player.load(gif_path):
            player.play()

    root.mainloop()
