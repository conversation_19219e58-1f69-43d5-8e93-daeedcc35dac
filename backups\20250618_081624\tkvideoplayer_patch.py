"""
Monkey patch for tkVideoPlayer to fix the 'NoneType' object has no attribute 'close' error.
This patch adds safety checks to the TkinterVideo class to prevent crashes when the container is None.
"""

import sys
import traceback
from functools import wraps

def apply_patches():
    """Apply all patches to the tkVideoPlayer library"""
    try:
        # Import the tkVideoPlayer module
        from tkVideoPlayer import TkinterVideo
        
        # Store the original _load method
        original_load = TkinterVideo._load
        
        # Create a patched version of the _load method
        @wraps(original_load)
        def patched_load(self, *args, **kwargs):
            try:
                # Add safety check before closing container
                if hasattr(self, '_container') and self._container is not None:
                    try:
                        self._container.close()
                    except Exception as e:
                        print(f"Warning: Error closing container: {e}")
                else:
                    # Container is None, just log a warning
                    print("Warning: Container is None, skipping close operation")
                
                # Call the original method
                return original_load(self, *args, **kwargs)
            except Exception as e:
                print(f"Error in patched _load method: {e}")
                # Print the full traceback for debugging
                traceback.print_exc()
                # Set container to None to prevent further errors
                self._container = None
                # Raise the exception to be handled by the caller
                raise
        
        # Replace the original method with our patched version
        TkinterVideo._load = patched_load
        
        # Store the original stop method
        original_stop = TkinterVideo.stop
        
        # Create a patched version of the stop method
        @wraps(original_stop)
        def patched_stop(self, *args, **kwargs):
            try:
                # Call the original method
                return original_stop(self, *args, **kwargs)
            except Exception as e:
                print(f"Error in patched stop method: {e}")
                # Don't propagate the exception to prevent crashes
                pass
        
        # Replace the original method with our patched version
        TkinterVideo.stop = patched_stop
        
        # Store the original load method
        original_public_load = TkinterVideo.load
        
        # Create a patched version of the public load method
        @wraps(original_public_load)
        def patched_public_load(self, path, *args, **kwargs):
            try:
                # Call the original method
                return original_public_load(self, path, *args, **kwargs)
            except Exception as e:
                print(f"Error in patched load method: {e}")
                # Print the full traceback for debugging
                traceback.print_exc()
                # Set container to None to prevent further errors
                self._container = None
                # Raise the exception to be handled by the caller
                raise
        
        # Replace the original method with our patched version
        TkinterVideo.load = patched_public_load
        
        print("Successfully applied patches to tkVideoPlayer")
        return True
    except ImportError:
        print("tkVideoPlayer module not found, skipping patches")
        return False
    except Exception as e:
        print(f"Error applying patches to tkVideoPlayer: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Test the patches
    apply_patches()
    print("Patches applied successfully")
