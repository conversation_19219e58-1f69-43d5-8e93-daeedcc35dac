@echo off
setlocal enabledelayedexpansion
title FramePack URL Handler

REM Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo ===============================================
echo           FRAMEPACK URL HANDLER
echo ===============================================
echo.

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    color 0C
    echo.
    echo Error: Python is not installed or not in your PATH.
    echo Please install Python and make sure it's added to your PATH.
    echo.
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist venv\Scripts\activate.bat (
    echo Virtual environment not found. Checking if we can create it...

    REM Try to create virtual environment
    echo Creating virtual environment...
    python -m venv venv
    if %ERRORLEVEL% NEQ 0 (
        color 0C
        echo.
        echo Error: Failed to create virtual environment.
        echo Please make sure you have the venv module installed.
        echo You may need to run: pip install virtualenv
        echo.
        pause
        exit /b 1
    )

    echo Virtual environment created successfully.
    echo Installing required packages...

    REM Activate and install requirements
    call venv\Scripts\activate.bat
    pip install -r requirements.txt
    if %ERRORLEVEL% NEQ 0 (
        color 0C
        echo.
        echo Error: Failed to install required packages.
        echo Please check your internet connection and try again.
        echo.
        pause
        exit /b 1
    )

    echo Required packages installed successfully.
) else (
    echo Virtual environment found.
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Create temp directory if it doesn't exist
if not exist "temp" mkdir "temp"

REM Check if URL was provided
if "%~1"=="" (
    echo No URL provided. Please provide a URL to an image.
    echo Usage: %~nx0 "https://example.com/image.jpg"
    pause
    exit /b 1
)

REM Extract the base URL (before the ?)
set "base_url=%~1"
if "!base_url:~0,7!"=="http://" (
    echo Detected HTTP URL
) else if "!base_url:~0,8!"=="https://" (
    echo Detected HTTPS URL
) else (
    echo Not a valid URL: %base_url%
    pause
    exit /b 1
)

REM Create a URL file with just the base URL (no query parameters)
set "url_file=temp\url_to_download.txt"
echo %base_url%> "%url_file%"

REM Download the image using the URL from the file
echo Processing URL: %base_url%
call venv\Scripts\python.exe download_url_image.py "%base_url%" > "temp\url_output.txt"

REM Check the result
set "found_path="
for /f "tokens=1,* delims==" %%a in (temp\url_output.txt) do (
    if "%%a"=="DOWNLOADED_PATH" (
        echo Downloaded image to: %%b
        set "downloaded_path=%%b"
        set "found_path=1"
    ) else (
        echo %%a %%b
    )
)

if not defined found_path (
    echo Error: Failed to download image from URL
    type "temp\url_output.txt"
    pause
    exit /b 1
)

REM Create a temporary file to store the file path
set "temp_file=temp\framepack_temp_files.txt"
if exist "%temp_file%" del "%temp_file%"
echo %downloaded_path%> "%temp_file%"

REM Launch the GUI with the file list
echo Launching GUI with file list: %temp_file%
call venv\Scripts\python.exe framepack_gui.py --file-list "%temp_file%"

REM Clean up
if exist "temp\url_output.txt" del "temp\url_output.txt"
if exist "%temp_file%" del "%temp_file%"

echo.
echo URL processing completed successfully!
echo.
