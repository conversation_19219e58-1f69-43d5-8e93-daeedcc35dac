@echo off
setlocal enabledelayedexpansion
title FramePack URL Launcher

REM This script handles URLs with query parameters by passing the entire URL as a single argument
REM Usage: url_to_framepack.bat "https://example.com/image.jpg?w=1000"

REM Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo ===============================================
echo           FRAMEPACK URL LAUNCHER
echo ===============================================
echo

REM Check if URL was provided
if "%~1"=="" (
    echo No URL provided. Please provide a URL to an image.
    echo Usage: %~nx0 "https://example.com/image.jpg"
    pause
    exit /b 1
)

REM Process the URL
echo Processing URL: %~1
call "%SCRIPT_DIR%process_url.bat" "%~1"
if %ERRORLEVEL% NEQ 0 (
    echo Error processing URL.
    pause
    exit /b 1
)

REM Launch the GUI with the file list
echo Launching FramePack GUI with the downloaded image...
call venv\Scripts\python.exe framepack_gui.py --file-list "%SCRIPT_DIR%temp\framepack_temp_files.txt"

REM Clean up
if exist "%SCRIPT_DIR%temp\url_output.txt" del "%SCRIPT_DIR%temp\url_output.txt"
if exist "%SCRIPT_DIR%temp\framepack_temp_files.txt" del "%SCRIPT_DIR%temp\framepack_temp_files.txt"

echo
echo URL processing completed successfully!
echo
