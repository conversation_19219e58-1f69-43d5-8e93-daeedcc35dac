#!/usr/bin/env python3
"""
Video Black Frame Trimmer - v1.0
Scans the beginning of MP4 files and removes black frames while maintaining maximum quality.

Features:
- Detects black frames at the beginning of videos (50% black threshold)
- Removes black frames using stream copy for maximum quality preservation
- Supports single files, multiple files, directory processing, and txt list processing
- Only scans beginning until no more black frames are found
- Drag-and-drop support via batch launcher
- Progress indication and error handling

Dependencies: ffmpeg (must be in PATH), opencv-python
"""

import sys
import os
import subprocess
import json
import cv2
import numpy as np
from pathlib import Path
from typing import List, Optional, Tuple
import argparse
import time


def check_dependencies():
    """Check if required dependencies are available"""
    missing = []
    
    # Check ffmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            missing.append("ffmpeg")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        missing.append("ffmpeg")
    
    # Check opencv
    try:
        import cv2
    except ImportError:
        missing.append("opencv-python (pip install opencv-python)")
    
    return missing


def get_video_info(video_path: str) -> Optional[dict]:
    """Get video information using ffprobe"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return json.loads(result.stdout)
        return None
    except (subprocess.TimeoutExpired, json.JSONDecodeError, FileNotFoundError):
        return None


def get_video_fps(video_info: dict) -> float:
    """Extract video frame rate from ffprobe output"""
    try:
        for stream in video_info.get('streams', []):
            if stream.get('codec_type') == 'video':
                fps_str = stream.get('r_frame_rate', '30/1')
                if '/' in fps_str:
                    num, den = fps_str.split('/')
                    return float(num) / float(den)
                return float(fps_str)
        return 30.0  # Default fallback
    except (ValueError, KeyError):
        return 30.0


def is_frame_black(frame: np.ndarray, threshold: float = 0.5) -> bool:
    """
    Determine if a frame is considered 'black' based on threshold
    
    Args:
        frame: OpenCV frame (BGR format)
        threshold: Percentage threshold (0.5 = 50% black pixels)
    
    Returns:
        True if frame is considered black
    """
    # Convert to grayscale
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    # Count pixels below a low threshold (considered black)
    black_pixels = np.sum(gray < 30)  # Pixels with value < 30 are considered black
    total_pixels = gray.shape[0] * gray.shape[1]
    
    black_percentage = black_pixels / total_pixels
    return black_percentage >= threshold


def find_first_non_black_frame(video_path: str, black_threshold: float = 0.5, 
                              max_frames_to_check: int = 300) -> Optional[float]:
    """
    Find the timestamp of the first non-black frame
    
    Args:
        video_path: Path to video file
        black_threshold: Threshold for considering a frame black (0.5 = 50%)
        max_frames_to_check: Maximum number of frames to check from beginning
    
    Returns:
        Timestamp in seconds of first non-black frame, or None if all frames are black
    """
    try:
        print(f"Analyzing video for black frames: {os.path.basename(video_path)}")
        
        # Open video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"Error: Could not open video file: {video_path}")
            return None
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0:
            fps = 30.0  # Default fallback
        
        frame_count = 0
        consecutive_non_black = 0
        required_consecutive = 3  # Require 3 consecutive non-black frames to be sure
        
        print(f"Scanning frames (threshold: {black_threshold*100}% black)...")
        
        while frame_count < max_frames_to_check:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            if is_frame_black(frame, black_threshold):
                consecutive_non_black = 0
                if frame_count % 30 == 0:  # Progress update every 30 frames
                    print(f"  Frame {frame_count}: BLACK")
            else:
                consecutive_non_black += 1
                print(f"  Frame {frame_count}: NON-BLACK (consecutive: {consecutive_non_black})")
                
                if consecutive_non_black >= required_consecutive:
                    # Found enough consecutive non-black frames
                    first_good_frame = frame_count - consecutive_non_black + 1
                    timestamp = (first_good_frame - 1) / fps
                    print(f"  First non-black frame found at frame {first_good_frame} (timestamp: {timestamp:.3f}s)")
                    cap.release()
                    return timestamp
        
        cap.release()
        
        if frame_count >= max_frames_to_check:
            print(f"  Checked {max_frames_to_check} frames - all appear to be black")
        else:
            print(f"  Reached end of video ({frame_count} frames) - all frames appear to be black")
        
        return None
        
    except Exception as e:
        print(f"Error analyzing video: {str(e)}")
        return None


def validate_video_file(file_path: str) -> bool:
    """Validate if file is a supported video format"""
    if not os.path.exists(file_path):
        return False
    
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv', '.m4v'}
    return Path(file_path).suffix.lower() in video_extensions


def generate_output_path(input_path: str) -> str:
    """Generate output path with appropriate suffix"""
    path = Path(input_path)
    suffix = "_black_frames_removed"
    return str(path.parent / f"{path.stem}{suffix}{path.suffix}")


def trim_black_frames(input_path: str, start_time: float) -> bool:
    """
    Trim black frames from the beginning of video using stream copy for maximum quality
    
    Args:
        input_path: Path to input video
        start_time: Time in seconds to start from (skip black frames)
    
    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"Trimming black frames from: {os.path.basename(input_path)}")
        print(f"Starting from: {start_time:.3f} seconds")
        
        # Generate output path
        output_path = generate_output_path(input_path)
        
        # Check if output already exists
        if os.path.exists(output_path):
            response = input(f"Output file already exists: {os.path.basename(output_path)}\nOverwrite? (y/N): ")
            if response.lower() != 'y':
                print("Operation cancelled.")
                return False
        
        print(f"Output will be saved to: {os.path.basename(output_path)}")
        print("\nStarting video processing...")
        
        # Build ffmpeg command with stream copy for maximum quality
        cmd = [
            'ffmpeg', '-ss', str(start_time), '-i', input_path,
            '-c', 'copy',  # Stream copy - no re-encoding
            '-avoid_negative_ts', 'make_zero',  # Handle timestamp issues
            '-y',  # Overwrite output
            output_path
        ]
        
        print("Running ffmpeg command...")
        print("Using stream copy for maximum quality preservation...")
        
        # Execute ffmpeg
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"\n✓ Successfully trimmed black frames!")
            print(f"Output saved to: {os.path.basename(output_path)}")
            
            # Show file sizes for comparison
            input_size = os.path.getsize(input_path) / (1024 * 1024)
            output_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"Original size: {input_size:.1f} MB")
            print(f"Trimmed size: {output_size:.1f} MB")
            print(f"Removed: {start_time:.3f} seconds from beginning")
            
            return True
        else:
            print(f"\n✗ Error processing video:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"\nError: {str(e)}")
        return False


def process_single_video(video_path: str, black_threshold: float = 0.5) -> bool:
    """Process a single video file"""
    if not validate_video_file(video_path):
        print(f"Error: Invalid or unsupported video file: {video_path}")
        return False

    # Find first non-black frame
    start_time = find_first_non_black_frame(video_path, black_threshold)

    if start_time is None:
        print("No black frames detected at the beginning, or entire video is black.")
        return False

    if start_time <= 0.1:  # Less than 0.1 seconds
        print("No significant black frames detected at the beginning (< 0.1s).")
        return False

    # Trim the video
    return trim_black_frames(video_path, start_time)


def read_file_paths_from_txt(txt_file_path: str) -> List[str]:
    """Read file paths from a text file (one per line)"""
    file_paths = []

    try:
        with open(txt_file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line or line.startswith('#'):  # Skip empty lines and comments
                    continue

                # Remove quotes if present
                if (line.startswith('"') and line.endswith('"')) or \
                   (line.startswith("'") and line.endswith("'")):
                    line = line[1:-1]

                if os.path.exists(line):
                    file_paths.append(line)
                else:
                    print(f"Warning: File not found (line {line_num}): {line}")

    except Exception as e:
        print(f"Error reading text file {txt_file_path}: {str(e)}")

    return file_paths


def get_video_files_from_directory(directory_path: str) -> List[str]:
    """Get all video files from a directory"""
    video_files = []
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv', '.m4v'}

    try:
        for file_path in Path(directory_path).iterdir():
            if file_path.is_file() and file_path.suffix.lower() in video_extensions:
                video_files.append(str(file_path))
    except Exception as e:
        print(f"Error reading directory {directory_path}: {str(e)}")

    return sorted(video_files)


def process_multiple_videos(video_paths: List[str], black_threshold: float = 0.5) -> Tuple[int, int]:
    """
    Process multiple video files

    Returns:
        Tuple of (successful_count, total_count)
    """
    successful = 0
    total = len(video_paths)

    print(f"\nProcessing {total} video files...")
    print("=" * 50)

    for i, video_path in enumerate(video_paths, 1):
        print(f"\n[{i}/{total}] Processing: {os.path.basename(video_path)}")
        print("-" * 40)

        try:
            if process_single_video(video_path, black_threshold):
                successful += 1
                print(f"✓ Successfully processed: {os.path.basename(video_path)}")
            else:
                print(f"✗ Failed to process: {os.path.basename(video_path)}")
        except Exception as e:
            print(f"✗ Error processing {os.path.basename(video_path)}: {str(e)}")

        # Add separator between files
        if i < total:
            print("\n" + "=" * 50)

    return successful, total


def get_user_input() -> Tuple[List[str], float]:
    """Get user input for video files and black threshold"""
    print("\nVideo Black Frame Trimmer")
    print("=" * 40)
    print("Options:")
    print("1. Single video file")
    print("2. Multiple video files (space-separated)")
    print("3. Directory (process all video files)")
    print("4. Text file list (one file path per line)")

    while True:
        choice = input("\nEnter your choice (1-4): ").strip()

        if choice == '1':
            # Single file
            file_path = input("Enter video file path (or drag & drop): ").strip()
            file_path = file_path.strip('"\'')

            if validate_video_file(file_path):
                video_files = [file_path]
                break
            else:
                print("Invalid video file. Please try again.")

        elif choice == '2':
            # Multiple files
            files_input = input("Enter video file paths (space-separated, or drag & drop multiple): ").strip()
            # Simple split - could be improved for quoted paths with spaces
            file_paths = [f.strip('"\'') for f in files_input.split()]

            video_files = [f for f in file_paths if validate_video_file(f)]

            if video_files:
                print(f"Found {len(video_files)} valid video files")
                break
            else:
                print("No valid video files found. Please try again.")

        elif choice == '3':
            # Directory
            dir_path = input("Enter directory path (or drag & drop): ").strip()
            dir_path = dir_path.strip('"\'')

            if os.path.isdir(dir_path):
                video_files = get_video_files_from_directory(dir_path)
                if video_files:
                    print(f"Found {len(video_files)} video files in directory")
                    break
                else:
                    print("No video files found in directory. Please try again.")
            else:
                print("Invalid directory path. Please try again.")

        elif choice == '4':
            # Text file list
            txt_path = input("Enter text file path (or drag & drop): ").strip()
            txt_path = txt_path.strip('"\'')

            if os.path.isfile(txt_path) and txt_path.lower().endswith('.txt'):
                video_files = read_file_paths_from_txt(txt_path)
                video_files = [f for f in video_files if validate_video_file(f)]

                if video_files:
                    print(f"Found {len(video_files)} valid video files in text file")
                    break
                else:
                    print("No valid video files found in text file. Please try again.")
            else:
                print("Invalid text file. Please try again.")

        else:
            print("Invalid choice. Please enter 1, 2, 3, or 4.")

    # Get black threshold
    while True:
        threshold_input = input(f"\nEnter black threshold percentage (default 50%, press Enter for default): ").strip()

        if not threshold_input:
            black_threshold = 0.5
            break

        try:
            threshold_value = float(threshold_input)
            if 0 < threshold_value <= 100:
                black_threshold = threshold_value / 100.0
                break
            else:
                print("Threshold must be between 1 and 100")
        except ValueError:
            print("Please enter a valid number")

    return video_files, black_threshold


def main():
    """Main function"""
    print("Video Black Frame Trimmer - v1.0")
    print("=" * 40)

    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        print("Error: Missing dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPlease install missing dependencies and try again.")
        input("Press Enter to exit...")
        return

    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description='Remove black frames from the beginning of video files',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python video_black_frame_trimmer.py video.mp4
    python video_black_frame_trimmer.py video1.mp4 video2.mp4 video3.mp4
    python video_black_frame_trimmer.py --directory /path/to/videos
    python video_black_frame_trimmer.py --txt-list video_list.txt
    python video_black_frame_trimmer.py video.mp4 --threshold 60
        """
    )

    parser.add_argument('files', nargs='*', help='Video files to process')
    parser.add_argument('--directory', '-d', help='Process all video files in directory')
    parser.add_argument('--txt-list', '-t', help='Process video files listed in text file')
    parser.add_argument('--threshold', '-th', type=float, default=50.0,
                       help='Black threshold percentage (default: 50%%)')

    args = parser.parse_args()

    # Convert threshold to decimal
    black_threshold = args.threshold / 100.0
    if not (0 < black_threshold <= 1.0):
        print("Error: Threshold must be between 1 and 100")
        return

    video_files = []

    # Determine input method
    if args.directory:
        # Directory processing
        if not os.path.isdir(args.directory):
            print(f"Error: Directory not found: {args.directory}")
            input("Press Enter to exit...")
            return

        video_files = get_video_files_from_directory(args.directory)
        if not video_files:
            print(f"No video files found in directory: {args.directory}")
            input("Press Enter to exit...")
            return

        print(f"Found {len(video_files)} video files in directory")

    elif args.txt_list:
        # Text file list processing
        if not os.path.isfile(args.txt_list):
            print(f"Error: Text file not found: {args.txt_list}")
            input("Press Enter to exit...")
            return

        video_files = read_file_paths_from_txt(args.txt_list)
        video_files = [f for f in video_files if validate_video_file(f)]

        if not video_files:
            print(f"No valid video files found in text file: {args.txt_list}")
            input("Press Enter to exit...")
            return

        print(f"Found {len(video_files)} valid video files in text file")

    elif args.files:
        # Command line files
        video_files = [f for f in args.files if validate_video_file(f)]

        if not video_files:
            print("No valid video files provided")
            input("Press Enter to exit...")
            return

        invalid_files = [f for f in args.files if not validate_video_file(f)]
        if invalid_files:
            print("Warning: Invalid video files ignored:")
            for f in invalid_files:
                print(f"  - {f}")

    else:
        # Interactive mode
        video_files, black_threshold = get_user_input()

    # Process videos
    if len(video_files) == 1:
        # Single video
        print(f"\nProcessing single video with {black_threshold*100}% black threshold...")
        success = process_single_video(video_files[0], black_threshold)

        if success:
            print("\n" + "=" * 40)
            print("Processing completed successfully!")
        else:
            print("\n" + "=" * 40)
            print("Processing failed!")

    else:
        # Multiple videos
        print(f"\nProcessing {len(video_files)} videos with {black_threshold*100}% black threshold...")
        successful, total = process_multiple_videos(video_files, black_threshold)

        print("\n" + "=" * 50)
        print("PROCESSING SUMMARY")
        print("=" * 50)
        print(f"Total videos processed: {total}")
        print(f"Successful: {successful}")
        print(f"Failed: {total - successful}")

        if successful == total:
            print("\n✓ All videos processed successfully!")
        elif successful > 0:
            print(f"\n⚠ {successful} out of {total} videos processed successfully")
        else:
            print("\n✗ No videos were processed successfully")

    input("\nPress Enter to exit...")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        input("Press Enter to exit...")
    except Exception as e:
        print(f"\nUnexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
