"""
Test script for the TkinterVideo player to verify that the fixes work correctly.
This script creates a simple GUI with a video player and loads a test video.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk
import threading
import time
import glob

# Add the parent directory to the path so we can import from it
sys.path.append('..')

# Import TkinterVideo
try:
    from tkVideoPlayer import TkinterVideo
except ImportError:
    print("Error: tkVideoPlayer not found. Make sure it's installed.")
    print("You can install it with: pip install tkvideoplayer")
    sys.exit(1)

class TestVideoPlayer:
    def __init__(self, root):
        self.root = root
        self.root.title("TkinterVideo Test")
        self.root.geometry("800x600")
        
        # Set custom exception handler
        self.root.report_callback_exception = self.report_callback_exception
        
        # Create variables
        self.video_load_lock = threading.Lock()
        self.preview_loading = False
        self.current_preview = None
        self._restarting_preview = False
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create a frame for the video player
        video_frame = ttk.LabelFrame(main_frame, text="Video Player")
        video_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Create a frame to hold the video player
        self.player_frame = ttk.Frame(video_frame)
        self.player_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Initialize the video player
        try:
            self.video_player = TkinterVideo(master=self.player_frame, scaled=True, keep_aspect=True)
            self.video_player.pack(fill=tk.BOTH, expand=True)
            
            # Set a minimum height for the video player
            self.video_player.config(height=400)
            
            # Bind click event to pause/resume the video
            self.video_player.bind("<Button-1>", self.toggle_playback)
        except Exception as e:
            print(f"Error initializing video player: {e}")
            self.video_player = None
            ttk.Label(self.player_frame, text="Video player initialization failed.").pack(pady=10)
        
        # Create a label to show status
        self.status_label = ttk.Label(video_frame, text="No video loaded")
        self.status_label.pack(fill=tk.X, padx=5, pady=5)
        
        # Create buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="Load Test Video", command=self.load_test_video).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Play", command=self.play_video).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Pause", command=self.pause_video).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Restart", command=self.restart_video).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Simulate Error", command=self.simulate_error).pack(side=tk.LEFT, padx=5)
        
        # Start checking for videos in the temp directory
        self.check_for_videos()
    
    def report_callback_exception(self, exc, val, tb):
        """Custom exception handler to prevent the application from closing on unhandled exceptions"""
        import traceback
        print("Exception in Tkinter callback:")
        print(f"Exception type: {exc}")
        print(f"Exception value: {val}")
        print("Traceback:")
        traceback.print_tb(tb)
        
        # Show error message to user
        error_message = f"An error occurred: {val}\n\nThe application will continue running, but some features may not work correctly."
        tk.messagebox.showerror("Error", error_message)
    
    def load_test_video(self):
        """Load a test video from the temp directory"""
        try:
            # Check if the video player is available
            if not hasattr(self, 'video_player') or self.video_player is None:
                print("Video player is not available")
                return
            
            # Look for video files in the temp directory
            temp_dir = os.path.join('..', 'temp')
            if not os.path.exists(temp_dir):
                print(f"Temp directory not found: {temp_dir}")
                return
            
            # Look for latent preview files
            preview_files = glob.glob(os.path.join(temp_dir, "*latent_preview_*.mp4"))
            if not preview_files:
                preview_files = glob.glob(os.path.join(temp_dir, "*latent_preview_*.webm"))
            
            if not preview_files:
                print("No preview files found")
                self.status_label.config(text="No preview files found in temp directory")
                return
            
            # Sort by modification time to get the latest
            latest_preview = max(preview_files, key=os.path.getmtime)
            
            # Use a lock to prevent multiple video loading operations
            if not self.video_load_lock.acquire(blocking=False):
                print("Another video is already being loaded")
                return
            
            try:
                # Set the loading flag
                self.preview_loading = True
                
                # Store the current preview path
                self.current_preview = latest_preview
                
                # Initialize the restarting flag if not already set
                self._restarting_preview = False
                
                # Safely stop and unload any existing video to prevent errors
                try:
                    if hasattr(self, 'video_player') and self.video_player is not None:
                        self.video_player.pause()
                        self.video_player.unbind("<<Ended>>")
                except Exception as e:
                    print(f"Error pausing video: {e}")
                
                # Load the video with a delay
                def load_video():
                    try:
                        # Check if the video player is still available
                        if not hasattr(self, 'video_player') or self.video_player is None:
                            print("Video player is no longer available")
                            return
                        
                        # Check if the file still exists
                        if not os.path.exists(latest_preview):
                            print(f"Preview file no longer exists: {latest_preview}")
                            return
                        
                        # Load the video
                        self.video_player.load(latest_preview)
                        
                        # Set up a video ended event handler to restart the video (looping)
                        self.video_player.bind("<<Ended>>", self.restart_video)
                        
                        # Play the video
                        self.video_player.play()
                        
                        # Update the status label
                        self.status_label.config(text=f"Playing: {os.path.basename(latest_preview)}")
                    except Exception as e:
                        print(f"Error loading video: {e}")
                        self.status_label.config(text=f"Error loading video: {str(e)}")
                    finally:
                        # Reset the loading flag
                        self.preview_loading = False
                        # Release the lock
                        self.video_load_lock.release()
                
                # Schedule the video loading with a delay
                self.root.after(500, load_video)
            
            except Exception as e:
                print(f"Error loading test video: {e}")
                self.status_label.config(text=f"Error: {str(e)}")
                # Reset the loading flag
                self.preview_loading = False
                # Release the lock
                self.video_load_lock.release()
        
        except Exception as e:
            print(f"Unexpected error in load_test_video: {e}")
    
    def play_video(self):
        """Play the video"""
        try:
            if hasattr(self, 'video_player') and self.video_player is not None:
                self.video_player.play()
                self.status_label.config(text="Playing video")
        except Exception as e:
            print(f"Error playing video: {e}")
    
    def pause_video(self):
        """Pause the video"""
        try:
            if hasattr(self, 'video_player') and self.video_player is not None:
                self.video_player.pause()
                self.status_label.config(text="Paused")
        except Exception as e:
            print(f"Error pausing video: {e}")
    
    def restart_video(self, event=None):
        """Restart the video"""
        try:
            if not hasattr(self, 'video_player') or self.video_player is None:
                return
            
            if not hasattr(self, 'current_preview') or not self.current_preview:
                return
            
            # Check if we're already in the process of restarting
            if hasattr(self, '_restarting_preview') and self._restarting_preview:
                return
            
            # Set flag to prevent multiple restarts
            self._restarting_preview = True
            
            # Add a small delay before restarting
            def delayed_restart():
                try:
                    # Check if the video player is still available
                    if not hasattr(self, 'video_player') or self.video_player is None:
                        return
                    
                    # Seek to the beginning of the video
                    self.video_player.seek(0)
                    # Play the video again
                    self.video_player.play()
                    # Update the status label
                    self.status_label.config(text="Restarted video")
                except Exception as e:
                    print(f"Error restarting video: {e}")
                finally:
                    # Reset the flag
                    self._restarting_preview = False
            
            # Schedule the restart with a delay
            self.root.after(200, delayed_restart)
        
        except Exception as e:
            print(f"Unexpected error in restart_video: {e}")
            # Reset the flag
            self._restarting_preview = False
    
    def toggle_playback(self, event=None):
        """Toggle play/pause of the video when clicked"""
        try:
            if hasattr(self, 'video_player') and self.video_player is not None:
                try:
                    if self.video_player.is_paused():
                        self.video_player.play()
                        self.status_label.config(text="Playing video")
                    else:
                        self.video_player.pause()
                        self.status_label.config(text="Paused")
                except Exception as e:
                    print(f"Error toggling video playback: {e}")
        except Exception as e:
            print(f"Unexpected error in toggle_playback: {e}")
    
    def simulate_error(self):
        """Simulate an error in the video player"""
        try:
            if hasattr(self, 'video_player') and self.video_player is not None:
                # Force an error by setting the container to None
                self.video_player._container = None
                # Try to access a method that will cause an error
                self.video_player.play()
        except Exception as e:
            print(f"Simulated error: {e}")
    
    def check_for_videos(self):
        """Check for new videos in the temp directory"""
        try:
            # Look for video files in the temp directory
            temp_dir = os.path.join('..', 'temp')
            if os.path.exists(temp_dir):
                # Look for latent preview files
                preview_files = glob.glob(os.path.join(temp_dir, "*latent_preview_*.mp4"))
                if not preview_files:
                    preview_files = glob.glob(os.path.join(temp_dir, "*latent_preview_*.webm"))
                
                if preview_files:
                    # Update the status label with the number of preview files found
                    self.status_label.config(text=f"Found {len(preview_files)} preview files in temp directory")
        except Exception as e:
            print(f"Error checking for videos: {e}")
        
        # Schedule the next check
        self.root.after(5000, self.check_for_videos)

def main():
    root = tk.Tk()
    app = TestVideoPlayer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
