#!/usr/bin/env python
"""
batch_f1_lock.py

This script serves as a lock mechanism for the FramePack F1 model processing.
It detects whether image or video files are being processed and routes them
to the appropriate script (batch_f1.py for images, batch_f1_video.py for videos).

The lock system prevents multiple instances from running simultaneously,
which could cause memory issues or conflicts.

Usage:
  python batch_f1_lock.py [arguments]

All arguments are passed through to the appropriate processing script.
"""

import os
import sys
import time
import argparse
import subprocess
import traceback
from pathlib import Path

# Lock file path
LOCK_FILE = "framepack_f1_lock.lock"

# Function to check if a file is a video file
def is_video_file(file_path):
    """Check if a file is a video file based on its extension"""
    valid_video_extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
    ext = Path(file_path).suffix.lower()
    return ext in valid_video_extensions

# Function to check for video files in command line arguments and input files
def check_for_video_files(args):
    """
    Check if any video files are being processed based on command line arguments
    and input files, OR if F1 chain video mode is enabled.

    Returns:
        bool: True if video files are detected or F1 chain video mode is enabled, False otherwise
    """
    # CRITICAL FIX: For prompt chain mode, handle routing specially
    if hasattr(args, 'prompt_chain_mode') and args.prompt_chain_mode:
        chain_index = getattr(args, 'chain_index', 0)
        f1_chain_use_video = getattr(args, 'f1_chain_use_video', False)

        if chain_index == 0:
            # First step: Always use batch_f1.py regardless of F1 video mode
            # The first step processes the original input files (images)
            print(f"Prompt chain mode: First step (chain_index 0). Using batch_f1.py for original input processing.")
            return False
        elif f1_chain_use_video:
            # Subsequent steps with F1 video mode: Use batch_f1_video.py
            print(f"Prompt chain mode: Step {chain_index + 1} with F1 video mode enabled. Using batch_f1_video.py for video processing.")
            return True
        else:
            # Subsequent steps without F1 video mode: Use batch_f1.py (will use end frame from previous step)
            print(f"Prompt chain mode: Step {chain_index + 1} without F1 video mode. Using batch_f1.py for end frame processing.")
            return False

    # Non-prompt-chain mode: Check for F1 chain video mode (legacy behavior)
    if hasattr(args, 'f1_chain_use_video') and args.f1_chain_use_video:
        chain_index = getattr(args, 'chain_index', 0)
        if chain_index > 0:
            print(f"F1 chain video mode enabled (--f1_chain_use_video) for chain step {chain_index + 1}. Using batch_f1_video.py for prompt chain processing.")
            return True
        else:
            print("F1 chain video mode enabled, but this is the first step (chain_index 0). Checking actual input files to determine processing type.")

    # Check direct file arguments
    if hasattr(args, 'files') and args.files:
        for file_path in args.files:
            if is_video_file(file_path):
                print(f"Detected video file in --files argument: {file_path}")
                return True

    # Check file list
    if hasattr(args, 'file_list') and args.file_list and os.path.exists(args.file_list):
        try:
            with open(args.file_list, 'r', encoding='utf-8') as f:
                for line in f:
                    file_path = line.strip()
                    if file_path and os.path.exists(file_path) and is_video_file(file_path):
                        print(f"Detected video file in file list: {file_path}")
                        return True
        except Exception as e:
            print(f"Error reading file list: {e}")

    # Check unified list
    if hasattr(args, 'unified_list') and args.unified_list and os.path.exists(args.unified_list):
        try:
            with open(args.unified_list, 'r', encoding='utf-8') as f:
                for line in f:
                    file_path = line.strip()
                    if file_path and os.path.exists(file_path) and is_video_file(file_path):
                        print(f"Detected video file in unified list: {file_path}")
                        return True
        except Exception as e:
            print(f"Error reading unified list: {e}")

    # Check input directory if specified
    if hasattr(args, 'input_dir') and args.input_dir and os.path.isdir(args.input_dir):
        for root, _, files in os.walk(args.input_dir):
            for file in files:
                if is_video_file(file):
                    print(f"Detected video file in input directory: {os.path.join(root, file)}")
                    return True

    return False

# Function to acquire lock
def acquire_lock():
    """
    Try to acquire the lock file.

    Returns:
        bool: True if lock was acquired, False otherwise
    """
    if os.path.exists(LOCK_FILE):
        # Check if the lock file is stale (older than 2 hours)
        lock_age = time.time() - os.path.getmtime(LOCK_FILE)
        if lock_age > 7200:  # 2 hours in seconds
            print(f"Warning: Found stale lock file (age: {lock_age:.1f} seconds). Removing it.")
            try:
                os.remove(LOCK_FILE)
            except Exception as e:
                print(f"Error removing stale lock file: {e}")
                return False
        else:
            print(f"Error: Another instance is already running (lock age: {lock_age:.1f} seconds).")
            print("If you're sure no other instance is running, delete the lock file manually:")
            print(f"  {os.path.abspath(LOCK_FILE)}")
            return False

    # Create the lock file
    try:
        with open(LOCK_FILE, 'w') as f:
            f.write(f"Locked by process {os.getpid()} at {time.ctime()}\n")
        return True
    except Exception as e:
        print(f"Error creating lock file: {e}")
        return False

# Function to release lock
def release_lock():
    """Release the lock file if it exists"""
    if os.path.exists(LOCK_FILE):
        try:
            os.remove(LOCK_FILE)
            print("Lock released.")
        except Exception as e:
            print(f"Error releasing lock: {e}")

# Main function
# Function to read prompts from a prompt list file
def read_prompt_list(prompt_list_file):
    """
    Read prompts from a prompt list file.

    Args:
        prompt_list_file (str): Path to the prompt list file

    Returns:
        list: List of prompts from the file, or empty list if file doesn't exist or is empty
    """
    prompt_list = []
    if os.path.exists(prompt_list_file):
        try:
            with open(prompt_list_file, 'r', encoding='utf-8') as f:
                prompt_list = [line.strip() for line in f.readlines() if line.strip() and not line.strip().startswith('#')]
            print(f"Loaded {len(prompt_list)} prompts from {prompt_list_file}")
        except Exception as e:
            print(f"Error loading prompt list: {e}")
    else:
        print(f"Warning: Prompt list file not found: {prompt_list_file}")

    return prompt_list


# convert_lora_arguments function removed - F1 now supports multiple LoRAs natively


def main():
    """Main function to handle lock and route to appropriate script"""
    # Parse arguments
    parser = argparse.ArgumentParser(description="FramePack F1 Lock System")

    # Add all possible arguments that might be passed to either script
    # These will be parsed and passed through to the appropriate script
    parser.add_argument("--input_dir", type=str, help="Directory containing input files")
    parser.add_argument("--output_dir", type=str, help="Directory to save output files")
    parser.add_argument("--prompt", type=str, help="Prompt to guide the generation")
    parser.add_argument("--seed", type=int, help="Random seed, -1 for random")
    parser.add_argument("--use_teacache", action="store_true", help="Use TeaCache for faster processing")
    parser.add_argument("--video_length", type=float, help="Video length in seconds")
    parser.add_argument("--steps", type=int, help="Number of sampling steps")
    parser.add_argument("--distilled_cfg", type=float, help="Distilled CFG scale")
    parser.add_argument("--flow_shift", type=float, help="Flow shift parameter, 0.0=auto-calculated, 1.0-10.0=manual override")
    parser.add_argument("--gpu_memory", type=float, help="GPU memory preservation in GB")
    parser.add_argument("--mp4_crf", type=int, help="MP4 compression quality")
    parser.add_argument("--randomize_order", action="store_true", help="Randomize processing order")
    parser.add_argument("--filter_black_transparent", action="store_true", help="Filter out images with more than 10%% black or transparent pixels")
    parser.add_argument("--clear_processed_list", action="store_true", help="Clear processed files list")
    parser.add_argument("--use_image_prompt", action="store_true", help="Use image metadata for prompt")
    parser.add_argument("--no_image_prompt", action="store_false", dest="use_image_prompt", help="Don't use image metadata")
    parser.add_argument("--overwrite", action="store_true", help="Overwrite existing output files")
    parser.add_argument("--allow_duplicates", action="store_true", help="Allow duplicate files")
    parser.add_argument("--fix_encoding", action="store_true", dest="fix_encoding", help="Fix video encoding")
    parser.add_argument("--no_fix_encoding", action="store_false", dest="fix_encoding", help="Don't fix encoding")
    parser.add_argument("--use_prompt_list_file", action="store_true", help="Use prompt list file")
    parser.add_argument("--prompt_list_file", type=str, help="Path to prompt list file")
    parser.add_argument("--files", nargs="+", type=str, help="List of specific files to process")
    parser.add_argument("--file-list", type=str, help="Path to a text file with file paths")
    parser.add_argument("--url-list", type=str, help="Path to a text file with URLs")
    parser.add_argument("--combined-list", type=str, help="Path to a text file with files and URLs")
    parser.add_argument("--unified-list", type=str, help="Path to a text file with directories, files, and URLs")
    parser.add_argument("--apply_all_prompts", action="store_true", help="Apply each prompt to each image")
    parser.add_argument("--latent_window_size", type=int, help="Latent window size")
    parser.add_argument("--temp_dir", type=str, help="Directory for temporary files")
    parser.add_argument("--show_latent_preview", action="store_true", dest="show_latent_preview", help="Show preview")
    parser.add_argument("--no_latent_preview", action="store_false", dest="show_latent_preview", help="No preview")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")

    # LoRA and optimization arguments (F1 model now supports multiple LoRAs)
    # Multiple LoRA arguments for compatibility with GUI
    parser.add_argument("--lora_file_1", type=str, help="Path to first LoRA file")
    parser.add_argument("--lora_multiplier_1", type=float, help="First LoRA strength multiplier, range 0.0-1.0")
    parser.add_argument("--lora_file_2", type=str, help="Path to second LoRA file")
    parser.add_argument("--lora_multiplier_2", type=float, help="Second LoRA strength multiplier, range 0.0-1.0")
    parser.add_argument("--lora_file_3", type=str, help="Path to third LoRA file")
    parser.add_argument("--lora_multiplier_3", type=float, help="Third LoRA strength multiplier, range 0.0-1.0")

    # Backward compatibility arguments
    parser.add_argument("--lora_file", type=str, help="Path to LoRA file (backward compatibility)")
    parser.add_argument("--lora_multiplier", type=float, help="LoRA strength multiplier (backward compatibility)")
    parser.add_argument("--fp8_optimization", action="store_true", help="Enable FP8 optimization for memory efficiency")
    parser.add_argument("--custom_model_path", type=str, help="Path to custom merged model directory")

    # Prompt chain arguments
    parser.add_argument("--prompt_chain_mode", action="store_true", help="Enable prompt chain processing mode")
    parser.add_argument("--chain_index", type=int, help="Current index in the prompt chain (0-based)")
    parser.add_argument("--chain_total", type=int, help="Total number of prompts in the chain")
    parser.add_argument("--use_chain_input", action="store_true", help="Use previous chain step's end frame as input")
    parser.add_argument("--f1_chain_use_video", action="store_true", help="For F1 chains: use video input instead of last frame")
    parser.add_argument("--job_id", type=str, help="Job ID to use for consistent naming across chain steps")

    # Video-specific arguments
    parser.add_argument("--resolution", type=int, help="Maximum resolution for video")
    parser.add_argument("--no_resize", action="store_true", help="Don't resize input video")
    parser.add_argument("--num_clean_frames", type=int, help="Number of clean frames from input video")
    parser.add_argument("--vae_batch", type=int, help="VAE batch size for processing input video")

    # Video trimming options
    parser.add_argument("--trim_seconds", type=float, help="Number of seconds to trim from video")
    parser.add_argument("--trim_from_beginning", action="store_true", help="Trim from beginning of video instead of end")
    parser.add_argument("--no_trim_from_beginning", action="store_false", dest="trim_from_beginning", help="Trim from end of video")

    # Auto crop to face arguments
    parser.add_argument("--auto_crop_to_face", action="store_true", default=False,
                        help="Automatically crop all images to focus on detected faces during generation")
    parser.add_argument("--auto_crop_fill_percentage", type=int, default=60,
                        help="Face fill percentage for auto crop (10-95, default: 60)")
    parser.add_argument("--auto_crop_padding_pixels", type=int, default=0,
                        help="Additional pixels to add to the specified side(s) before auto cropping (default: 0)")
    parser.add_argument("--auto_crop_padding_side", type=str, default="top",
                        choices=["top", "bottom", "left", "right", "top_bottom", "left_right"],
                        help="Which side(s) to add padding to during auto cropping (default: top)")

    # Text-to-video arguments
    parser.add_argument("--use_noise", action="store_true", default=False,
                        help="Use noise method for text-to-video instead of transparent images (default: False)")
    parser.add_argument("--pixel_trick", action="store_true", default=False,
                        help="Add 1-pixel opaque dot to transparent images to avoid black-frame trap (default: False)")

    # LoRA keyword arguments
    parser.add_argument("--append_lora_keywords", action="store_true", default=False,
                        help="Whether LoRA keywords were appended to the prompt (for metadata tracking)")

    # Skip no face arguments
    parser.add_argument("--skip_no_face", action="store_true", default=False,
                        help="Skip generation if no face is detected when auto crop to face is enabled")
    parser.add_argument("--skip_multiple_faces", action="store_true", default=False,
                        help="Skip entire image when multiple faces are detected")
    parser.add_argument("--no_crop_multiple_faces", action="store_true", default=False,
                        help="Skip cropping but still process original image when multiple faces are detected")

    args = parser.parse_args()

    # F1 model now supports multiple LoRAs - no conversion needed
    # Just pass through all LoRA arguments to the underlying scripts

    # Try to acquire lock
    if not acquire_lock():
        return 1

    try:
        # Determine which processor to use based on prompt chain logic or file detection
        use_video_processor = False

        # For prompt chains, use special routing logic
        if args.prompt_chain_mode and args.chain_index is not None:
            f1_chain_use_video = getattr(args, 'f1_chain_use_video', False)

            if args.chain_index == 0:
                # First step: Always use batch_f1.py regardless of F1 video mode
                print("Prompt chain mode: First step (chain_index 0). Using batch_f1.py for original input processing.")
                use_video_processor = False
            elif f1_chain_use_video:
                # Subsequent steps with F1 video mode: Use batch_f1_video.py
                print(f"Prompt chain mode: Step {args.chain_index + 1} with F1 video mode enabled. Using batch_f1_video.py for video processing.")
                use_video_processor = True
            else:
                # Subsequent steps without F1 video mode: Use batch_f1.py
                print(f"Prompt chain mode: Step {args.chain_index + 1} without F1 video mode. Using batch_f1.py for image processing.")
                use_video_processor = False
        else:
            # Normal file detection logic for non-chain processing
            use_video_processor = check_for_video_files(args)

        if use_video_processor:
            print("Video files detected. Using batch_f1_video.py for processing.")

            # Handle prompt list for video files
            if args.use_prompt_list_file and args.prompt_list_file:
                print(f"Prompt list file specified: {args.prompt_list_file}")
                prompt_list = read_prompt_list(args.prompt_list_file)

                if prompt_list:
                    # Use the first prompt from the list for videos
                    # This maintains the priority order where prompt list has higher priority than fallback prompt
                    print(f"Using first prompt from prompt list: {prompt_list[0]}")

                    # Create a new argument list without prompt list related arguments
                    video_args = []
                    skip_next = False

                    for i, arg in enumerate(sys.argv[1:]):
                        if skip_next:
                            skip_next = False
                            continue

                        # Skip the original prompt argument and its value
                        if arg == "--prompt" and i + 1 < len(sys.argv[1:]):
                            skip_next = True
                            continue
                        elif arg.startswith("--prompt="):
                            continue
                        # Skip prompt list related arguments
                        elif arg == "--use_prompt_list_file":
                            continue
                        elif arg == "--prompt_list_file" and i + 1 < len(sys.argv[1:]):
                            skip_next = True
                            continue
                        elif arg == "--apply_all_prompts":
                            continue
                        elif arg.startswith("--use_prompt_list_file=") or arg.startswith("--prompt_list_file=") or arg.startswith("--apply_all_prompts="):
                            continue
                        else:
                            video_args.append(arg)

                    # No LoRA conversion needed - F1 now supports multiple LoRAs

                    # Add the prompt from the prompt list
                    video_args.extend(["--prompt", prompt_list[0]])

                    # Run batch_f1_video.py with the modified arguments
                    cmd = [sys.executable, "batch_f1_video.py"] + video_args
                    print(f"Running command with prompt from list: {' '.join(cmd)}")

                    try:
                        result = subprocess.run(cmd)
                        exit_code = result.returncode
                        print(f"batch_f1_video.py (with prompt list) completed with exit code: {exit_code}")

                        # Handle different exit codes
                        if exit_code == 0:
                            print("Processing completed successfully")
                        elif exit_code == 1:
                            print("Processing failed with standard error code")
                        elif exit_code == -1:
                            print("Processing failed with exit code -1 (possible signal termination)")
                            # Convert -1 to 1 for consistency
                            exit_code = 1
                        else:
                            print(f"Processing failed with unexpected exit code: {exit_code}")

                        return exit_code

                    except Exception as subprocess_error:
                        print(f"Error running batch_f1_video.py (with prompt list) subprocess: {subprocess_error}")
                        traceback.print_exc()
                        return 1
                else:
                    print("No prompts found in prompt list file. Using original prompt.")

            # If no prompt list or it's empty, filter out unsupported arguments
            video_args = []
            skip_next = False

            for i, arg in enumerate(sys.argv[1:]):
                if skip_next:
                    skip_next = False
                    continue

                # Skip arguments not supported by batch_f1_video.py
                if arg == "--use_prompt_list_file":
                    print(f"Warning: Skipping argument {arg} as it's not supported by batch_f1_video.py")
                    continue
                elif arg == "--prompt_list_file" and i + 1 < len(sys.argv[1:]):
                    print(f"Warning: Skipping argument {arg} and its value as it's not supported by batch_f1_video.py")
                    skip_next = True
                    continue
                elif arg == "--apply_all_prompts":
                    print(f"Warning: Skipping argument {arg} as it's not supported by batch_f1_video.py")
                    continue
                elif arg.startswith("--use_prompt_list_file=") or arg.startswith("--prompt_list_file=") or arg.startswith("--apply_all_prompts="):
                    print(f"Warning: Skipping argument {arg} as it's not supported by batch_f1_video.py")
                    continue
                else:
                    video_args.append(arg)

            # No LoRA conversion needed - F1 now supports multiple LoRAs

            # Run batch_f1_video.py with the filtered arguments
            cmd = [sys.executable, "batch_f1_video.py"] + video_args
            print(f"Running command: {' '.join(cmd)}")

            try:
                result = subprocess.run(cmd)
                exit_code = result.returncode
                print(f"batch_f1_video.py completed with exit code: {exit_code}")

                # Handle different exit codes
                if exit_code == 0:
                    print("Processing completed successfully")
                elif exit_code == 1:
                    print("Processing failed with standard error code")
                elif exit_code == -1:
                    print("Processing failed with exit code -1 (possible signal termination)")
                    # Convert -1 to 1 for consistency
                    exit_code = 1
                else:
                    print(f"Processing failed with unexpected exit code: {exit_code}")

                return exit_code

            except Exception as subprocess_error:
                print(f"Error running batch_f1_video.py subprocess: {subprocess_error}")
                traceback.print_exc()
                return 1
        else:
            print("No video files detected. Using batch_f1.py for processing.")

            # No LoRA conversion needed - F1 now supports multiple LoRAs
            # Filter out arguments not supported by batch_f1.py
            image_args = []
            skip_next = False

            for i, arg in enumerate(sys.argv[1:]):
                if skip_next:
                    skip_next = False
                    continue

                # Skip --f1_chain_use_video as batch_f1.py doesn't support it
                if arg == "--f1_chain_use_video":
                    continue
                else:
                    image_args.append(arg)

            # For chain steps 2+, remove face cropping arguments to avoid interfering with video frames
            if args.prompt_chain_mode and args.chain_index is not None and args.chain_index > 0:
                print(f"Prompt chain step {args.chain_index + 1}: Disabling face cropping to preserve video frame integrity.")
                face_crop_args = [
                    '--auto_crop_to_face',
                    '--auto_crop_fill_percentage',
                    '--auto_crop_padding_pixels',
                    '--auto_crop_padding_side',
                    '--skip_no_face'
                ]

                # Remove face cropping arguments and their values
                filtered_args = []
                i = 0
                while i < len(image_args):
                    if image_args[i] in face_crop_args:
                        # Skip the argument
                        if (i + 1 < len(image_args) and
                            not image_args[i + 1].startswith('--') and
                            image_args[i] in ['--auto_crop_fill_percentage', '--auto_crop_padding_pixels', '--auto_crop_padding_side']):
                            # Skip both argument and its value
                            i += 2
                        else:
                            # Skip just the flag argument
                            i += 1
                    else:
                        filtered_args.append(image_args[i])
                        i += 1

                image_args = filtered_args

            # Run batch_f1.py with the filtered arguments
            cmd = [sys.executable, "batch_f1.py"] + image_args
            print(f"Running command: {' '.join(cmd)}")

            try:
                result = subprocess.run(cmd)
                exit_code = result.returncode
                print(f"batch_f1.py completed with exit code: {exit_code}")

                # Handle different exit codes
                if exit_code == 0:
                    print("Processing completed successfully")
                elif exit_code == 1:
                    print("Processing failed with standard error code")
                elif exit_code == -1:
                    print("Processing failed with exit code -1 (possible signal termination)")
                    # Convert -1 to 1 for consistency
                    exit_code = 1
                else:
                    print(f"Processing failed with unexpected exit code: {exit_code}")

                return exit_code

            except Exception as subprocess_error:
                print(f"Error running batch_f1.py subprocess: {subprocess_error}")
                traceback.print_exc()
                return 1

    except Exception as e:
        print(f"Error in batch_f1_lock.py: {e}")
        traceback.print_exc()
        return 1

    finally:
        # Always release the lock when done
        release_lock()

if __name__ == "__main__":
    sys.exit(main())
