#!/usr/bin/env python
"""
batch_f1_video.py

This script processes video files using the FramePack F1 model to extend videos with AI-generated content.
It can be used in batch mode to process multiple videos or in interactive mode for a single video.

Usage:
  python batch_f1_video.py --files video1.mp4 video2.mp4 --prompt "your prompt" --video_length 5
  python batch_f1_video.py --file-list videos.txt --prompt "your prompt"
  python batch_f1_video.py --url-list video_urls.txt
  python batch_f1_video.py --files video1.mp4 --trim_seconds 3 --trim_from_beginning

  # Interactive mode (when only a file is provided):
  python batch_f1_video.py video.mp4

  # Drag and drop support via framepack_f1_video_files.bat
"""

import os
import argparse
import torch
import numpy as np
import traceback
import shutil
import glob
from pathlib import Path
import random
from tqdm import tqdm
from PIL import Image, PngImagePlugin
import subprocess
import json
import cv2
import requests
import tempfile
import urllib.parse
import sys
import time
import threading
import gc
import signal
import atexit
from datetime import datetime

# Import auto sorter functionality
from auto_sorter import auto_sort_after_generation



# Global flag for graceful shutdown
shutdown_requested = False

def signal_handler(signum, frame):
    """Handle termination signals for immediate shutdown"""
    global shutdown_requested
    print(f"\n⚠️ Received termination signal {signum}. Initiating immediate shutdown...")
    shutdown_requested = True

    # Perform immediate cleanup
    try:
        # Clean up any temporary files
        temp_files = [
            "framepack_progress.txt",
            "stop_framepack.flag",
            "stop_queue.flag",
            "skip_generation.flag"
        ]
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    print(f"Cleaned up: {temp_file}")
                except Exception:
                    pass

        # Force CUDA cleanup if available
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                print("CUDA cache cleared during shutdown")
        except Exception:
            pass

    except Exception as e:
        print(f"Error during signal cleanup: {e}")

    print("Batch F1 Video process terminated by signal")
    sys.exit(1)

def cleanup_on_exit():
    """Cleanup function called on normal exit"""
    try:
        # Clean up temporary files
        temp_files = [
            "framepack_progress.txt"
        ]
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except Exception:
                    pass
    except Exception:
        pass

# Register signal handlers for immediate termination
if os.name == 'nt':  # Windows
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
else:  # Unix-like systems
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGQUIT, signal_handler)

# Register cleanup function for normal exit
atexit.register(cleanup_on_exit)

# Define exception classes
class MemoryExhaustionError(Exception):
    """Exception raised when memory is exhausted during processing."""
    pass

class StopGenerationRequestedException(Exception):
    """Exception raised when the user requests to stop generation."""
    pass

class ReferenceImageDeletedException(Exception):
    """Exception raised when the start frame image is deleted during generation."""
    pass

class SectionTimeoutException(Exception):
    """Exception raised when a section times out."""
    pass

# Function to check if a file is a supported video file
def is_supported_file(file_path):
    """Check if a file is a supported video or image file and handle accordingly"""
    valid_video_extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
    valid_image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']

    ext = Path(file_path).suffix.lower()

    if ext in valid_video_extensions:
        return True, None  # Supported video file
    elif ext in valid_image_extensions:
        message = f"Warning: Skipping {file_path} - not a supported video file. This script is for video processing only. To process images, use batch_f1.py or batch_f1_lock.py instead."
        return False, message
    else:
        message = f"Warning: Skipping {file_path} - not a supported media file"
        return False, message

# Function to perform aggressive memory cleanup
def aggressive_memory_cleanup():
    """Perform aggressive memory cleanup to recover from memory issues."""
    print("Performing aggressive memory cleanup...")
    # Clear CUDA cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

    # Run garbage collection multiple times
    for _ in range(3):
        gc.collect()

    # Report memory status
    if torch.cuda.is_available():
        free_mem_gb = get_cuda_free_memory_gb(gpu)
        print(f"Free VRAM after cleanup: {free_mem_gb:.2f} GB")

    return

# Set environment variable for HF_HOME
os.environ['HF_HOME'] = os.path.abspath(os.path.realpath(os.path.join(os.path.dirname(__file__), './hf_download')))

# Import required libraries
import decord
import pathlib
from datetime import datetime
import imageio_ffmpeg

from diffusers import AutoencoderKLHunyuanVideo
from transformers import LlamaModel, CLIPTextModel, LlamaTokenizerFast, CLIPTokenizer
from diffusers_helper.hunyuan import encode_prompt_conds, vae_decode, vae_encode, vae_decode_fake
from diffusers_helper.utils import save_bcthw_as_mp4, crop_or_pad_yield_mask, soft_append_bcthw, resize_and_center_crop, generate_timestamp
from diffusers_helper.models.hunyuan_video_packed import HunyuanVideoTransformer3DModelPacked
from diffusers_helper.pipelines.k_diffusion_hunyuan import sample_hunyuan
from diffusers_helper.memory import gpu, get_cuda_free_memory_gb, move_model_to_device_with_memory_preservation, offload_model_from_device_for_memory_preservation, fake_diffusers_current_device, DynamicSwapInstaller, unload_complete_models, load_model_as_complete
from transformers import SiglipImageProcessor, SiglipVisionModel
from diffusers_helper.clip_vision import hf_clip_vision_encode
from diffusers_helper.bucket_tools import find_nearest_bucket
from utils.lora_utils import merge_lora_to_state_dict
from utils.fp8_optimization_utils import optimize_state_dict_with_fp8, apply_fp8_monkey_patch

# Default settings
input_dir          = 'input'            # Directory containing input videos
output_dir         = 'outputs'          # Directory to save output videos
temp_dir           = 'temp'             # Directory for temporary files
seed               = -1                 # Random seed; -1 means random each run
use_teacache       = True               # Use TeaCache for faster processing (may affect hand quality and prompt adherence)
video_length       = 5                  # Additional video length in seconds (range: 1-120)
steps              = 25                 # Number of sampling steps per video
distilled_cfg      = 3.0                # Distilled CFG scale for model guidance (higher values increase prompt adherence but reduce video details)
flow_shift         = 0.0                # Flow shift parameter (0.0 = auto-calculated, 1.0-10.0 = manual override)
gpu_memory         = 8.0                # GPU memory to preserve (GB)
latent_window_size = 9                  # Latent window size for generation
mp4_crf            = 16                 # MP4 compression quality (0-51, lower is better)
resolution         = 640                # Maximum resolution (width or height)
no_resize          = False              # Don't resize input video
num_clean_frames   = 5                  # Number of clean frames to use from input video (higher values retain more video details)
vae_batch          = 32                 # VAE batch size for processing input video (higher values improve quality for fast motion)
fix_encoding       = True               # Fix video encoding for web compatibility
fallback_prompt    = ""                 # Fallback prompt if no other prompt source is found
use_image_prompt   = True               # Use video metadata as prompt if available
trim_seconds       = 0                  # Number of seconds to trim from video (0 means no trimming)
trim_from_beginning = False             # Whether to trim from beginning (True) or end (False)

# LoRA and optimization settings (supports up to 3 LoRAs)
# Multiple LoRAs are merged sequentially in order: LoRA 1, then LoRA 2, then LoRA 3
# Each LoRA can have its own strength multiplier for fine-tuning the effect
lora_file_1        = None               # Path to first LoRA file (None to disable)
lora_multiplier_1  = 0.8                # First LoRA strength multiplier (0.0-1.0)
lora_file_2        = None               # Path to second LoRA file (None to disable)
lora_multiplier_2  = 0.8                # Second LoRA strength multiplier (0.0-1.0)
lora_file_3        = None               # Path to third LoRA file (None to disable)
lora_multiplier_3  = 0.8                # Third LoRA strength multiplier (0.0-1.0)

# Backward compatibility
lora_file          = None               # Path to LoRA file (backward compatibility, maps to lora_file_1)
lora_multiplier    = 0.8                # LoRA strength multiplier (backward compatibility, maps to lora_multiplier_1)
fp8_optimization   = False              # Enable FP8 optimization for memory efficiency

# Function to encode video frames into latents
@torch.no_grad()
def video_encode(video_path, resolution, no_resize, vae, vae_batch_size=16, device="cuda", width=None, height=None):
    """
    Encode a video into latent representations using the VAE.

    Args:
        video_path: Path to the input video file.
        resolution: Target resolution for resizing frames.
        no_resize: Whether to keep original video resolution.
        vae: AutoencoderKLHunyuanVideo model.
        vae_batch_size: Number of frames to process per batch.
        device: Device for computation (e.g., "cuda").
        width, height: Target resolution for resizing frames (optional).

    Returns:
        start_latent: Latent of the first frame.
        input_image_np: First frame as numpy array.
        history_latents: Latents of all frames.
        fps: Frames per second of the input video.
        target_height, target_width: Dimensions of the processed video.
        input_video_pixels: Original video pixels.
    """
    # Normalize video path for Windows compatibility
    video_path = str(pathlib.Path(video_path).resolve())
    print(f"Processing video: {video_path}")

    # Check CUDA availability and fallback to CPU if needed
    if device == "cuda" and not torch.cuda.is_available():
        print("CUDA is not available, falling back to CPU")
        device = torch.device("cpu")

    try:
        # Load video and get FPS
        print("Initializing VideoReader...")
        vr = decord.VideoReader(video_path)
        fps = vr.get_avg_fps()  # Get input video FPS
        num_real_frames = len(vr)
        print(f"Video loaded: {num_real_frames} frames, FPS: {fps}")

        # Truncate to nearest latent size (multiple of 4)
        latent_size_factor = 4
        num_frames = (num_real_frames // latent_size_factor) * latent_size_factor
        if num_frames != num_real_frames:
            print(f"Truncating video from {num_real_frames} to {num_frames} frames for latent size compatibility")
        num_real_frames = num_frames

        # Get video dimensions
        first_frame = vr[0].asnumpy()
        H, W, _ = first_frame.shape

        # Determine target dimensions
        if no_resize:
            print(f"Using original video dimensions: {W}x{H}")
            target_width, target_height = W, H
        else:
            # Find nearest bucket size
            target_height, target_width = find_nearest_bucket(H, W, resolution=resolution)
            print(f"Resizing video from {W}x{H} to {target_width}x{target_height}")

        # Extract frames
        print(f"Extracting {num_frames} frames...")
        frames = []
        for i in tqdm(range(0, num_frames), desc="Extracting frames"):
            frame = vr[i].asnumpy()
            # Resize if needed
            if not no_resize:
                frame_pil = Image.fromarray(frame)
                frame_pil = frame_pil.resize((target_width, target_height), Image.LANCZOS)
                frame = np.array(frame_pil)
            frames.append(frame)

        # Convert to tensor
        input_video_pixels = np.stack(frames)  # Shape: (frames, height, width, channels)
        input_video_pixels = np.expand_dims(input_video_pixels, axis=0)  # Add batch dimension
        input_video_pixels = input_video_pixels.transpose(0, 4, 1, 2, 3)  # BCTHW format

        # Save first frame for CLIP vision encoding
        input_image_np = input_video_pixels[0, :, 0].transpose(1, 2, 0)  # First frame in HWC format

        # Convert to PyTorch tensor
        frames_pt = torch.from_numpy(input_video_pixels).float() / 127.5 - 1.0

        # Unload all models before VAE encoding
        if device == "cuda":
            # Load VAE to device with proper memory management
            print(f"Loading VAE to {device} for encoding...")
            load_model_as_complete(vae, target_device=device)
        else:
            # Just move VAE to device
            vae.to(device)

        vae.eval()

        # Encode frames in batches
        print(f"Encoding input video frames in VAE batch size {vae_batch_size}")
        latents = []
        with torch.no_grad():
            for i in tqdm(range(0, frames_pt.shape[2], vae_batch_size), desc="Encoding video frames"):
                try:
                    batch = frames_pt[:, :, i:i + vae_batch_size]  # Shape: (1, channels, batch_size, height, width)
                    batch = batch.to(device)
                    batch_latent = vae_encode(batch, vae)
                    latents.append(batch_latent.cpu())

                    # Explicitly delete batch tensors to free memory
                    del batch
                    del batch_latent

                    # Clear CUDA cache after each batch
                    if device == "cuda":
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()

                except RuntimeError as e:
                    if "out of memory" in str(e).lower():
                        # Perform aggressive cleanup
                        aggressive_memory_cleanup()

                        # Try again with a smaller batch size
                        reduced_batch_size = max(1, vae_batch_size // 2)
                        print(f"CUDA out of memory. Retrying with reduced batch size: {reduced_batch_size}")

                        # Process this batch with reduced size
                        for j in range(i, min(i + vae_batch_size, frames_pt.shape[2]), reduced_batch_size):
                            sub_batch = frames_pt[:, :, j:j + reduced_batch_size]
                            sub_batch = sub_batch.to(device)
                            sub_batch_latent = vae_encode(sub_batch, vae)
                            latents.append(sub_batch_latent.cpu())

                            # Explicitly delete tensors
                            del sub_batch
                            del sub_batch_latent

                            # Clear CUDA cache
                            if device == "cuda":
                                torch.cuda.empty_cache()
                                torch.cuda.synchronize()
                    else:
                        # Re-raise other errors
                        raise

        # Concatenate latents
        print("Concatenating latents...")
        history_latents = torch.cat(latents, dim=2)  # Shape: (1, channels, frames, height//8, width//8)
        print(f"History latents shape: {history_latents.shape}")

        # Get first frame's latent
        start_latent = history_latents[:, :, :1]  # Shape: (1, channels, 1, height//8, width//8)
        print(f"Start latent shape: {start_latent.shape}")

        # Unload all models after encoding
        if device == "cuda":
            unload_complete_models()
            print("All models unloaded and CUDA cache cleared")

        return start_latent, input_image_np, history_latents, fps, target_height, target_width, input_video_pixels

    except Exception as e:
        print(f"Error in video_encode: {str(e)}")
        traceback.print_exc()
        raise

# Function to add metadata to MP4 file
def add_metadata_to_mp4(input_file, comments):
    """Add metadata to an MP4 file using FFmpeg"""
    try:
        # Get the path to FFmpeg from imageio-ffmpeg
        ffmpeg_path = imageio_ffmpeg.get_ffmpeg_exe()

        # Create a temporary file path
        temp_file = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False).name

        # FFmpeg command using the bundled binary
        command = [
            ffmpeg_path,                   # Use imageio-ffmpeg's FFmpeg
            '-i', input_file,              # input file
            '-metadata', f'comment={comments}',  # set comment metadata
            '-c:v', 'copy',                # copy video stream without re-encoding
            '-c:a', 'copy',                # copy audio stream without re-encoding
            '-y',                          # overwrite output file if it exists
            temp_file                      # temporary output file
        ]

        # Run the command
        result = subprocess.run(command, check=True, capture_output=True, text=True)

        # Verify temp file was created
        if not os.path.exists(temp_file):
            raise Exception(f"Temp file was not created: {temp_file}")

        temp_size = os.path.getsize(temp_file)
        if temp_size == 0:
            raise Exception(f"Temp file is empty: {temp_file}")

        # Replace the original file with the new one
        shutil.move(temp_file, input_file)

        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: FFmpeg CalledProcessError adding metadata to MP4: {e}")
        print(f"ERROR: FFmpeg return code: {e.returncode}")
        print(f"ERROR: FFmpeg stderr: {e.stderr}")
        print(f"ERROR: FFmpeg stdout: {e.stdout}")
        # Clean up temp file if it exists
        if 'temp_file' in locals() and os.path.exists(temp_file):
            try:
                os.remove(temp_file)
                pass
            except Exception as cleanup_error:
                print(f"Error cleaning up temp file: {cleanup_error}")
        return False
    except Exception as e:
        print(f"ERROR: General exception adding metadata to MP4: {e}")
        print(f"ERROR: Exception type: {type(e).__name__}")
        traceback.print_exc()
        # Clean up temp file if it exists
        if 'temp_file' in locals() and os.path.exists(temp_file):
            try:
                os.remove(temp_file)
                pass
            except Exception as cleanup_error:
                print(f"Error cleaning up temp file: {cleanup_error}")
        return False

# Function to trim video using ffmpeg
def trim_video(input_path, trim_seconds, trim_from_beginning=False):
    """
    Trim video using ffmpeg without re-encoding to maintain quality.
    Creates a copy of the video in the temp folder and trims that copy.

    Args:
        input_path: Path to the input video file
        trim_seconds: Number of seconds to trim
        trim_from_beginning: If True, trim from beginning; if False, trim from end

    Returns:
        Path to the trimmed video file or original path if trimming failed
    """
    if trim_seconds <= 0:
        print(f"No trimming needed (trim_seconds={trim_seconds})")
        return input_path

    try:
        input_path = Path(input_path)

        # Ensure temp directory exists
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)

        # Create a unique output filename in the temp directory
        timestamp = int(time.time())
        trim_type = "begin" if trim_from_beginning else "end"
        output_filename = f"{input_path.stem}_trim{trim_seconds}s_{trim_type}_{timestamp}{input_path.suffix}"
        output_path = temp_dir / output_filename

        print(f"Will create trimmed copy in temp folder: {output_path}")

        # Get video duration using ffprobe
        duration_cmd = [
            "ffprobe",
            "-v", "error",
            "-show_entries", "format=duration",
            "-of", "json",
            str(input_path)
        ]

        result = subprocess.run(duration_cmd, capture_output=True, text=True, check=True)
        data = json.loads(result.stdout)
        total_duration = float(data['format']['duration'])

        print(f"Video duration: {total_duration:.2f} seconds")

        # Ensure trim_seconds is not longer than the video
        if trim_seconds >= total_duration:
            print(f"Warning: Trim seconds ({trim_seconds}) is greater than or equal to video duration ({total_duration:.2f})")
            print("Using 50% of video duration instead")
            trim_seconds = total_duration / 2

        # Calculate start and end times based on trim mode
        if trim_from_beginning:
            # Trim from beginning: start at trim_seconds, end at total_duration
            start_time = trim_seconds
            end_time = total_duration
            print(f"Trimming {trim_seconds:.2f} seconds from beginning of video")
        else:
            # Trim from end: start at 0, end at (total_duration - trim_seconds)
            start_time = 0
            end_time = total_duration - trim_seconds
            print(f"Trimming {trim_seconds:.2f} seconds from end of video")

        # Build ffmpeg command for trimming
        ffmpeg_cmd = [
            "ffmpeg",
            "-i", str(input_path),
            "-ss", str(start_time),
            "-to", str(end_time),
            "-c", "copy",  # Use stream copy to avoid re-encoding
            "-y",
            str(output_path)
        ]

        print(f"Running ffmpeg command: {' '.join(ffmpeg_cmd)}")
        subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)

        # Verify the trimmed file exists and has content
        if not output_path.exists() or output_path.stat().st_size == 0:
            print(f"Error: Trimmed file {output_path} does not exist or is empty")
            return input_path

        print(f"Successfully created trimmed copy: {input_path} -> {output_path}")
        return str(output_path)
    except Exception as e:
        print(f"Error trimming video: {e}")
        traceback.print_exc()
        return input_path

# Function to fix video encoding
def fix_video_encoding(input_path):
    """Re-encode video to ensure web compatibility with minimal quality loss using FFmpeg"""
    try:
        input_path = Path(input_path)
        output_path = input_path.with_stem(input_path.stem + "_fixed")

        ffmpeg_cmd = [
            "ffmpeg",
            "-i", str(input_path),
            "-c:v", "libx264",
            "-preset", "fast",
            "-crf", "17",  # Lower CRF for high quality
            "-c:a", "aac",
            "-b:a", "192k",
            "-movflags", "+faststart",
            "-pix_fmt", "yuv420p",
            "-y",
            str(output_path)
        ]

        subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)
        print(f"Successfully fixed encoding: {input_path} -> {output_path}")
        return output_path
    except Exception as e:
        print(f"Error fixing video encoding: {e}")
        return None


def resolve_custom_model_path_f1(custom_model_path):
    """Resolve custom model path to handle both HuggingFace cache format and simple format"""
    if not custom_model_path:
        return None

    # Convert to absolute path
    if not os.path.isabs(custom_model_path):
        custom_model_path = os.path.abspath(custom_model_path)

    # Check if it's HuggingFace cache format (models--*)
    if os.path.basename(custom_model_path).startswith("models--"):
        snapshots_dir = os.path.join(custom_model_path, "snapshots")
        if os.path.exists(snapshots_dir) and os.path.isdir(snapshots_dir):
            # Find the latest snapshot (there should typically be only one)
            snapshot_dirs = [d for d in os.listdir(snapshots_dir) if os.path.isdir(os.path.join(snapshots_dir, d))]
            if snapshot_dirs:
                # Use the first (and typically only) snapshot
                snapshot_path = os.path.join(snapshots_dir, snapshot_dirs[0])
                if os.path.exists(os.path.join(snapshot_path, "config.json")):
                    return snapshot_path

    # Check if it's simple format (config.json directly in the directory)
    if os.path.exists(os.path.join(custom_model_path, "config.json")):
        return custom_model_path

    # If neither format is found, return the original path and let the error occur
    return custom_model_path


def load_transformer_with_lora_f1(lora_file_1=None, lora_multiplier_1=0.8,
                                  lora_file_2=None, lora_multiplier_2=0.8,
                                  lora_file_3=None, lora_multiplier_3=0.8,
                                  fp8_optimization=False, custom_model_path=None,
                                  # Backward compatibility parameters
                                  lora_file=None, lora_multiplier=0.8):
    """Load F1 transformer model with optional multiple LoRAs and FP8 optimizations"""
    print("Loading F1 transformer...")

    # Handle backward compatibility - if old parameters are used, map them to new ones
    if lora_file is not None and lora_file_1 is None:
        lora_file_1 = lora_file
        lora_multiplier_1 = lora_multiplier

    # Determine model path
    if custom_model_path:
        resolved_path = resolve_custom_model_path_f1(custom_model_path)
        print(f"Loading custom F1 model from: {custom_model_path}")
        if resolved_path != custom_model_path:
            print(f"Resolved to: {resolved_path}")
        transformer = HunyuanVideoTransformer3DModelPacked.from_pretrained(
            resolved_path, torch_dtype=torch.bfloat16
        ).cpu()
    else:
        print("Loading standard F1 model")
        transformer = HunyuanVideoTransformer3DModelPacked.from_pretrained(
            'lllyasviel/FramePack_F1_I2V_HY_20250503', torch_dtype=torch.bfloat16
        ).cpu()
    transformer.eval()
    transformer.high_quality_fp32_output_for_inference = True
    print("transformer.high_quality_fp32_output_for_inference = True")
    transformer.to(dtype=torch.bfloat16)
    transformer.requires_grad_(False)

    # Apply LoRAs and/or FP8 optimizations if requested
    lora_configs = [
        (lora_file_1, lora_multiplier_1, 1),
        (lora_file_2, lora_multiplier_2, 2),
        (lora_file_3, lora_multiplier_3, 3)
    ]

    has_loras = any(lora_file is not None for lora_file, _, _ in lora_configs)

    if has_loras or fp8_optimization:
        state_dict = transformer.state_dict()

        # Apply LoRAs sequentially (should be merged before fp8 optimization)
        for lora_file, lora_multiplier, lora_index in lora_configs:
            if lora_file is not None:
                print(f"Merging LoRA {lora_index}: {os.path.basename(lora_file)} with multiplier {lora_multiplier}...")
                state_dict = merge_lora_to_state_dict(state_dict, lora_file, lora_multiplier, device=gpu)
                gc.collect()

        if fp8_optimization:
            TARGET_KEYS = ["transformer_blocks", "single_transformer_blocks"]
            EXCLUDE_KEYS = ["norm"]  # Exclude norm layers (e.g., LayerNorm, RMSNorm) from FP8
            print("Optimizing for fp8...")
            state_dict = optimize_state_dict_with_fp8(
                state_dict, gpu, TARGET_KEYS, EXCLUDE_KEYS, move_to_device=False
            )
            # apply monkey patching
            apply_fp8_monkey_patch(transformer, state_dict, use_scaled_mm=False)
            gc.collect()

        info = transformer.load_state_dict(state_dict, strict=True, assign=True)
        print(f"LoRA and/or fp8 optimization applied: {info}")

    return transformer


@torch.no_grad()
def process_single_video(video_path, output_dir, prompt="", n_prompt="", seed=-1,
                         video_length=5.0, steps=25, gs=3.0, cfg=3.0, flow_shift=0.0, gpu_memory=6.0,
                         use_teacache=False, high_vram=False, latent_window_size=9,
                         text_encoder=None, text_encoder_2=None, tokenizer=None, tokenizer_2=None,
                         vae=None, feature_extractor=None, image_encoder=None, transformer=None,
                         fix_encoding=True, mp4_crf=16, resolution=640,
                         no_resize=False, num_clean_frames=5, vae_batch=32,
                         trim_seconds=0, trim_from_beginning=False,
                         lora_file=None, lora_multiplier=0.8, fp8_optimization=False, custom_model_path=None,
                         # Prompt chain parameters
                         job_id=None, chain_index=None, chain_total=None, prompt_chain_mode=False,
                         f1_chain_use_video=False):
    """Process a single video to generate an extended video using FramePack F1"""

    # Generate a unique timestamp ID for this job (or use provided job_id for prompt chains)
    if job_id is None:
        job_id = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Store reference to temporary files for cleanup in case of exceptions
    temp_files = []

    # Handle prompt chain input - for F1 video processing, use previous video output
    if prompt_chain_mode and f1_chain_use_video and chain_index is not None and chain_index > 0:
        print("F1 prompt chain mode: Looking for previous video output...")

        # Look for the most recent video output from the previous chain step
        video_pattern = os.path.join(output_dir, f"{job_id}_*.mp4")
        video_files = glob.glob(video_pattern)
        if video_files:
            # Sort by modification time to get the most recent
            video_files.sort(key=os.path.getmtime, reverse=True)
            previous_video = video_files[0]
            print(f"F1 chain mode: Using previous video as input: {os.path.basename(previous_video)}")
            video_path = previous_video
        else:
            print(f"WARNING: F1 chain mode enabled but no previous video found with pattern {video_pattern}")

    # Print prompt chain information if enabled
    if prompt_chain_mode:
        print(f"Prompt Chain Mode: Enabled")
        print(f"Chain Index: {chain_index + 1}/{chain_total}")
        if f1_chain_use_video and chain_index is not None and chain_index > 0:
            print(f"Using Chain Input: Previous video")
        else:
            print(f"Using Chain Input: Original video")

    # Define start frame path for later use and monitoring deletion
    start_frame_path = os.path.join(output_dir, f'{job_id}_start.png')
    reference_image_path = start_frame_path  # For compatibility with existing code

    # Original video path (for reference in auto sorter)
    original_video_path = video_path

    # Use random seed if seed is -1
    if seed == -1:
        seed = random.randint(0, 2**32 - 1)
        print(f"Using random seed: {seed}")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Apply video trimming if requested
    if trim_seconds > 0:
        print(f"\nTrimming video: {trim_seconds} seconds from {'beginning' if trim_from_beginning else 'end'}")
        trimmed_video_path = trim_video(video_path, trim_seconds, trim_from_beginning)
        if trimmed_video_path != video_path:
            video_path = trimmed_video_path
            temp_files.append(video_path)  # Add to temp files for cleanup in case of errors
            print(f"Using trimmed video: {video_path}")
        else:
            print("Video trimming failed or was not needed, using original video")
    else:
        print("No video trimming requested")

    try:
        # Remove any existing stop flag file to ensure clean start
        stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
        if os.path.exists(stop_flag_path):
            try:
                os.remove(stop_flag_path)
                print(f"Removed existing stop flag file: {stop_flag_path}")
            except Exception as e:
                print(f"Warning: Could not remove existing stop flag file: {e}")

        # Clear CUDA cache before starting
        torch.cuda.empty_cache()
        print("CUDA cache cleared before processing")

        # Text encoding
        print("Text encoding...")

        # Clean GPU - unload all models before text encoding
        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )

            # Load only the text encoders for prompt encoding
            fake_diffusers_current_device(text_encoder, gpu)  # More efficient for single encoding
            load_model_as_complete(text_encoder_2, target_device=gpu)
        else:
            # In high VRAM mode, just move the text encoders to GPU
            text_encoder.to(gpu)
            text_encoder_2.to(gpu)

        # Ensure tokenizers are properly set up
        tokenizer.padding_side = "left"
        tokenizer_2.padding_side = "left"

        # Make sure we have a valid prompt
        if not prompt or prompt.strip() == "":
            prompt = "A video"
            print(f"Warning: Empty prompt provided, using default: '{prompt}'")

        # Encode the prompt using the improved encode_prompt_conds function
        print(f"Encoding prompt: '{prompt}'")
        llama_vec, clip_l_pooler = encode_prompt_conds(prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)

        # Special handling for cfg parameter (from demo_gradio_f1_video.py)
        # If cfg > 1, set distilled_cfg (gs) to 1
        if cfg > 1:
            gs = 1.0
            print(f"Using normal CFG: {cfg}, setting distilled CFG to 1")

        # Handle negative prompt based on cfg value
        if cfg == 1:
            print("Creating negative prompt embeddings (zeros for F1 model)")
            llama_vec_n, clip_l_pooler_n = torch.zeros_like(llama_vec), torch.zeros_like(clip_l_pooler)
        else:
            print(f"Using normal negative prompt with CFG: {cfg}")
            llama_vec_n, clip_l_pooler_n = encode_prompt_conds(n_prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)

        # Crop or pad the embeddings to the required length
        print("Preparing attention masks...")
        llama_vec, llama_attention_mask = crop_or_pad_yield_mask(llama_vec, length=512)
        llama_vec_n, llama_attention_mask_n = crop_or_pad_yield_mask(llama_vec_n, length=512)

        # Processing input video
        print("Video processing...")
        _, input_image_np, video_latents, fps, height, width, _ = video_encode(
            video_path, resolution, no_resize, vae, vae_batch_size=vae_batch, device=gpu
        )



        # CLIP Vision
        print("CLIP Vision encoding...")

        # Unload all models before CLIP vision encoding
        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )
            # Load only the image encoder for CLIP vision encoding
            load_model_as_complete(image_encoder, target_device=gpu)
        else:
            # In high VRAM mode, just move the image encoder to GPU
            image_encoder.to(gpu)

        # Process the input image
        print(f"Processing input image for CLIP vision, shape: {input_image_np.shape}")
        image_encoder_output = hf_clip_vision_encode(input_image_np, feature_extractor, image_encoder)
        image_encoder_last_hidden_state = image_encoder_output.last_hidden_state

        # Dtype
        llama_vec = llama_vec.to(transformer.dtype)
        llama_vec_n = llama_vec_n.to(transformer.dtype)
        clip_l_pooler = clip_l_pooler.to(transformer.dtype)
        clip_l_pooler_n = clip_l_pooler_n.to(transformer.dtype)
        image_encoder_last_hidden_state = image_encoder_last_hidden_state.to(transformer.dtype)

        # Calculate total latent sections
        total_latent_sections = (video_length * fps) / (latent_window_size * 4)
        total_latent_sections = int(max(round(total_latent_sections), 1))

        # Prepare for sampling
        print("Start sampling...")
        rnd = torch.Generator("cpu").manual_seed(seed)

        # Initialize history_latents with video latents
        history_latents = video_latents.cpu()
        total_generated_latent_frames = history_latents.shape[2]
        history_pixels = None

        # Save the start frame with metadata before generating any video sections
        print(f"Saving start frame with metadata before video generation")
        # Create start frame metadata
        start_frame_metadata = {
            "prompt": prompt,
            "negative_prompt": n_prompt,
            "seed": seed,
            "steps": steps,
            "cfg_scale": cfg,
            "distilled_cfg_scale": gs,
            "cfg_rescale": 0.0,  # Fixed guidance_rescale parameter
            "flow_shift": flow_shift,
            "use_teacache": use_teacache,
            "total_video_length": f"{video_length} seconds",
            "total_sections": total_latent_sections,
            "frame_type": "start_frame",
            "job_id": job_id,
            # Model and optimization settings
            "model_type": "f1_video"
        }

        # Save the first frame as PNG with metadata
        first_frame = Image.fromarray(input_image_np)
        # Create PngInfo object
        png_info = PngImagePlugin.PngInfo()
        # Add metadata to the PngInfo object
        png_info.add_text("FramePack", json.dumps(start_frame_metadata))
        # Save the image with metadata
        first_frame.save(start_frame_path, format="PNG", pnginfo=png_info)
        print(f"Saved start frame to {start_frame_path}")

        # Process in sections
        for section_idx in range(total_latent_sections):
            print(f"Processing section {section_idx + 1}/{total_latent_sections}")

            # Unload all models before loading transformer
            if not high_vram:
                unload_complete_models()
                # Load transformer with proper memory preservation
                move_model_to_device_with_memory_preservation(transformer, target_device=gpu, preserved_memory_gb=gpu_memory)
            else:
                # In high VRAM mode, just move transformer to GPU
                transformer.to(gpu)

            # Initialize TeaCache if enabled
            if use_teacache:
                transformer.initialize_teacache(enable_teacache=True, num_steps=steps)
            else:
                transformer.initialize_teacache(enable_teacache=False)

            # Use the same clean frames approach as demo_gradio_f1_video.py
            # Make sure history_latents is on CPU before slicing
            if history_latents.device != torch.device('cpu'):
                history_latents = history_latents.cpu()

            # Get available frames from history
            available_frames = history_latents.shape[2]
            print(f"Available frames: {available_frames}")

            # Calculate max pixel frames and adjusted latent frames
            max_pixel_frames = min(latent_window_size * 4 - 3, available_frames * 4)
            adjusted_latent_frames = max(1, (max_pixel_frames + 3) // 4)

            # Adjust num_clean_frames to match original behavior
            effective_clean_frames = max(0, num_clean_frames - 1) if num_clean_frames > 1 else 0
            effective_clean_frames = min(effective_clean_frames, available_frames - 2) if available_frames > 2 else 0
            num_2x_frames = min(2, max(1, available_frames - effective_clean_frames - 1)) if available_frames > effective_clean_frames + 1 else 0
            num_4x_frames = min(16, max(1, available_frames - effective_clean_frames - num_2x_frames)) if available_frames > effective_clean_frames + num_2x_frames else 0

            total_context_frames = num_4x_frames + num_2x_frames + effective_clean_frames
            total_context_frames = min(total_context_frames, available_frames)

            print(f"Using clean frames: 1x={effective_clean_frames}, 2x={num_2x_frames}, 4x={num_4x_frames}")

            # Create indices tensor
            indices = torch.arange(0, sum([1, num_4x_frames, num_2x_frames, effective_clean_frames, adjusted_latent_frames])).unsqueeze(0)
            clean_latent_indices_start, clean_latent_4x_indices, clean_latent_2x_indices, clean_latent_1x_indices, latent_indices = indices.split(
                [1, num_4x_frames, num_2x_frames, effective_clean_frames, adjusted_latent_frames], dim=1
            )
            clean_latent_indices = torch.cat([clean_latent_indices_start, clean_latent_1x_indices], dim=1)

            # Split history_latents dynamically based on available frames
            fallback_frame_count = 2
            context_frames = history_latents[:, :, -total_context_frames:, :, :] if total_context_frames > 0 else history_latents[:, :, :fallback_frame_count, :, :]

            if total_context_frames > 0:
                split_sizes = [num_4x_frames, num_2x_frames, effective_clean_frames]
                split_sizes = [s for s in split_sizes if s > 0]  # Remove zero sizes

                if split_sizes:
                    splits = context_frames.split(split_sizes, dim=2)
                    split_idx = 0

                    clean_latents_4x = splits[split_idx] if num_4x_frames > 0 else history_latents[:, :, :fallback_frame_count, :, :]
                    if clean_latents_4x.shape[2] < 2:  # Edge case for <=1 sec videos
                        clean_latents_4x = torch.cat([clean_latents_4x, clean_latents_4x[:, :, -1:, :, :]], dim=2)[:, :, :2, :, :]
                    split_idx += 1 if num_4x_frames > 0 else 0

                    clean_latents_2x = splits[split_idx] if num_2x_frames > 0 and split_idx < len(splits) else history_latents[:, :, :fallback_frame_count, :, :]
                    if clean_latents_2x.shape[2] < 2:  # Edge case for <=1 sec videos
                        clean_latents_2x = torch.cat([clean_latents_2x, clean_latents_2x[:, :, -1:, :, :]], dim=2)[:, :, :2, :, :]
                    split_idx += 1 if num_2x_frames > 0 else 0

                    clean_latents_1x = splits[split_idx] if effective_clean_frames > 0 and split_idx < len(splits) else history_latents[:, :, :fallback_frame_count, :, :]
                else:
                    clean_latents_4x = clean_latents_2x = clean_latents_1x = history_latents[:, :, :fallback_frame_count, :, :]
            else:
                clean_latents_4x = clean_latents_2x = clean_latents_1x = history_latents[:, :, :fallback_frame_count, :, :]

            # Move all tensors to the same device (GPU)
            start_latent = history_latents[:, :, :1].to(gpu)
            clean_latents_1x = clean_latents_1x.to(gpu)
            clean_latents_2x = clean_latents_2x.to(gpu)
            clean_latents_4x = clean_latents_4x.to(gpu)

            # Create clean_latents by combining start_latent and clean_latents_1x
            clean_latents = torch.cat([start_latent, clean_latents_1x], dim=2)

            # Fix for <=1 sec videos
            max_frames = min(latent_window_size * 4 - 3, history_latents.shape[2] * 4)

            # Define callback function to check for stop flag and start image deletion
            def callback(d):
                current_step = d['i'] + 1

                # Check for immediate shutdown signal first
                global shutdown_requested
                if shutdown_requested:
                    print(f"\n⚠️ Immediate shutdown requested. Terminating generation...")
                    raise StopGenerationRequestedException("Immediate shutdown requested")

                # Check for stop queue flag file first (terminates entire batch)
                stop_queue_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_queue.flag")
                if os.path.exists(stop_queue_flag_path):
                    print(f"\n⚠️ Stop queue flag detected. Terminating entire batch process...")
                    raise StopGenerationRequestedException("Stop queue requested by user")

                # Check for skip generation flag file (skips current item, continues batch)
                skip_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "skip_generation.flag")
                if os.path.exists(skip_flag_path):
                    print(f"\n⚠️ Skip generation flag detected. Skipping current item...")
                    raise StopGenerationRequestedException("Skip generation requested by user")

                # Check for legacy stop flag file for backward compatibility
                stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
                if os.path.exists(stop_flag_path):
                    print(f"\n⚠️ Legacy stop generation flag detected. Stopping generation gracefully...")
                    raise StopGenerationRequestedException("Stop generation requested by user")

                # Show progress every step for more accurate ETA calculation
                progress_message = f"Step {current_step}/{steps} - Section {section_idx + 1}/{total_latent_sections} - Total frames: {int(max(0, total_generated_latent_frames * 4 - 3))}"
                print(progress_message)

                # Write progress to file for GUI ETA display
                try:
                    with open("framepack_progress.txt", "w") as f:
                        f.write(progress_message)
                except Exception:
                    pass  # Don't let file write errors stop generation

                    # Check if start frame image still exists
                    if not os.path.exists(reference_image_path):
                        print(f"\n⚠️ Start frame image {reference_image_path} has been deleted. Stopping generation...")
                        raise ReferenceImageDeletedException("Start frame image was deleted during generation")
                return

            # Sample using Hunyuan
            generated_latents = sample_hunyuan(
                transformer=transformer,
                sampler='unipc',
                width=width,
                height=height,
                frames=max_frames,
                real_guidance_scale=cfg,
                distilled_guidance_scale=gs,
                guidance_rescale=0.0,  # Fixed guidance_rescale parameter
                shift=None if flow_shift == 0.0 else flow_shift,
                num_inference_steps=steps,
                generator=rnd,
                prompt_embeds=llama_vec,
                callback=callback,  # Add callback function
                prompt_embeds_mask=llama_attention_mask,
                prompt_poolers=clip_l_pooler,
                negative_prompt_embeds=llama_vec_n,
                negative_prompt_embeds_mask=llama_attention_mask_n,
                negative_prompt_poolers=clip_l_pooler_n,
                device=gpu,
                dtype=torch.bfloat16,
                image_embeddings=image_encoder_last_hidden_state,
                latent_indices=latent_indices,
                clean_latents=clean_latents,
                clean_latent_indices=clean_latent_indices,
                clean_latents_2x=clean_latents_2x,
                clean_latent_2x_indices=clean_latent_2x_indices,
                clean_latents_4x=clean_latents_4x,
                clean_latent_4x_indices=clean_latent_4x_indices
            )

            # Append generated latents to history
            history_latents = torch.cat([history_latents, generated_latents.cpu()], dim=2)
            total_generated_latent_frames = history_latents.shape[2]

            # Offload transformer and load VAE for decoding
            if not high_vram:
                offload_model_from_device_for_memory_preservation(transformer, target_device=gpu, preserved_memory_gb=8)
                load_model_as_complete(vae, target_device=gpu)
            else:
                # In high VRAM mode, just move VAE to GPU
                vae.to(gpu)

            # Decode latents to pixels
            print("Decoding latents to pixels...")
            real_history_latents = history_latents[:, :, -total_generated_latent_frames:, :, :]

            try:
                if history_pixels is None:
                    # Perform aggressive cleanup before decoding
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()

                    history_pixels = vae_decode(real_history_latents.to(gpu), vae).cpu()

                    # Explicitly delete tensors
                    del real_history_latents
                else:
                    section_latent_frames = latent_window_size * 2
                    overlapped_frames = latent_window_size * 4 - 3

                    # Perform aggressive cleanup before decoding
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()

                    current_pixels = vae_decode(real_history_latents[:, :, -section_latent_frames:].to(gpu), vae).cpu()
                    history_pixels = soft_append_bcthw(history_pixels, current_pixels, overlapped_frames)

                    # Explicitly delete tensors
                    del real_history_latents
                    del current_pixels
            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    print("CUDA out of memory during decoding. Performing aggressive cleanup and retrying with smaller batch...")

                    # Perform aggressive cleanup
                    aggressive_memory_cleanup()

                    # Try again with smaller chunks
                    if history_pixels is None:
                        # Decode in smaller chunks
                        chunk_size = 4  # Small chunk size for decoding
                        pixel_chunks = []

                        for i in range(0, real_history_latents.shape[2], chunk_size):
                            chunk = real_history_latents[:, :, i:i+chunk_size, :, :]
                            pixel_chunk = vae_decode(chunk.to(gpu), vae).cpu()
                            pixel_chunks.append(pixel_chunk)

                            # Clear cache after each chunk
                            torch.cuda.empty_cache()
                            torch.cuda.synchronize()

                        # Concatenate chunks
                        history_pixels = torch.cat(pixel_chunks, dim=2)

                        # Clean up
                        del pixel_chunks
                    else:
                        # Similar approach for appending
                        section_latent_frames = latent_window_size * 2
                        overlapped_frames = latent_window_size * 4 - 3

                        # Decode in smaller chunks
                        chunk_size = 4  # Small chunk size for decoding
                        section_latents = real_history_latents[:, :, -section_latent_frames:, :, :]
                        pixel_chunks = []

                        for i in range(0, section_latents.shape[2], chunk_size):
                            chunk = section_latents[:, :, i:i+chunk_size, :, :]
                            pixel_chunk = vae_decode(chunk.to(gpu), vae).cpu()
                            pixel_chunks.append(pixel_chunk)

                            # Clear cache after each chunk
                            torch.cuda.empty_cache()
                            torch.cuda.synchronize()

                        # Concatenate chunks
                        current_pixels = torch.cat(pixel_chunks, dim=2)
                        history_pixels = soft_append_bcthw(history_pixels, current_pixels, overlapped_frames)

                        # Clean up
                        del pixel_chunks
                        del section_latents
                        del current_pixels
                else:
                    # Re-raise other errors
                    raise

            # Unload all models after decoding
            if not high_vram:
                unload_complete_models()
                aggressive_memory_cleanup()
            else:
                # In high VRAM mode, just clear CUDA cache
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

            print("Cleared CUDA cache after decoding")

            # Save intermediate output
            temp_output_filename = os.path.join(output_dir, f'{job_id}_{total_generated_latent_frames}.mp4')
            try:
                save_bcthw_as_mp4(history_pixels, temp_output_filename, fps=fps, crf=mp4_crf)
                print(f"Saved intermediate output to {temp_output_filename}")

                # Verify the file was created and has content
                if not os.path.exists(temp_output_filename):
                    raise Exception(f"Output file was not created: {temp_output_filename}")

                file_size = os.path.getsize(temp_output_filename)
                if file_size == 0:
                    raise Exception(f"Output file is empty: {temp_output_filename}")

                print(f"Video file verification passed: {file_size} bytes")

            except Exception as e:
                print(f"ERROR: Exception during video save: {e}")
                print(f"ERROR: Exception type: {type(e).__name__}")
                traceback.print_exc()
                raise  # Re-raise the exception to be handled by the outer try-catch

        # Use the timestamp-based filename as the final output
        # Add duration to the temp filename for better identification
        duration_seconds = int(video_length)  # Truncate to whole number
        final_output_filename = os.path.join(output_dir, f'{job_id}_{total_generated_latent_frames}_{duration_seconds}s.mp4')

        # Rename the temp file to include the duration
        try:
            os.rename(temp_output_filename, final_output_filename)
            print(f"Renamed temp file to final output: {final_output_filename}")
        except Exception as e:
            print(f"Error renaming temp file: {e}")
            # If rename fails, use the temp file as the final output
            final_output_filename = temp_output_filename
            print(f"Using temp file as final output: {final_output_filename}")

        # Add metadata to the MP4 file (same format as demo_gradio_f1_video.py)
        metadata = f"Prompt: {prompt} | Negative Prompt: {n_prompt}"
        try:
            if add_metadata_to_mp4(final_output_filename, metadata):
                print(f"Added metadata to {final_output_filename}")
            else:
                print(f"Warning: Failed to add metadata to {final_output_filename}, but continuing...")
        except Exception as e:
            print(f"Error adding metadata to {final_output_filename}: {e}")
            print("Continuing without metadata...")

        # Handle encoding fix
        if fix_encoding:
            try:
                fixed_output_path = fix_video_encoding(final_output_filename)
                if fixed_output_path:
                    # Replace the original output with the fixed version
                    shutil.move(fixed_output_path, final_output_filename)
                    print(f"Fixed encoding: {final_output_filename}")
                else:
                    print("Warning: Encoding fix failed, but continuing...")
            except Exception as e:
                print(f"Error fixing encoding: {e}")

        # Run the auto sorter to copy the largest file to the sorted folder and deduplicate
        print("\nRunning automatic sorting and deduplication...")
        try:
            # Get the filename of the video we just created
            recent_file = os.path.basename(final_output_filename)

            # Get the input directory (parent directory of the input video)
            input_dir = str(Path(video_path).parent)

            # Pass the input directory and original input file to the auto sorter
            auto_sort_after_generation(
                outputs_folder=output_dir,
                recent_file=recent_file,
                input_dir=input_dir,
                original_input_file=str(original_video_path)
            )
        except Exception as sort_error:
            print(f"Error during auto-sorting: {sort_error}")
            traceback.print_exc()
        return final_output_filename

    except (ReferenceImageDeletedException, StopGenerationRequestedException) as e:
        print(f"⚠️ Stopping generation for {video_path}: {e}")

        # Clean up any intermediate files
        print("Cleaning up intermediate files...")
        try:
            # Find and remove all temporary MP4 files for this job
            temp_files = glob.glob(os.path.join(output_dir, f'{job_id}_*.mp4'))
            for temp_file in temp_files:
                try:
                    os.remove(temp_file)
                    print(f"Removed temporary file: {temp_file}")
                except Exception as cleanup_error:
                    print(f"Error removing temporary file {temp_file}: {cleanup_error}")

            # Also remove the start frame image if it hasn't been deleted yet
            if os.path.exists(reference_image_path):
                os.remove(reference_image_path)
                print(f"Removed start frame image: {reference_image_path}")

            # Determine the type of stop request and handle flag files accordingly
            if isinstance(e, StopGenerationRequestedException):
                stop_reason = str(e).lower()

                # For skip generation, only remove the skip flag but preserve others
                if "skip generation" in stop_reason:
                    skip_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "skip_generation.flag")
                    if os.path.exists(skip_flag_path):
                        os.remove(skip_flag_path)
                        print(f"Removed skip generation flag file: {skip_flag_path}")
                    # Return a special value to indicate this was a skip, not a stop
                    # Unload all models and clear CUDA cache
                    try:
                        unload_complete_models(
                            text_encoder, text_encoder_2, image_encoder, vae, transformer
                        )
                        torch.cuda.empty_cache()
                        print("All models unloaded and CUDA cache cleared")
                    except Exception as cleanup_error:
                        print(f"Error during cleanup: {cleanup_error}")
                    return "SKIPPED"
                else:
                    # For other stop types, remove all flag files
                    flag_files = [
                        "stop_queue.flag",
                        "skip_generation.flag",
                        "stop_framepack.flag"  # legacy
                    ]
                    for flag_file in flag_files:
                        flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), flag_file)
                        if os.path.exists(flag_path):
                            os.remove(flag_path)
                            print(f"Removed flag file: {flag_path}")

            # Clean up progress file for GUI ETA display
            try:
                progress_file = "framepack_progress.txt"
                if os.path.exists(progress_file):
                    os.remove(progress_file)
            except Exception:
                pass  # Don't let cleanup errors affect the main process
        except Exception as cleanup_error:
            print(f"Error during cleanup: {cleanup_error}")

        # Unload all models and clear CUDA cache
        try:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )
            torch.cuda.empty_cache()
            print("All models unloaded and CUDA cache cleared")
        except Exception as cleanup_error:
            print(f"Error during cleanup: {cleanup_error}")

        return None

    except Exception as e:
        print(f"❌ Error processing {video_path}: {e}")
        traceback.print_exc()

        # Unload all models and clear CUDA cache
        try:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )
            torch.cuda.empty_cache()
            print("All models unloaded and CUDA cache cleared")
        except Exception as cleanup_error:
            print(f"Error during cleanup: {cleanup_error}")

        return None

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="FramePack F1 Video-to-Video Batch Processing")

    # Input/output options
    parser.add_argument("--input_dir", type=str, default=input_dir,
                        help=f"Directory containing input videos (default: {input_dir})")
    parser.add_argument("--output_dir", type=str, default=output_dir,
                        help=f"Directory to save output videos (default: {output_dir})")
    parser.add_argument("--temp_dir", type=str, default=temp_dir,
                        help=f"Directory for temporary files (default: {temp_dir})")

    # Processing options
    parser.add_argument("--prompt", type=str, default=fallback_prompt,
                        help=f"Prompt to guide the generation (fallback: '{fallback_prompt}')")
    parser.add_argument("--seed", type=int, default=seed,
                        help=f"Random seed, -1 for random (default: {seed})")
    parser.add_argument("--video_length", type=float, default=video_length,
                        help=f"Additional video length in seconds (default: {video_length})")
    parser.add_argument("--steps", type=int, default=steps,
                        help=f"Number of sampling steps (default: {steps})")
    parser.add_argument("--distilled_cfg", type=float, default=distilled_cfg,
                        help=f"Distilled CFG scale (default: {distilled_cfg}). Higher values (5-10) increase prompt adherence but reduce video details. Lower values (1-3) preserve more video details but reduce prompt influence.")
    parser.add_argument("--flow_shift", type=float, default=flow_shift,
                        help=f"Flow shift parameter, 0.0=auto-calculated, 1.0-10.0=manual override (default: {flow_shift})")
    parser.add_argument("--cfg", type=float, default=3.0,
                        help="Real guidance scale (CFG) for prompt adherence (default: 3.0)")
    parser.add_argument("--gpu_memory", type=float, default=gpu_memory,
                        help=f"GPU memory to preserve in GB (default: {gpu_memory})")
    parser.add_argument("--max_retries", type=int, default=2,
                        help="Maximum number of retries for memory errors (default: 2)")
    parser.add_argument("--latent_window_size", type=int, default=latent_window_size,
                        help=f"Latent window size (default: {latent_window_size})")
    parser.add_argument("--mp4_crf", type=int, default=mp4_crf,
                        help=f"MP4 compression quality, lower is better (default: {mp4_crf})")
    parser.add_argument("--resolution", type=int, default=resolution,
                        help=f"Maximum resolution (width or height) (default: {resolution})")
    parser.add_argument("--vae_batch", type=int, default=vae_batch,
                        help=f"VAE batch size for processing input video (default: {vae_batch})")
    parser.add_argument("--num_clean_frames", type=int, default=num_clean_frames,
                        help=f"Number of clean frames to use from input video (default: {num_clean_frames})")

    # Boolean flags
    parser.add_argument("--use_teacache", action="store_true", default=use_teacache,
                        help=f"Use TeaCache - faster processing but may affect hand quality and prompt adherence (default: {use_teacache})")
    parser.add_argument("--no_teacache", action="store_false", dest="use_teacache",
                        help="Disable TeaCache")
    parser.add_argument("--fix_encoding", action="store_true", default=fix_encoding,
                        help=f"Fix video encoding for web compatibility (default: {fix_encoding})")
    parser.add_argument("--no_fix_encoding", action="store_false", dest="fix_encoding",
                        help="Don't fix video encoding")
    parser.add_argument("--no_resize", action="store_true", default=no_resize,
                        help=f"Don't resize input video (default: {no_resize})")
    parser.add_argument("--use_image_prompt", action="store_true", default=use_image_prompt,
                        help=f"Use video metadata as prompt if available (default: {use_image_prompt})")
    parser.add_argument("--no_image_prompt", action="store_false", dest="use_image_prompt",
                        help="Don't use video metadata as prompt")
    parser.add_argument("--allow_duplicates", action="store_true", default=False,
                        help="Allow duplicate files to be processed and ignore tracking (default: False)")
    parser.add_argument("--overwrite", action="store_true", default=False,
                        help="Whether to overwrite existing output files (default: False)")

    # Video trimming options
    parser.add_argument("--trim_seconds", type=float, default=trim_seconds,
                        help=f"Number of seconds to trim from video (default: {trim_seconds}). 0 means no trimming.")
    parser.add_argument("--trim_from_beginning", action="store_true", default=trim_from_beginning,
                        help=f"Trim from beginning of video instead of end (default: {trim_from_beginning})")
    parser.add_argument("--no_trim_from_beginning", action="store_false", dest="trim_from_beginning",
                        help="Trim from end of video (default)")

    # Input file options
    parser.add_argument("--files", nargs="+", type=str, default=None,
                        help="List of specific video files to process (full or relative paths). If provided, input_dir is ignored.")
    parser.add_argument("--file-list", type=str, default=None,
                        help="Path to a text file containing a list of video files to process (one file path per line). If provided, input_dir is ignored.")
    parser.add_argument("--url-list", type=str, default=None,
                        help="Path to a text file containing a list of video URLs to process (one URL per line). If provided, input_dir is ignored.")
    parser.add_argument("--combined-list", type=str, default=None,
                        help="Path to a text file containing a mixed list of video files and URLs to process (one item per line). If provided, input_dir is ignored.")
    parser.add_argument("--unified-list", type=str, default=None,
                        help="Path to a text file containing a unified list of directories, video files, and URLs to process (one item per line). If provided, input_dir is ignored.")
    parser.add_argument("--randomize_order", action="store_true", default=False,
                        help="Randomize the order of processing files")
    parser.add_argument("--filter_black_transparent", action="store_true", default=False,
                        help="Filter out images with more than 10 percent black or transparent pixels")

    # Positional arguments for drag and drop support
    parser.add_argument("video_files", nargs="*", help="Video files to process (for drag and drop support)")

    # LoRA and optimization arguments (supports up to 3 LoRAs)
    parser.add_argument("--lora_file_1", type=str, default=lora_file_1,
                        help=f"Path to first LoRA file (default: {lora_file_1})")
    parser.add_argument("--lora_multiplier_1", type=float, default=lora_multiplier_1,
                        help=f"First LoRA strength multiplier, range 0.0-1.0 (default: {lora_multiplier_1})")
    parser.add_argument("--lora_file_2", type=str, default=lora_file_2,
                        help=f"Path to second LoRA file (default: {lora_file_2})")
    parser.add_argument("--lora_multiplier_2", type=float, default=lora_multiplier_2,
                        help=f"Second LoRA strength multiplier, range 0.0-1.0 (default: {lora_multiplier_2})")
    parser.add_argument("--lora_file_3", type=str, default=lora_file_3,
                        help=f"Path to third LoRA file (default: {lora_file_3})")
    parser.add_argument("--lora_multiplier_3", type=float, default=lora_multiplier_3,
                        help=f"Third LoRA strength multiplier, range 0.0-1.0 (default: {lora_multiplier_3})")

    # Backward compatibility arguments
    parser.add_argument("--lora_file", type=str, default=lora_file,
                        help=f"Path to LoRA file (backward compatibility, maps to --lora_file_1) (default: {lora_file})")
    parser.add_argument("--lora_multiplier", type=float, default=lora_multiplier,
                        help=f"LoRA strength multiplier (backward compatibility, maps to --lora_multiplier_1) (default: {lora_multiplier})")
    parser.add_argument("--fp8_optimization", action="store_true", default=fp8_optimization,
                        help=f"Enable FP8 optimization for memory efficiency (default: {fp8_optimization})")

    parser.add_argument("--custom_model_path", type=str, default=None,
                        help="Path to custom merged model directory (overrides default F1 model)")



    # Prompt chain arguments
    parser.add_argument("--prompt_chain_mode", action="store_true", default=False,
                        help="Enable prompt chain processing mode")
    parser.add_argument("--chain_index", type=int, default=0,
                        help="Current index in the prompt chain (0-based)")
    parser.add_argument("--chain_total", type=int, default=1,
                        help="Total number of prompts in the chain")

    parser.add_argument("--f1_chain_use_video", action="store_true", default=False,
                        help="For F1 chains: use video input instead of last frame")
    parser.add_argument("--job_id", type=str, default=None,
                        help="Job ID to use for consistent naming across chain steps")

    # LoRA keyword arguments
    parser.add_argument("--append_lora_keywords", action="store_true", default=False,
                        help="Whether LoRA keywords were appended to the prompt (for metadata tracking)")

    # Skip no face arguments
    parser.add_argument("--skip_no_face", action="store_true", default=False,
                        help="Skip generation if no face is detected when auto crop to face is enabled")
    parser.add_argument("--skip_multiple_faces", action="store_true", default=False,
                        help="Skip entire image when multiple faces are detected")
    parser.add_argument("--no_crop_multiple_faces", action="store_true", default=False,
                        help="Skip cropping but still process original image when multiple faces are detected")

    # Face cropping arguments (ignored for F1 video processing - no images to crop)
    parser.add_argument("--auto_crop_to_face", action="store_true", default=False,
                        help="Auto crop to face (ignored for F1 video processing)")
    parser.add_argument("--auto_crop_fill_percentage", type=float, default=100.0,
                        help="Auto crop fill percentage (ignored for F1 video processing)")
    parser.add_argument("--auto_crop_padding_pixels", type=int, default=0,
                        help="Auto crop padding pixels (ignored for F1 video processing)")
    parser.add_argument("--auto_crop_padding_side", type=str, default="all",
                        help="Auto crop padding side (ignored for F1 video processing)")

    # Text-to-video arguments
    parser.add_argument("--use_noise", action="store_true", default=False,
                        help="Use noise method for text-to-video instead of transparent images (default: False)")
    parser.add_argument("--pixel_trick", action="store_true", default=False,
                        help="Add 1-pixel opaque dot to transparent images to avoid black-frame trap (default: False)")

    return parser.parse_args()

def get_lora_keywords(lora_file_1=None, lora_file_2=None, lora_file_3=None, append_lora_keywords=False):
    """Get keywords from LoRA .txt files and return them as a comma-separated string"""
    if not append_lora_keywords:
        return ""

    keywords = []

    # Check all three LoRA slots
    lora_configs = [
        (lora_file_1, 1),
        (lora_file_2, 2),
        (lora_file_3, 3)
    ]

    for lora_file, lora_index in lora_configs:
        if lora_file and lora_file.strip():
            lora_file = lora_file.strip()

            # Get the base filename without extension
            base_name = os.path.splitext(os.path.basename(lora_file))[0]

            # Try different possible paths for the txt file
            txt_file_paths = []

            # If LoRA file has a full path, look for txt file in the same directory
            if os.path.dirname(lora_file):
                txt_file_paths.append(os.path.join(os.path.dirname(lora_file), f"{base_name}.txt"))

            # Also try in the lora/ folder
            txt_file_paths.append(os.path.join("lora", f"{base_name}.txt"))

            # Try to read keywords from the txt file
            for txt_file_path in txt_file_paths:
                try:
                    if os.path.exists(txt_file_path):
                        with open(txt_file_path, 'r', encoding='utf-8') as f:
                            # Read the first line and strip whitespace
                            first_line = f.readline().strip()
                            if first_line:
                                keywords.append(first_line)
                                print(f"✓ Loaded keywords from {os.path.basename(txt_file_path)}: {first_line}")
                                break  # Found keywords, no need to try other paths
                except Exception as e:
                    print(f"Warning: Could not read keywords from {txt_file_path}: {e}")

    # Join all keywords with ", " separator
    if keywords:
        result = ", ".join(keywords)
        print(f"✓ Total LoRA keywords to append: {result}")
        return result

    return ""


def append_keywords_to_prompt(original_prompt, lora_file_1=None, lora_file_2=None, lora_file_3=None, append_lora_keywords=False):
    """Append LoRA keywords to the prompt if the setting is enabled"""
    if not append_lora_keywords:
        return original_prompt

    keywords = get_lora_keywords(lora_file_1, lora_file_2, lora_file_3, append_lora_keywords)
    if keywords:
        if original_prompt and original_prompt.strip():
            return f"{original_prompt}, {keywords}"
        else:
            return keywords

    return original_prompt


def download_video(url, temp_dir):
    """Download a video from a URL to the temp directory with a timestamp in the filename"""
    try:
        # Create temp directory if it doesn't exist
        os.makedirs(temp_dir, exist_ok=True)

        # Extract filename from URL or use a timestamp
        parsed_url = urllib.parse.urlparse(url)
        path = parsed_url.path
        filename = os.path.basename(path)

        if not filename or '.' not in filename:
            # Use a timestamp if no valid filename
            filename = f"video_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4"

        # Add timestamp to the filename to ensure uniqueness
        timestamp = int(time.time())
        name, ext = os.path.splitext(filename)
        unique_filename = f"{name}_{timestamp}{ext}"

        # Full path to save the file
        save_path = os.path.join(temp_dir, unique_filename)

        # Download the file
        print(f"Downloading {url} to {save_path}...")
        print(f"Will save as {unique_filename} with timestamp to prevent duplicates")
        response = requests.get(url, stream=True)
        response.raise_for_status()

        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        print(f"Downloaded {url} to {save_path}")
        return save_path

    except Exception as e:
        print(f"Error downloading {url}: {e}")
        return None

def prompt_for_input(prompt_text, default=""):
    """Prompt the user for input with a default value"""
    if default:
        user_input = input(f"{prompt_text} [{default}]: ").strip()
        if not user_input:
            return default
        return user_input
    else:
        while True:
            user_input = input(f"{prompt_text}: ").strip()
            if user_input:
                return user_input
            print("Please enter a value.")

def main():
    """Main function"""
    args = parse_args()

    # Check if video files were provided as positional arguments (drag and drop)
    if args.video_files:
        args.files = args.video_files

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Process specific files if provided via --files, --file-list, --url-list, --combined-list, or --unified-list
    if args.files or args.file_list or args.url_list or args.combined_list or args.unified_list:
        video_files = []
        downloaded_files = []  # Track downloaded files for cleanup later

        # For URL, file list, combined list, and unified list modes, we'll use appropriate directories for processed_files.txt
        if args.url_list or args.combined_list or args.unified_list:
            # For URL-based processing, use the temp directory
            os.makedirs(args.temp_dir, exist_ok=True)
            args.input_dir = args.temp_dir
        elif args.file_list:
            # For file list processing, use the directory of the file list as the input directory
            # This allows tracking processed files across multiple runs with the same file list
            file_list_dir = os.path.dirname(os.path.abspath(args.file_list))
            if file_list_dir:
                args.input_dir = file_list_dir
        elif args.files:
            # For individual files mode, we'll use the directory of the first file as the input directory
            # This ensures the processed_files.txt is stored alongside the input files
            if len(args.files) > 0:
                first_file_dir = os.path.dirname(os.path.abspath(args.files[0]))
                if first_file_dir and os.path.isdir(first_file_dir):
                    args.input_dir = first_file_dir

        # Process files from --files parameter
        if args.files:
            for file_path in args.files:
                # Handle both absolute and relative paths
                if os.path.isabs(file_path):
                    path = Path(file_path)
                else:
                    path = Path(os.path.abspath(file_path))

                if path.exists() and path.is_file():
                    # Use the helper function to check if it's a supported file
                    is_supported, message = is_supported_file(path)
                    if is_supported:
                        video_files.append(path)
                    else:
                        if message:
                            print(message)
                else:
                    print(f"Warning: File not found or not a file: {path}")

        # Process files from --file-list parameter
        if args.file_list:
            try:
                with open(args.file_list, 'r', encoding='utf-8') as f:
                    for line in f:
                        file_path = line.strip()
                        if file_path and not file_path.startswith('#'):  # Skip empty lines and comments
                            path = Path(os.path.abspath(file_path))
                            if path.exists() and path.is_file():
                                # Use the helper function to check if it's a supported file
                                is_supported, message = is_supported_file(path)
                                if is_supported:
                                    video_files.append(path)
                                else:
                                    if message:
                                        print(message)
                            else:
                                print(f"Warning: File not found or not a file: {path}")
            except Exception as e:
                print(f"Error reading file list: {e}")

        # Process URLs from --url-list parameter
        if args.url_list:
            try:
                with open(args.url_list, 'r', encoding='utf-8') as f:
                    for line in f:
                        url = line.strip()
                        if url and not url.startswith('#'):  # Skip empty lines and comments
                            downloaded_file = download_video(url, args.temp_dir)
                            if downloaded_file:
                                video_files.append(Path(downloaded_file))
                                downloaded_files.append(downloaded_file)
            except Exception as e:
                print(f"Error reading URL list: {e}")

        # Process combined list from --combined-list parameter
        if args.combined_list:
            try:
                with open(args.combined_list, 'r', encoding='utf-8') as f:
                    for line in f:
                        item = line.strip()
                        if item and not item.startswith('#'):  # Skip empty lines and comments
                            if item.lower().startswith(('http://', 'https://')):
                                # It's a URL
                                downloaded_file = download_video(item, args.temp_dir)
                                if downloaded_file:
                                    video_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                            else:                                # It's a file path
                                path = Path(os.path.abspath(item))
                                print(f"Processing file path from unified list: {path}")
                                if path.exists() and path.is_file():
                                    # Use the helper function to check if it's a supported file
                                    is_supported, message = is_supported_file(path)
                                    if is_supported:
                                        video_files.append(path)
                                    else:
                                        if message:
                                            print(message)
                                else:
                                    print(f"Warning: File not found or not a file: {path}")
            except Exception as e:
                print(f"Error reading combined list: {e}")

        # Process unified list from --unified-list parameter
        if args.unified_list:
            try:
                with open(args.unified_list, 'r', encoding='utf-8') as f:
                    for line in f:
                        item = line.strip()
                        if item and not item.startswith('#'):  # Skip empty lines and comments
                            if item.startswith('DIR:'):
                                # It's a directory, extract the path and process all videos in it
                                dir_path = item[4:]  # Remove the DIR: prefix
                                # Normalize the path to handle forward/backward slash differences
                                dir_path = os.path.normpath(dir_path)
                                print(f"Processing directory from unified list: {dir_path}")

                                if os.path.exists(dir_path) and os.path.isdir(dir_path):
                                    # Get all video files from the directory
                                    valid_extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
                                    dir_video_files = []

                                    for ext in valid_extensions:
                                        dir_video_files.extend([f for f in Path(dir_path).glob(f'*{ext}') if f.is_file()])
                                        dir_video_files.extend([f for f in Path(dir_path).glob(f'*{ext.upper()}') if f.is_file()])

                                    if dir_video_files:
                                        print(f"Found {len(dir_video_files)} video files in directory {dir_path}")
                                        video_files.extend(dir_video_files)
                                    else:
                                        print(f"Warning: No video files found in directory: {dir_path}")
                                else:
                                    print(f"Warning: Directory not found: {dir_path}")
                            elif item.startswith('URL:'):
                                # It's a URL with the URL: prefix to handle special characters
                                url = item[4:]  # Remove the URL: prefix
                                print(f"Processing URL from unified list: {url}")
                                downloaded_file = download_video(url, args.temp_dir)
                                if downloaded_file:
                                    video_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                            elif item.lower().startswith(('http://', 'https://')):
                                # It's a URL without prefix (for backward compatibility)
                                downloaded_file = download_video(item, args.temp_dir)
                                if downloaded_file:
                                    video_files.append(Path(downloaded_file))
                                    downloaded_files.append(downloaded_file)
                            else:                                # It's a file path
                                path = Path(os.path.abspath(item))
                                print(f"Processing file path from unified list: {path}")
                                if path.exists() and path.is_file():
                                    # Use the helper function to check if it's a supported file
                                    is_supported, message = is_supported_file(path)
                                    if is_supported:
                                        video_files.append(path)
                                    else:
                                        if message:
                                            print(message)
                                else:
                                    print(f"Warning: File not found or not a file: {path}")
            except Exception as e:
                print(f"Error reading unified list: {e}")
                traceback.print_exc()

        # Create a processed files tracking file (only for directory mode, not individual files)
        processed_files = set()
        processed_files_path = None

        # Only apply duplicate checking for directory processing, not individual files or lists
        if not args.allow_duplicates and not args.files and not args.file_list and not args.url_list and not args.combined_list and not args.unified_list:
            # Store processed_files.txt in the input directory instead of output directory
            processed_files_path = os.path.join(args.input_dir, "processed_files.txt")

            # Load previously processed files if the file exists
            if os.path.exists(processed_files_path):
                try:
                    with open(processed_files_path, 'r') as f:
                        processed_files = set(line.strip() for line in f.readlines())
                    print(f"Loaded {len(processed_files)} previously processed files from tracking file in input folder")
                except Exception as e:
                    print(f"Error loading processed files tracking: {e}")

            print(f"Total processed files loaded: {len(processed_files)}")
        elif args.files or args.file_list or args.url_list or args.combined_list or args.unified_list:
            print("Individual files/lists mode: Duplicate checking disabled (files will always be processed)")

        # Check for existing output files if overwrite is disabled and duplicates are not allowed
        # Only apply this check for directory processing, not individual files or lists
        if not args.overwrite and not args.allow_duplicates and not args.files and not args.file_list and not args.url_list and not args.combined_list and not args.unified_list:
            skipped_files = []
            files_to_process = []

            for video_path in video_files:
                # Check if file has already been processed according to tracking
                if str(video_path) in processed_files:
                    skipped_files.append(video_path)
                else:
                    files_to_process.append(video_path)

            if skipped_files:
                print(f"\nSkipping {len(skipped_files)} files that have already been processed:")
                for i, video in enumerate(skipped_files):
                    print(f"  {i+1}. {video.name}")

            video_files = files_to_process

            if not video_files:
                print(f"\nNo files to process. All videos have already been processed.")
                print(f"Use --overwrite to regenerate videos for existing files.")
                print(f"Or use --allow_duplicates to process files multiple times.")
                # Create a lock release message to ensure the GUI knows we're done
                lock_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "framepack.lock")
                if os.path.exists(lock_file):
                    try:
                        os.remove(lock_file)
                        print("Lock released successfully")
                    except Exception as e:
                        print(f"Error releasing lock: {e}")
                return

        # Randomize order if requested
        if args.randomize_order and video_files:
            print("Randomizing processing order...")
            random.shuffle(video_files)

        # Check if we have any files to process
        if not video_files:
            print("No valid video files found in the provided file/URL list")
            # Don't return immediately, as we want to create the completion signal file
            # This allows the GUI to continue processing the queue
            # Create a lock release message to ensure the GUI knows we're done
            lock_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "framepack.lock")
            if os.path.exists(lock_file):
                try:
                    os.remove(lock_file)
                    print("Lock released successfully")
                except Exception as e:
                    print(f"Error releasing lock: {e}")
            return

        # Remove any existing stop flag file before starting
        stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
        if os.path.exists(stop_flag_path):
            try:
                os.remove(stop_flag_path)
                print(f"Removed existing stop flag file: {stop_flag_path}")
            except Exception as e:
                print(f"Warning: Could not remove existing stop flag file: {e}")

        # Print batch processing settings
        print("\nProcessing Settings:")
        print(f"  Number of Videos: {len(video_files)}")
        print(f"  Output Directory: {args.output_dir}")
        print(f"  Prompt: {args.prompt if args.prompt else '(Will prompt for each video)'}")
        print(f"  Additional Video Length: {args.video_length} seconds")
        print(f"  Steps: {args.steps}")
        print(f"  Seed: {args.seed if args.seed != -1 else 'Random'}")
        print(f"  Real CFG: {args.cfg}")
        print(f"  Distilled CFG: {args.distilled_cfg}")
        print(f"  TeaCache: {args.use_teacache}")
        print(f"  GPU Memory: {args.gpu_memory} GB")
        print(f"  Resolution: {args.resolution}")
        print(f"  No Resize: {args.no_resize}")
        print(f"  Clean Frames: {args.num_clean_frames}")
        print(f"  VAE Batch Size: {args.vae_batch}")
        print()

        # Load models
        print("Loading models...")
        # Check CUDA availability
        if not torch.cuda.is_available():
            print("CUDA is not available. This script requires a CUDA-capable GPU.")
            return

        # Determine high VRAM mode
        free_mem_gb = get_cuda_free_memory_gb(gpu)
        high_vram = free_mem_gb > 60
        print(f"Free VRAM: {free_mem_gb:.2f} GB")
        print(f"High-VRAM Mode: {high_vram}")

        # Load models - always start on CPU to manage memory better
        print("Loading text encoders...")
        text_encoder = LlamaModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder', torch_dtype=torch.float16).cpu()
        text_encoder_2 = CLIPTextModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder_2', torch_dtype=torch.float16).cpu()

        print("Loading tokenizers...")
        tokenizer = LlamaTokenizerFast.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer')
        tokenizer_2 = CLIPTokenizer.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer_2')

        # Set tokenizer padding side
        tokenizer.padding_side = "left"
        tokenizer_2.padding_side = "left"

        print("Loading VAE...")
        vae = AutoencoderKLHunyuanVideo.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='vae', torch_dtype=torch.float16).cpu()

        print("Loading image encoder...")
        feature_extractor = SiglipImageProcessor.from_pretrained("lllyasviel/flux_redux_bfl", subfolder='feature_extractor')
        image_encoder = SiglipVisionModel.from_pretrained("lllyasviel/flux_redux_bfl", subfolder='image_encoder', torch_dtype=torch.float16).cpu()

        print("Loading F1 transformer model...")
        # Use the F1 model with LoRA support
        # Handle backward compatibility and new multi-LoRA parameters
        transformer = load_transformer_with_lora_f1(
            lora_file_1=args.lora_file_1 if hasattr(args, 'lora_file_1') else args.lora_file,
            lora_multiplier_1=args.lora_multiplier_1 if hasattr(args, 'lora_multiplier_1') else args.lora_multiplier,
            lora_file_2=args.lora_file_2 if hasattr(args, 'lora_file_2') else None,
            lora_multiplier_2=args.lora_multiplier_2 if hasattr(args, 'lora_multiplier_2') else 0.8,
            lora_file_3=args.lora_file_3 if hasattr(args, 'lora_file_3') else None,
            lora_multiplier_3=args.lora_multiplier_3 if hasattr(args, 'lora_multiplier_3') else 0.8,
            fp8_optimization=args.fp8_optimization,
            custom_model_path=args.custom_model_path
        )

        # Set models to evaluation mode
        vae.eval()
        text_encoder.eval()
        text_encoder_2.eval()
        image_encoder.eval()
        transformer.eval()

        # Configure models for low VRAM mode
        if not high_vram:
            vae.enable_slicing()
            vae.enable_tiling()

            # Install DynamicSwap for models in low VRAM mode
            print("Installing DynamicSwap for transformer and text encoder...")
            DynamicSwapInstaller.install_model(transformer, device=gpu)
            DynamicSwapInstaller.install_model(text_encoder, device=gpu)

        # Set high quality output for transformer
        transformer.high_quality_fp32_output_for_inference = True
        print('transformer.high_quality_fp32_output_for_inference = True')

        # Process each video
        processed_count = 0
        for video_idx, video_path in enumerate(video_files):
            # Check for stop queue flag before processing each video (terminates entire batch)
            stop_queue_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_queue.flag")
            if os.path.exists(stop_queue_flag_path):
                print(f"\n⚠️ Stop queue flag detected. Terminating entire batch processing...")
                # Remove the stop queue flag file
                try:
                    os.remove(stop_queue_flag_path)
                    print(f"Removed stop queue flag file: {stop_queue_flag_path}")
                except Exception as e:
                    print(f"Error removing stop queue flag file: {e}")
                break

            # Check for skip generation flag before processing each video (skips current item)
            skip_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "skip_generation.flag")
            if os.path.exists(skip_flag_path):
                print(f"\n⚠️ Skip generation flag detected. Skipping current item and continuing batch...")
                # Remove the skip flag file
                try:
                    os.remove(skip_flag_path)
                    print(f"Removed skip flag file: {skip_flag_path}")
                except Exception as e:
                    print(f"Error removing skip flag file: {e}")

                # Create completion signal for GUI to detect the skip
                try:
                    with open("framepack_completed.signal", "w") as f:
                        f.write("SKIPPED\n")
                    print("Created SKIPPED completion signal for GUI")
                except Exception as e:
                    print(f"Warning: Could not create skip completion signal: {e}")

                # Continue to next item instead of breaking
                continue

            # Check for legacy stop flag for backward compatibility
            stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
            if os.path.exists(stop_flag_path):
                print(f"\n⚠️ Legacy stop generation flag detected. Stopping batch processing...")
                # Remove the stop flag file
                try:
                    os.remove(stop_flag_path)
                    print(f"Removed stop flag file: {stop_flag_path}")
                except Exception as e:
                    print(f"Error removing stop flag file: {e}")
                break

            print(f"\n[{video_idx + 1}/{len(video_files)}] Processing {video_path}")

            # Determine the prompt to use for this video
            actual_prompt = args.prompt

            # If no prompt provided, prompt the user
            if not actual_prompt:
                actual_prompt = prompt_for_input("Enter prompt for this video")

            # Append LoRA keywords to the prompt if enabled
            if args.append_lora_keywords:
                original_prompt = actual_prompt
                actual_prompt = append_keywords_to_prompt(
                    actual_prompt,
                    args.lora_file_1 if hasattr(args, 'lora_file_1') else None,
                    args.lora_file_2 if hasattr(args, 'lora_file_2') else None,
                    args.lora_file_3 if hasattr(args, 'lora_file_3') else None,
                    args.append_lora_keywords
                )
                if actual_prompt != original_prompt:
                    print(f"✓ Appended LoRA keywords to prompt")

            # If only one video and no video_length specified, prompt the user
            actual_video_length = args.video_length
            if len(video_files) == 1 and args.video_length == video_length:
                user_length = prompt_for_input(f"Enter additional video length in seconds", str(video_length))
                try:
                    actual_video_length = float(user_length)
                except ValueError:
                    print(f"Invalid length, using default: {video_length}")
                    actual_video_length = video_length

            # Process the video
            max_retries = args.max_retries
            retry_count = 0
            success = False

            while retry_count <= max_retries and not success:
                try:
                    # Perform aggressive memory cleanup before each attempt
                    aggressive_memory_cleanup()

                    # Adjust memory preservation based on retry count
                    current_gpu_memory = args.gpu_memory
                    if retry_count > 0:
                        # Increase memory preservation on retries
                        current_gpu_memory = args.gpu_memory + (retry_count * 2.0)
                        print(f"Retry {retry_count}/{max_retries}: Increasing GPU memory preservation to {current_gpu_memory} GB")

                    # Adjust VAE batch size based on retry count
                    current_vae_batch = args.vae_batch
                    if retry_count > 0:
                        # Reduce batch size on retries
                        current_vae_batch = max(4, args.vae_batch // (retry_count + 1))
                        print(f"Retry {retry_count}/{max_retries}: Reducing VAE batch size to {current_vae_batch}")

                    # Process the video with current settings
                    result = process_single_video(
                        video_path=str(video_path),
                        output_dir=args.output_dir,
                        prompt=actual_prompt,
                        n_prompt="",
                        seed=args.seed,
                        video_length=actual_video_length,
                        steps=args.steps,
                        gs=args.distilled_cfg,
                        cfg=args.cfg,
                        flow_shift=args.flow_shift,
                        gpu_memory=current_gpu_memory,
                        use_teacache=args.use_teacache,
                        high_vram=high_vram,
                        latent_window_size=args.latent_window_size,
                        text_encoder=text_encoder,
                        text_encoder_2=text_encoder_2,
                        tokenizer=tokenizer,
                        tokenizer_2=tokenizer_2,
                        vae=vae,
                        feature_extractor=feature_extractor,
                        image_encoder=image_encoder,
                        transformer=transformer,
                        fix_encoding=args.fix_encoding,
                        mp4_crf=args.mp4_crf,
                        resolution=args.resolution,
                        no_resize=args.no_resize,
                        num_clean_frames=args.num_clean_frames,
                        vae_batch=current_vae_batch,
                        trim_seconds=args.trim_seconds,
                        trim_from_beginning=args.trim_from_beginning,
                        lora_file=args.lora_file,
                        lora_multiplier=args.lora_multiplier,
                        fp8_optimization=args.fp8_optimization,
                        custom_model_path=args.custom_model_path,
                        # Prompt chain parameters
                        job_id=args.job_id,
                        chain_index=args.chain_index,
                        chain_total=args.chain_total,
                        prompt_chain_mode=args.prompt_chain_mode,
                        f1_chain_use_video=args.f1_chain_use_video
                    )

                    # Check if the generation was skipped
                    if result == "SKIPPED":
                        print(f"⚠️ Skipped generation for {video_path} - continuing with next item")
                        # Note: Completion signal already created when skip flag was detected
                        break  # Break out of retry loop to continue with next video

                    # If we get here, processing was successful
                    if result is not None:
                        processed_count += 1
                        success = True

                        # Update processed_files.txt to track this file as processed (only for directory mode, not individual files or lists)
                        if not args.allow_duplicates and not args.files and not args.file_list and not args.url_list and not args.combined_list and not args.unified_list and processed_files_path:
                            # Add the file to the processed files set
                            processed_files.add(str(video_path))

                            # Update the processed_files.txt file
                            from auto_sorter import update_processed_files_txt
                            if update_processed_files_txt(str(video_path), args.input_dir, debug=True):
                                print(f"✅ Successfully updated processed_files.txt with: {str(video_path)}")
                                print(f"   (in directory: {args.input_dir})")
                            else:
                                print(f"❌ Failed to update processed_files.txt with: {str(video_path)}")

                        # Clear CUDA cache after successful processing
                        aggressive_memory_cleanup()
                        print("Memory cleanup completed after successful processing")

                except StopGenerationRequestedException as e:
                    print(f"⚠️ Stopping generation for {video_path}: {e}")

                    # Clean up any intermediate files
                    print("Cleaning up intermediate files...")
                    try:
                        # Determine the type of stop request and handle flag files accordingly
                        stop_reason = str(e).lower()

                        # For skip generation, only remove the skip flag but preserve others
                        if "skip generation" in stop_reason:
                            skip_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "skip_generation.flag")
                            if os.path.exists(skip_flag_path):
                                os.remove(skip_flag_path)
                                print(f"Removed skip generation flag file: {skip_flag_path}")

                            # Create completion signal for GUI to detect the skip
                            try:
                                with open("framepack_completed.signal", "w") as f:
                                    f.write("SKIPPED\n")
                                print("Created SKIPPED completion signal for GUI")
                            except Exception as e:
                                print(f"Warning: Could not create skip completion signal: {e}")

                            # Perform memory cleanup
                            aggressive_memory_cleanup()
                            # Continue to next video instead of breaking
                            print(f"⚠️ Skipped generation for {video_path} - continuing with next item")
                            break  # Break out of retry loop to continue with next video
                        else:
                            # For other stop types, remove all flag files
                            flag_files = [
                                "stop_queue.flag",
                                "skip_generation.flag",
                                "stop_framepack.flag"  # legacy
                            ]
                            for flag_file in flag_files:
                                flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), flag_file)
                                if os.path.exists(flag_path):
                                    os.remove(flag_path)
                                    print(f"Removed flag file: {flag_path}")

                        # Clean up progress file for GUI ETA display
                        try:
                            progress_file = "framepack_progress.txt"
                            if os.path.exists(progress_file):
                                os.remove(progress_file)
                        except Exception:
                            pass  # Don't let cleanup errors affect the main process
                    except Exception as cleanup_error:
                        print(f"Error during cleanup: {cleanup_error}")

                    # Perform memory cleanup
                    aggressive_memory_cleanup()
                    break

                except RuntimeError as e:
                    if "out of memory" in str(e).lower() or "cuda" in str(e).lower():
                        # Memory-related error, try again with adjusted parameters
                        print(f"Memory error during processing: {e}")
                        retry_count += 1

                        if retry_count <= max_retries:
                            print(f"Will retry with adjusted memory settings ({retry_count}/{max_retries})")
                            # Perform aggressive cleanup
                            aggressive_memory_cleanup()
                        else:
                            print(f"Failed after {max_retries} retries. Moving to next video.")
                    else:
                        # Other runtime error, print and move on
                        print(f"Runtime error processing video: {e}")
                        traceback.print_exc()
                        break

                except Exception as e:
                    # General exception, print and move on
                    print(f"Error processing video: {e}")
                    traceback.print_exc()
                    # Perform cleanup
                    aggressive_memory_cleanup()
                    break

        # Print summary
        print(f"\nProcessing complete: {processed_count}/{len(video_files)} videos processed successfully")

        # Clean up downloaded files
        if downloaded_files:
            print(f"Cleaning up {len(downloaded_files)} downloaded files...")
            for file_path in downloaded_files:
                try:
                    os.remove(file_path)
                    print(f"Removed temporary file: {file_path}")
                except Exception as e:
                    print(f"Error removing temporary file {file_path}: {e}")

    else:
        # No files specified, print usage
        print("No video files specified. Use one of the following options:")
        print("  --files video1.mp4 video2.mp4")
        print("  --file-list videos.txt")
        print("  --url-list video_urls.txt")
        print("  --combined-list mixed_list.txt")
        print("  --unified-list unified_list.txt")
        print("  --cfg 7 --distilled_cfg 1  (Use real CFG 7 for strong prompt adherence)")
        print("Or simply drag and drop video files onto the batch file.")

    # Create completion signal for GUI detection
    try:
        with open("framepack_completed.signal", "w") as f:
            f.write("SUCCESS\n")
        print("Created completion signal for GUI")
    except Exception as e:
        print(f"Warning: Could not create completion signal: {e}")

    # Release lock file for GUI detection
    try:
        lock_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "framepack.lock")
        if os.path.exists(lock_file):
            os.remove(lock_file)
            print("Lock released successfully")
    except Exception as e:
        print(f"Warning: Could not release lock: {e}")

    # Clean up progress file for GUI ETA display
    try:
        progress_file = "framepack_progress.txt"
        if os.path.exists(progress_file):
            os.remove(progress_file)
    except Exception:
        pass  # Don't let cleanup errors affect the main process

if __name__ == "__main__":
    try:
        main()
        print("Batch F1 Video processing completed successfully")
    except Exception as e:
        print(f"FATAL ERROR: Exception in batch F1 video processing: {e}")
        print(f"FATAL ERROR: Exception type: {type(e).__name__}")
        traceback.print_exc()

        # Clean up progress file for GUI ETA display
        try:
            progress_file = "framepack_progress.txt"
            if os.path.exists(progress_file):
                os.remove(progress_file)
        except Exception as cleanup_error:
            print(f"Error cleaning up progress file: {cleanup_error}")

        # Exit with error code to signal failure
        import sys
        sys.exit(1)
