#!/usr/bin/env python
"""
batch_f1_video.py

This script processes video files using the FramePack F1 model to extend videos with AI-generated content.
It can be used in batch mode to process multiple videos or in interactive mode for a single video.

Usage:
  python batch_f1_video.py --files video1.mp4 video2.mp4 --prompt "your prompt" --video_length 5
  python batch_f1_video.py --file-list videos.txt --prompt "your prompt"
  python batch_f1_video.py --url-list video_urls.txt

  # Interactive mode (when only a file is provided):
  python batch_f1_video.py video.mp4

  # Drag and drop support via framepack_f1_video_files.bat
"""

import os
import argparse
import torch
import numpy as np
import traceback
import shutil
import glob
from pathlib import Path
import random
from tqdm import tqdm
from PIL import Image, PngImagePlugin
import subprocess
import json
import cv2
import requests
import tempfile
import urllib.parse
import sys
import time
import threading
import gc

# Import auto sorter functionality
try:
    from auto_sorter import sort_file
    AUTO_SORTER_AVAILABLE = True
except ImportError:
    AUTO_SORTER_AVAILABLE = False
    print("Auto sorter not available. Skipping auto sorting.")

# Import TeaCache if available
try:
    from teacache import TeaCache
    TEACACHE_AVAILABLE = True
except ImportError:
    TEACACHE_AVAILABLE = False
    print("TeaCache not available. Continuing without TeaCache.")

# Import transformers and diffusers
from transformers import SiglipImageProcessor, SiglipVisionModel
from datetime import datetime

# Import custom exceptions
class StopGenerationRequestedException(Exception):
    """Exception raised when a stop generation flag is detected."""
    pass

class TimeoutException(Exception):
    """Exception raised when a section times out."""
    pass

# Function to check if a file is a supported video file
def is_supported_file(file_path):
    """Check if a file is a supported video or image file and handle accordingly"""
    valid_video_extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
    valid_image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
    
    ext = Path(file_path).suffix.lower()
    
    if ext in valid_video_extensions:
        return True, None  # Supported video file
    elif ext in valid_image_extensions:
        message = f"Warning: Skipping {file_path} - not a supported video file. This script is for video processing only. To process images, use batch_f1.py or batch_f1_lock.py instead."
        return False, message
    else:
        message = f"Warning: Skipping {file_path} - not a supported media file"
        return False, message

# Function to perform aggressive memory cleanup
def aggressive_memory_cleanup():
    """Perform aggressive memory cleanup to recover from memory issues."""
    print("Performing aggressive memory cleanup...")
    # Clear CUDA cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

    # Run garbage collection multiple times
    for _ in range(3):
        gc.collect()

    # Report memory status
    if torch.cuda.is_available():
        free_mem_gb = get_cuda_free_memory_gb(gpu)
        print(f"Free VRAM after cleanup: {free_mem_gb:.2f} GB")

    return

# Function to download a video from a URL
def download_video(url, temp_dir):
    """Download a video from a URL to a temporary file"""
    try:
        # Create a temporary file with a unique name
        os.makedirs(temp_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_file = os.path.join(temp_dir, f"downloaded_video_{timestamp}.mp4")
        
        print(f"Downloading video from {url} to {temp_file}...")
        
        # Download the video
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        # Save the video to the temporary file
        with open(temp_file, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"Video downloaded successfully to {temp_file}")
        return temp_file
    except Exception as e:
        print(f"Error downloading video from {url}: {e}")
        return None

# Function to prompt for input
def prompt_for_input(prompt_text, default=""):
    """Prompt the user for input with a default value"""
    if default:
        user_input = input(f"{prompt_text} [{default}]: ").strip()
        if not user_input:
            return default
        return user_input
    else:
        while True:
            user_input = input(f"{prompt_text}: ").strip()
            if user_input:
                return user_input
            print("Please enter a value.")
