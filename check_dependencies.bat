@echo off
:: FramePack Dependency Checker for Windows
:: This batch file runs the Python dependency checker

title FramePack Dependency Checker

echo FramePack Dependency Checker
echo ==============================
echo.

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    echo Please install Python 3.8+ and make sure it's in your PATH.
    echo.
    echo You can download Python from: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

:: Display Python version
echo Checking Python installation...
python --version

echo.
echo Running dependency checker...
echo.

:: Run the dependency checker
python check_dependencies.py

:: Check the exit code
if %ERRORLEVEL% equ 0 (
    echo.
    echo ✅ Dependency check completed successfully!
) else (
    echo.
    echo ⚠️  Some dependencies are missing. Please follow the installation instructions above.
)

echo.
echo Press any key to exit...
pause >nul
