#!/usr/bin/env python3
"""
FramePack Dependency Checker

This script checks if all required and optional dependencies are installed
and provides installation instructions for missing packages.
"""

import sys
import importlib
import subprocess
from typing import Dict, List, Tuple

def check_package(package_name: str, import_name: str = None) -> Tuple[bool, str]:
    """
    Check if a package is installed and importable.
    
    Args:
        package_name: The name of the package as it appears in pip
        import_name: The name to use for importing (if different from package_name)
    
    Returns:
        Tuple of (is_installed, version_or_error)
    """
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'unknown')
        return True, version
    except ImportError as e:
        return False, str(e)
    except Exception as e:
        return False, f"Error: {str(e)}"

def check_dependencies():
    """Check all FramePack dependencies and report status."""
    
    # Core dependencies (required for basic functionality)
    core_deps = {
        'torch': 'torch',
        'numpy': 'numpy', 
        'pillow': 'PIL',
        'opencv-contrib-python': 'cv2',
        'diffusers': 'diffusers',
        'transformers': 'transformers',
        'accelerate': 'accelerate',
        'safetensors': 'safetensors',
        'einops': 'einops',
        'requests': 'requests',
        'tqdm': 'tqdm',
        'av': 'av',
        'scipy': 'scipy',
        'sentencepiece': 'sentencepiece',
        'torchsde': 'torchsde',
    }
    
    # GUI dependencies
    gui_deps = {
        'tkinterdnd2': 'tkinterdnd2',
        'tkvideoplayer': 'tkVideoPlayer',
        'moviepy': 'moviepy.editor',
        'pyperclip': 'pyperclip',
    }
    
    # Optional dependencies
    optional_deps = {
        'mediapipe': 'mediapipe',
        'face-recognition': 'face_recognition',
        'gradio': 'gradio',
        'psutil': 'psutil',
    }
    
    print("FramePack Dependency Checker")
    print("=" * 50)
    
    # Check core dependencies
    print("\n🔧 Core Dependencies (Required)")
    print("-" * 30)
    core_missing = []
    for package, import_name in core_deps.items():
        installed, version = check_package(package, import_name)
        status = "✅" if installed else "❌"
        print(f"{status} {package:<25} {version}")
        if not installed:
            core_missing.append(package)
    
    # Check GUI dependencies
    print("\n🖥️  GUI Dependencies (For GUI functionality)")
    print("-" * 45)
    gui_missing = []
    for package, import_name in gui_deps.items():
        installed, version = check_package(package, import_name)
        status = "✅" if installed else "❌"
        print(f"{status} {package:<25} {version}")
        if not installed:
            gui_missing.append(package)
    
    # Check optional dependencies
    print("\n🔍 Optional Dependencies (Enhanced features)")
    print("-" * 45)
    optional_missing = []
    for package, import_name in optional_deps.items():
        installed, version = check_package(package, import_name)
        status = "✅" if installed else "⚠️"
        print(f"{status} {package:<25} {version}")
        if not installed:
            optional_missing.append(package)
    
    # Summary and recommendations
    print("\n" + "=" * 50)
    print("📋 Summary")
    print("=" * 50)
    
    if not core_missing and not gui_missing:
        print("🎉 All essential dependencies are installed!")
        print("   FramePack should work perfectly.")
    elif not core_missing:
        print("✅ Core dependencies are installed.")
        print("   Basic batch processing will work.")
        if gui_missing:
            print("⚠️  Some GUI dependencies are missing.")
            print("   GUI features may not work properly.")
    else:
        print("❌ Some core dependencies are missing.")
        print("   FramePack may not work properly.")
    
    # Installation instructions
    if core_missing or gui_missing or optional_missing:
        print("\n🔧 Installation Instructions")
        print("-" * 30)
        
        if core_missing:
            print("\n📦 Install missing core dependencies:")
            print("   pip install -r requirements-minimal.txt")
            print("   Or individually:")
            for package in core_missing:
                print(f"   pip install {package}")
        
        if gui_missing:
            print("\n🖥️  Install missing GUI dependencies:")
            print("   pip install -r requirements-gui.txt")
            print("   Or individually:")
            for package in gui_missing:
                print(f"   pip install {package}")
        
        if optional_missing:
            print("\n🔍 Install missing optional dependencies:")
            print("   pip install -r requirements-optional.txt")
            print("   Or individually:")
            for package in optional_missing:
                print(f"   pip install {package}")
        
        print("\n💡 For complete installation:")
        print("   pip install -r requirements.txt")
    
    # Special notes
    print("\n📝 Special Notes")
    print("-" * 15)
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 8):
        print(f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"⚠️  Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
        print("   Python 3.8+ is recommended for best compatibility.")
    
    # Check for CUDA
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.version.cuda}")
            print(f"   GPU devices: {torch.cuda.device_count()}")
        else:
            print("⚠️  CUDA not available (CPU-only mode)")
    except:
        print("❓ Could not check CUDA availability")
    
    print("\n" + "=" * 50)
    return len(core_missing) == 0

if __name__ == "__main__":
    success = check_dependencies()
    sys.exit(0 if success else 1)
