@echo off
setlocal enabledelayedexpansion

:: FramePack Filename Checker
:: This batch file checks for MP4 files that don't match the expected naming schemes
:: or are missing the seconds indicator at the end of the filename.

title FramePack Filename Checker

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Python is not installed or not in the PATH.
    echo Please install Python and make sure it's in your PATH.
    pause
    exit /b 1
)

:: Check if the sorter script exists
if not exist framepack_output_sorter.py (
    echo ERROR: framepack_output_sorter.py not found in the current directory.
    echo Please make sure you're running this batch file from the same directory as the script.
    pause
    exit /b 1
)

:: Default values
set "directory=."

:menu
cls
echo ===================================================
echo              FRAMEPACK FILENAME CHECKER
echo ===================================================
echo.
echo This tool checks MP4 files for naming convention issues:
echo  - Files that don't match any recognized naming scheme
echo  - Files that match a scheme but are missing seconds indicator
echo.
echo Current Settings:
echo   Directory to scan: %directory%
echo.
echo Options:
echo   1. Set directory to scan (current: %directory%)
echo   2. Run check with current settings
echo   3. Exit
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto set_directory
if "%choice%"=="2" goto run_check
if "%choice%"=="3" goto end
goto menu

:set_directory
cls
echo ===================================================
echo                SET DIRECTORY TO SCAN
echo ===================================================
echo.
echo Enter the path to the directory you want to scan.
echo Default is the current directory.
echo.
set /p directory="Directory path: "
if "!directory!"=="" set "directory=."
goto menu

:run_check
cls
echo ===================================================
echo               CHECKING FILENAMES
echo ===================================================
echo.
echo Running with the following settings:
echo   Directory: %directory%
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

:: Build the command
set "cmd=python framepack_output_sorter.py --directory "%directory%" --check-only"

echo.
echo Running command: !cmd!
echo.
echo ===================================================
echo                  CHECK OUTPUT
echo ===================================================
echo.

:: Run the command
!cmd!

echo.
echo ===================================================
echo                  CHECK FINISHED
echo ===================================================
echo.
echo Press any key to return to the menu...
pause >nul
goto menu

:end
echo.
echo Thank you for using FramePack Filename Checker.
echo.
endlocal
exit /b 0
