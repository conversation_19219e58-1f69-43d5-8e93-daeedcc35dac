@echo off
REM Convert latent preview MP4 files to GIF format for better compatibility

echo Converting latent preview MP4 files to GIF format...

REM Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is not installed or not in the PATH.
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Check if ffmpeg is installed
ffmpeg -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: ffmpeg is not installed or not in the PATH.
    echo Please install ffmpeg and try again.
    echo You can download ffmpeg from https://ffmpeg.org/download.html
    pause
    exit /b 1
)

REM Install Pillow if not already installed
echo Checking for Pillow...
python -c "import PIL" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Installing Pillow...
    pip install pillow
    if %ERRORLEVEL% NEQ 0 (
        echo Error: Failed to install Pillow.
        echo Please try installing it manually: pip install pillow
        pause
        exit /b 1
    )
    echo Pillow installed successfully.
) else (
    echo Pillow is already installed.
)

REM Create latent_previews directory if it doesn't exist
if not exist "latent_previews" (
    echo Creating latent_previews directory...
    mkdir latent_previews
)

REM Create temp directory if it doesn't exist
if not exist "temp" (
    echo Creating temp directory...
    mkdir temp
)

REM Run the converter script
echo Running generate_gif_preview.py...
python generate_gif_preview.py --all

echo Conversion completed.
pause
