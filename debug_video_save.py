#!/usr/bin/env python3
"""
Debug script to test video saving functionality and identify potential issues
that might cause exit code -1 on different systems.
"""

import os
import sys
import torch
import torchvision
import tempfile
import subprocess
import traceback
import numpy as np
import einops

def test_torch_video_save():
    """Test torchvision.io.write_video functionality"""
    print("=" * 60)
    print("Testing torchvision.io.write_video...")
    print("=" * 60)
    
    try:
        # Create a simple test tensor (similar to what FramePack generates)
        # Shape: (batch, channels, time, height, width)
        b, c, t, h, w = 1, 3, 25, 320, 240  # 25 frames, 320x240 resolution
        
        print(f"Creating test tensor with shape: ({b}, {c}, {t}, {h}, {w})")
        
        # Create random data similar to what the model would output
        x = torch.randn(b, c, t, h, w)
        
        # Process the tensor the same way as in save_bcthw_as_mp4
        per_row = b
        for p in [6, 5, 4, 3, 2]:
            if b % p == 0:
                per_row = p
                break
        
        print(f"Using per_row: {per_row}")
        
        # Create output directory
        output_dir = "debug_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # Process tensor
        x = torch.clamp(x.float(), -1., 1.) * 127.5 + 127.5
        x = x.detach().cpu().to(torch.uint8)
        x = einops.rearrange(x, '(m n) c t h w -> t (m h) (n w) c', n=per_row)
        
        print(f"Processed tensor shape: {x.shape}")
        print(f"Tensor dtype: {x.dtype}")
        print(f"Tensor min/max: {x.min()}/{x.max()}")
        
        # Test different video save configurations
        test_configs = [
            {"fps": 10, "crf": 0, "name": "default"},
            {"fps": 10, "crf": 16, "name": "crf16"},
            {"fps": 30, "crf": 16, "name": "fps30_crf16"},
        ]
        
        for config in test_configs:
            output_filename = os.path.join(output_dir, f"test_video_{config['name']}.mp4")
            print(f"\nTesting config: {config}")
            print(f"Output file: {output_filename}")
            
            try:
                # Try basic save
                torchvision.io.write_video(
                    output_filename, 
                    x, 
                    fps=config['fps'], 
                    video_codec='libx264', 
                    options={'crf': str(int(config['crf']))}
                )
                
                # Verify file was created
                if os.path.exists(output_filename):
                    file_size = os.path.getsize(output_filename)
                    print(f"✓ SUCCESS: Video saved ({file_size} bytes)")
                else:
                    print("✗ FAILED: Video file not created")
                    
            except Exception as e:
                print(f"✗ FAILED: {type(e).__name__}: {e}")
                traceback.print_exc()
                
                # Try fallback with different options
                try:
                    print("Trying fallback with additional options...")
                    torchvision.io.write_video(
                        output_filename, 
                        x, 
                        fps=config['fps'], 
                        video_codec='libx264', 
                        options={
                            'crf': str(int(config['crf'])),
                            'preset': 'medium',
                            'pix_fmt': 'yuv420p'
                        }
                    )
                    
                    if os.path.exists(output_filename):
                        file_size = os.path.getsize(output_filename)
                        print(f"✓ FALLBACK SUCCESS: Video saved ({file_size} bytes)")
                    else:
                        print("✗ FALLBACK FAILED: Video file not created")
                        
                except Exception as fallback_error:
                    print(f"✗ FALLBACK FAILED: {type(fallback_error).__name__}: {fallback_error}")
        
        return True
        
    except Exception as e:
        print(f"FATAL ERROR in test_torch_video_save: {e}")
        traceback.print_exc()
        return False

def test_ffmpeg_availability():
    """Test FFmpeg availability and functionality"""
    print("\n" + "=" * 60)
    print("Testing FFmpeg availability...")
    print("=" * 60)
    
    try:
        # Test imageio-ffmpeg
        import imageio_ffmpeg
        ffmpeg_path = imageio_ffmpeg.get_ffmpeg_exe()
        print(f"imageio-ffmpeg path: {ffmpeg_path}")
        
        # Test basic FFmpeg functionality
        result = subprocess.run([ffmpeg_path, '-version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ FFmpeg is working")
            # Print first few lines of version info
            version_lines = result.stdout.split('\n')[:3]
            for line in version_lines:
                if line.strip():
                    print(f"  {line}")
        else:
            print(f"✗ FFmpeg failed with return code: {result.returncode}")
            print(f"stderr: {result.stderr}")
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"ERROR testing FFmpeg: {e}")
        traceback.print_exc()
        return False

def test_metadata_addition():
    """Test metadata addition functionality"""
    print("\n" + "=" * 60)
    print("Testing metadata addition...")
    print("=" * 60)
    
    try:
        # First create a simple test video
        test_video = "debug_output/test_video_default.mp4"
        if not os.path.exists(test_video):
            print("No test video found, skipping metadata test")
            return False
            
        import imageio_ffmpeg
        ffmpeg_path = imageio_ffmpeg.get_ffmpeg_exe()
        
        # Create a temporary file for metadata test
        temp_file = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False).name
        
        # Test metadata command
        command = [
            ffmpeg_path,
            '-i', test_video,
            '-metadata', 'comment=Test metadata comment',
            '-c:v', 'copy',
            '-c:a', 'copy',
            '-y',
            temp_file
        ]
        
        print(f"Running metadata command...")
        result = subprocess.run(command, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                print("✓ Metadata addition successful")
                os.remove(temp_file)
                return True
            else:
                print("✗ Metadata addition failed: output file empty or missing")
        else:
            print(f"✗ Metadata addition failed with return code: {result.returncode}")
            print(f"stderr: {result.stderr}")
            
        # Clean up temp file
        if os.path.exists(temp_file):
            os.remove(temp_file)
            
        return False
        
    except Exception as e:
        print(f"ERROR testing metadata addition: {e}")
        traceback.print_exc()
        return False

def print_system_info():
    """Print system information for debugging"""
    print("=" * 60)
    print("System Information")
    print("=" * 60)
    
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"Torchvision version: {torchvision.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA version: {torch.version.cuda}")
        print(f"GPU count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    
    print(f"Operating system: {os.name}")
    print(f"Current working directory: {os.getcwd()}")

def main():
    """Main test function"""
    print("FramePack Video Save Debug Tool")
    print("This tool will test the video saving functionality to identify potential issues.")
    print()
    
    # Print system info
    print_system_info()
    
    # Run tests
    tests = [
        ("FFmpeg Availability", test_ffmpeg_availability),
        ("Torch Video Save", test_torch_video_save),
        ("Metadata Addition", test_metadata_addition),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"FATAL ERROR in {test_name}: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n✓ All tests passed! The issue might be elsewhere.")
    else:
        print("\n✗ Some tests failed. This may indicate the source of the problem.")
    
    print(f"\nDebug output saved to: {os.path.abspath('debug_output')}")

if __name__ == "__main__":
    main()
