# FramePack GUI ETA Display Feature

## Overview

The FramePack GUI now includes an ETA (Estimated Time of Arrival) display system that shows:

1. **Current Section ETA**: Progress and estimated time remaining for the current generation section
2. **Total Generation ETA**: Estimated time remaining for the entire generation process

## Features

### Real-time Progress Tracking
- Monitors CLI output for step progress (e.g., "Step 15/30")
- Updates progress percentage and time estimates in real-time
- Becomes more accurate as generation progresses

### Intelligent Time Estimation
- Considers video length, step count, model type, and LoRA usage
- Adjusts estimates based on actual processing speed
- Provides different estimates for F1 vs Standard models

### Visual Display
- Shows current section progress with percentage and time remaining
- Displays total generation ETA with overall progress
- Includes helpful tooltips explaining each display
- Automatically clears when generation completes or is stopped

## Location

The ETA display appears in the Queue section of the GUI, below the queue status and progress labels:

```
Queue: 1 job(s)                    Processing job 1/1, iteration 1/1 - Files: 1

Current: 2m 15s remaining (45%)     Total: 3m 30s remaining (est.)
```

## How It Works

### Progress Monitoring
1. When generation starts, the GUI initializes ETA tracking
2. Monitors for progress updates from the CLI batch script
3. Parses step information (e.g., "Step 15/30") to calculate progress
4. Updates time estimates based on elapsed time and progress rate

### Time Estimation Algorithm
1. **Initial Estimate**: Based on settings (video length, steps, model type, LoRAs)
2. **Real-time Updates**: Adjusts based on actual processing speed
3. **Accuracy Improvement**: Becomes more precise as more steps complete

### Settings Considered
- **Video Length**: Longer videos take more time per step
- **Step Count**: More steps = longer generation
- **Model Type**: F1 models are generally faster than standard models
- **LoRA Usage**: Each LoRA adds processing overhead
- **Custom Models**: Take longer to load initially

## Example Time Estimates

### 5-second video, 20 steps, Standard model, no LoRAs:
- Initial estimate: ~2 minutes
- Overhead: 30 seconds (model loading)
- Total: ~2.5 minutes

### 10-second video, 30 steps, F1 model, 2 LoRAs:
- Base time: 30 steps × 1.5s × 1.3 (length multiplier) = ~59 seconds
- Overhead: 30 seconds + 30 seconds (2 LoRAs)
- Total: ~2 minutes

### 20-second video, 40 steps, Custom model, 3 LoRAs:
- Base time: 40 steps × 2.0s × 1.6 (length multiplier) = ~128 seconds
- Overhead: 45 seconds + 45 seconds (3 LoRAs) + 10 seconds (extra steps)
- Total: ~3.8 minutes

## Benefits

1. **Better User Experience**: Users know how long to wait
2. **Progress Visibility**: Clear indication that generation is progressing
3. **Planning**: Users can plan other activities based on remaining time
4. **Troubleshooting**: Helps identify if generation is stuck or progressing normally

## Technical Implementation

- **Progress Parsing**: Regex pattern matching on CLI output
- **File Monitoring**: Checks for progress updates every 2 seconds
- **Time Calculations**: Uses elapsed time and progress rate for accuracy
- **UI Updates**: Updates display every 3 seconds during generation
- **Memory Efficient**: Minimal overhead on system resources

## Future Enhancements

Potential improvements for future versions:
- Frame-level progress tracking
- Section-by-section progress for multi-section videos
- Historical data for better time estimates
- Hardware-specific calibration
- Queue-wide ETA for multiple jobs
