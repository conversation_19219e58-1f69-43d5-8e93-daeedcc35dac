from diffusers_helper.hf_login import login

import os
import sys, asyncio
import subprocess

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


os.environ['HF_HOME'] = os.path.abspath(os.path.realpath(os.path.join(os.path.dirname(__file__), './hf_download')))

# Import auto sorter functionality
from auto_sorter import auto_sort_after_generation

# Suppress OpenCV FFMPEG errors
os.environ['OPENCV_FFMPEG_LOGLEVEL'] = '0'  # Suppress FFMPEG output
os.environ['OPENCV_FFMPEG_DEBUG'] = '0'     # Disable FFMPEG debug messages
os.environ['OPENCV_LOG_LEVEL'] = '0'        # Suppress all OpenCV logging

# Create a custom output suppressor
class SuppressOutput:
    def __init__(self):
        self.original_stdout = None
        self.original_stderr = None
        self.null_file = None

    def __enter__(self):
        # Save original stdout and stderr
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        # Open a null file
        self.null_file = open(os.devnull, 'w')
        # Redirect stdout and stderr to the null file
        sys.stdout = self.null_file
        sys.stderr = self.null_file
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore original stdout and stderr
        sys.stdout = self.original_stdout
        sys.stderr = self.original_stderr
        # Close the null file
        if self.null_file:
            self.null_file.close()

import gradio as gr
import torch
import traceback
import einops
import safetensors.torch as sf
import numpy as np
import argparse
import math
import random
import re
import time
import json
import base64

# Helper function to save images with metadata
def save_image_with_metadata(image_array, filepath, metadata_dict):
    """
    Save an image with metadata embedded in the PNG file.

    Args:
        image_array: Numpy array containing the image data
        filepath: Path where the image will be saved
        metadata_dict: Dictionary containing metadata to embed
    """
    # Convert numpy array to PIL Image
    img = Image.fromarray(image_array)

    # Convert metadata to JSON string
    metadata_json = json.dumps(metadata_dict)

    # Create PngInfo object
    png_info = PngImagePlugin.PngInfo()

    # Add metadata to the PngInfo object
    png_info.add_text("FramePack", metadata_json)

    # Save the image with metadata
    img.save(filepath, format="PNG", pnginfo=png_info)

    print(f"Saved image with metadata to {filepath}")
    print(f"Metadata: {metadata_dict}")


# Helper function to extract metadata from PNG files
def extract_metadata_from_png(image_path):
    """
    Extract metadata from a PNG file.

    Args:
        image_path: Path to the PNG file

    Returns:
        Dictionary containing the extracted metadata, or None if no metadata is found
    """
    try:
        # Open the image
        img = Image.open(image_path)

        # Get the metadata
        metadata_json = img.info.get('FramePack')
        if metadata_json:
            # Parse the JSON
            metadata = json.loads(metadata_json)
            print(f"Extracted metadata from {image_path}: {metadata}")
            return metadata
        else:
            print(f"No FramePack metadata found in {image_path}")
            return None
    except Exception as e:
        print(f"Error extracting metadata from {image_path}: {e}")
        return None


# Function to load metadata from an image and populate the UI fields
def load_metadata_from_image(image):
    """
    Load metadata from an image and return values for UI fields.

    Args:
        image: Image data from Gradio

    Returns:
        Values for prompt, seed, steps, etc. to populate the UI
    """
    if image is None:
        print("No image provided")
        return [gr.update() for _ in range(9)]  # Return no updates if no image

    try:
        # First, try to directly extract metadata from the uploaded image
        # Convert numpy array to PIL Image
        try:
            print("Attempting to extract metadata directly from uploaded image...")
            pil_image = Image.fromarray(image)

            # Try to get metadata directly from the PIL image
            metadata_json = pil_image.info.get('FramePack')
            if metadata_json:
                # Parse the JSON
                metadata = json.loads(metadata_json)
                print(f"Successfully extracted metadata directly from uploaded image: {metadata}")

                # Extract values from metadata
                prompt_value = metadata.get('prompt', '')
                n_prompt_value = metadata.get('negative_prompt', '')
                seed_value = metadata.get('seed', -1)
                steps_value = metadata.get('steps', 25)
                cfg_value = metadata.get('cfg_scale', 1.0)
                gs_value = metadata.get('distilled_cfg_scale', 10.0)
                rs_value = metadata.get('cfg_rescale', 0.0)
                use_teacache_value = metadata.get('use_teacache', True)
                total_second_length_value = float(metadata.get('total_video_length', '5 seconds').split()[0])

                # Return values for UI update
                return [
                    prompt_value,                # prompt
                    n_prompt_value,              # n_prompt
                    seed_value,                  # seed
                    total_second_length_value,   # total_second_length
                    steps_value,                 # steps
                    cfg_value,                   # cfg
                    gs_value,                    # gs
                    rs_value,                    # rs
                    use_teacache_value,          # use_teacache
                ]
            else:
                print("No metadata found in the uploaded image directly")
        except Exception as e:
            print(f"Error extracting metadata directly from uploaded image: {e}")

        # Second, look for the image in the temp folder (where Gradio stores uploaded files)
        try:
            print("Looking for image in temp folder...")

            # Calculate a hash of the image to help identify it
            import hashlib
            image_hash = hashlib.md5(image.tobytes()).hexdigest()
            print(f"Image hash: {image_hash}")

            # Look through all files in the temp folder
            for root, _, files in os.walk(temp_folder):
                for filename in files:
                    if filename.endswith('.png') or filename.endswith('.jpg') or filename.endswith('.jpeg'):
                        try:
                            file_path = os.path.join(root, filename)
                            print(f"Checking temp file: {file_path}")

                            # Open the image and check if it matches
                            file_image = np.array(Image.open(file_path))

                            # Skip if shapes don't match
                            if file_image.shape != image.shape:
                                continue

                            # Check if the images are similar (using a hash)
                            file_hash = hashlib.md5(file_image.tobytes()).hexdigest()

                            if file_hash == image_hash:
                                print(f"Found matching file in temp folder: {file_path}")

                                # Extract metadata from the file
                                metadata = extract_metadata_from_png(file_path)

                                if metadata:
                                    print(f"Successfully extracted metadata from temp file: {metadata}")
                                    # Extract values from metadata
                                    prompt_value = metadata.get('prompt', '')
                                    n_prompt_value = metadata.get('negative_prompt', '')
                                    seed_value = metadata.get('seed', -1)
                                    steps_value = metadata.get('steps', 25)
                                    cfg_value = metadata.get('cfg_scale', 1.0)
                                    gs_value = metadata.get('distilled_cfg_scale', 10.0)
                                    rs_value = metadata.get('cfg_rescale', 0.0)
                                    use_teacache_value = metadata.get('use_teacache', True)
                                    total_second_length_value = float(metadata.get('total_video_length', '5 seconds').split()[0])

                                    # Return values for UI update
                                    return [
                                        prompt_value,                # prompt
                                        n_prompt_value,              # n_prompt
                                        seed_value,                  # seed
                                        total_second_length_value,   # total_second_length
                                        steps_value,                 # steps
                                        cfg_value,                   # cfg
                                        gs_value,                    # gs
                                        rs_value,                    # rs
                                        use_teacache_value,          # use_teacache
                                    ]
                        except Exception as e:
                            print(f"Error checking temp file {filename}: {e}")
        except Exception as e:
            print(f"Error searching temp folder: {e}")

        # Third, try to find a matching file in the outputs folder
        # This is necessary because Gradio strips metadata when uploading images
        found_file = None

        print("Looking for image in outputs folder...")
        # Look through all PNG files in the outputs folder
        for filename in os.listdir(outputs_folder):
            if filename.endswith('.png'):
                try:
                    file_path = os.path.join(outputs_folder, filename)
                    file_image = np.array(Image.open(file_path))

                    # Skip if shapes don't match
                    if file_image.shape != image.shape:
                        continue

                    # Check if the images are similar (using a hash)
                    file_hash = hashlib.md5(file_image.tobytes()).hexdigest()

                    if file_hash == image_hash:
                        found_file = file_path
                        print(f"Found matching file in outputs folder: {found_file}")
                        break
                except Exception as e:
                    print(f"Error checking file {filename}: {e}")

        # If we found a matching file, extract metadata from it
        if found_file:
            metadata = extract_metadata_from_png(found_file)

            if metadata:
                print(f"Successfully extracted metadata from outputs folder file: {metadata}")
                # Extract values from metadata
                prompt_value = metadata.get('prompt', '')
                n_prompt_value = metadata.get('negative_prompt', '')
                seed_value = metadata.get('seed', -1)
                steps_value = metadata.get('steps', 25)
                cfg_value = metadata.get('cfg_scale', 1.0)
                gs_value = metadata.get('distilled_cfg_scale', 10.0)
                rs_value = metadata.get('cfg_rescale', 0.0)
                use_teacache_value = metadata.get('use_teacache', True)
                total_second_length_value = float(metadata.get('total_video_length', '5 seconds').split()[0])

                # Return values for UI update
                return [
                    prompt_value,                # prompt
                    n_prompt_value,              # n_prompt
                    seed_value,                  # seed
                    total_second_length_value,   # total_second_length
                    steps_value,                 # steps
                    cfg_value,                   # cfg
                    gs_value,                    # gs
                    rs_value,                    # rs
                    use_teacache_value,          # use_teacache
                ]
            else:
                print(f"No metadata found in the matching file from outputs folder: {found_file}")
        else:
            print("No matching file found in outputs folder")

        # If we're running locally (not accessed from another PC), we can try the file dialog approach
        # Check if we're running in a desktop environment where tkinter will work
        try:
            # Only try to use tkinter if we're not in a headless environment
            # This is a simple check that might not work in all cases
            if os.environ.get('DISPLAY') or os.name == 'nt':
                import tkinter as tk
                from tkinter import filedialog

                # Create a root window but hide it
                root = tk.Tk()
                root.withdraw()

                # Show a message to the user
                print("Could not find metadata automatically. Please select a PNG file with metadata.")

                # Ask the user to select a PNG file
                file_path = filedialog.askopenfilename(
                    title="Select PNG file with metadata",
                    filetypes=[("PNG files", "*.png")],
                    initialdir=outputs_folder
                )

                # If the user cancels the dialog, return no updates
                if not file_path:
                    print("User cancelled file selection")
                    return [gr.update() for _ in range(9)]

                # Extract metadata from the selected file
                metadata = extract_metadata_from_png(file_path)

                if metadata:
                    # Extract values from metadata
                    prompt_value = metadata.get('prompt', '')
                    n_prompt_value = metadata.get('negative_prompt', '')
                    seed_value = metadata.get('seed', -1)
                    steps_value = metadata.get('steps', 25)
                    cfg_value = metadata.get('cfg_scale', 1.0)
                    gs_value = metadata.get('distilled_cfg_scale', 10.0)
                    rs_value = metadata.get('cfg_rescale', 0.0)
                    use_teacache_value = metadata.get('use_teacache', True)
                    total_second_length_value = float(metadata.get('total_video_length', '5 seconds').split()[0])

                    # Return values for UI update
                    return [
                        prompt_value,                # prompt
                        n_prompt_value,              # n_prompt
                        seed_value,                  # seed
                        total_second_length_value,   # total_second_length
                        steps_value,                 # steps
                        cfg_value,                   # cfg
                        gs_value,                    # gs
                        rs_value,                    # rs
                        use_teacache_value,          # use_teacache
                    ]
                else:
                    print(f"No metadata found in the selected file: {file_path}")
        except Exception as e:
            print(f"Error using file dialog: {e}")

        # If all methods failed, return no updates
        print("All metadata extraction methods failed")
        return [gr.update() for _ in range(9)]
    except Exception as e:
        print(f"Error loading metadata: {e}")
        # Return no updates on error
        return [gr.update() for _ in range(9)]

# Custom exception for graceful termination
class GracefulExit(Exception):
    """Exception raised when user manually stops generation."""
    pass



from PIL import Image, PngImagePlugin
import cv2
from diffusers import AutoencoderKLHunyuanVideo
from transformers import LlamaModel, CLIPTextModel, LlamaTokenizerFast, CLIPTokenizer
from diffusers_helper.hunyuan import encode_prompt_conds, vae_decode, vae_encode, vae_decode_fake
from diffusers_helper.utils import save_bcthw_as_mp4, crop_or_pad_yield_mask, soft_append_bcthw, resize_and_center_crop, state_dict_weighted_merge, state_dict_offset_merge, generate_timestamp, torch_safe_save
from diffusers_helper.models.hunyuan_video_packed import HunyuanVideoTransformer3DModelPacked
from diffusers_helper.pipelines.k_diffusion_hunyuan import sample_hunyuan
from diffusers_helper.memory import cpu, gpu, get_cuda_free_memory_gb, move_model_to_device_with_memory_preservation, offload_model_from_device_for_memory_preservation, fake_diffusers_current_device, DynamicSwapInstaller, unload_complete_models, load_model_as_complete
from diffusers_helper.thread_utils import AsyncStream, async_run
from diffusers_helper.gradio.progress_bar import make_progress_bar_css, make_progress_bar_html
from transformers import SiglipImageProcessor, SiglipVisionModel
from diffusers_helper.clip_vision import hf_clip_vision_encode
from diffusers_helper.bucket_tools import find_nearest_bucket
from diffusers_helper.tqdm_capture import set_progress_callback


parser = argparse.ArgumentParser()
parser.add_argument('--share', action='store_true')
parser.add_argument("--server", type=str, default='0.0.0.0')
parser.add_argument("--port", type=int, required=False)
parser.add_argument("--inbrowser", action='store_true')
args = parser.parse_args()

# for win desktop probably use --server 127.0.0.1 --inbrowser
# For linux server probably use --server 127.0.0.1 or do not use any cmd flags
print(args)

free_mem_gb = get_cuda_free_memory_gb(gpu)
high_vram = free_mem_gb > 60

print(f'Free VRAM {free_mem_gb} GB')
print(f'High-VRAM Mode: {high_vram}')

text_encoder = LlamaModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder', torch_dtype=torch.float16).cpu()
text_encoder_2 = CLIPTextModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder_2', torch_dtype=torch.float16).cpu()
tokenizer = LlamaTokenizerFast.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer')
tokenizer_2 = CLIPTokenizer.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer_2')
vae = AutoencoderKLHunyuanVideo.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='vae', torch_dtype=torch.float16).cpu()

feature_extractor = SiglipImageProcessor.from_pretrained("lllyasviel/flux_redux_bfl", subfolder='feature_extractor')
image_encoder = SiglipVisionModel.from_pretrained("lllyasviel/flux_redux_bfl", subfolder='image_encoder', torch_dtype=torch.float16).cpu()

transformer = HunyuanVideoTransformer3DModelPacked.from_pretrained('lllyasviel/FramePackI2V_HY', torch_dtype=torch.bfloat16).cpu()

vae.eval()
text_encoder.eval()
text_encoder_2.eval()
image_encoder.eval()
transformer.eval()

if not high_vram:
    vae.enable_slicing()
    vae.enable_tiling()

transformer.high_quality_fp32_output_for_inference = True
print('transformer.high_quality_fp32_output_for_inference = True')

transformer.to(dtype=torch.bfloat16)
vae.to(dtype=torch.float16)
image_encoder.to(dtype=torch.float16)
text_encoder.to(dtype=torch.float16)
text_encoder_2.to(dtype=torch.float16)

vae.requires_grad_(False)
text_encoder.requires_grad_(False)
text_encoder_2.requires_grad_(False)
image_encoder.requires_grad_(False)
transformer.requires_grad_(False)

if not high_vram:
    # DynamicSwapInstaller is same as huggingface's enable_sequential_offload but 3x faster
    DynamicSwapInstaller.install_model(transformer, device=gpu)
    DynamicSwapInstaller.install_model(text_encoder, device=gpu)
else:
    text_encoder.to(gpu)
    text_encoder_2.to(gpu)
    image_encoder.to(gpu)
    vae.to(gpu)
    transformer.to(gpu)

stream = AsyncStream()

# Set up folders for outputs and temporary files
outputs_folder = './outputs/'
temp_folder = './temp/'
os.makedirs(outputs_folder, exist_ok=True)
os.makedirs(temp_folder, exist_ok=True)

# Set Gradio's temporary directory to our temp folder
import tempfile
# Set the Python tempdir
tempfile.tempdir = os.path.abspath(temp_folder)
# Set environment variables that Gradio uses for temp files
os.environ["GRADIO_TEMP_DIR"] = os.path.abspath(temp_folder)
os.environ["GRADIO_CACHE_DIR"] = os.path.abspath(os.path.join(temp_folder, "gradio_cache"))
print(f"Setting temporary directory to: {os.path.abspath(temp_folder)}")


from image_blank import create_transparent_image
from PIL import Image
import io
import tempfile
import os

@torch.no_grad()
def worker(input_image, end_image, prompt, n_prompt, seed, total_second_length, latent_window_size, steps, cfg, gs, rs, gpu_memory_preservation, use_teacache, mp4_crf):
    # Check if no start frame is supplied but an end frame is supplied
    if input_image is None and end_image is not None:
        print("No start frame supplied but end frame is supplied. Creating transparent start frame...")

        # Create a temporary file to save the end image
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            temp_path = temp_file.name

        try:
            # Save the end image to the temporary file
            end_img = Image.fromarray(end_image)
            end_img.save(temp_path)

            # Create a transparent version of the end image
            transparent_path = create_transparent_image(temp_path)

            # Load the transparent image
            input_image = np.array(Image.open(transparent_path))

            # Make sure the image has the right format (RGB)
            if input_image.shape[2] == 4:  # RGBA
                # Convert RGBA to RGB by filling transparent areas with white
                alpha = input_image[:, :, 3:4] / 255.0
                rgb = input_image[:, :, :3]
                white_background = np.ones_like(rgb) * 255
                input_image = rgb * alpha + white_background * (1 - alpha)
                input_image = input_image.astype(np.uint8)

            print(f"Created transparent start frame with shape {input_image.shape}")

            # Clean up temporary files
            os.remove(temp_path)
            # Don't remove the transparent image as it might be useful for the user

        except Exception as e:
            print(f"Error creating transparent start frame: {e}")
            # Fallback method if the above fails
            H, W, C = end_image.shape

            # Create a transparent image using PIL
            transparent_img = Image.new('RGBA', (W, H), (0, 0, 0, 0))

            # Convert PIL image to numpy array
            buffer = io.BytesIO()
            transparent_img.save(buffer, format='PNG')
            buffer.seek(0)

            # Load the image back as a numpy array
            input_image = np.array(Image.open(buffer))

            # Make sure the image has the right format (RGB)
            if input_image.shape[2] == 4:  # RGBA
                # Convert RGBA to RGB by filling transparent areas with white
                alpha = input_image[:, :, 3:4] / 255.0
                rgb = input_image[:, :, :3]
                white_background = np.ones_like(rgb) * 255
                input_image = rgb * alpha + white_background * (1 - alpha)
                input_image = input_image.astype(np.uint8)

            print(f"Created transparent start frame with shape {input_image.shape} (fallback method)")

    # Calculate the total number of latent sections
    # Use math.ceil instead of round to ensure we have enough sections
    import math
    frames_per_second = 30
    total_frames = total_second_length * frames_per_second
    frames_per_section = latent_window_size * 4
    raw_sections = total_frames / frames_per_section

    # Use ceiling to ensure we have enough sections to cover the requested video length
    total_latent_sections = int(max(math.ceil(raw_sections), 1))

    # Print the calculation for debugging
    print(f"Video length: {total_second_length} seconds = {total_frames} frames")
    print(f"Each section covers {frames_per_section} frames")
    print(f"Raw section count: {raw_sections}, Ceiling: {math.ceil(raw_sections)}")
    print(f"Using {total_latent_sections} sections for generation")

    # Calculate the total number of steps across all sections
    total_steps = total_latent_sections * steps

    # Variables to track overall progress
    current_section = 0
    displayed_section = 0  # The section number shown in the UI
    steps_completed = 0
    start_time = time.time()
    in_transition = False  # Flag to track when we're transitioning between sections

    # For estimating time remaining
    section_start_times = {}
    section_end_times = {}

    job_id = generate_timestamp()

    # Set up the progress callback to update the UI with tqdm progress
    def progress_callback(progress_info):
        try:
            # Extract the progress information from the current section
            percentage = progress_info.get('percentage', 0)
            current = progress_info.get('current', 0)
            total = progress_info.get('total', 0)
            description = progress_info.get('description', '')

            # Only update if we have valid progress information
            if current > 0 and total > 0:
                # Update our tracking variables
                nonlocal steps_completed, current_section, displayed_section, in_transition

                # Only update the displayed section at the beginning of a new section
                # This prevents the section number from changing during transitions
                if current == 1:
                    # We're starting a new section
                    in_transition = False
                    displayed_section = current_section

                    # Record the time for the current section
                    if current_section not in section_start_times:
                        section_start_times[current_section] = time.time()
                        print(f"Starting section {current_section+1}/{total_latent_sections}")

                # If we're at the end of a section, mark that we're entering a transition
                if current == total:
                    in_transition = True
                    print(f"Finishing section {displayed_section+1}/{total_latent_sections}")

                # Calculate overall progress
                current_section_steps = current
                steps_completed = (displayed_section * steps) + current_section_steps
                overall_percentage = int((steps_completed / total_steps) * 100)

                # Calculate time remaining for current section
                section_elapsed_time = time.time() - section_start_times[displayed_section]
                if current > 1:  # Need at least 2 steps to estimate
                    section_time_per_step = section_elapsed_time / (current - 1)  # -1 because we're still on the current step
                    section_remaining_steps = total - current
                    section_estimated_remaining_time = section_remaining_steps * section_time_per_step

                    # Format the section remaining time
                    if section_estimated_remaining_time < 60:
                        section_remaining_time_str = f"{section_estimated_remaining_time:.0f} seconds"
                    elif section_estimated_remaining_time < 3600:
                        section_remaining_time_str = f"{section_estimated_remaining_time/60:.1f} minutes"
                    else:
                        section_remaining_time_str = f"{section_estimated_remaining_time/3600:.1f} hours"
                else:
                    section_remaining_time_str = "Calculating..."

                # Calculate total time remaining based on overall progress
                total_elapsed_time = time.time() - start_time

                # Simple method: Based on steps completed so far
                # This is the most reliable method and doesn't make assumptions about section times
                if steps_completed > 0:
                    # Calculate time per step based on all steps completed so far
                    time_per_step = total_elapsed_time / steps_completed
                    remaining_steps = total_steps - steps_completed
                    estimated_remaining_time = remaining_steps * time_per_step

                    # Make sure we don't get negative time
                    estimated_remaining_time = max(0, estimated_remaining_time)

                    # Format the remaining time
                    if estimated_remaining_time < 60:
                        remaining_time_str = f"{estimated_remaining_time:.0f} seconds"
                    elif estimated_remaining_time < 3600:
                        minutes = int(estimated_remaining_time // 60)
                        seconds = int(estimated_remaining_time % 60)
                        remaining_time_str = f"{minutes}m {seconds}s"
                    else:
                        hours = int(estimated_remaining_time // 3600)
                        minutes = int((estimated_remaining_time % 3600) // 60)
                        seconds = int(estimated_remaining_time % 60)
                        remaining_time_str = f"{hours}h {minutes}m {seconds}s"
                else:
                    remaining_time_str = "Calculating..."

                # Calculate total estimated time (elapsed + remaining)
                if steps_completed > 0:
                    total_estimated_time = total_elapsed_time + estimated_remaining_time

                    # Format the total estimated time
                    if total_estimated_time < 60:
                        total_time_str = f"{total_estimated_time:.0f} seconds"
                    elif total_estimated_time < 3600:
                        minutes = int(total_estimated_time // 60)
                        seconds = int(total_estimated_time % 60)
                        total_time_str = f"{minutes}m {seconds}s"
                    else:
                        hours = int(total_estimated_time // 3600)
                        minutes = int((total_estimated_time % 3600) // 60)
                        seconds = int(total_estimated_time % 60)
                        total_time_str = f"{hours}h {minutes}m {seconds}s"
                else:
                    total_time_str = "Calculating..."

                # Create a comprehensive progress message
                total_frames = int(max(0, total_generated_latent_frames * 4 - 3))
                video_length = max(0, (total_generated_latent_frames * 4 - 3) / 30)

                # Format elapsed time since the start of generation
                elapsed_time_str = ""
                if total_elapsed_time < 60:
                    elapsed_time_str = f"{total_elapsed_time:.0f} seconds"
                elif total_elapsed_time < 3600:
                    minutes = int(total_elapsed_time // 60)
                    seconds = int(total_elapsed_time % 60)
                    elapsed_time_str = f"{minutes}m {seconds}s"
                else:
                    hours = int(total_elapsed_time // 3600)
                    minutes = int((total_elapsed_time % 3600) // 60)
                    seconds = int(total_elapsed_time % 60)
                    elapsed_time_str = f"{hours}h {minutes}m {seconds}s"

                # Create a hint message that includes the progress bar
                # Format with line breaks at natural break points for better display
                hint = f"Section {displayed_section+1}/{total_latent_sections}: {current}/{total} steps (Section ETA: {section_remaining_time_str})<br>"
                hint += f"Overall: {steps_completed}/{total_steps} steps ({overall_percentage}%)<br>"

                # Add seed information if using random seed
                if seed == -1 and random_seed is not None:
                    hint += f"Seed: {random_seed}<br>"

                # Add elapsed time, remaining time and total time estimates
                hint += f"Elapsed: {elapsed_time_str}<br>"
                hint += f"Remaining: {remaining_time_str}<br>"
                hint += f"Total estimated time: {total_time_str}"

                # Create a description with video information
                description = f"Generated frames: {total_frames}, Video length: {video_length:.2f} seconds (FPS-30)"

                # Update the UI with the progress information
                html = make_progress_bar_html(overall_percentage, hint)

                # Force an update to the UI with both the progress bar and a description
                stream.output_queue.push(('progress', (None, description, html)))

                # If this is the last step in the section, record the end time and increment section counter
                if current == total:
                    section_end_times[current_section] = time.time()
                    current_section += 1
            # Skip updates with invalid values silently
        except Exception as e:
            print(f"Error in progress callback: {e}")

    # Set the progress callback
    set_progress_callback(progress_callback)

    # The total steps are: 3 processing steps + total_latent_sections generation steps
    total_process_steps = 3 + total_latent_sections

    # Show initial progress with total steps information
    initial_message = f"Starting generation of {total_second_length:.1f} second video ({steps} sampling steps across {total_latent_sections} sections)"
    stream.output_queue.push(('progress', (None, initial_message, make_progress_bar_html(0, initial_message))))

    try:
        # Clean GPU
        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )

        # Text encoding
        processing_message = f"Step 1/{total_process_steps}: Text encoding"
        stream.output_queue.push(('progress', (None, processing_message, make_progress_bar_html(0, processing_message))))

        if not high_vram:
            fake_diffusers_current_device(text_encoder, gpu)  # since we only encode one text - that is one model move and one encode, offload is same time consumption since it is also one load and one encode.
            load_model_as_complete(text_encoder_2, target_device=gpu)

        llama_vec, clip_l_pooler = encode_prompt_conds(prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)

        if cfg == 1:
            llama_vec_n, clip_l_pooler_n = torch.zeros_like(llama_vec), torch.zeros_like(clip_l_pooler)
        else:
            llama_vec_n, clip_l_pooler_n = encode_prompt_conds(n_prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)

        llama_vec, llama_attention_mask = crop_or_pad_yield_mask(llama_vec, length=512)
        llama_vec_n, llama_attention_mask_n = crop_or_pad_yield_mask(llama_vec_n, length=512)

        # Processing input image (start frame)
        processing_message = f"Step 2/{total_process_steps}: Processing start frame"
        stream.output_queue.push(('progress', (None, processing_message, make_progress_bar_html(0, processing_message))))

        H, W, C = input_image.shape
        height, width = find_nearest_bucket(H, W, resolution=640)
        input_image_np = resize_and_center_crop(input_image, target_width=width, target_height=height)

        # We'll save the start frame with metadata after determining the seed
        # Store the input image for now
        start_frame_path = os.path.join(outputs_folder, f'{job_id}_start.png')
        temp_start_image = input_image_np.copy()

        input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1
        input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]

        # Processing end image (if provided)
        has_end_image = end_image is not None
        if has_end_image:
            processing_message = f"Step 2b/{total_process_steps}: Processing end frame"
            stream.output_queue.push(('progress', (None, processing_message, make_progress_bar_html(0, processing_message))))

            H_end, W_end, C_end = end_image.shape
            end_image_np = resize_and_center_crop(end_image, target_width=width, target_height=height)

            # We'll save the end frame with metadata after determining the seed
            # Store the end image for now
            end_frame_path = os.path.join(outputs_folder, f'{job_id}_end.png')
            temp_end_image = end_image_np.copy()

            end_image_pt = torch.from_numpy(end_image_np).float() / 127.5 - 1
            end_image_pt = end_image_pt.permute(2, 0, 1)[None, :, None]

        # VAE encoding
        stream.output_queue.push(('progress', (None, '', make_progress_bar_html(0, 'VAE encoding ...'))))

        if not high_vram:
            load_model_as_complete(vae, target_device=gpu)

        # Normal image encoding
        start_latent = vae_encode(input_image_pt, vae)

        if has_end_image:
            end_latent = vae_encode(end_image_pt, vae)

        # CLIP Vision
        stream.output_queue.push(('progress', (None, '', make_progress_bar_html(0, 'CLIP Vision encoding ...'))))

        if not high_vram:
            load_model_as_complete(image_encoder, target_device=gpu)

        image_encoder_output = hf_clip_vision_encode(input_image_np, feature_extractor, image_encoder)
        image_encoder_last_hidden_state = image_encoder_output.last_hidden_state

        if has_end_image:
            end_image_encoder_output = hf_clip_vision_encode(end_image_np, feature_extractor, image_encoder)
            end_image_encoder_last_hidden_state = end_image_encoder_output.last_hidden_state
            # Combine both image embeddings or use a weighted approach
            image_encoder_last_hidden_state = (image_encoder_last_hidden_state + end_image_encoder_last_hidden_state) / 2

        # Dtype
        llama_vec = llama_vec.to(transformer.dtype)
        llama_vec_n = llama_vec_n.to(transformer.dtype)
        clip_l_pooler = clip_l_pooler.to(transformer.dtype)
        clip_l_pooler_n = clip_l_pooler_n.to(transformer.dtype)
        image_encoder_last_hidden_state = image_encoder_last_hidden_state.to(transformer.dtype)

        # Sampling
        processing_message = f"Step 3/{total_process_steps}: Preparing for sampling"
        stream.output_queue.push(('progress', (None, processing_message, make_progress_bar_html(0, processing_message))))

        # If seed is -1, use a random seed
        random_seed = None
        if seed == -1:
            random_seed = random.randint(0, 2**32 - 1)
            print(f"Random seed: {random_seed}")
            rnd = torch.Generator("cpu").manual_seed(random_seed)
            # Update progress bar to show the random seed that was used
            stream.output_queue.push(('progress', (None, f'Using random seed: {random_seed}', make_progress_bar_html(0, f'Start sampling with random seed {random_seed} ...'))))

            # Store the start frame metadata for later use when the video is complete
            start_frame_metadata = {
                "prompt": prompt,
                "negative_prompt": n_prompt,
                "seed": random_seed,
                "steps": steps,
                "cfg_scale": cfg,
                "distilled_cfg_scale": gs,
                "cfg_rescale": rs,
                "use_teacache": use_teacache,
                "total_video_length": f"{total_second_length} seconds",
                "total_sections": total_latent_sections,
                "frame_type": "start_frame",
                "job_id": job_id
            }

            # We'll save the start frame only when the video is complete
            # For now, just store the path and metadata
            start_frame_path = os.path.join(outputs_folder, f'{job_id}_start.png')

            # If we have an end frame, store its metadata too
            if has_end_image:
                end_frame_metadata = {
                    "prompt": prompt,
                    "negative_prompt": n_prompt,
                    "seed": random_seed,
                    "steps": steps,
                    "cfg_scale": cfg,
                    "distilled_cfg_scale": gs,
                    "cfg_rescale": rs,
                    "use_teacache": use_teacache,
                    "total_video_length": f"{total_second_length} seconds",
                    "total_sections": total_latent_sections,
                    "frame_type": "end_frame",
                    "job_id": job_id
                }

                # We'll save the end frame only when the video is complete
                # For now, just store the path
                end_frame_path = os.path.join(outputs_folder, f'{job_id}_end.png')
        else:
            rnd = torch.Generator("cpu").manual_seed(seed)

            # Store the start frame metadata for later use when the video is complete
            start_frame_metadata = {
                "prompt": prompt,
                "negative_prompt": n_prompt,
                "seed": seed,
                "steps": steps,
                "cfg_scale": cfg,
                "distilled_cfg_scale": gs,
                "cfg_rescale": rs,
                "use_teacache": use_teacache,
                "total_video_length": f"{total_second_length} seconds",
                "total_sections": total_latent_sections,
                "frame_type": "start_frame",
                "job_id": job_id
            }

            # We'll save the start frame only when the video is complete
            # For now, just store the path and metadata
            start_frame_path = os.path.join(outputs_folder, f'{job_id}_start.png')

            # If we have an end frame, store its metadata too
            if has_end_image:
                end_frame_metadata = {
                    "prompt": prompt,
                    "negative_prompt": n_prompt,
                    "seed": seed,
                    "steps": steps,
                    "cfg_scale": cfg,
                    "distilled_cfg_scale": gs,
                    "cfg_rescale": rs,
                    "use_teacache": use_teacache,
                    "total_video_length": f"{total_second_length} seconds",
                    "total_sections": total_latent_sections,
                    "frame_type": "end_frame",
                    "job_id": job_id
                }

                # We'll save the end frame only when the video is complete
                # For now, just store the path
                end_frame_path = os.path.join(outputs_folder, f'{job_id}_end.png')

        num_frames = latent_window_size * 4 - 3

        history_latents = torch.zeros(size=(1, 16, 1 + 2 + 16, height // 8, width // 8), dtype=torch.float32).cpu()
        history_pixels = None
        total_generated_latent_frames = 0

        # 将迭代器转换为列表
        latent_paddings = list(reversed(range(total_latent_sections)))

        if total_latent_sections > 4:
            # In theory the latent_paddings should follow the above sequence, but it seems that duplicating some
            # items looks better than expanding it when total_latent_sections > 4
            # One can try to remove below trick and just
            # use `latent_paddings = list(reversed(range(total_latent_sections)))` to compare
            latent_paddings = [3] + [2] * (total_latent_sections - 3) + [1, 0]

            # Print the actual latent_paddings for debugging
            print(f"Using {total_latent_sections} sections with paddings: {latent_paddings}")

        for section_idx, latent_padding in enumerate(latent_paddings):
            is_last_section = latent_padding == 0
            is_first_section = latent_padding == latent_paddings[0]
            latent_padding_size = latent_padding * latent_window_size

            if stream.input_queue.top() == 'end':
                stream.output_queue.push(('end', None))
                raise GracefulExit('Generation stopped by user')

            # Update the section number for progress tracking
            current_section = section_idx
            displayed_section = section_idx  # Update displayed section at the start of each section
            in_transition = False  # Reset transition flag

            # Show section processing message
            # Calculate the current step (3 processing steps + current section)
            current_step = 3 + section_idx + 1

            # Make sure we don't exceed the total steps
            current_step = min(current_step, total_process_steps)

            processing_message = f"Step {current_step}/{total_process_steps}: Processing section {section_idx + 1}/{total_latent_sections}"
            stream.output_queue.push(('progress', (None, processing_message, make_progress_bar_html(int((section_idx / total_latent_sections) * 100), processing_message))))

            # Only print this in debug mode or comment out to reduce console output
            # print(f'latent_padding_size = {latent_padding_size}, is_last_section = {is_last_section}, is_first_section = {is_first_section}')

            indices = torch.arange(0, sum([1, latent_padding_size, latent_window_size, 1, 2, 16])).unsqueeze(0)
            clean_latent_indices_pre, blank_indices, latent_indices, clean_latent_indices_post, clean_latent_2x_indices, clean_latent_4x_indices = indices.split([1, latent_padding_size, latent_window_size, 1, 2, 16], dim=1)
            clean_latent_indices = torch.cat([clean_latent_indices_pre, clean_latent_indices_post], dim=1)

            # Get the start latent and ensure it has the right shape
            clean_latents_pre = start_latent.to(history_latents)

            # Print shapes for debugging
            print(f"start_latent shape: {start_latent.shape}")
            print(f"history_latents shape: {history_latents.shape}")

            # Extract the post latents from history
            clean_latents_post, clean_latents_2x, clean_latents_4x = history_latents[:, :, :1 + 2 + 16, :, :].split([1, 2, 16], dim=2)

            # Print shapes for debugging
            print(f"clean_latents_pre shape: {clean_latents_pre.shape}, clean_latents_post shape: {clean_latents_post.shape}")

            # We need to ensure that clean_latents_pre and clean_latents_post have compatible shapes
            # They should have the same batch size, channels, height, and width
            # Only the time dimension (dim=2) can differ for concatenation

            # First, ensure batch dimension matches
            if clean_latents_pre.shape[0] != clean_latents_post.shape[0]:
                print(f"Adjusting batch dimension")
                if clean_latents_pre.shape[0] == 1:
                    clean_latents_pre = clean_latents_pre.repeat(clean_latents_post.shape[0], 1, 1, 1, 1)
                else:
                    clean_latents_post = clean_latents_post.repeat(clean_latents_pre.shape[0] // clean_latents_post.shape[0], 1, 1, 1, 1)

            # Ensure channel dimension matches
            if clean_latents_pre.shape[1] != clean_latents_post.shape[1]:
                print(f"Adjusting channel dimension")
                # This is trickier - we'll need to reshape or pad
                # For simplicity, we'll use the history_latents shape as the target
                target_channels = history_latents.shape[1]
                if clean_latents_pre.shape[1] != target_channels:
                    # Reshape to match target channels
                    h, w = clean_latents_pre.shape[-2], clean_latents_pre.shape[-1]
                    clean_latents_pre = clean_latents_pre.reshape(clean_latents_pre.shape[0], target_channels, -1, h, w)
                if clean_latents_post.shape[1] != target_channels:
                    # Reshape to match target channels
                    h, w = clean_latents_post.shape[-2], clean_latents_post.shape[-1]
                    clean_latents_post = clean_latents_post.reshape(clean_latents_post.shape[0], target_channels, -1, h, w)

            # Ensure height and width match
            if clean_latents_pre.shape[3] != clean_latents_post.shape[3] or clean_latents_pre.shape[4] != clean_latents_post.shape[4]:
                print(f"Adjusting spatial dimensions")
                # Resize to match the history_latents spatial dimensions
                target_h, target_w = history_latents.shape[3], history_latents.shape[4]

                # Resize clean_latents_pre if needed
                if clean_latents_pre.shape[3] != target_h or clean_latents_pre.shape[4] != target_w:
                    # Reshape to match target spatial dimensions
                    clean_latents_pre = torch.nn.functional.interpolate(
                        clean_latents_pre.reshape(-1, 1, clean_latents_pre.shape[3], clean_latents_pre.shape[4]),
                        size=(target_h, target_w),
                        mode='bilinear'
                    ).reshape(clean_latents_pre.shape[0], clean_latents_pre.shape[1], clean_latents_pre.shape[2], target_h, target_w)

                # Resize clean_latents_post if needed
                if clean_latents_post.shape[3] != target_h or clean_latents_post.shape[4] != target_w:
                    # Reshape to match target spatial dimensions
                    clean_latents_post = torch.nn.functional.interpolate(
                        clean_latents_post.reshape(-1, 1, clean_latents_post.shape[3], clean_latents_post.shape[4]),
                        size=(target_h, target_w),
                        mode='bilinear'
                    ).reshape(clean_latents_post.shape[0], clean_latents_post.shape[1], clean_latents_post.shape[2], target_h, target_w)

            # If clean_latents_pre has only 1 frame in the time dimension but we need to match clean_latents_post
            # which has a different number of frames, we need to expand it
            if clean_latents_pre.shape[2] == 1 and clean_latents_post.shape[2] != 1:
                # Repeat the single frame to match the expected size
                clean_latents_pre = clean_latents_pre.repeat(1, 1, clean_latents_post.shape[2], 1, 1)
                print(f"Expanded clean_latents_pre to shape: {clean_latents_pre.shape}")

            # Print final shapes before concatenation
            print(f"Final shapes - clean_latents_pre: {clean_latents_pre.shape}, clean_latents_post: {clean_latents_post.shape}")

            # Now concatenate them
            clean_latents = torch.cat([clean_latents_pre, clean_latents_post], dim=2)

            # Use end image latent for the first section if provided
            if has_end_image and is_first_section:
                clean_latents_post = end_latent.to(history_latents)

                # Print shapes for debugging
                print(f"end_latent shape: {end_latent.shape}, clean_latents_pre shape: {clean_latents_pre.shape}")

                # We need to ensure that clean_latents_pre and clean_latents_post have compatible shapes
                # They should have the same batch size, channels, height, and width
                # Only the time dimension (dim=2) can differ for concatenation

                # First, ensure batch dimension matches
                if clean_latents_pre.shape[0] != clean_latents_post.shape[0]:
                    print(f"Adjusting batch dimension for end latent")
                    if clean_latents_post.shape[0] == 1:
                        clean_latents_post = clean_latents_post.repeat(clean_latents_pre.shape[0], 1, 1, 1, 1)
                    else:
                        clean_latents_pre = clean_latents_pre.repeat(clean_latents_post.shape[0] // clean_latents_pre.shape[0], 1, 1, 1, 1)

                # Ensure channel dimension matches
                if clean_latents_pre.shape[1] != clean_latents_post.shape[1]:
                    print(f"Adjusting channel dimension for end latent")
                    # This is trickier - we'll need to reshape or pad
                    # For simplicity, we'll use the history_latents shape as the target
                    target_channels = history_latents.shape[1]
                    if clean_latents_post.shape[1] != target_channels:
                        # Reshape to match target channels
                        h, w = clean_latents_post.shape[-2], clean_latents_post.shape[-1]
                        clean_latents_post = clean_latents_post.reshape(clean_latents_post.shape[0], target_channels, -1, h, w)

                # Ensure height and width match
                if clean_latents_pre.shape[3] != clean_latents_post.shape[3] or clean_latents_pre.shape[4] != clean_latents_post.shape[4]:
                    print(f"Adjusting spatial dimensions for end latent")
                    # Resize to match the clean_latents_pre spatial dimensions
                    target_h, target_w = clean_latents_pre.shape[3], clean_latents_pre.shape[4]

                    # Resize clean_latents_post if needed
                    if clean_latents_post.shape[3] != target_h or clean_latents_post.shape[4] != target_w:
                        # Reshape to match target spatial dimensions
                        clean_latents_post = torch.nn.functional.interpolate(
                            clean_latents_post.reshape(-1, 1, clean_latents_post.shape[3], clean_latents_post.shape[4]),
                            size=(target_h, target_w),
                            mode='bilinear'
                        ).reshape(clean_latents_post.shape[0], clean_latents_post.shape[1], clean_latents_post.shape[2], target_h, target_w)

                # If clean_latents_post has only 1 frame in the time dimension but we need to match clean_latents_pre
                if clean_latents_post.shape[2] == 1 and clean_latents_pre.shape[2] != 1:
                    # Repeat the single frame to match the expected size
                    clean_latents_post = clean_latents_post.repeat(1, 1, clean_latents_pre.shape[2], 1, 1)
                    print(f"Expanded clean_latents_post to shape: {clean_latents_post.shape}")

                # Print final shapes before concatenation
                print(f"Final shapes for end latent - clean_latents_pre: {clean_latents_pre.shape}, clean_latents_post: {clean_latents_post.shape}")

                # Now concatenate them
                clean_latents = torch.cat([clean_latents_pre, clean_latents_post], dim=2)

            if not high_vram:
                unload_complete_models()
                move_model_to_device_with_memory_preservation(transformer, target_device=gpu, preserved_memory_gb=gpu_memory_preservation)

            if use_teacache:
                transformer.initialize_teacache(enable_teacache=True, num_steps=steps)
            else:
                transformer.initialize_teacache(enable_teacache=False)

            def callback(d):
                preview = d['denoised']
                preview = vae_decode_fake(preview)

                preview = (preview * 255.0).detach().cpu().numpy().clip(0, 255).astype(np.uint8)

                # Store the original shape for individual frame extraction
                original_shape = preview.shape  # Should be [b, c, t, h, w]

                # Rearrange for display
                preview = einops.rearrange(preview, 'b c t h w -> (b h) (t w) c')

                if stream.input_queue.top() == 'end':
                    stream.output_queue.push(('end', None))
                    raise GracefulExit('Generation stopped by user')

                # Only update the UI with the preview image
                # The progress bar is updated by the progress_callback
                stream.output_queue.push(('progress', (preview, None, None)))
                return

            generated_latents = sample_hunyuan(
                transformer=transformer,
                sampler='unipc',
                width=width,
                height=height,
                frames=num_frames,
                real_guidance_scale=cfg,
                distilled_guidance_scale=gs,
                guidance_rescale=rs,
                # shift=3.0,
                num_inference_steps=steps,
                generator=rnd,
                prompt_embeds=llama_vec,
                prompt_embeds_mask=llama_attention_mask,
                prompt_poolers=clip_l_pooler,
                negative_prompt_embeds=llama_vec_n,
                negative_prompt_embeds_mask=llama_attention_mask_n,
                negative_prompt_poolers=clip_l_pooler_n,
                device=gpu,
                dtype=torch.bfloat16,
                image_embeddings=image_encoder_last_hidden_state,
                latent_indices=latent_indices,
                clean_latents=clean_latents,
                clean_latent_indices=clean_latent_indices,
                clean_latents_2x=clean_latents_2x,
                clean_latent_2x_indices=clean_latent_2x_indices,
                clean_latents_4x=clean_latents_4x,
                clean_latent_4x_indices=clean_latent_4x_indices,
                callback=callback,
            )

            if is_last_section:
                # Get the start latent and ensure it has the right shape
                start_latent_for_cat = start_latent.to(generated_latents)

                # Print shapes for debugging
                print(f"Last section - start_latent shape: {start_latent_for_cat.shape}, generated_latents shape: {generated_latents.shape}")

                # We need to ensure that start_latent_for_cat and generated_latents have compatible shapes
                # They should have the same batch size, channels, height, and width
                # Only the time dimension (dim=2) can differ for concatenation

                # First, ensure batch dimension matches
                if start_latent_for_cat.shape[0] != generated_latents.shape[0]:
                    print(f"Adjusting batch dimension for last section")
                    if start_latent_for_cat.shape[0] == 1:
                        start_latent_for_cat = start_latent_for_cat.repeat(generated_latents.shape[0], 1, 1, 1, 1)
                    else:
                        # This shouldn't happen, but just in case
                        start_latent_for_cat = start_latent_for_cat[:1]

                # Ensure channel dimension matches
                if start_latent_for_cat.shape[1] != generated_latents.shape[1]:
                    print(f"Adjusting channel dimension for last section")
                    # Use the generated_latents channels as the target
                    target_channels = generated_latents.shape[1]
                    if start_latent_for_cat.shape[1] != target_channels:
                        # Reshape to match target channels
                        h, w = start_latent_for_cat.shape[-2], start_latent_for_cat.shape[-1]
                        start_latent_for_cat = start_latent_for_cat.reshape(start_latent_for_cat.shape[0], target_channels, -1, h, w)

                # Ensure height and width match
                if start_latent_for_cat.shape[3] != generated_latents.shape[3] or start_latent_for_cat.shape[4] != generated_latents.shape[4]:
                    print(f"Adjusting spatial dimensions for last section")
                    # Resize to match the generated_latents spatial dimensions
                    target_h, target_w = generated_latents.shape[3], generated_latents.shape[4]

                    # Resize start_latent_for_cat if needed
                    if start_latent_for_cat.shape[3] != target_h or start_latent_for_cat.shape[4] != target_w:
                        # Reshape to match target spatial dimensions
                        start_latent_for_cat = torch.nn.functional.interpolate(
                            start_latent_for_cat.reshape(-1, 1, start_latent_for_cat.shape[3], start_latent_for_cat.shape[4]),
                            size=(target_h, target_w),
                            mode='bilinear'
                        ).reshape(start_latent_for_cat.shape[0], start_latent_for_cat.shape[1], start_latent_for_cat.shape[2], target_h, target_w)

                # Print final shapes before concatenation
                print(f"Final shapes for last section - start_latent_for_cat: {start_latent_for_cat.shape}, generated_latents: {generated_latents.shape}")

                # Now concatenate them - we only need 1 frame from start_latent
                if start_latent_for_cat.shape[2] > 1:
                    start_latent_for_cat = start_latent_for_cat[:,:,:1]

                generated_latents = torch.cat([start_latent_for_cat, generated_latents], dim=2)
                print(f"Concatenated in last section, new shape: {generated_latents.shape}")

            total_generated_latent_frames += int(generated_latents.shape[2])
            history_latents = torch.cat([generated_latents.to(history_latents), history_latents], dim=2)

            if not high_vram:
                offload_model_from_device_for_memory_preservation(transformer, target_device=gpu, preserved_memory_gb=8)
                load_model_as_complete(vae, target_device=gpu)

            real_history_latents = history_latents[:, :, :total_generated_latent_frames, :, :]

            if history_pixels is None:
                history_pixels = vae_decode(real_history_latents, vae).cpu()
            else:
                section_latent_frames = (latent_window_size * 2 + 1) if is_last_section else (latent_window_size * 2)
                overlapped_frames = latent_window_size * 4 - 3

                current_pixels = vae_decode(real_history_latents[:, :, :section_latent_frames], vae).cpu()
                history_pixels = soft_append_bcthw(current_pixels, history_pixels, overlapped_frames)

            if not high_vram:
                unload_complete_models()

            # Include the random seed in the filename if one was used
            if seed == -1 and random_seed is not None:
                output_filename = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4')
            else:
                output_filename = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}.mp4')

            # If this is the first section, save the start frame with metadata before saving the video
            if section_idx == 0:
                print(f"Saving start frame with metadata before first video section")
                # Save the start frame with metadata
                save_image_with_metadata(temp_start_image, start_frame_path, start_frame_metadata)
                print(f"Saved start frame to {start_frame_path}")

            save_bcthw_as_mp4(history_pixels, output_filename, fps=30, crf=mp4_crf)

            # Only print this in debug mode or comment out to reduce console output
            # print(f'Decoded. Current latent shape {real_history_latents.shape}; pixel shape {history_pixels.shape}')

            # Push the file update to the UI with a timestamp to force refresh
            timestamp = int(time.time())
            print(f"Generated video section {current_section+1}/{total_latent_sections}: {output_filename}")

            # Add a small delay to ensure the file is fully written before updating the UI
            time.sleep(0.1)

            # Push the update to the UI
            stream.output_queue.push(('file', f"{output_filename}?t={timestamp}"))

            # Also update the progress bar with a message about the new video
            if section_idx == 0:
                section_message = f"Section {current_section+1}/{total_latent_sections} complete<br>Start frame saved and video updated"
            else:
                section_message = f"Section {current_section+1}/{total_latent_sections} complete<br>Video updated"
            stream.output_queue.push(('progress', (None, section_message, make_progress_bar_html(int(((current_section+1) / total_latent_sections) * 100), section_message))))

            if is_last_section:
                print("***** LAST SECTION DETECTED *****")
                # Wait a moment to ensure the video file is fully written
                print("Waiting for video file to be fully written...")
                # Use time module that's already imported at the top of the file
                time.sleep(3)  # Wait 3 seconds

                # Extract and save the last frame as a PNG with "_end" suffix
                try:
                    # Add debug print to see if this code is being executed
                    print(f"Attempting to save end frame for job {job_id}")

                    # List all video files in the output folder for debugging
                    print("Listing all video files in the output folder:")
                    for file in os.listdir(outputs_folder):
                        if file.endswith('.mp4') and file.startswith(job_id):
                            print(f"  - {file}")

                    # First check if the video file exists
                    # Use the most recent video file that was created
                    if seed == -1 and random_seed is not None:
                        video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4')
                    else:
                        video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}.mp4')
                    print(f"Looking for video file at: {video_path}")

                    if not os.path.exists(video_path):
                        print(f"Video file not found at {video_path}")
                        # Try to find any video file with this job ID
                        print("Trying to find any video file with this job ID...")
                        found_video = False
                        for file in os.listdir(outputs_folder):
                            if file.endswith('.mp4') and file.startswith(job_id):
                                video_path = os.path.join(outputs_folder, file)
                                print(f"Found alternative video file: {video_path}")
                                found_video = True
                                break

                        if not found_video:
                            print("No alternative video file found. Waiting for video to be saved...")
                            # If the video file doesn't exist yet, wait a moment for it to be saved
                            # Use time module that's already imported at the top of the file
                            time.sleep(2)  # Wait 2 seconds

                    if os.path.exists(video_path):
                        print(f"Video file found at {video_path}")
                        # Extract the last frame directly from the saved video file
                        # Use cv2 module that will be imported at the top of the file

                        # Open the video file
                        video = cv2.VideoCapture(video_path)

                        # Get the total number of frames
                        total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
                        print(f"Video has {total_frames} frames")

                        if total_frames > 0:
                            # Set the position to the last frame
                            video.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)

                            # Read the last frame
                            success, last_frame = video.read()

                            if success:
                                print(f"Successfully read last frame from video")
                                # Convert from BGR to RGB (OpenCV uses BGR by default)
                                last_frame_rgb = cv2.cvtColor(last_frame, cv2.COLOR_BGR2RGB)

                                # Create the end frame filename
                                end_frame_path = os.path.join(outputs_folder, f'{job_id}_end.png')

                                # Create metadata for the end frame
                                metadata_dict = {
                                    'prompt': prompt,
                                    'negative_prompt': n_prompt,
                                    'seed': random_seed if seed == -1 else seed,
                                    'steps': steps,
                                    'cfg_scale': cfg,
                                    'distilled_cfg_scale': gs,
                                    'cfg_rescale': rs,
                                    'use_teacache': use_teacache,
                                    'total_video_length': f"{total_second_length} seconds",
                                    'total_sections': total_latent_sections,
                                    'frame_type': 'end_frame',
                                    'job_id': job_id,
                                    'extracted_from_video': True
                                }

                                # Save the end frame with metadata
                                save_image_with_metadata(last_frame_rgb, end_frame_path, metadata_dict)

                                print(f"Saved end frame to {end_frame_path} (extracted from video)")

                                # Start frame is already saved at the beginning of the first section

                                # Run the auto sorter to copy the largest file to the sorted folder and deduplicate
                                print("\nRunning automatic sorting and deduplication...")
                                try:
                                    # Get the most recent video file that was created
                                    if seed == -1 and random_seed is not None:
                                        recent_file = f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4'
                                    else:
                                        recent_file = f'{job_id}_{total_generated_latent_frames}.mp4'

                                    print(f"Processing recent file: {recent_file}")
                                    num_copied, num_deleted = auto_sort_after_generation(outputs_folder, recent_file)
                                    sort_message = f"<br>Auto-sorted {num_copied} files, removed {num_deleted} duplicates"
                                    print(f"Auto-sorting complete: {num_copied} files copied, {num_deleted} duplicates removed")
                                except Exception as sort_error:
                                    sort_message = "<br>Auto-sorting failed"
                                    print(f"Error during auto-sorting: {sort_error}")

                                # Show completion message with end frame info and sorting results
                                completion_message = f"Video generation complete<br>{total_generated_latent_frames * 4 - 3} frames, {(total_generated_latent_frames * 4 - 3) / 30:.2f} seconds<br>End frame saved{sort_message}"

                                # Close the video file
                                video.release()
                            else:
                                print("Failed to read the last frame from the video")
                                video.release()
                                raise Exception("Failed to read the last frame from the video")
                        else:
                            print("Video file appears to be empty")
                            video.release()
                            raise Exception("Video file appears to be empty")
                    else:
                        print(f"Video file still not found at {video_path} after waiting")
                        print("No video file found. Falling back to latent method immediately.")
                        # Don't raise an exception, just continue to the latent fallback

                    # If we get here without extracting from the video, fall back to latent method
                    if 'completion_message' not in locals():
                        print("Falling back to latent method for end frame")
                        # Get the last frame from the latents
                        last_latent_frame = history_latents[0, :, -1:, :, :]  # Shape should be [B, C, 1, H, W]

                        # Decode it using the VAE to get the pixel values
                        with torch.no_grad():
                            # Move to the right device
                            last_latent_frame = last_latent_frame.to(vae.device)

                            # Decode the latent to get the pixel values
                            decoded_frame = vae_decode(last_latent_frame, vae)  # Should return [B, C, 1, H, W]

                            # Extract the single frame and convert to numpy
                            # The decoded frame is in range [-1, 1], so we need to convert to [0, 255]
                            decoded_frame = decoded_frame[0, :, 0]  # Shape: [C, H, W]
                            decoded_frame_np = decoded_frame.permute(1, 2, 0).cpu().numpy()  # Convert to [H, W, C]
                            decoded_frame_np = ((decoded_frame_np + 1) / 2 * 255).clip(0, 255).astype(np.uint8)

                        # Create the end frame filename
                        end_frame_path = os.path.join(outputs_folder, f'{job_id}_end.png')

                        # Create metadata for the end frame
                        metadata_dict = {
                            'prompt': prompt,
                            'negative_prompt': n_prompt,
                            'seed': random_seed if seed == -1 else seed,
                            'steps': steps,
                            'cfg_scale': cfg,
                            'distilled_cfg_scale': gs,
                            'cfg_rescale': rs,
                            'use_teacache': use_teacache,
                            'total_video_length': f"{total_second_length} seconds",
                            'total_sections': total_latent_sections,
                            'frame_type': 'end_frame',
                            'job_id': job_id,
                            'extracted_from_latents': True
                        }

                        # Save the end frame with metadata
                        save_image_with_metadata(decoded_frame_np, end_frame_path, metadata_dict)

                        print(f"Saved end frame to {end_frame_path} (from latents)")

                        # Start frame is already saved at the beginning of the first section

                        # Run the auto sorter to copy the largest file to the sorted folder and deduplicate
                        print("\nRunning automatic sorting and deduplication...")
                        try:
                            # Get the most recent video file that was created
                            if seed == -1 and random_seed is not None:
                                recent_file = f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4'
                            else:
                                recent_file = f'{job_id}_{total_generated_latent_frames}.mp4'

                            print(f"Processing recent file: {recent_file}")
                            num_copied, num_deleted = auto_sort_after_generation(outputs_folder, recent_file)
                            sort_message = f"<br>Auto-sorted {num_copied} files, removed {num_deleted} duplicates"
                            print(f"Auto-sorting complete: {num_copied} files copied, {num_deleted} duplicates removed")
                        except Exception as sort_error:
                            sort_message = "<br>Auto-sorting failed"
                            print(f"Error during auto-sorting: {sort_error}")

                        # Show completion message with end frame info and sorting results
                        completion_message = f"Video generation complete<br>{total_generated_latent_frames * 4 - 3} frames, {(total_generated_latent_frames * 4 - 3) / 30:.2f} seconds<br>End frame saved{sort_message}"
                except Exception as e:
                    print(f"Error saving end frame: {e}")

                    # Still run the auto sorter even if there was an error saving the end frame
                    print("\nRunning automatic sorting and deduplication...")
                    try:
                        # Get the most recent video file that was created
                        if seed == -1 and random_seed is not None:
                            recent_file = f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4'
                        else:
                            recent_file = f'{job_id}_{total_generated_latent_frames}.mp4'

                        print(f"Processing recent file: {recent_file}")
                        num_copied, num_deleted = auto_sort_after_generation(outputs_folder, recent_file)
                        sort_message = f"<br>Auto-sorted {num_copied} files, removed {num_deleted} duplicates"
                        print(f"Auto-sorting complete: {num_copied} files copied, {num_deleted} duplicates removed")
                    except Exception as sort_error:
                        sort_message = "<br>Auto-sorting failed"
                        print(f"Error during auto-sorting: {sort_error}")

                    # Show regular completion message if there was an error, but include sorting info
                    completion_message = f"Video generation complete<br>{total_generated_latent_frames * 4 - 3} frames, {(total_generated_latent_frames * 4 - 3) / 30:.2f} seconds{sort_message}"

                stream.output_queue.push(('progress', (None, completion_message, make_progress_bar_html(100, completion_message))))
                break
    except GracefulExit:
        print("Generation stopped by user")

        # Try to save the last rendered frame if we have any frames
        # First check if the video file exists
        # Use the most recent video file that was created
        if seed == -1 and random_seed is not None:
            video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4')
        else:
            video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}.mp4')
        print(f"Looking for video file at: {video_path}")
        if os.path.exists(video_path):
            try:
                # Extract the last frame directly from the saved video file
                # Use cv2 module that will be imported at the top of the file

                # Open the video file
                video = cv2.VideoCapture(video_path)

                # Get the total number of frames
                total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))

                if total_frames > 0:
                    # Set the position to the last frame
                    video.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)

                    # Read the last frame
                    success, last_frame = video.read()

                    if success:
                        # Convert from BGR to RGB (OpenCV uses BGR by default)
                        last_frame_rgb = cv2.cvtColor(last_frame, cv2.COLOR_BGR2RGB)

                        # Create the cancelled frame filename
                        cancelled_frame_path = os.path.join(outputs_folder, f'{job_id}_cancelled.png')

                        # Create metadata for the cancelled frame
                        metadata_dict = {
                            'prompt': prompt,
                            'negative_prompt': n_prompt,
                            'seed': random_seed if seed == -1 else seed,
                            'steps': steps,
                            'cfg_scale': cfg,
                            'distilled_cfg_scale': gs,
                            'cfg_rescale': rs,
                            'use_teacache': use_teacache,
                            'total_video_length': f"{total_second_length} seconds",
                            'total_sections': total_latent_sections,
                            'frame_type': 'cancelled_frame',
                            'job_id': job_id,
                            'completed_sections': current_section,
                            'total_frames_generated': total_generated_latent_frames * 4 - 3 if 'total_generated_latent_frames' in locals() else 0,
                            'extracted_from_video': True
                        }

                        # Save the cancelled frame with metadata
                        save_image_with_metadata(last_frame_rgb, cancelled_frame_path, metadata_dict)

                        print(f"Saved cancelled frame to {cancelled_frame_path} (extracted from video)")

                        # Show message with cancelled frame info
                        cancel_message = f"Generation stopped by user<br>Last frame saved as {os.path.basename(cancelled_frame_path)}"
                        stream.output_queue.push(('progress', (None, cancel_message, make_progress_bar_html(100, cancel_message))))

                        # Close the video file
                        video.release()
                        return

                # Close the video file
                video.release()
            except Exception as e:
                print(f"Error extracting frame from video: {e}")
                # Fall back to the latent method if video extraction fails

        # If we couldn't extract from the video (either it doesn't exist or there was an error),
        # fall back to the latent method
        if 'history_latents' in locals() and history_latents is not None and history_latents.shape[2] > 0:
            try:
                # Get the last frame directly from the decoded pixels
                # This ensures we get the exact same image that appears in the video

                # First, get the last latent frame
                last_latent_frame = history_latents[0, :, -1:, :, :]  # Shape should be [B, C, 1, H, W]

                # Decode it using the VAE to get the pixel values
                with torch.no_grad():
                    # Move to the right device
                    last_latent_frame = last_latent_frame.to(vae.device)

                    # Decode the latent to get the pixel values
                    decoded_frame = vae_decode(last_latent_frame, vae)  # Should return [B, C, 1, H, W]

                    # Extract the single frame and convert to numpy
                    # The decoded frame is in range [-1, 1], so we need to convert to [0, 255]
                    decoded_frame = decoded_frame[0, :, 0]  # Shape: [C, H, W]
                    decoded_frame_np = decoded_frame.permute(1, 2, 0).cpu().numpy()  # Convert to [H, W, C]
                    decoded_frame_np = ((decoded_frame_np + 1) / 2 * 255).clip(0, 255).astype(np.uint8)

                # Create the cancelled frame filename
                cancelled_frame_path = os.path.join(outputs_folder, f'{job_id}_cancelled.png')

                # Create metadata for the cancelled frame
                metadata_dict = {
                    'prompt': prompt,
                    'negative_prompt': n_prompt,
                    'seed': random_seed if seed == -1 else seed,
                    'steps': steps,
                    'cfg_scale': cfg,
                    'distilled_cfg_scale': gs,
                    'cfg_rescale': rs,
                    'use_teacache': use_teacache,
                    'total_video_length': f"{total_second_length} seconds",
                    'total_sections': total_latent_sections,
                    'frame_type': 'cancelled_frame',
                    'job_id': job_id,
                    'completed_sections': current_section,
                    'total_frames_generated': total_generated_latent_frames * 4 - 3 if 'total_generated_latent_frames' in locals() else 0,
                    'extracted_from_latents': True
                }

                # Save the cancelled frame with metadata
                save_image_with_metadata(decoded_frame_np, cancelled_frame_path, metadata_dict)

                print(f"Saved cancelled frame to {cancelled_frame_path} (from latents)")

                # Show message with cancelled frame info
                cancel_message = f"Generation stopped by user<br>Last frame saved as {os.path.basename(cancelled_frame_path)}"
                stream.output_queue.push(('progress', (None, cancel_message, make_progress_bar_html(100, cancel_message))))
            except Exception as e:
                print(f"Error saving cancelled frame: {e}")
                # Show regular cancellation message if there was an error
                stream.output_queue.push(('progress', (None, 'Generation stopped by user', make_progress_bar_html(100, 'Generation stopped by user'))))
        else:
            # No frames were generated yet
            stream.output_queue.push(('progress', (None, 'Generation stopped by user (no frames were generated)', make_progress_bar_html(100, 'Generation stopped by user'))))

        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )
    except Exception as e:
        # For other exceptions, print the traceback
        traceback.print_exc()

        # Try to save the last rendered frame if we have any frames
        # First check if the video file exists
        # Use the most recent video file that was created
        if seed == -1 and random_seed is not None:
            video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4')
        else:
            video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}.mp4')
        print(f"Looking for video file at: {video_path}")
        if os.path.exists(video_path):
            try:
                # Extract the last frame directly from the saved video file
                # Use cv2 module that will be imported at the top of the file

                # Open the video file
                video = cv2.VideoCapture(video_path)

                # Get the total number of frames
                total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))

                if total_frames > 0:
                    # Set the position to the last frame
                    video.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)

                    # Read the last frame
                    success, last_frame = video.read()

                    if success:
                        # Convert from BGR to RGB (OpenCV uses BGR by default)
                        last_frame_rgb = cv2.cvtColor(last_frame, cv2.COLOR_BGR2RGB)

                        # Create the error frame filename
                        error_frame_path = os.path.join(outputs_folder, f'{job_id}_error.png')

                        # Create metadata for the error frame
                        metadata_dict = {
                            'prompt': prompt,
                            'negative_prompt': n_prompt,
                            'seed': random_seed if seed == -1 else seed,
                            'steps': steps,
                            'cfg_scale': cfg,
                            'distilled_cfg_scale': gs,
                            'cfg_rescale': rs,
                            'use_teacache': use_teacache,
                            'total_video_length': f"{total_second_length} seconds",
                            'total_sections': total_latent_sections,
                            'frame_type': 'error_frame',
                            'job_id': job_id,
                            'completed_sections': current_section,
                            'total_frames_generated': total_generated_latent_frames * 4 - 3 if 'total_generated_latent_frames' in locals() else 0,
                            'error': str(e),
                            'extracted_from_video': True
                        }

                        # Save the error frame with metadata
                        save_image_with_metadata(last_frame_rgb, error_frame_path, metadata_dict)

                        print(f"Saved error frame to {error_frame_path} (extracted from video)")

                        # Show message with error frame info
                        error_message = f"Error during generation: {str(e)}<br>Last frame saved as {os.path.basename(error_frame_path)}"
                        stream.output_queue.push(('progress', (None, error_message, make_progress_bar_html(100, error_message))))

                        # Close the video file
                        video.release()
                        return

                # Close the video file
                video.release()
            except Exception as save_error:
                print(f"Error extracting frame from video: {save_error}")
                # Fall back to the latent method if video extraction fails

        # If we couldn't extract from the video (either it doesn't exist or there was an error),
        # fall back to the latent method
        if 'history_latents' in locals() and history_latents is not None and history_latents.shape[2] > 0:
            try:
                # Get the last frame directly from the decoded pixels
                # This ensures we get the exact same image that appears in the video

                # First, get the last latent frame
                last_latent_frame = history_latents[0, :, -1:, :, :]  # Shape should be [B, C, 1, H, W]

                # Decode it using the VAE to get the pixel values
                with torch.no_grad():
                    # Move to the right device
                    last_latent_frame = last_latent_frame.to(vae.device)

                    # Decode the latent to get the pixel values
                    decoded_frame = vae_decode(last_latent_frame, vae)  # Should return [B, C, 1, H, W]

                    # Extract the single frame and convert to numpy
                    # The decoded frame is in range [-1, 1], so we need to convert to [0, 255]
                    decoded_frame = decoded_frame[0, :, 0]  # Shape: [C, H, W]
                    decoded_frame_np = decoded_frame.permute(1, 2, 0).cpu().numpy()  # Convert to [H, W, C]
                    decoded_frame_np = ((decoded_frame_np + 1) / 2 * 255).clip(0, 255).astype(np.uint8)

                # Create the error frame filename
                error_frame_path = os.path.join(outputs_folder, f'{job_id}_error.png')

                # Create metadata for the error frame
                metadata_dict = {
                    'prompt': prompt,
                    'negative_prompt': n_prompt,
                    'seed': random_seed if seed == -1 else seed,
                    'steps': steps,
                    'cfg_scale': cfg,
                    'distilled_cfg_scale': gs,
                    'cfg_rescale': rs,
                    'use_teacache': use_teacache,
                    'total_video_length': f"{total_second_length} seconds",
                    'total_sections': total_latent_sections,
                    'frame_type': 'error_frame',
                    'job_id': job_id,
                    'completed_sections': current_section,
                    'total_frames_generated': total_generated_latent_frames * 4 - 3 if 'total_generated_latent_frames' in locals() else 0,
                    'error': str(e),
                    'extracted_from_latents': True
                }

                # Save the error frame with metadata
                save_image_with_metadata(decoded_frame_np, error_frame_path, metadata_dict)

                print(f"Saved error frame to {error_frame_path} (from latents)")

                # Show message with error frame info
                error_message = f"Error during generation: {str(e)}<br>Last frame saved as {os.path.basename(error_frame_path)}"
                stream.output_queue.push(('progress', (None, error_message, make_progress_bar_html(100, error_message))))
            except Exception as save_error:
                print(f"Error saving error frame: {save_error}")
                # Show regular error message if there was an error saving the frame
                stream.output_queue.push(('progress', (None, f'Error during generation: {str(e)}', make_progress_bar_html(100, f'Error: {str(e)}'))))
        else:
            # No frames were generated yet
            stream.output_queue.push(('progress', (None, f'Error during generation: {str(e)} (no frames were generated)', make_progress_bar_html(100, f'Error: {str(e)}'))))

        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )

    stream.output_queue.push(('end', None))
    return


def process(input_image, end_image, prompt, n_prompt, seed, total_second_length, latent_window_size, steps, cfg, gs, rs, gpu_memory_preservation, use_teacache, mp4_crf):
    global stream
    # Import os module at the beginning of the function to ensure it's available
    import os

    # Check if we have either a start frame or end frame
    if input_image is None and end_image is None:
        raise ValueError('Please provide at least a start frame or end frame!')

    # If we have an end frame but no start frame, we'll create a transparent start frame in the worker function
    if input_image is None and end_image is not None:
        print("No start frame provided but end frame is provided. Will create a transparent start frame.")

    yield None, None, '', '', gr.update(interactive=False), gr.update(interactive=True), None

    stream = AsyncStream()

    # Create a list to store latent previews for animation
    latent_previews = []
    current_section = 0
    latent_animation_path = os.path.join(temp_folder, "latent_animation.webm")
    # Create a flag to track if we've processed a section already
    processed_sections = set()

    async_run(worker, input_image, end_image, prompt, n_prompt, seed, total_second_length, latent_window_size, steps, cfg, gs, rs, gpu_memory_preservation, use_teacache, mp4_crf)

    output_filename = None

    while True:
        flag, data = stream.output_queue.next()

        if flag == 'file':
            output_filename = data
            # Strip any query parameters (like ?t=timestamp) before yielding
            clean_filename = output_filename.split('?')[0]
            print(f"Updating video to: {clean_filename}")

            # Force a complete refresh of the video element
            yield gr.update(value=clean_filename), gr.update(), gr.update(), gr.update(), gr.update(interactive=False), gr.update(interactive=True), gr.update()

        if flag == 'progress':
            try:
                preview, desc, html = data

                # Process received progress update

                # Handle the case where preview is None
                preview_update = gr.update(visible=True, value=preview) if preview is not None else gr.update()

                # If we have a preview image, store it for animation
                if preview is not None:
                    # Create a unique key for this preview
                    section_key = f"preview_{len(latent_previews)}"

                    # Only process each preview once
                    if section_key not in processed_sections:
                        print(f"Processing new preview for animation: {section_key}")
                        processed_sections.add(section_key)

                        # Store the preview for animation
                        latent_previews.append(preview)

                        # Create a temporary folder for the frames
                        temp_dir = os.path.join(temp_folder, "latent_frames")
                        os.makedirs(temp_dir, exist_ok=True)

                        # Extract individual frames from the latest preview
                        # Each preview has 9 frames side by side
                        new_frames = []

                        # Get the height and width of the preview
                        h, w, _ = preview.shape

                        # Calculate the width of each individual frame
                        frame_width = w // 9

                        # Extract each individual frame from the latest preview
                        for j in range(9):
                            frame = preview[:, j*frame_width:(j+1)*frame_width, :]
                            new_frames.append(frame)

                            # Save the frame as an image with a sequential number
                            frame_count = (len(latent_previews) - 1) * 9 + j + 1
                            frame_path = os.path.join(temp_dir, f"frame_{frame_count:04d}.png")
                            cv2.imwrite(frame_path, cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

                        # Create or append to the video file
                        # Use OpenCV to create a video
                        if len(new_frames) > 0:
                            h, w, _ = new_frames[0].shape
                            # Use VP9 codec for WebM format which has better browser compatibility
                            fourcc = cv2.VideoWriter_fourcc(*'VP90')
                            # The latent preview shows 9 frames per section, while the final video has many more frames
                            # Each section typically produces 36 frames in the final video (9 latent frames × 4)
                            # Reduce the frame rate by 15% to make the animation play slower
                            # Original: 15 fps (50% of final 30 fps)
                            # New: 12.75 fps (15 fps reduced by 15%)
                            fps = 12.75  # 15.0 * 0.85 (15% reduction)

                            # Create a new animation file that only shows the latest batch of latent frames
                            # Use a timestamp to ensure uniqueness and avoid file access conflicts
                            import time
                            timestamp = int(time.time() * 1000)
                            new_animation_path = os.path.join(temp_folder, f"latest_latent_preview_{timestamp}.webm")
                            print(f"Creating latest latent preview: {new_animation_path}")

                            # Create a video writer for the new file
                            # Use VP9 codec for WebM format which has better browser compatibility
                            try:
                                # Completely suppress all output during WebM creation
                                with SuppressOutput():
                                    # Try to create a WebM video with VP9 codec
                                    fourcc = cv2.VideoWriter_fourcc(*'VP90')
                                    video_writer = cv2.VideoWriter(new_animation_path, fourcc, fps, (w, h))

                                    # Only include the frames from the current preview
                                    for frame in new_frames:
                                        video_writer.write(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

                                    # Release the video writer
                                    video_writer.release()

                                # Check if the file was created successfully
                                if not os.path.exists(new_animation_path) or os.path.getsize(new_animation_path) == 0:
                                    raise Exception("WebM file was not created properly")

                            except Exception as e:
                                # If WebM creation fails, try MP4 as a fallback
                                try:
                                    # Create an MP4 file instead
                                    mp4_path = new_animation_path.replace('.webm', '.mp4')
                                    with SuppressOutput():
                                        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                                        video_writer = cv2.VideoWriter(mp4_path, fourcc, fps, (w, h))

                                        # Only include the frames from the current preview
                                        for frame in new_frames:
                                            video_writer.write(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

                                        # Release the video writer
                                        video_writer.release()

                                    # Use the MP4 file instead
                                    new_animation_path = mp4_path
                                    print(f"Created MP4 animation as fallback: {mp4_path}")
                                except Exception as e2:
                                    print(f"Warning: Failed to create animation (non-critical): {e2}")

                            # Update the animation path to the new file
                            latent_animation_path = new_animation_path

                            # Update the UI with the animation
                            animation_update = gr.update(visible=True, value=latent_animation_path)
                        else:
                            animation_update = gr.update()
                    else:
                        # We've already processed this section or it's not a new section
                        animation_update = gr.update()
                else:
                    animation_update = gr.update()

                # If html is None, don't update the progress bar
                # This happens when we're just updating the preview image
                if html is None:
                    # If description is also None, don't update the description either
                    if desc is None:
                        yield gr.update(), preview_update, gr.update(), gr.update(), gr.update(interactive=False), gr.update(interactive=True), animation_update
                    else:
                        yield gr.update(), preview_update, desc, gr.update(), gr.update(interactive=False), gr.update(interactive=True), animation_update
                else:
                    # If we have HTML, update both the progress bar and description
                    yield gr.update(), preview_update, desc if desc else gr.update(), html, gr.update(interactive=False), gr.update(interactive=True), animation_update
            except Exception as e:
                print(f"Error updating UI: {e}")
                # Provide a safe fallback to prevent crashes
                yield gr.update(), gr.update(), gr.update(), gr.update(), gr.update(interactive=False), gr.update(interactive=True), gr.update()

        if flag == 'end':
            # Make sure we have the latest output_filename
            if output_filename:
                # Strip any query parameters
                clean_filename = output_filename.split('?')[0]
                print(f"Final video: {clean_filename}")

                # Force one final refresh of the video element
                yield gr.update(value=clean_filename), gr.update(visible=False), gr.update(), '', gr.update(interactive=True), gr.update(interactive=False), gr.update(visible=False)
            else:
                yield gr.update(), gr.update(visible=False), gr.update(), '', gr.update(interactive=True), gr.update(interactive=False), gr.update(visible=False)
            break


def end_process():
    stream.input_queue.push('end')


# Function to load quick prompts from JSON file
def load_quick_prompts_from_json():
    # Default prompts to use if JSON file doesn't exist or has issues
    default_prompts = [
        'The girl dances gracefully, with clear movements, full of charm.',
        'A character doing some simple body movements.',
    ]

    json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "quick_list.json")

    try:
        # Check if the JSON file exists
        if os.path.exists(json_path):
            print(f"Loading quick prompts from {json_path}")
            with open(json_path, 'r', encoding='utf-8') as f:
                try:
                    prompts_data = json.load(f)

                    # Handle different possible JSON structures
                    if isinstance(prompts_data, list):
                        # If it's a simple list of strings
                        if all(isinstance(item, str) for item in prompts_data):
                            print(f"Loaded {len(prompts_data)} prompts from JSON file (list format)")
                            return prompts_data
                        # If it's a list of objects with a 'prompt' field
                        elif all(isinstance(item, dict) and 'prompt' in item for item in prompts_data):
                            result = [item['prompt'] for item in prompts_data]
                            print(f"Loaded {len(result)} prompts from JSON file (object list format)")
                            return result
                    # If it's an object with a 'prompts' field that contains the list
                    elif isinstance(prompts_data, dict) and 'prompts' in prompts_data and isinstance(prompts_data['prompts'], list):
                        result = prompts_data['prompts']
                        print(f"Loaded {len(result)} prompts from JSON file (prompts field format)")
                        return result

                    print(f"JSON format not recognized, using default prompts")
                except json.JSONDecodeError:
                    print(f"JSON file is invalid, using default prompts")

                    # Try to backup the corrupted file
                    import shutil
                    try:
                        backup_path = json_path + ".bak"
                        shutil.copy2(json_path, backup_path)
                        print(f"Backed up corrupted JSON file to {backup_path}")
                    except Exception as backup_error:
                        print(f"Failed to backup corrupted JSON file: {backup_error}")
        else:
            print(f"Quick prompts JSON file not found at {json_path}, using default prompts")

            # Create a sample JSON file for the user
            sample_data = {
                "prompts": default_prompts
            }
            try:
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(sample_data, f, indent=2, ensure_ascii=False)
                print(f"Created sample quick_list.json file at {json_path}")
            except Exception as e:
                print(f"Failed to create sample JSON file: {e}")

        return default_prompts
    except Exception as e:
        print(f"Error loading quick prompts from JSON: {e}")
        return default_prompts

# Function to save a prompt to the quick list JSON file
def save_prompt_to_quick_list(prompt_text):
    if not prompt_text or prompt_text.strip() == "":
        print("Cannot save empty prompt to quick list")
        return None

    json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "quick_list.json")

    try:
        # Check if the JSON file exists and read it directly
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                try:
                    data = json.load(f)
                    # Make sure we have the expected structure
                    if isinstance(data, dict) and "prompts" in data and isinstance(data["prompts"], list):
                        existing_prompts = data["prompts"]
                    else:
                        print("JSON file has unexpected format, creating new structure")
                        existing_prompts = []
                except json.JSONDecodeError:
                    print("JSON file is invalid, creating new structure")
                    existing_prompts = []
        else:
            print("JSON file doesn't exist, creating new file")
            existing_prompts = []

        # Check if the prompt already exists (case-sensitive comparison)
        if prompt_text not in existing_prompts:
            # Add the new prompt to the list
            existing_prompts.append(prompt_text)

            # Save the updated list back to the JSON file
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump({"prompts": existing_prompts}, f, indent=2, ensure_ascii=False)

            print(f"Added prompt to quick list: {prompt_text}")
        else:
            print(f"Prompt already exists in quick list: {prompt_text}")

        # Don't try to update the UI, just return None
        return None
    except Exception as e:
        print(f"Error saving prompt to quick list: {e}")
        return None

# Function to truncate text for display while preserving the full text in tooltips
def truncate_for_display(text, max_length=140):
    """
    Truncates text to max_length characters for display, adding "..." if truncated.
    Returns a tuple of (truncated_text, original_text) for display and tooltip.
    """
    if len(text) <= max_length:
        return text
    else:
        return text[:max_length-3] + "..."

# Function to reopen the console window
def reopen_console_window():
    try:
        # Get the path to the current script
        script_path = os.path.abspath(__file__)
        script_dir = os.path.dirname(script_path)

        # Path to the launcher batch file
        launcher_path = os.path.join(script_dir, "launch_framepack.bat")

        # Check if the launcher exists
        if not os.path.exists(launcher_path):
            print(f"Launcher not found at: {launcher_path}")
            # Try the LAN version as fallback
            launcher_path = os.path.join(script_dir, "launch_framepack_lan.bat")
            if not os.path.exists(launcher_path):
                print("No launcher batch file found. Cannot reopen console window.")
                return "Console window could not be reopened. Launcher not found."

        # Start a new process with the launcher
        subprocess.Popen(["cmd.exe", "/c", "start", "cmd.exe", "/c", launcher_path],
                         shell=True,
                         creationflags=subprocess.CREATE_NEW_CONSOLE)

        print("Reopened console window using launcher: " + launcher_path)
        return "Console window reopened successfully!"
    except Exception as e:
        print(f"Error reopening console window: {e}")
        return f"Error: {str(e)}"

# Load prompts directly from the JSON file at startup
json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "quick_list.json")
if os.path.exists(json_path):
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            if isinstance(data, dict) and "prompts" in data and isinstance(data["prompts"], list):
                quick_prompts = data["prompts"]
                print(f"Successfully loaded {len(quick_prompts)} prompts directly from quick_list.json")
            else:
                quick_prompts = load_quick_prompts_from_json()
    except Exception as e:
        print(f"Error loading quick_list.json directly: {e}")
        quick_prompts = load_quick_prompts_from_json()
else:
    quick_prompts = load_quick_prompts_from_json()

# Format the prompts for the Dataset component, truncating for display
quick_prompts = [[truncate_for_display(x)] for x in quick_prompts]


css = make_progress_bar_css()

# Add CSS for the buttons
css += """
<style>
/* Style for the randomize seed button */
.randomize-seed-btn {
    font-size: 18px !important;
    padding: 0 10px !important;
    height: 36px !important;
    margin-top: 24px !important; /* Align with the seed input */
    background-color: #f0f0f0 !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    transition: background-color 0.2s !important;
}

.randomize-seed-btn:hover {
    background-color: #e0e0e0 !important;
}

/* Make the info text also clickable as a fallback */
.info:contains("Set to -1 for a random seed") {
    cursor: pointer !important;
    text-decoration: underline !important;
    color: #0000EE !important;
}

/* Style for the quick list buttons */
button:contains("Save to Quick List") {
    margin-top: 5px !important;
    margin-bottom: 10px !important;
    background-color: #e6f7ff !important;
    border-color: #91d5ff !important;
}

button:contains("Save to Quick List"):hover {
    background-color: #bae7ff !important;
    border-color: #69c0ff !important;
}

/* Style for the truncated prompts in the Dataset component */
.gradio-dataset .cell-content {
    max-width: 100% !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    cursor: pointer !important;
}

.gradio-dataset .cell-content:hover {
    text-decoration: underline !important;
}

/* Style for the reopen console button */
button:contains("Reopen Console Window") {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
    background-color: #e8f4f8 !important;
    border-color: #a8d1e0 !important;
    display: block !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

button:contains("Reopen Console Window"):hover {
    background-color: #c8e6f0 !important;
    border-color: #88c1d0 !important;
}
</style>
"""

block = gr.Blocks(css=css).queue()
with block:
    gr.Markdown('# FramePack')
    with gr.Row():
        with gr.Column():
            with gr.Row():
                with gr.Column():
                    # Add a button to load metadata from the image
                    load_metadata_btn = gr.Button("📋 Load Settings", size="sm")
                    input_image = gr.Image(sources='upload', type="numpy", label="Start Frame", height=320)
                with gr.Column():
                    # Add a button to load metadata from the image
                    load_end_metadata_btn = gr.Button("📋 Load Settings", size="sm")
                    end_image = gr.Image(sources='upload', type="numpy", label="End Frame (Optional)", height=320)

            # Add a description for the Load Settings buttons
            gr.Markdown("*Click the '📋 Load Settings' button above an image to extract its metadata and populate the settings fields below.*")

            prompt = gr.Textbox(label="Prompt", value='')

            # Add a row for the quick list controls
            with gr.Row():
                # Add a button to save the current prompt to the quick list
                save_to_quick_list_btn = gr.Button(value="💾 Save to Quick List", size="sm")

            # Create a Dataset component with truncated prompts
            example_quick_prompts = gr.Dataset(
                samples=quick_prompts,
                label='Quick List (truncated to 140 chars, hover for full text)',
                samples_per_page=1000,
                components=[prompt]
            )

            # When clicking a prompt, use the original full text from the JSON file
            def load_full_prompt(selected_data):
                # The input is a list containing the selected prompt
                if not selected_data or not isinstance(selected_data, list) or len(selected_data) == 0:
                    return ""

                # Extract the truncated prompt from the list
                truncated_prompt = selected_data[0]

                # If the prompt is not a string, return it as is
                if not isinstance(truncated_prompt, str):
                    print(f"Warning: Expected string but got {type(truncated_prompt)}")
                    return truncated_prompt

                # If the prompt is truncated (ends with "..."), find the original in the JSON file
                if truncated_prompt.endswith("..."):
                    try:
                        with open(json_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if isinstance(data, dict) and "prompts" in data:
                                # Find the first prompt that starts with the truncated text (minus "...")
                                prefix = truncated_prompt[:-3]
                                for full_prompt in data["prompts"]:
                                    if full_prompt.startswith(prefix):
                                        print(f"Found full prompt for: {truncated_prompt}")
                                        return full_prompt
                    except Exception as e:
                        print(f"Error finding full prompt: {e}")

                # If not truncated or not found, return the prompt as is
                return truncated_prompt

            example_quick_prompts.click(
                fn=load_full_prompt,
                inputs=[example_quick_prompts],
                outputs=prompt,
                show_progress=False,
                queue=False
            )

            with gr.Row():
                start_button = gr.Button(value="Start Generation")
                end_button = gr.Button(value="End Generation", interactive=False)

            with gr.Group():
                use_teacache = gr.Checkbox(label='Use TeaCache', value=True, info='Faster speed, but often makes hands and fingers slightly worse.')

                n_prompt = gr.Textbox(label="Negative Prompt", value="", info="Specify what you don't want to see in the generated video")

                # Create a row for the seed input and randomize button
                with gr.Row():
                    seed = gr.Number(label="Seed", value=-1, precision=0, info='Set to -1 for a random seed')
                    # Add a button to set seed to -1
                    randomize_seed_btn = gr.Button(value="🎲", size="sm", elem_classes="randomize-seed-btn")

                total_second_length = gr.Slider(label="Total Video Length (Seconds)", minimum=1, maximum=120, value=50, step=0.1)
                latent_window_size = gr.Slider(label="Latent Window Size", minimum=1, maximum=33, value=9, step=1, visible=False)  # Should not change
                steps = gr.Slider(label="Steps", minimum=1, maximum=100, value=25, step=1, info='Changing this value is not recommended.')

                cfg = gr.Slider(label="CFG Scale", minimum=1.0, maximum=32.0, value=1.0, step=0.01, visible=False)  # Should not change
                gs = gr.Slider(label="Distilled CFG Scale", minimum=1.0, maximum=32.0, value=10.0, step=0.01, info='Changing this value is not recommended.')
                rs = gr.Slider(label="CFG Re-Scale", minimum=0.0, maximum=1.0, value=0.0, step=0.01, visible=False)  # Should not change

                gpu_memory_preservation = gr.Slider(label="GPU Inference Preserved Memory (GB) (larger means slower)", minimum=6, maximum=128, value=6, step=0.1, info="Set this number to a larger value if you encounter OOM. Larger value causes slower speed.")

                mp4_crf = gr.Slider(label="MP4 Compression", minimum=0, maximum=100, value=8, step=1, info="Lower means better quality. 0 is uncompressed. Change to 16 if you get black outputs. ")

        with gr.Column():
            with gr.Row():
                preview_image = gr.Image(label="Next Latents", height=200, visible=False)
                latent_animation = gr.Video(label="Latent Animation", height=260, width=340, visible=False, autoplay=True, loop=True)
            result_video = gr.Video(label="Finished Frames", autoplay=True, show_share_button=False, height=512, loop=True)
            gr.Markdown('When using only a start frame, the ending actions will be generated before the starting actions due to the inverted sampling. If using both start and end frames, the model will try to create a smooth transition between them.')
            progress_desc = gr.Markdown('', elem_classes='no-generating-animation')
            progress_bar = gr.HTML('', elem_classes='no-generating-animation')

    # Add a button to reopen the console window
    reopen_console_btn = gr.Button(value="🖥️ Reopen Console Window", size="sm")

    # Add a message area to show the result of reopening the console
    console_reopen_result = gr.Markdown("")

    # Connect the reopen console button to our function
    reopen_console_btn.click(
        fn=reopen_console_window,
        inputs=[],
        outputs=[console_reopen_result]
    )

    gr.HTML('<div style="text-align:center; margin-top:20px;">Share your results and find ideas at the <a href="https://x.com/search?q=framepack&f=live" target="_blank">FramePack Twitter (X) thread</a></div>')

    ips = [input_image, end_image, prompt, n_prompt, seed, total_second_length, latent_window_size, steps, cfg, gs, rs, gpu_memory_preservation, use_teacache, mp4_crf]
    start_button.click(fn=process, inputs=ips, outputs=[result_video, preview_image, progress_desc, progress_bar, start_button, end_button, latent_animation])
    end_button.click(fn=end_process)

    # Connect the load metadata buttons to our function
    load_metadata_btn.click(
        fn=load_metadata_from_image,
        inputs=[input_image],
        outputs=[prompt, n_prompt, seed, total_second_length, steps, cfg, gs, rs, use_teacache]
    )

    load_end_metadata_btn.click(
        fn=load_metadata_from_image,
        inputs=[end_image],
        outputs=[prompt, n_prompt, seed, total_second_length, steps, cfg, gs, rs, use_teacache]
    )

    # Function to set seed to -1
    def set_seed_to_random():
        return -1

    # Connect the randomize seed button to our function
    randomize_seed_btn.click(
        fn=set_seed_to_random,
        inputs=[],
        outputs=[seed]
    )

    # Connect the save to quick list button to our function
    save_to_quick_list_btn.click(
        fn=save_prompt_to_quick_list,
        inputs=[prompt],
        outputs=[]  # No outputs since we're not updating the UI
    )



# Function to clean up temporary files
def cleanup_temp_files():
    import shutil

    print("\nCleaning up temporary files...")
    try:
        # Check if the temp folder exists
        if os.path.exists(temp_folder):
            # List all files in the temp folder
            temp_files = os.listdir(temp_folder)
            print(f"Found {len(temp_files)} temporary files/folders to clean up")

            # Remove all files and subdirectories in the temp folder
            for item in temp_files:
                item_path = os.path.join(temp_folder, item)
                try:
                    if os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                        print(f"Removed directory: {item_path}")
                    else:
                        # For files, check if they're in use before trying to delete
                        try:
                            # Try to open the file to see if it's locked
                            with open(item_path, 'a'):
                                pass
                            # If we get here, the file isn't locked
                            os.remove(item_path)
                            print(f"Removed file: {item_path}")
                        except PermissionError:
                            print(f"Skipping file that's in use: {item_path}")
                except Exception as e:
                    print(f"Error removing {item_path}: {e}")

            print("Temporary files cleanup complete")
    except Exception as e:
        print(f"Error during cleanup: {e}")

# Register the cleanup function to run at exit
import atexit
atexit.register(cleanup_temp_files)

# Make the application not close on Ctrl+C
import signal

# Define a custom signal handler for Ctrl+C
def signal_handler(sig, frame):
    print("\nCtrl+C pressed. The application will continue running.")
    print("Press Ctrl+C again to exit.")

    # Reset the handler to default for the next Ctrl+C
    signal.signal(signal.SIGINT, original_sigint_handler)

# Store the original signal handler
original_sigint_handler = signal.getsignal(signal.SIGINT)

# Set our custom handler
signal.signal(signal.SIGINT, signal_handler)

block.queue(api_open=True)
# Launch the application
block.launch(
    server_name=args.server,
    server_port=args.port,
    share=args.share,
    inbrowser=args.inbrowser
)
# This is the end of the file