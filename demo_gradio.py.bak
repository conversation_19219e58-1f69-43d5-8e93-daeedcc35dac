from diffusers_helper.hf_login import login

import os

import sys, asyncio
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


os.environ['HF_HOME'] = os.path.abspath(os.path.realpath(os.path.join(os.path.dirname(__file__), './hf_download')))

import gradio as gr
import torch
import traceback
import einops
import safetensors.torch as sf
import numpy as np
import argparse
import math
import random
import re
import time
import json
import base64

# Helper function to save images with metadata
def save_image_with_metadata(image_array, filepath, metadata_dict):
    """
    Save an image with metadata embedded in the PNG file.

    Args:
        image_array: Numpy array containing the image data
        filepath: Path where the image will be saved
        metadata_dict: Dictionary containing metadata to embed
    """
    # Convert numpy array to PIL Image
    img = Image.fromarray(image_array)

    # Convert metadata to JSON string
    metadata_json = json.dumps(metadata_dict)

    # Create PngInfo object
    png_info = PngImagePlugin.PngInfo()

    # Add metadata to the PngInfo object
    png_info.add_text("FramePack", metadata_json)

    # Save the image with metadata
    img.save(filepath, format="PNG", pnginfo=png_info)

    print(f"Saved image with metadata to {filepath}")
    print(f"Metadata: {metadata_dict}")


# Helper function to extract metadata from PNG files
def extract_metadata_from_png(image_path):
    """
    Extract metadata from a PNG file.

    Args:
        image_path: Path to the PNG file

    Returns:
        Dictionary containing the extracted metadata, or None if no metadata is found
    """
    try:
        # Open the image
        img = Image.open(image_path)

        # Get the metadata
        metadata_json = img.info.get('FramePack')
        if metadata_json:
            # Parse the JSON
            metadata = json.loads(metadata_json)
            print(f"Extracted metadata from {image_path}: {metadata}")
            return metadata
        else:
            print(f"No FramePack metadata found in {image_path}")
            return None
    except Exception as e:
        print(f"Error extracting metadata from {image_path}: {e}")
        return None


# Function to load metadata from an image and populate the UI fields
def load_metadata_from_image(image):
    """
    Load metadata from an image and return values for UI fields.

    Args:
        image: Image data from Gradio

    Returns:
        Values for prompt, seed, steps, etc. to populate the UI
    """
    if image is None:
        print("No image provided")
        return [gr.update() for _ in range(9)]  # Return no updates if no image

    try:
        # First, try to find a matching file in the outputs folder
        # This is necessary because Gradio strips metadata when uploading images
        found_file = None

        # Calculate a simple hash of the image to use for comparison
        import hashlib
        image_hash = hashlib.md5(image.tobytes()).hexdigest()
        print(f"Looking for image with hash: {image_hash}")

        # Look through all PNG files in the outputs folder
        for filename in os.listdir(outputs_folder):
            if filename.endswith('.png'):
                try:
                    file_path = os.path.join(outputs_folder, filename)
                    file_image = np.array(Image.open(file_path))

                    # Skip if shapes don't match
                    if file_image.shape != image.shape:
                        continue

                    # Check if the images are similar (using a hash)
                    file_hash = hashlib.md5(file_image.tobytes()).hexdigest()

                    if file_hash == image_hash:
                        found_file = file_path
                        print(f"Found matching file: {found_file}")
                        break
                except Exception as e:
                    print(f"Error checking file {filename}: {e}")

        # If we found a matching file, extract metadata from it
        if found_file:
            metadata = extract_metadata_from_png(found_file)

            if metadata:
                print(f"Successfully extracted metadata from {found_file}")
                # Extract values from metadata
                prompt_value = metadata.get('prompt', '')
                n_prompt_value = metadata.get('negative_prompt', '')
                seed_value = metadata.get('seed', -1)
                steps_value = metadata.get('steps', 25)
                cfg_value = metadata.get('cfg_scale', 1.0)
                gs_value = metadata.get('distilled_cfg_scale', 10.0)
                rs_value = metadata.get('cfg_rescale', 0.0)
                use_teacache_value = metadata.get('use_teacache', True)
                total_second_length_value = float(metadata.get('total_video_length', '5 seconds').split()[0])

                # Return values for UI update
                return [
                    prompt_value,                # prompt
                    n_prompt_value,              # n_prompt
                    seed_value,                  # seed
                    total_second_length_value,   # total_second_length
                    steps_value,                 # steps
                    cfg_value,                   # cfg
                    gs_value,                    # gs
                    rs_value,                    # rs
                    use_teacache_value,          # use_teacache
                ]
            else:
                print(f"No metadata found in the matching file: {found_file}")
        else:
            print("No matching file found in outputs folder")

        # If we couldn't find a matching file or extract metadata, ask the user to select a file
        import tkinter as tk
        from tkinter import filedialog

        # Create a root window but hide it
        root = tk.Tk()
        root.withdraw()

        # Show a message to the user
        print("Could not find metadata automatically. Please select a PNG file with metadata.")

        # Ask the user to select a PNG file
        file_path = filedialog.askopenfilename(
            title="Select PNG file with metadata",
            filetypes=[("PNG files", "*.png")],
            initialdir=outputs_folder
        )

        # If the user cancels the dialog, return no updates
        if not file_path:
            print("User cancelled file selection")
            return [gr.update() for _ in range(9)]

        # Extract metadata from the selected file
        metadata = extract_metadata_from_png(file_path)

        if metadata:
            # Extract values from metadata
            prompt_value = metadata.get('prompt', '')
            n_prompt_value = metadata.get('negative_prompt', '')
            seed_value = metadata.get('seed', -1)
            steps_value = metadata.get('steps', 25)
            cfg_value = metadata.get('cfg_scale', 1.0)
            gs_value = metadata.get('distilled_cfg_scale', 10.0)
            rs_value = metadata.get('cfg_rescale', 0.0)
            use_teacache_value = metadata.get('use_teacache', True)
            total_second_length_value = float(metadata.get('total_video_length', '5 seconds').split()[0])

            # Return values for UI update
            return [
                prompt_value,                # prompt
                n_prompt_value,              # n_prompt
                seed_value,                  # seed
                total_second_length_value,   # total_second_length
                steps_value,                 # steps
                cfg_value,                   # cfg
                gs_value,                    # gs
                rs_value,                    # rs
                use_teacache_value,          # use_teacache
            ]
        else:
            print(f"No metadata found in the selected file: {file_path}")
            # If no metadata found, return no updates
            return [gr.update() for _ in range(9)]
    except Exception as e:
        print(f"Error loading metadata: {e}")
        # Return no updates on error
        return [gr.update() for _ in range(9)]

# Custom exception for graceful termination
class GracefulExit(Exception):
    """Exception raised when user manually stops generation."""
    pass

from PIL import Image, PngImagePlugin
import cv2
from diffusers import AutoencoderKLHunyuanVideo
from transformers import LlamaModel, CLIPTextModel, LlamaTokenizerFast, CLIPTokenizer
from diffusers_helper.hunyuan import encode_prompt_conds, vae_decode, vae_encode, vae_decode_fake
from diffusers_helper.utils import save_bcthw_as_mp4, crop_or_pad_yield_mask, soft_append_bcthw, resize_and_center_crop, state_dict_weighted_merge, state_dict_offset_merge, generate_timestamp
from diffusers_helper.models.hunyuan_video_packed import HunyuanVideoTransformer3DModelPacked
from diffusers_helper.pipelines.k_diffusion_hunyuan import sample_hunyuan
from diffusers_helper.memory import cpu, gpu, get_cuda_free_memory_gb, move_model_to_device_with_memory_preservation, offload_model_from_device_for_memory_preservation, fake_diffusers_current_device, DynamicSwapInstaller, unload_complete_models, load_model_as_complete
from diffusers_helper.thread_utils import AsyncStream, async_run
from diffusers_helper.gradio.progress_bar import make_progress_bar_css, make_progress_bar_html
from transformers import SiglipImageProcessor, SiglipVisionModel
from diffusers_helper.clip_vision import hf_clip_vision_encode
from diffusers_helper.bucket_tools import find_nearest_bucket
from diffusers_helper.tqdm_capture import set_progress_callback


parser = argparse.ArgumentParser()
parser.add_argument('--share', action='store_true')
parser.add_argument("--server", type=str, default='0.0.0.0')
parser.add_argument("--port", type=int, required=False)
parser.add_argument("--inbrowser", action='store_true')
args = parser.parse_args()

# for win desktop probably use --server 127.0.0.1 --inbrowser
# For linux server probably use --server 127.0.0.1 or do not use any cmd flags
print(args)

free_mem_gb = get_cuda_free_memory_gb(gpu)
high_vram = free_mem_gb > 60

print(f'Free VRAM {free_mem_gb} GB')
print(f'High-VRAM Mode: {high_vram}')

text_encoder = LlamaModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder', torch_dtype=torch.float16).cpu()
text_encoder_2 = CLIPTextModel.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='text_encoder_2', torch_dtype=torch.float16).cpu()
tokenizer = LlamaTokenizerFast.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer')
tokenizer_2 = CLIPTokenizer.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='tokenizer_2')
vae = AutoencoderKLHunyuanVideo.from_pretrained("hunyuanvideo-community/HunyuanVideo", subfolder='vae', torch_dtype=torch.float16).cpu()

feature_extractor = SiglipImageProcessor.from_pretrained("lllyasviel/flux_redux_bfl", subfolder='feature_extractor')
image_encoder = SiglipVisionModel.from_pretrained("lllyasviel/flux_redux_bfl", subfolder='image_encoder', torch_dtype=torch.float16).cpu()

transformer = HunyuanVideoTransformer3DModelPacked.from_pretrained('lllyasviel/FramePackI2V_HY', torch_dtype=torch.bfloat16).cpu()

vae.eval()
text_encoder.eval()
text_encoder_2.eval()
image_encoder.eval()
transformer.eval()

if not high_vram:
    vae.enable_slicing()
    vae.enable_tiling()

transformer.high_quality_fp32_output_for_inference = True
print('transformer.high_quality_fp32_output_for_inference = True')

transformer.to(dtype=torch.bfloat16)
vae.to(dtype=torch.float16)
image_encoder.to(dtype=torch.float16)
text_encoder.to(dtype=torch.float16)
text_encoder_2.to(dtype=torch.float16)

vae.requires_grad_(False)
text_encoder.requires_grad_(False)
text_encoder_2.requires_grad_(False)
image_encoder.requires_grad_(False)
transformer.requires_grad_(False)

if not high_vram:
    # DynamicSwapInstaller is same as huggingface's enable_sequential_offload but 3x faster
    DynamicSwapInstaller.install_model(transformer, device=gpu)
    DynamicSwapInstaller.install_model(text_encoder, device=gpu)
else:
    text_encoder.to(gpu)
    text_encoder_2.to(gpu)
    image_encoder.to(gpu)
    vae.to(gpu)
    transformer.to(gpu)

# Global variables to track generation state
stream = None
generation_in_progress = False
generation_stopping = False
generation_stop_time = None

# Set up folders for outputs and temporary files
outputs_folder = './outputs/'
temp_folder = './temp/'
os.makedirs(outputs_folder, exist_ok=True)
os.makedirs(temp_folder, exist_ok=True)

# Set Gradio's temporary directory to our temp folder
import tempfile
# Set the Python tempdir
tempfile.tempdir = os.path.abspath(temp_folder)
# Set environment variables that Gradio uses for temp files
os.environ["GRADIO_TEMP_DIR"] = os.path.abspath(temp_folder)
os.environ["GRADIO_CACHE_DIR"] = os.path.abspath(os.path.join(temp_folder, "gradio_cache"))
print(f"Setting temporary directory to: {os.path.abspath(temp_folder)}")


@torch.no_grad()
def worker(input_image, end_image, prompt, n_prompt, seed, total_second_length, latent_window_size, steps, cfg, gs, rs, gpu_memory_preservation, use_teacache, mp4_crf):
    global generation_in_progress, generation_stopping, generation_stop_time
    # Calculate the total number of latent sections
    # Use math.ceil instead of round to ensure we have enough sections
    import math
    frames_per_second = 30
    total_frames = total_second_length * frames_per_second
    frames_per_section = latent_window_size * 4
    raw_sections = total_frames / frames_per_section

    # Use ceiling to ensure we have enough sections to cover the requested video length
    total_latent_sections = int(max(math.ceil(raw_sections), 1))

    # Print the calculation for debugging
    print(f"Video length: {total_second_length} seconds = {total_frames} frames")
    print(f"Each section covers {frames_per_section} frames")
    print(f"Raw section count: {raw_sections}, Ceiling: {math.ceil(raw_sections)}")
    print(f"Using {total_latent_sections} sections for generation")

    # Calculate the total number of steps across all sections
    total_steps = total_latent_sections * steps

    # Variables to track overall progress
    current_section = 0
    displayed_section = 0  # The section number shown in the UI
    steps_completed = 0
    start_time = time.time()
    in_transition = False  # Flag to track when we're transitioning between sections

    # For estimating time remaining
    section_start_times = {}
    section_end_times = {}

    job_id = generate_timestamp()

    # Set up the progress callback to update the UI with tqdm progress
    def progress_callback(progress_info):
        try:
            # Extract the progress information from the current section
            percentage = progress_info.get('percentage', 0)
            current = progress_info.get('current', 0)
            total = progress_info.get('total', 0)
            description = progress_info.get('description', '')

            # Only update if we have valid progress information
            if current > 0 and total > 0:
                # Update our tracking variables
                nonlocal steps_completed, current_section, displayed_section, in_transition

                # Only update the displayed section at the beginning of a new section
                # This prevents the section number from changing during transitions
                if current == 1:
                    # We're starting a new section
                    in_transition = False
                    displayed_section = current_section

                    # Record the time for the current section
                    if current_section not in section_start_times:
                        section_start_times[current_section] = time.time()
                        print(f"Starting section {current_section+1}/{total_latent_sections}")

                # If we're at the end of a section, mark that we're entering a transition
                if current == total:
                    in_transition = True
                    print(f"Finishing section {displayed_section+1}/{total_latent_sections}")

                # Calculate overall progress
                current_section_steps = current
                steps_completed = (displayed_section * steps) + current_section_steps
                overall_percentage = int((steps_completed / total_steps) * 100)

                # Calculate time remaining for current section
                section_elapsed_time = time.time() - section_start_times[displayed_section]
                if current > 1:  # Need at least 2 steps to estimate
                    section_time_per_step = section_elapsed_time / (current - 1)  # -1 because we're still on the current step
                    section_remaining_steps = total - current
                    section_estimated_remaining_time = section_remaining_steps * section_time_per_step

                    # Format the section remaining time
                    if section_estimated_remaining_time < 60:
                        section_remaining_time_str = f"{section_estimated_remaining_time:.0f} seconds"
                    elif section_estimated_remaining_time < 3600:
                        section_remaining_time_str = f"{section_estimated_remaining_time/60:.1f} minutes"
                    else:
                        section_remaining_time_str = f"{section_estimated_remaining_time/3600:.1f} hours"
                else:
                    section_remaining_time_str = "Calculating..."

                # Calculate total time remaining based on overall progress
                total_elapsed_time = time.time() - start_time

                # Simple method: Based on steps completed so far
                # This is the most reliable method and doesn't make assumptions about section times
                if steps_completed > 0:
                    # Calculate time per step based on all steps completed so far
                    time_per_step = total_elapsed_time / steps_completed
                    remaining_steps = total_steps - steps_completed
                    estimated_remaining_time = remaining_steps * time_per_step

                    # Make sure we don't get negative time
                    estimated_remaining_time = max(0, estimated_remaining_time)

                    # Format the remaining time
                    if estimated_remaining_time < 60:
                        remaining_time_str = f"{estimated_remaining_time:.0f} seconds"
                    elif estimated_remaining_time < 3600:
                        minutes = int(estimated_remaining_time // 60)
                        seconds = int(estimated_remaining_time % 60)
                        remaining_time_str = f"{minutes}m {seconds}s"
                    else:
                        hours = int(estimated_remaining_time // 3600)
                        minutes = int((estimated_remaining_time % 3600) // 60)
                        seconds = int(estimated_remaining_time % 60)
                        remaining_time_str = f"{hours}h {minutes}m {seconds}s"
                else:
                    remaining_time_str = "Calculating..."

                # Calculate total estimated time (elapsed + remaining)
                if steps_completed > 0:
                    total_estimated_time = total_elapsed_time + estimated_remaining_time

                    # Format the total estimated time
                    if total_estimated_time < 60:
                        total_time_str = f"{total_estimated_time:.0f} seconds"
                    elif total_estimated_time < 3600:
                        minutes = int(total_estimated_time // 60)
                        seconds = int(total_estimated_time % 60)
                        total_time_str = f"{minutes}m {seconds}s"
                    else:
                        hours = int(total_estimated_time // 3600)
                        minutes = int((total_estimated_time % 3600) // 60)
                        seconds = int(total_estimated_time % 60)
                        total_time_str = f"{hours}h {minutes}m {seconds}s"
                else:
                    total_time_str = "Calculating..."

                # Create a comprehensive progress message
                total_frames = int(max(0, total_generated_latent_frames * 4 - 3))
                video_length = max(0, (total_generated_latent_frames * 4 - 3) / 30)

                # Format elapsed time since the start of generation
                elapsed_time_str = ""
                if total_elapsed_time < 60:
                    elapsed_time_str = f"{total_elapsed_time:.0f} seconds"
                elif total_elapsed_time < 3600:
                    minutes = int(total_elapsed_time // 60)
                    seconds = int(total_elapsed_time % 60)
                    elapsed_time_str = f"{minutes}m {seconds}s"
                else:
                    hours = int(total_elapsed_time // 3600)
                    minutes = int((total_elapsed_time % 3600) // 60)
                    seconds = int(total_elapsed_time % 60)
                    elapsed_time_str = f"{hours}h {minutes}m {seconds}s"

                # Create a hint message that includes the progress bar
                # Format with line breaks at natural break points for better display
                hint = f"Section {displayed_section+1}/{total_latent_sections}: {current}/{total} steps (Section ETA: {section_remaining_time_str})<br>"
                hint += f"Overall: {steps_completed}/{total_steps} steps ({overall_percentage}%)<br>"

                # Add seed information if using random seed
                if seed == -1 and random_seed is not None:
                    hint += f"Seed: {random_seed}<br>"

                # Add elapsed time, remaining time and total time estimates
                hint += f"Elapsed: {elapsed_time_str}<br>"
                hint += f"Remaining: {remaining_time_str}<br>"
                hint += f"Total estimated time: {total_time_str}"

                # Create a description with video information
                description = f"Generated frames: {total_frames}, Video length: {video_length:.2f} seconds (FPS-30)"

                # Update the UI with the progress information
                html = make_progress_bar_html(overall_percentage, hint)

                # Force an update to the UI with both the progress bar and a description
                stream.output_queue.push(('progress', (None, description, html)))

                # If this is the last step in the section, record the end time and increment section counter
                if current == total:
                    section_end_times[current_section] = time.time()
                    current_section += 1
            # Skip updates with invalid values silently
        except Exception as e:
            print(f"Error in progress callback: {e}")

    # Set the progress callback
    set_progress_callback(progress_callback)

    # The total steps are: 3 processing steps + total_latent_sections generation steps
    total_process_steps = 3 + total_latent_sections

    # Show initial progress with total steps information
    initial_message = f"Starting generation of {total_second_length:.1f} second video ({steps} sampling steps across {total_latent_sections} sections)"
    stream.output_queue.push(('progress', (None, initial_message, make_progress_bar_html(0, initial_message))))

    try:
        # Clean GPU
        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )

        # Text encoding
        processing_message = f"Step 1/{total_process_steps}: Text encoding"
        stream.output_queue.push(('progress', (None, processing_message, make_progress_bar_html(0, processing_message))))

        if not high_vram:
            fake_diffusers_current_device(text_encoder, gpu)  # since we only encode one text - that is one model move and one encode, offload is same time consumption since it is also one load and one encode.
            load_model_as_complete(text_encoder_2, target_device=gpu)

        llama_vec, clip_l_pooler = encode_prompt_conds(prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)

        if cfg == 1:
            llama_vec_n, clip_l_pooler_n = torch.zeros_like(llama_vec), torch.zeros_like(clip_l_pooler)
        else:
            llama_vec_n, clip_l_pooler_n = encode_prompt_conds(n_prompt, text_encoder, text_encoder_2, tokenizer, tokenizer_2)

        llama_vec, llama_attention_mask = crop_or_pad_yield_mask(llama_vec, length=512)
        llama_vec_n, llama_attention_mask_n = crop_or_pad_yield_mask(llama_vec_n, length=512)

        # Processing input image (start frame)
        processing_message = f"Step 2/{total_process_steps}: Processing start frame"
        stream.output_queue.push(('progress', (None, processing_message, make_progress_bar_html(0, processing_message))))

        H, W, C = input_image.shape
        height, width = find_nearest_bucket(H, W, resolution=640)
        input_image_np = resize_and_center_crop(input_image, target_width=width, target_height=height)

        # We'll save the start frame with metadata after determining the seed
        # Store the input image for now
        start_frame_path = os.path.join(outputs_folder, f'{job_id}_start.png')
        temp_start_image = input_image_np.copy()

        input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1
        input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]

        # Processing end image (if provided)
        has_end_image = end_image is not None
        if has_end_image:
            processing_message = f"Step 2b/{total_process_steps}: Processing end frame"
            stream.output_queue.push(('progress', (None, processing_message, make_progress_bar_html(0, processing_message))))

            H_end, W_end, C_end = end_image.shape
            end_image_np = resize_and_center_crop(end_image, target_width=width, target_height=height)

            # We'll save the end frame with metadata after determining the seed
            # Store the end image for now
            end_frame_path = os.path.join(outputs_folder, f'{job_id}_end.png')
            temp_end_image = end_image_np.copy()

            end_image_pt = torch.from_numpy(end_image_np).float() / 127.5 - 1
            end_image_pt = end_image_pt.permute(2, 0, 1)[None, :, None]

        # VAE encoding
        stream.output_queue.push(('progress', (None, '', make_progress_bar_html(0, 'VAE encoding ...'))))

        if not high_vram:
            load_model_as_complete(vae, target_device=gpu)

        start_latent = vae_encode(input_image_pt, vae)

        if has_end_image:
            end_latent = vae_encode(end_image_pt, vae)

        # CLIP Vision
        stream.output_queue.push(('progress', (None, '', make_progress_bar_html(0, 'CLIP Vision encoding ...'))))

        if not high_vram:
            load_model_as_complete(image_encoder, target_device=gpu)

        image_encoder_output = hf_clip_vision_encode(input_image_np, feature_extractor, image_encoder)
        image_encoder_last_hidden_state = image_encoder_output.last_hidden_state

        if has_end_image:
            end_image_encoder_output = hf_clip_vision_encode(end_image_np, feature_extractor, image_encoder)
            end_image_encoder_last_hidden_state = end_image_encoder_output.last_hidden_state
            # Combine both image embeddings or use a weighted approach
            image_encoder_last_hidden_state = (image_encoder_last_hidden_state + end_image_encoder_last_hidden_state) / 2

        # Dtype
        llama_vec = llama_vec.to(transformer.dtype)
        llama_vec_n = llama_vec_n.to(transformer.dtype)
        clip_l_pooler = clip_l_pooler.to(transformer.dtype)
        clip_l_pooler_n = clip_l_pooler_n.to(transformer.dtype)
        image_encoder_last_hidden_state = image_encoder_last_hidden_state.to(transformer.dtype)

        # Sampling
        processing_message = f"Step 3/{total_process_steps}: Preparing for sampling"
        stream.output_queue.push(('progress', (None, processing_message, make_progress_bar_html(0, processing_message))))

        # If seed is -1, use a random seed
        random_seed = None
        if seed == -1:
            random_seed = random.randint(0, 2**32 - 1)
            print(f"Random seed: {random_seed}")
            rnd = torch.Generator("cpu").manual_seed(random_seed)
            # Update progress bar to show the random seed that was used
            stream.output_queue.push(('progress', (None, f'Using random seed: {random_seed}', make_progress_bar_html(0, f'Start sampling with random seed {random_seed} ...'))))

            # Store the start frame metadata for later use when the video is complete
            start_frame_metadata = {
                "prompt": prompt,
                "negative_prompt": n_prompt,
                "seed": random_seed,
                "steps": steps,
                "cfg_scale": cfg,
                "distilled_cfg_scale": gs,
                "cfg_rescale": rs,
                "use_teacache": use_teacache,
                "total_video_length": f"{total_second_length} seconds",
                "total_sections": total_latent_sections,
                "frame_type": "start_frame",
                "job_id": job_id
            }

            # We'll save the start frame only when the video is complete
            # For now, just store the path and metadata
            start_frame_path = os.path.join(outputs_folder, f'{job_id}_start.png')

            # If we have an end frame, store its metadata too
            if has_end_image:
                end_frame_metadata = {
                    "prompt": prompt,
                    "negative_prompt": n_prompt,
                    "seed": random_seed,
                    "steps": steps,
                    "cfg_scale": cfg,
                    "distilled_cfg_scale": gs,
                    "cfg_rescale": rs,
                    "use_teacache": use_teacache,
                    "total_video_length": f"{total_second_length} seconds",
                    "total_sections": total_latent_sections,
                    "frame_type": "end_frame",
                    "job_id": job_id
                }

                # We'll save the end frame only when the video is complete
                # For now, just store the path
                end_frame_path = os.path.join(outputs_folder, f'{job_id}_end.png')
        else:
            rnd = torch.Generator("cpu").manual_seed(seed)

            # Store the start frame metadata for later use when the video is complete
            start_frame_metadata = {
                "prompt": prompt,
                "negative_prompt": n_prompt,
                "seed": seed,
                "steps": steps,
                "cfg_scale": cfg,
                "distilled_cfg_scale": gs,
                "cfg_rescale": rs,
                "use_teacache": use_teacache,
                "total_video_length": f"{total_second_length} seconds",
                "total_sections": total_latent_sections,
                "frame_type": "start_frame",
                "job_id": job_id
            }

            # We'll save the start frame only when the video is complete
            # For now, just store the path and metadata
            start_frame_path = os.path.join(outputs_folder, f'{job_id}_start.png')

            # If we have an end frame, store its metadata too
            if has_end_image:
                end_frame_metadata = {
                    "prompt": prompt,
                    "negative_prompt": n_prompt,
                    "seed": seed,
                    "steps": steps,
                    "cfg_scale": cfg,
                    "distilled_cfg_scale": gs,
                    "cfg_rescale": rs,
                    "use_teacache": use_teacache,
                    "total_video_length": f"{total_second_length} seconds",
                    "total_sections": total_latent_sections,
                    "frame_type": "end_frame",
                    "job_id": job_id
                }

                # We'll save the end frame only when the video is complete
                # For now, just store the path
                end_frame_path = os.path.join(outputs_folder, f'{job_id}_end.png')

        num_frames = latent_window_size * 4 - 3

        history_latents = torch.zeros(size=(1, 16, 1 + 2 + 16, height // 8, width // 8), dtype=torch.float32).cpu()
        history_pixels = None
        total_generated_latent_frames = 0

        # 将迭代器转换为列表
        latent_paddings = list(reversed(range(total_latent_sections)))

        if total_latent_sections > 4:
            # In theory the latent_paddings should follow the above sequence, but it seems that duplicating some
            # items looks better than expanding it when total_latent_sections > 4
            # One can try to remove below trick and just
            # use `latent_paddings = list(reversed(range(total_latent_sections)))` to compare
            latent_paddings = [3] + [2] * (total_latent_sections - 3) + [1, 0]

            # Print the actual latent_paddings for debugging
            print(f"Using {total_latent_sections} sections with paddings: {latent_paddings}")

        for section_idx, latent_padding in enumerate(latent_paddings):
            is_last_section = latent_padding == 0
            is_first_section = latent_padding == latent_paddings[0]
            latent_padding_size = latent_padding * latent_window_size

            # Check if the user has requested to stop generation
            if stream.input_queue.top() == 'end':
                print("End signal detected in section loop, stopping generation...")
                stream.output_queue.push(('end', None))
                raise GracefulExit('Generation stopped by user')

            # Check if we've been in stopping state for too long (more than 30 seconds)
            if generation_stopping and generation_stop_time and (time.time() - generation_stop_time > 30):
                print("Generation has been in stopping state for too long, forcing exit...")
                stream.output_queue.push(('end', None))
                raise GracefulExit('Generation stop timeout')

            # Update the section number for progress tracking
            current_section = section_idx
            displayed_section = section_idx  # Update displayed section at the start of each section
            in_transition = False  # Reset transition flag

            # Show section processing message
            # Calculate the current step (3 processing steps + current section)
            current_step = 3 + section_idx + 1

            # Make sure we don't exceed the total steps
            current_step = min(current_step, total_process_steps)

            processing_message = f"Step {current_step}/{total_process_steps}: Processing section {section_idx + 1}/{total_latent_sections}"
            stream.output_queue.push(('progress', (None, processing_message, make_progress_bar_html(int((section_idx / total_latent_sections) * 100), processing_message))))

            # Only print this in debug mode or comment out to reduce console output
            # print(f'latent_padding_size = {latent_padding_size}, is_last_section = {is_last_section}, is_first_section = {is_first_section}')

            indices = torch.arange(0, sum([1, latent_padding_size, latent_window_size, 1, 2, 16])).unsqueeze(0)
            clean_latent_indices_pre, blank_indices, latent_indices, clean_latent_indices_post, clean_latent_2x_indices, clean_latent_4x_indices = indices.split([1, latent_padding_size, latent_window_size, 1, 2, 16], dim=1)
            clean_latent_indices = torch.cat([clean_latent_indices_pre, clean_latent_indices_post], dim=1)

            clean_latents_pre = start_latent.to(history_latents)
            clean_latents_post, clean_latents_2x, clean_latents_4x = history_latents[:, :, :1 + 2 + 16, :, :].split([1, 2, 16], dim=2)
            clean_latents = torch.cat([clean_latents_pre, clean_latents_post], dim=2)

            # Use end image latent for the first section if provided
            if has_end_image and is_first_section:
                clean_latents_post = end_latent.to(history_latents)
                clean_latents = torch.cat([clean_latents_pre, clean_latents_post], dim=2)

            if not high_vram:
                unload_complete_models()
                move_model_to_device_with_memory_preservation(transformer, target_device=gpu, preserved_memory_gb=gpu_memory_preservation)

            if use_teacache:
                transformer.initialize_teacache(enable_teacache=True, num_steps=steps)
            else:
                transformer.initialize_teacache(enable_teacache=False)

            def callback(d):
                global generation_stopping, generation_stop_time

                preview = d['denoised']
                preview = vae_decode_fake(preview)

                preview = (preview * 255.0).detach().cpu().numpy().clip(0, 255).astype(np.uint8)

                # Store the original shape for individual frame extraction
                original_shape = preview.shape  # Should be [b, c, t, h, w]

                # Rearrange for display
                preview = einops.rearrange(preview, 'b c t h w -> (b h) (t w) c')

                # Check if the user has requested to stop generation
                if stream.input_queue.top() == 'end':
                    print("End signal detected in callback, stopping generation...")
                    stream.output_queue.push(('end', None))
                    raise GracefulExit('Generation stopped by user')

                # Check if we've been in stopping state for too long (more than 30 seconds)
                if generation_stopping and generation_stop_time and (time.time() - generation_stop_time > 30):
                    print("Generation has been in stopping state for too long, forcing exit...")
                    stream.output_queue.push(('end', None))
                    raise GracefulExit('Generation stop timeout')

                # Only update the UI with the preview image
                # The progress bar is updated by the progress_callback
                stream.output_queue.push(('progress', (preview, None, None)))
                return

            generated_latents = sample_hunyuan(
                transformer=transformer,
                sampler='unipc',
                width=width,
                height=height,
                frames=num_frames,
                real_guidance_scale=cfg,
                distilled_guidance_scale=gs,
                guidance_rescale=rs,
                # shift=3.0,
                num_inference_steps=steps,
                generator=rnd,
                prompt_embeds=llama_vec,
                prompt_embeds_mask=llama_attention_mask,
                prompt_poolers=clip_l_pooler,
                negative_prompt_embeds=llama_vec_n,
                negative_prompt_embeds_mask=llama_attention_mask_n,
                negative_prompt_poolers=clip_l_pooler_n,
                device=gpu,
                dtype=torch.bfloat16,
                image_embeddings=image_encoder_last_hidden_state,
                latent_indices=latent_indices,
                clean_latents=clean_latents,
                clean_latent_indices=clean_latent_indices,
                clean_latents_2x=clean_latents_2x,
                clean_latent_2x_indices=clean_latent_2x_indices,
                clean_latents_4x=clean_latents_4x,
                clean_latent_4x_indices=clean_latent_4x_indices,
                callback=callback,
            )

            if is_last_section:
                generated_latents = torch.cat([start_latent.to(generated_latents), generated_latents], dim=2)

            total_generated_latent_frames += int(generated_latents.shape[2])
            history_latents = torch.cat([generated_latents.to(history_latents), history_latents], dim=2)

            if not high_vram:
                offload_model_from_device_for_memory_preservation(transformer, target_device=gpu, preserved_memory_gb=8)
                load_model_as_complete(vae, target_device=gpu)

            real_history_latents = history_latents[:, :, :total_generated_latent_frames, :, :]

            if history_pixels is None:
                history_pixels = vae_decode(real_history_latents, vae).cpu()
            else:
                section_latent_frames = (latent_window_size * 2 + 1) if is_last_section else (latent_window_size * 2)
                overlapped_frames = latent_window_size * 4 - 3

                current_pixels = vae_decode(real_history_latents[:, :, :section_latent_frames], vae).cpu()
                history_pixels = soft_append_bcthw(current_pixels, history_pixels, overlapped_frames)

            if not high_vram:
                unload_complete_models()

            # Include the random seed in the filename if one was used
            if seed == -1 and random_seed is not None:
                output_filename = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4')
            else:
                output_filename = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}.mp4')

            save_bcthw_as_mp4(history_pixels, output_filename, fps=30, crf=mp4_crf)

            # Only print this in debug mode or comment out to reduce console output
            # print(f'Decoded. Current latent shape {real_history_latents.shape}; pixel shape {history_pixels.shape}')

            # Push the file update to the UI with a timestamp to force refresh
            timestamp = int(time.time())
            print(f"Generated video section {current_section+1}/{total_latent_sections}: {output_filename}")

            # Add a small delay to ensure the file is fully written before updating the UI
            time.sleep(0.1)

            # Push the update to the UI
            stream.output_queue.push(('file', f"{output_filename}?t={timestamp}"))

            # Also update the progress bar with a message about the new video
            section_message = f"Section {current_section+1}/{total_latent_sections} complete<br>Video updated"
            stream.output_queue.push(('progress', (None, section_message, make_progress_bar_html(int(((current_section+1) / total_latent_sections) * 100), section_message))))

            if is_last_section:
                print("***** LAST SECTION DETECTED *****")
                # Wait a moment to ensure the video file is fully written
                print("Waiting for video file to be fully written...")
                # Use time module that's already imported at the top of the file
                time.sleep(3)  # Wait 3 seconds

                # Extract and save the last frame as a PNG with "_end" suffix
                try:
                    # Add debug print to see if this code is being executed
                    print(f"Attempting to save end frame for job {job_id}")

                    # List all video files in the output folder for debugging
                    print("Listing all video files in the output folder:")
                    for file in os.listdir(outputs_folder):
                        if file.endswith('.mp4') and file.startswith(job_id):
                            print(f"  - {file}")

                    # First check if the video file exists
                    # Use the most recent video file that was created
                    if seed == -1 and random_seed is not None:
                        video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4')
                    else:
                        video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}.mp4')
                    print(f"Looking for video file at: {video_path}")

                    if not os.path.exists(video_path):
                        print(f"Video file not found at {video_path}")
                        # Try to find any video file with this job ID
                        print("Trying to find any video file with this job ID...")
                        found_video = False
                        for file in os.listdir(outputs_folder):
                            if file.endswith('.mp4') and file.startswith(job_id):
                                video_path = os.path.join(outputs_folder, file)
                                print(f"Found alternative video file: {video_path}")
                                found_video = True
                                break

                        if not found_video:
                            print("No alternative video file found. Waiting for video to be saved...")
                            # If the video file doesn't exist yet, wait a moment for it to be saved
                            # Use time module that's already imported at the top of the file
                            time.sleep(2)  # Wait 2 seconds

                    if os.path.exists(video_path):
                        print(f"Video file found at {video_path}")
                        # Extract the last frame directly from the saved video file
                        # Use cv2 module that will be imported at the top of the file

                        # Open the video file
                        video = cv2.VideoCapture(video_path)

                        # Get the total number of frames
                        total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
                        print(f"Video has {total_frames} frames")

                        if total_frames > 0:
                            # Set the position to the last frame
                            video.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)

                            # Read the last frame
                            success, last_frame = video.read()

                            if success:
                                print(f"Successfully read last frame from video")
                                # Convert from BGR to RGB (OpenCV uses BGR by default)
                                last_frame_rgb = cv2.cvtColor(last_frame, cv2.COLOR_BGR2RGB)

                                # Create the end frame filename
                                end_frame_path = os.path.join(outputs_folder, f'{job_id}_end.png')

                                # Create metadata for the end frame
                                metadata_dict = {
                                    'prompt': prompt,
                                    'negative_prompt': n_prompt,
                                    'seed': random_seed if seed == -1 else seed,
                                    'steps': steps,
                                    'cfg_scale': cfg,
                                    'distilled_cfg_scale': gs,
                                    'cfg_rescale': rs,
                                    'use_teacache': use_teacache,
                                    'total_video_length': f"{total_second_length} seconds",
                                    'total_sections': total_latent_sections,
                                    'frame_type': 'end_frame',
                                    'job_id': job_id,
                                    'extracted_from_video': True
                                }

                                # Save the end frame with metadata
                                save_image_with_metadata(last_frame_rgb, end_frame_path, metadata_dict)

                                print(f"Saved end frame to {end_frame_path} (extracted from video)")

                                # Now that the video is complete, save the start frame with metadata
                                start_frame_metadata = {
                                    'prompt': prompt,
                                    'negative_prompt': n_prompt,
                                    'seed': random_seed if seed == -1 else seed,
                                    'steps': steps,
                                    'cfg_scale': cfg,
                                    'distilled_cfg_scale': gs,
                                    'cfg_rescale': rs,
                                    'use_teacache': use_teacache,
                                    'total_video_length': f"{total_second_length} seconds",
                                    'total_sections': total_latent_sections,
                                    'frame_type': 'start_frame',
                                    'job_id': job_id
                                }

                                # Save the start frame with metadata
                                start_frame_path = os.path.join(outputs_folder, f'{job_id}_start.png')
                                save_image_with_metadata(temp_start_image, start_frame_path, start_frame_metadata)
                                print(f"Saved start frame to {start_frame_path}")

                                # Show completion message with both frames info
                                completion_message = f"Video generation complete<br>{total_generated_latent_frames * 4 - 3} frames, {(total_generated_latent_frames * 4 - 3) / 30:.2f} seconds<br>Start and end frames saved"

                                # Close the video file
                                video.release()
                            else:
                                print("Failed to read the last frame from the video")
                                video.release()
                                raise Exception("Failed to read the last frame from the video")
                        else:
                            print("Video file appears to be empty")
                            video.release()
                            raise Exception("Video file appears to be empty")
                    else:
                        print(f"Video file still not found at {video_path} after waiting")
                        print("No video file found. Falling back to latent method immediately.")
                        # Don't raise an exception, just continue to the latent fallback

                    # If we get here without extracting from the video, fall back to latent method
                    if 'completion_message' not in locals():
                        print("Falling back to latent method for end frame")
                        # Get the last frame from the latents
                        last_latent_frame = history_latents[0, :, -1:, :, :]  # Shape should be [B, C, 1, H, W]

                        # Decode it using the VAE to get the pixel values
                        with torch.no_grad():
                            # Move to the right device
                            last_latent_frame = last_latent_frame.to(vae.device)

                            # Decode the latent to get the pixel values
                            decoded_frame = vae_decode(vae, last_latent_frame)  # Should return [B, C, 1, H, W]

                            # Extract the single frame and convert to numpy
                            # The decoded frame is in range [-1, 1], so we need to convert to [0, 255]
                            decoded_frame = decoded_frame[0, :, 0]  # Shape: [C, H, W]
                            decoded_frame_np = decoded_frame.permute(1, 2, 0).cpu().numpy()  # Convert to [H, W, C]
                            decoded_frame_np = ((decoded_frame_np + 1) / 2 * 255).clip(0, 255).astype(np.uint8)

                        # Create the end frame filename
                        end_frame_path = os.path.join(outputs_folder, f'{job_id}_end.png')

                        # Create metadata for the end frame
                        metadata_dict = {
                            'prompt': prompt,
                            'negative_prompt': n_prompt,
                            'seed': random_seed if seed == -1 else seed,
                            'steps': steps,
                            'cfg_scale': cfg,
                            'distilled_cfg_scale': gs,
                            'cfg_rescale': rs,
                            'use_teacache': use_teacache,
                            'total_video_length': f"{total_second_length} seconds",
                            'total_sections': total_latent_sections,
                            'frame_type': 'end_frame',
                            'job_id': job_id,
                            'extracted_from_latents': True
                        }

                        # Save the end frame with metadata
                        save_image_with_metadata(decoded_frame_np, end_frame_path, metadata_dict)

                        print(f"Saved end frame to {end_frame_path} (from latents)")

                        # Now that the video is complete, save the start frame with metadata
                        start_frame_metadata = {
                            'prompt': prompt,
                            'negative_prompt': n_prompt,
                            'seed': random_seed if seed == -1 else seed,
                            'steps': steps,
                            'cfg_scale': cfg,
                            'distilled_cfg_scale': gs,
                            'cfg_rescale': rs,
                            'use_teacache': use_teacache,
                            'total_video_length': f"{total_second_length} seconds",
                            'total_sections': total_latent_sections,
                            'frame_type': 'start_frame',
                            'job_id': job_id
                        }

                        # Save the start frame with metadata
                        start_frame_path = os.path.join(outputs_folder, f'{job_id}_start.png')
                        save_image_with_metadata(temp_start_image, start_frame_path, start_frame_metadata)
                        print(f"Saved start frame to {start_frame_path}")

                        # Show completion message with both frames info
                        completion_message = f"Video generation complete<br>{total_generated_latent_frames * 4 - 3} frames, {(total_generated_latent_frames * 4 - 3) / 30:.2f} seconds<br>Start and end frames saved"
                except Exception as e:
                    print(f"Error saving end frame: {e}")
                    # Show regular completion message if there was an error
                    completion_message = f"Video generation complete<br>{total_generated_latent_frames * 4 - 3} frames, {(total_generated_latent_frames * 4 - 3) / 30:.2f} seconds"

                stream.output_queue.push(('progress', (None, completion_message, make_progress_bar_html(100, completion_message))))
                break
    except GracefulExit:
        print("Generation stopped by user")

        # Try to save the last rendered frame if we have any frames
        # First check if the video file exists
        # Use the most recent video file that was created
        if seed == -1 and random_seed is not None:
            video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4')
        else:
            video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}.mp4')
        print(f"Looking for video file at: {video_path}")
        if os.path.exists(video_path):
            try:
                # Extract the last frame directly from the saved video file
                # Use cv2 module that will be imported at the top of the file

                # Open the video file
                video = cv2.VideoCapture(video_path)

                # Get the total number of frames
                total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))

                if total_frames > 0:
                    # Set the position to the last frame
                    video.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)

                    # Read the last frame
                    success, last_frame = video.read()

                    if success:
                        # Convert from BGR to RGB (OpenCV uses BGR by default)
                        last_frame_rgb = cv2.cvtColor(last_frame, cv2.COLOR_BGR2RGB)

                        # Create the cancelled frame filename
                        cancelled_frame_path = os.path.join(outputs_folder, f'{job_id}_cancelled.png')

                        # Create metadata for the cancelled frame
                        metadata_dict = {
                            'prompt': prompt,
                            'negative_prompt': n_prompt,
                            'seed': random_seed if seed == -1 else seed,
                            'steps': steps,
                            'cfg_scale': cfg,
                            'distilled_cfg_scale': gs,
                            'cfg_rescale': rs,
                            'use_teacache': use_teacache,
                            'total_video_length': f"{total_second_length} seconds",
                            'total_sections': total_latent_sections,
                            'frame_type': 'cancelled_frame',
                            'job_id': job_id,
                            'completed_sections': current_section,
                            'total_frames_generated': total_generated_latent_frames * 4 - 3 if 'total_generated_latent_frames' in locals() else 0,
                            'extracted_from_video': True
                        }

                        # Save the cancelled frame with metadata
                        save_image_with_metadata(last_frame_rgb, cancelled_frame_path, metadata_dict)

                        print(f"Saved cancelled frame to {cancelled_frame_path} (extracted from video)")

                        # Show message with cancelled frame info
                        cancel_message = f"Generation stopped by user<br>Last frame saved as {os.path.basename(cancelled_frame_path)}"
                        stream.output_queue.push(('progress', (None, cancel_message, make_progress_bar_html(100, cancel_message))))

                        # Close the video file
                        video.release()
                        return

                # Close the video file
                video.release()
            except Exception as e:
                print(f"Error extracting frame from video: {e}")
                # Fall back to the latent method if video extraction fails

        # If we couldn't extract from the video (either it doesn't exist or there was an error),
        # fall back to the latent method
        if 'history_latents' in locals() and history_latents is not None and history_latents.shape[2] > 0:
            try:
                # Get the last frame directly from the decoded pixels
                # This ensures we get the exact same image that appears in the video

                # First, get the last latent frame
                last_latent_frame = history_latents[0, :, -1:, :, :]  # Shape should be [B, C, 1, H, W]

                # Decode it using the VAE to get the pixel values
                with torch.no_grad():
                    # Move to the right device
                    last_latent_frame = last_latent_frame.to(vae.device)

                    # Decode the latent to get the pixel values
                    decoded_frame = vae_decode(vae, last_latent_frame)  # Should return [B, C, 1, H, W]

                    # Extract the single frame and convert to numpy
                    # The decoded frame is in range [-1, 1], so we need to convert to [0, 255]
                    decoded_frame = decoded_frame[0, :, 0]  # Shape: [C, H, W]
                    decoded_frame_np = decoded_frame.permute(1, 2, 0).cpu().numpy()  # Convert to [H, W, C]
                    decoded_frame_np = ((decoded_frame_np + 1) / 2 * 255).clip(0, 255).astype(np.uint8)

                # Create the cancelled frame filename
                cancelled_frame_path = os.path.join(outputs_folder, f'{job_id}_cancelled.png')

                # Create metadata for the cancelled frame
                metadata_dict = {
                    'prompt': prompt,
                    'negative_prompt': n_prompt,
                    'seed': random_seed if seed == -1 else seed,
                    'steps': steps,
                    'cfg_scale': cfg,
                    'distilled_cfg_scale': gs,
                    'cfg_rescale': rs,
                    'use_teacache': use_teacache,
                    'total_video_length': f"{total_second_length} seconds",
                    'total_sections': total_latent_sections,
                    'frame_type': 'cancelled_frame',
                    'job_id': job_id,
                    'completed_sections': current_section,
                    'total_frames_generated': total_generated_latent_frames * 4 - 3 if 'total_generated_latent_frames' in locals() else 0,
                    'extracted_from_latents': True
                }

                # Save the cancelled frame with metadata
                save_image_with_metadata(decoded_frame_np, cancelled_frame_path, metadata_dict)

                print(f"Saved cancelled frame to {cancelled_frame_path} (from latents)")

                # Show message with cancelled frame info
                cancel_message = f"Generation stopped by user<br>Last frame saved as {os.path.basename(cancelled_frame_path)}"
                stream.output_queue.push(('progress', (None, cancel_message, make_progress_bar_html(100, cancel_message))))
            except Exception as e:
                print(f"Error saving cancelled frame: {e}")
                # Show regular cancellation message if there was an error
                stream.output_queue.push(('progress', (None, 'Generation stopped by user', make_progress_bar_html(100, 'Generation stopped by user'))))
        else:
            # No frames were generated yet
            stream.output_queue.push(('progress', (None, 'Generation stopped by user (no frames were generated)', make_progress_bar_html(100, 'Generation stopped by user'))))

        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )
    except Exception as e:
        # For other exceptions, print the traceback
        traceback.print_exc()

        # Try to save the last rendered frame if we have any frames
        # First check if the video file exists
        # Use the most recent video file that was created
        if seed == -1 and random_seed is not None:
            video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}_seed{random_seed}.mp4')
        else:
            video_path = os.path.join(outputs_folder, f'{job_id}_{total_generated_latent_frames}.mp4')
        print(f"Looking for video file at: {video_path}")
        if os.path.exists(video_path):
            try:
                # Extract the last frame directly from the saved video file
                # Use cv2 module that will be imported at the top of the file

                # Open the video file
                video = cv2.VideoCapture(video_path)

                # Get the total number of frames
                total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))

                if total_frames > 0:
                    # Set the position to the last frame
                    video.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)

                    # Read the last frame
                    success, last_frame = video.read()

                    if success:
                        # Convert from BGR to RGB (OpenCV uses BGR by default)
                        last_frame_rgb = cv2.cvtColor(last_frame, cv2.COLOR_BGR2RGB)

                        # Create the error frame filename
                        error_frame_path = os.path.join(outputs_folder, f'{job_id}_error.png')

                        # Create metadata for the error frame
                        metadata_dict = {
                            'prompt': prompt,
                            'negative_prompt': n_prompt,
                            'seed': random_seed if seed == -1 else seed,
                            'steps': steps,
                            'cfg_scale': cfg,
                            'distilled_cfg_scale': gs,
                            'cfg_rescale': rs,
                            'use_teacache': use_teacache,
                            'total_video_length': f"{total_second_length} seconds",
                            'total_sections': total_latent_sections,
                            'frame_type': 'error_frame',
                            'job_id': job_id,
                            'completed_sections': current_section,
                            'total_frames_generated': total_generated_latent_frames * 4 - 3 if 'total_generated_latent_frames' in locals() else 0,
                            'error': str(e),
                            'extracted_from_video': True
                        }

                        # Save the error frame with metadata
                        save_image_with_metadata(last_frame_rgb, error_frame_path, metadata_dict)

                        print(f"Saved error frame to {error_frame_path} (extracted from video)")

                        # Show message with error frame info
                        error_message = f"Error during generation: {str(e)}<br>Last frame saved as {os.path.basename(error_frame_path)}"
                        stream.output_queue.push(('progress', (None, error_message, make_progress_bar_html(100, error_message))))

                        # Close the video file
                        video.release()
                        return

                # Close the video file
                video.release()
            except Exception as save_error:
                print(f"Error extracting frame from video: {save_error}")
                # Fall back to the latent method if video extraction fails

        # If we couldn't extract from the video (either it doesn't exist or there was an error),
        # fall back to the latent method
        if 'history_latents' in locals() and history_latents is not None and history_latents.shape[2] > 0:
            try:
                # Get the last frame directly from the decoded pixels
                # This ensures we get the exact same image that appears in the video

                # First, get the last latent frame
                last_latent_frame = history_latents[0, :, -1:, :, :]  # Shape should be [B, C, 1, H, W]

                # Decode it using the VAE to get the pixel values
                with torch.no_grad():
                    # Move to the right device
                    last_latent_frame = last_latent_frame.to(vae.device)

                    # Decode the latent to get the pixel values
                    decoded_frame = vae_decode(vae, last_latent_frame)  # Should return [B, C, 1, H, W]

                    # Extract the single frame and convert to numpy
                    # The decoded frame is in range [-1, 1], so we need to convert to [0, 255]
                    decoded_frame = decoded_frame[0, :, 0]  # Shape: [C, H, W]
                    decoded_frame_np = decoded_frame.permute(1, 2, 0).cpu().numpy()  # Convert to [H, W, C]
                    decoded_frame_np = ((decoded_frame_np + 1) / 2 * 255).clip(0, 255).astype(np.uint8)

                # Create the error frame filename
                error_frame_path = os.path.join(outputs_folder, f'{job_id}_error.png')

                # Create metadata for the error frame
                metadata_dict = {
                    'prompt': prompt,
                    'negative_prompt': n_prompt,
                    'seed': random_seed if seed == -1 else seed,
                    'steps': steps,
                    'cfg_scale': cfg,
                    'distilled_cfg_scale': gs,
                    'cfg_rescale': rs,
                    'use_teacache': use_teacache,
                    'total_video_length': f"{total_second_length} seconds",
                    'total_sections': total_latent_sections,
                    'frame_type': 'error_frame',
                    'job_id': job_id,
                    'completed_sections': current_section,
                    'total_frames_generated': total_generated_latent_frames * 4 - 3 if 'total_generated_latent_frames' in locals() else 0,
                    'error': str(e),
                    'extracted_from_latents': True
                }

                # Save the error frame with metadata
                save_image_with_metadata(decoded_frame_np, error_frame_path, metadata_dict)

                print(f"Saved error frame to {error_frame_path} (from latents)")

                # Show message with error frame info
                error_message = f"Error during generation: {str(e)}<br>Last frame saved as {os.path.basename(error_frame_path)}"
                stream.output_queue.push(('progress', (None, error_message, make_progress_bar_html(100, error_message))))
            except Exception as save_error:
                print(f"Error saving error frame: {save_error}")
                # Show regular error message if there was an error saving the frame
                stream.output_queue.push(('progress', (None, f'Error during generation: {str(e)}', make_progress_bar_html(100, f'Error: {str(e)}'))))
        else:
            # No frames were generated yet
            stream.output_queue.push(('progress', (None, f'Error during generation: {str(e)} (no frames were generated)', make_progress_bar_html(100, f'Error: {str(e)}'))))

        if not high_vram:
            unload_complete_models(
                text_encoder, text_encoder_2, image_encoder, vae, transformer
            )

    # Make sure to reset the generation state flags when the worker exits
    generation_in_progress = False
    generation_stopping = False
    generation_stop_time = None

    # Signal the UI that generation has ended
    stream.output_queue.push(('end', None))
    return


def process(input_image, end_image, prompt, n_prompt, seed, total_second_length, latent_window_size, steps, cfg, gs, rs, gpu_memory_preservation, use_teacache, mp4_crf):
    global stream, generation_in_progress, generation_stopping, generation_stop_time
    # Import os module at the beginning of the function to ensure it's available
    import os
    import time

    assert input_image is not None, 'No input image!'

    # Check if generation is already in progress or stopping
    if generation_in_progress:
        if generation_stopping:
            # If generation is in the process of stopping, show a message and don't start a new one
            stop_duration = time.time() - generation_stop_time if generation_stop_time else 0
            message = "Previous generation is still shutting down. Please wait..."
            if stop_duration > 5:  # If it's taking too long, offer a hint
                message += "<br>This is taking longer than expected. You may need to refresh the page if it doesn't respond."
            yield gr.update(), gr.update(), message, make_progress_bar_html(100, message), gr.update(), gr.update(), gr.update()
            return
        else:
            # If generation is running but not stopping, don't start a new one
            message = "Generation is already in progress. Please end the current generation first."
            yield gr.update(), gr.update(), message, make_progress_bar_html(100, message), gr.update(), gr.update(), gr.update()
            return

    # Set generation state to in progress
    generation_in_progress = True
    generation_stopping = False
    generation_stop_time = None

    # Update UI to show generation is starting
    yield None, None, 'Starting generation...', make_progress_bar_html(0, 'Initializing...'), gr.update(interactive=False), gr.update(interactive=True), None

    # Create a new stream for this generation
    stream = AsyncStream()

    # Create a list to store latent previews for animation
    latent_previews = []
    current_section = 0
    latent_animation_path = os.path.join(temp_folder, "latent_animation.mp4")
    # Create a flag to track if we've processed a section already
    processed_sections = set()

    # Start the worker in a separate thread
    async_run(worker, input_image, end_image, prompt, n_prompt, seed, total_second_length, latent_window_size, steps, cfg, gs, rs, gpu_memory_preservation, use_teacache, mp4_crf)

    output_filename = None

    while True:
        flag, data = stream.output_queue.next()

        if flag == 'file':
            output_filename = data
            # Strip any query parameters (like ?t=timestamp) before yielding
            clean_filename = output_filename.split('?')[0]
            print(f"Updating video to: {clean_filename}")

            # Force a complete refresh of the video element
            yield gr.update(value=clean_filename), gr.update(), gr.update(), gr.update(), gr.update(interactive=False), gr.update(interactive=True), gr.update()

        if flag == 'progress':
            try:
                preview, desc, html = data

                # Process received progress update

                # Handle the case where preview is None
                preview_update = gr.update(visible=True, value=preview) if preview is not None else gr.update()

                # If we have a preview image, store it for animation
                if preview is not None:
                    # Create a unique key for this preview
                    section_key = f"preview_{len(latent_previews)}"

                    # Only process each preview once
                    if section_key not in processed_sections:
                        print(f"Processing new preview for animation: {section_key}")
                        processed_sections.add(section_key)

                        # Store the preview for animation
                        latent_previews.append(preview)

                        # Create a temporary folder for the frames
                        temp_dir = os.path.join(temp_folder, "latent_frames")
                        os.makedirs(temp_dir, exist_ok=True)

                        # Extract individual frames from the latest preview
                        # Each preview has 9 frames side by side
                        new_frames = []

                        # Get the height and width of the preview
                        h, w, _ = preview.shape

                        # Calculate the width of each individual frame
                        frame_width = w // 9

                        # Extract each individual frame from the latest preview
                        for j in range(9):
                            frame = preview[:, j*frame_width:(j+1)*frame_width, :]
                            new_frames.append(frame)

                            # Save the frame as an image with a sequential number
                            frame_count = (len(latent_previews) - 1) * 9 + j + 1
                            frame_path = os.path.join(temp_dir, f"frame_{frame_count:04d}.png")
                            cv2.imwrite(frame_path, cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

                        # Create or append to the video file
                        # Use OpenCV to create a video
                        if len(new_frames) > 0:
                            h, w, _ = new_frames[0].shape
                            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                            # The latent preview shows 9 frames per section, while the final video has many more frames
                            # Each section typically produces 36 frames in the final video (9 latent frames × 4)
                            # So we use 15 fps (50% of final 30 fps) to approximate the real-time playback speed
                            fps = 15.0

                            # Create a new animation file that only shows the latest batch of latent frames
                            # Use a timestamp to ensure uniqueness and avoid file access conflicts
                            import time
                            timestamp = int(time.time() * 1000)
                            new_animation_path = os.path.join(temp_folder, f"latest_latent_preview_{timestamp}.mp4")
                            print(f"Creating latest latent preview: {new_animation_path}")

                            # Create a video writer for the new file
                            video_writer = cv2.VideoWriter(new_animation_path, fourcc, fps, (w, h))

                            # Only include the frames from the current preview
                            for frame in new_frames:
                                video_writer.write(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

                            # Release the video writer
                            video_writer.release()

                            # Update the animation path to the new file
                            latent_animation_path = new_animation_path

                            # Update the UI with the animation
                            animation_update = gr.update(visible=True, value=latent_animation_path)
                        else:
                            animation_update = gr.update()
                    else:
                        # We've already processed this section or it's not a new section
                        animation_update = gr.update()
                else:
                    animation_update = gr.update()

                # If html is None, don't update the progress bar
                # This happens when we're just updating the preview image
                if html is None:
                    # If description is also None, don't update the description either
                    if desc is None:
                        yield gr.update(), preview_update, gr.update(), gr.update(), gr.update(interactive=False), gr.update(interactive=True), animation_update
                    else:
                        yield gr.update(), preview_update, desc, gr.update(), gr.update(interactive=False), gr.update(interactive=True), animation_update
                else:
                    # If we have HTML, update both the progress bar and description
                    yield gr.update(), preview_update, desc if desc else gr.update(), html, gr.update(interactive=False), gr.update(interactive=True), animation_update
            except Exception as e:
                print(f"Error updating UI: {e}")
                # Provide a safe fallback to prevent crashes
                yield gr.update(), gr.update(), gr.update(), gr.update(), gr.update(interactive=False), gr.update(interactive=True), gr.update()

        if flag == 'end':
            # Update generation state
            generation_in_progress = False
            generation_stopping = False
            generation_stop_time = None

            # Make sure we have the latest output_filename
            if output_filename:
                # Strip any query parameters
                clean_filename = output_filename.split('?')[0]
                print(f"Final video: {clean_filename}")

                # Force one final refresh of the video element
                yield gr.update(value=clean_filename), gr.update(visible=False), gr.update(), '', gr.update(interactive=True), gr.update(interactive=False), gr.update(visible=False)
            else:
                yield gr.update(), gr.update(visible=False), gr.update(), '', gr.update(interactive=True), gr.update(interactive=False), gr.update(visible=False)
            break


def end_process():
    global generation_stopping, generation_stop_time, stream

    print("End process function called")

    # Check if stream is initialized
    if not stream:
        print("Warning: Stream is not initialized")
        return gr.update(), gr.update(), "No active generation to stop", make_progress_bar_html(100, "No active generation")

    # Set the generation state to stopping
    generation_stopping = True
    generation_stop_time = time.time()

    # Send the end signal to the worker
    print("Sending end signal to worker...")
    stream.input_queue.push('end')

    # Return a message to the UI
    return gr.update(), gr.update(), "Stopping generation...", make_progress_bar_html(100, "Stopping generation, please wait...")


quick_prompts = [
    'The girl dances gracefully, with clear movements, full of charm.',
    'A character doing some simple body movements.',
    'The girl with huge jiggly breasts does sexy jiggle dance and bounces her breasts around.',
    'The girl does a sexy dance.',
]
quick_prompts = [[x] for x in quick_prompts]


css = make_progress_bar_css()
block = gr.Blocks(css=css).queue()
with block:
    gr.Markdown('# FramePack')
    with gr.Row():
        with gr.Column():
            with gr.Row():
                with gr.Column():
                    # Add a button to load metadata from the image
                    load_metadata_btn = gr.Button("📋 Load Settings", size="sm")
                    input_image = gr.Image(sources='upload', type="numpy", label="Start Frame", height=320)
                with gr.Column():
                    # Add a button to load metadata from the image
                    load_end_metadata_btn = gr.Button("📋 Load Settings", size="sm")
                    end_image = gr.Image(sources='upload', type="numpy", label="End Frame (Optional)", height=320)

            # Add a description for the Load Settings buttons
            gr.Markdown("*Click the '📋 Load Settings' button above an image to extract its metadata and populate the settings fields below.*")

            prompt = gr.Textbox(label="Prompt", value='')
            example_quick_prompts = gr.Dataset(samples=quick_prompts, label='Quick List', samples_per_page=1000, components=[prompt])
            example_quick_prompts.click(lambda x: x[0], inputs=[example_quick_prompts], outputs=prompt, show_progress=False, queue=False)

            with gr.Row():
                start_button = gr.Button(value="Start Generation")
                end_button = gr.Button(value="End Generation", interactive=False)

            with gr.Group():
                use_teacache = gr.Checkbox(label='Use TeaCache', value=True, info='Faster speed, but often makes hands and fingers slightly worse.')

                n_prompt = gr.Textbox(label="Negative Prompt", value="", visible=False)  # Not used
                seed = gr.Number(label="Seed", value=-1, precision=0, info='Set to -1 for a random seed')

                total_second_length = gr.Slider(label="Total Video Length (Seconds)", minimum=1, maximum=120, value=5, step=0.1)
                latent_window_size = gr.Slider(label="Latent Window Size", minimum=1, maximum=33, value=9, step=1, visible=False)  # Should not change
                steps = gr.Slider(label="Steps", minimum=1, maximum=100, value=25, step=1, info='Changing this value is not recommended.')

                cfg = gr.Slider(label="CFG Scale", minimum=1.0, maximum=32.0, value=1.0, step=0.01, visible=False)  # Should not change
                gs = gr.Slider(label="Distilled CFG Scale", minimum=1.0, maximum=32.0, value=10.0, step=0.01, info='Changing this value is not recommended.')
                rs = gr.Slider(label="CFG Re-Scale", minimum=0.0, maximum=1.0, value=0.0, step=0.01, visible=False)  # Should not change

                gpu_memory_preservation = gr.Slider(label="GPU Inference Preserved Memory (GB) (larger means slower)", minimum=6, maximum=128, value=6, step=0.1, info="Set this number to a larger value if you encounter OOM. Larger value causes slower speed.")

                mp4_crf = gr.Slider(label="MP4 Compression", minimum=0, maximum=100, value=16, step=1, info="Lower means better quality. 0 is uncompressed. Change to 16 if you get black outputs. ")

        with gr.Column():
            with gr.Row():
                preview_image = gr.Image(label="Next Latents", height=200, visible=False)
                latent_animation = gr.Video(label="Latent Animation", height=260, width=340, visible=False, autoplay=True, loop=True)
            result_video = gr.Video(label="Finished Frames", autoplay=True, show_share_button=False, height=512, loop=True)
            gr.Markdown('When using only a start frame, the ending actions will be generated before the starting actions due to the inverted sampling. If using both start and end frames, the model will try to create a smooth transition between them.')
            progress_desc = gr.Markdown('', elem_classes='no-generating-animation')
            progress_bar = gr.HTML('', elem_classes='no-generating-animation')

    gr.HTML('<div style="text-align:center; margin-top:20px;">Share your results and find ideas at the <a href="https://x.com/search?q=framepack&f=live" target="_blank">FramePack Twitter (X) thread</a></div>')

    ips = [input_image, end_image, prompt, n_prompt, seed, total_second_length, latent_window_size, steps, cfg, gs, rs, gpu_memory_preservation, use_teacache, mp4_crf]
    start_button.click(fn=process, inputs=ips, outputs=[result_video, preview_image, progress_desc, progress_bar, start_button, end_button, latent_animation])
    end_button.click(fn=end_process, outputs=[result_video, preview_image, progress_desc, progress_bar])

    # Connect the load metadata buttons to our function
    load_metadata_btn.click(
        fn=load_metadata_from_image,
        inputs=[input_image],
        outputs=[prompt, n_prompt, seed, total_second_length, steps, cfg, gs, rs, use_teacache]
    )

    load_end_metadata_btn.click(
        fn=load_metadata_from_image,
        inputs=[end_image],
        outputs=[prompt, n_prompt, seed, total_second_length, steps, cfg, gs, rs, use_teacache]
    )


# Function to clean up temporary files
def cleanup_temp_files():
    import shutil

    print("\nCleaning up temporary files...")
    try:
        # Check if the temp folder exists
        if os.path.exists(temp_folder):
            # List all files in the temp folder
            temp_files = os.listdir(temp_folder)
            print(f"Found {len(temp_files)} temporary files/folders to clean up")

            # Remove all files and subdirectories in the temp folder
            for item in temp_files:
                item_path = os.path.join(temp_folder, item)
                try:
                    if os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                        print(f"Removed directory: {item_path}")
                    else:
                        # For files, check if they're in use before trying to delete
                        try:
                            # Try to open the file to see if it's locked
                            with open(item_path, 'a'):
                                pass
                            # If we get here, the file isn't locked
                            os.remove(item_path)
                            print(f"Removed file: {item_path}")
                        except PermissionError:
                            print(f"Skipping file that's in use: {item_path}")
                except Exception as e:
                    print(f"Error removing {item_path}: {e}")

            print("Temporary files cleanup complete")
    except Exception as e:
        print(f"Error during cleanup: {e}")

# Register the cleanup function to run at exit
import atexit
atexit.register(cleanup_temp_files)

# Make the application not close on Ctrl+C
import signal

# Define a custom signal handler for Ctrl+C
def signal_handler(sig, frame):
    print("\nCtrl+C pressed. The application will continue running.")
    print("Press Ctrl+C again to exit.")

    # Reset the handler to default for the next Ctrl+C
    signal.signal(signal.SIGINT, original_sigint_handler)

# Store the original signal handler
original_sigint_handler = signal.getsignal(signal.SIGINT)

# Set our custom handler
signal.signal(signal.SIGINT, signal_handler)

# Add debug print before launching
print("Launching application...")

try:
    # Launch the application
    block.launch(
        server_name=args.server,
        server_port=args.port,
        share=args.share,
        inbrowser=args.inbrowser
    )
except Exception as e:
    print(f"Error launching application: {e}")
    import traceback
    traceback.print_exc()
