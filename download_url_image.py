import os
import sys
import urllib.request
import urllib.parse
import urllib.error
import ssl
import hashlib
import time

def download_image_from_url(url, temp_dir='./temp'):
    """
    Download an image from a URL to the temp directory

    Args:
        url (str): URL of the image to download
        temp_dir (str): Directory to save the downloaded image

    Returns:
        str: Path to the downloaded image, or None if download failed
    """
    # Create temp directory if it doesn't exist
    os.makedirs(temp_dir, exist_ok=True)

    try:
        # Handle URLs with query parameters
        if '?' in url:
            # Split URL at the first question mark to separate query parameters
            url_parts = url.split('?', 1)
            base_url = url_parts[0]
            # We keep the full URL for downloading, but use the base URL for filename extraction
        else:
            base_url = url

        # Parse the URL
        parsed_url = urllib.parse.urlparse(base_url)

        # Extract the filename from the URL
        filename = os.path.basename(parsed_url.path)

        # If no filename could be extracted, use a hash of the URL
        if not filename or '.' not in filename:
            url_hash = hashlib.md5(url.encode()).hexdigest()
            filename = f"url_image_{url_hash}.jpg"

        # Add timestamp to ensure uniqueness
        timestamp = int(time.time())
        name, ext = os.path.splitext(filename)
        if not ext or ext.lower() not in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
            ext = '.jpg'  # Default to jpg if no valid extension

        unique_filename = f"{name}_{timestamp}{ext}"
        output_path = os.path.join(temp_dir, unique_filename)

        print(f"Downloading image from {url}")

        # Create SSL context that ignores certificate validation
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # Set up headers to mimic a browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # Create a request with headers
        req = urllib.request.Request(url, headers=headers)

        # Download the image with a timeout
        with urllib.request.urlopen(req, context=ssl_context, timeout=10) as response, open(output_path, 'wb') as out_file:
            out_file.write(response.read())

        # Convert to absolute path
        abs_output_path = os.path.abspath(output_path)
        print(f"Successfully downloaded image to {abs_output_path}")
        return abs_output_path

    except urllib.error.URLError as e:
        print(f"URL Error: {e}")
        return None
    except TimeoutError:
        print("Connection timed out")
        return None
    except Exception as e:
        print(f"Error downloading image from {url}: {e}")
        return None

if __name__ == "__main__":
    # Check if URL was provided
    if len(sys.argv) < 2:
        print("Usage: python download_url_image.py <url>")
        sys.exit(1)

    url = sys.argv[1]

    # Download the image
    local_path = download_image_from_url(url)

    # Print the local path for the batch file to capture
    if local_path:
        print(f"DOWNLOADED_PATH={local_path}")
    else:
        print("DOWNLOAD_FAILED")
        sys.exit(1)
