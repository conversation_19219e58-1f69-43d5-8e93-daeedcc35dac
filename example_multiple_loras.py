#!/usr/bin/env python3
"""
Example script demonstrating how to use multiple LoRAs with FramePack batch processing.

This script shows various ways to combine up to 3 LoRAs for enhanced video generation.
"""

import subprocess
import sys
import os

def run_batch_with_multiple_loras():
    """Example of using multiple LoRAs with different strengths"""
    
    # Example 1: Using 3 LoRAs with different purposes and strengths
    print("Example 1: Using 3 LoRAs (Style + Character + Lighting)")
    cmd1 = [
        sys.executable, "batch.py",
        "--input_dir", "input",
        "--output_dir", "output_multi_lora",
        "--prompt", "a beautiful landscape with dramatic lighting",
        "--lora_file_1", "loras/style_cinematic.safetensors",
        "--lora_multiplier_1", "0.8",
        "--lora_file_2", "loras/character_enhancement.safetensors", 
        "--lora_multiplier_2", "0.6",
        "--lora_file_3", "loras/lighting_dramatic.safetensors",
        "--lora_multiplier_3", "0.4",
        "--video_length", "3",
        "--steps", "20"
    ]
    
    print("Command:", " ".join(cmd1))
    print("This would merge 3 LoRAs sequentially:")
    print("  1. Cinematic style LoRA at 80% strength")
    print("  2. Character enhancement LoRA at 60% strength") 
    print("  3. Dramatic lighting LoRA at 40% strength")
    print()
    
    # Example 2: Using 2 LoRAs
    print("Example 2: Using 2 LoRAs (Style + Character)")
    cmd2 = [
        sys.executable, "batch.py",
        "--input_dir", "input",
        "--output_dir", "output_dual_lora",
        "--prompt", "anime character in cyberpunk setting",
        "--lora_file_1", "loras/anime_style.safetensors",
        "--lora_multiplier_1", "0.9",
        "--lora_file_2", "loras/cyberpunk_environment.safetensors",
        "--lora_multiplier_2", "0.7",
        "--video_length", "5",
        "--steps", "25"
    ]
    
    print("Command:", " ".join(cmd2))
    print("This would merge 2 LoRAs:")
    print("  1. Anime style LoRA at 90% strength")
    print("  2. Cyberpunk environment LoRA at 70% strength")
    print()
    
    # Example 3: Backward compatibility - using old single LoRA syntax
    print("Example 3: Backward compatibility (single LoRA)")
    cmd3 = [
        sys.executable, "batch.py",
        "--input_dir", "input", 
        "--output_dir", "output_single_lora",
        "--prompt", "realistic portrait",
        "--lora_file", "loras/portrait_realism.safetensors",  # Old syntax
        "--lora_multiplier", "0.8",  # Old syntax
        "--video_length", "4",
        "--steps", "30"
    ]
    
    print("Command:", " ".join(cmd3))
    print("This uses the old single LoRA syntax (maps to --lora_file_1)")
    print("  1. Portrait realism LoRA at 80% strength")
    print()
    
    # Example 4: Mixed usage - combining old and new syntax
    print("Example 4: Mixed syntax (not recommended but supported)")
    cmd4 = [
        sys.executable, "batch.py",
        "--input_dir", "input",
        "--output_dir", "output_mixed_lora", 
        "--prompt", "fantasy landscape",
        "--lora_file", "loras/fantasy_base.safetensors",  # Old syntax -> lora_file_1
        "--lora_multiplier", "0.8",  # Old syntax -> lora_multiplier_1
        "--lora_file_2", "loras/landscape_enhancement.safetensors",  # New syntax
        "--lora_multiplier_2", "0.6",  # New syntax
        "--video_length", "6",
        "--steps", "25"
    ]
    
    print("Command:", " ".join(cmd4))
    print("This mixes old and new syntax:")
    print("  1. Fantasy base LoRA at 80% strength (from --lora_file)")
    print("  2. Landscape enhancement LoRA at 60% strength (from --lora_file_2)")
    print()

def show_help():
    """Show help information about multiple LoRA usage"""
    print("Multiple LoRA Support in FramePack")
    print("=" * 40)
    print()
    print("FramePack now supports merging up to 3 LoRAs simultaneously!")
    print()
    print("New Arguments:")
    print("  --lora_file_1 PATH        Path to first LoRA file")
    print("  --lora_multiplier_1 FLOAT First LoRA strength (0.0-1.0)")
    print("  --lora_file_2 PATH        Path to second LoRA file") 
    print("  --lora_multiplier_2 FLOAT Second LoRA strength (0.0-1.0)")
    print("  --lora_file_3 PATH        Path to third LoRA file")
    print("  --lora_multiplier_3 FLOAT Third LoRA strength (0.0-1.0)")
    print()
    print("Backward Compatibility:")
    print("  --lora_file PATH          Maps to --lora_file_1")
    print("  --lora_multiplier FLOAT   Maps to --lora_multiplier_1")
    print()
    print("How it works:")
    print("- LoRAs are merged sequentially in order: 1 → 2 → 3")
    print("- Each LoRA can have its own strength multiplier")
    print("- Only specify the LoRAs you want to use (1, 2, or 3)")
    print("- The model is only reloaded when LoRA configuration changes")
    print()
    print("Tips:")
    print("- Use lower multipliers for subtle effects (0.3-0.5)")
    print("- Use higher multipliers for strong effects (0.7-1.0)")
    print("- Combine different types: style + character + lighting")
    print("- Test different combinations to find what works best")
    print()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        show_help()
    else:
        print("Multiple LoRA Examples for FramePack")
        print("=" * 40)
        print()
        print("Note: These are example commands. Make sure you have:")
        print("1. Input images in the 'input' directory")
        print("2. LoRA files in the specified paths")
        print("3. Sufficient GPU memory for multiple LoRAs")
        print()
        print("Run with --help to see detailed information about multiple LoRA support.")
        print()
        
        run_batch_with_multiple_loras()
        
        print("To actually run these commands, copy and paste them into your terminal.")
        print("Remember to adjust the paths to match your actual LoRA files!")
