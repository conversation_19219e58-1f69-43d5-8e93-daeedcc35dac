#!/usr/bin/env python3
"""
Example script showing how to use the new text-to-video functionality in FramePack.

This demonstrates the integration of transparent PNG detection and deterministic 
seed control for pure text-to-video generation.
"""

import os
import sys
from PIL import Image
from pathlib import Path

def create_transparent_png(width=512, height=512, output_path="transparent_t2v.png"):
    """
    Create a fully transparent PNG that will trigger text-to-video mode.
    
    This is the recommended way to enable pure text-to-video generation
    with deterministic seed control.
    """
    # Create a fully transparent RGBA image
    transparent_img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    transparent_img.save(output_path, format='PNG')
    print(f"Created transparent PNG for T2V mode: {output_path}")
    return output_path

def example_batch_processing():
    """
    Example of how to use the new T2V functionality with batch processing.
    """
    print("Example: Text-to-Video with Batch Processing")
    print("=" * 50)
    
    # Create a transparent PNG for text-to-video mode
    transparent_image = create_transparent_png(512, 512, "example_t2v_trigger.png")
    
    print(f"""
To use this transparent image for text-to-video generation:

1. Standard Batch Script (batch.py):
   python batch.py "{transparent_image}" \\
       --prompt "A beautiful sunset over mountains" \\
       --seed 42 \\
       --video_length 5.0 \\
       --steps 25

2. F1 Batch Script (batch_f1.py):
   python batch_f1.py "{transparent_image}" \\
       --prompt "A cat walking through a garden" \\
       --seed 12345 \\
       --video_length 3.0 \\
       --steps 20

Key Features:
- The transparent PNG will be automatically detected
- Text-to-video mode will be enabled automatically
- Deterministic noise will be generated using the specified seed
- Same seed = identical video output (reproducible results)
- No need for an actual input image - pure text-to-video generation

The system will print:
"🎬 Running in pure text-to-video mode with deterministic seed control"
""")

def example_programmatic_usage():
    """
    Example of how to use the new functionality programmatically.
    """
    print("\nExample: Programmatic Usage")
    print("=" * 50)
    
    print("""
For direct programmatic usage in your own scripts:

```python
from diffusers_helper.utils import load_start_latent_with_t2v_support

# Method 1: Use None for pure T2V (no image needed)
start_latent, is_text_to_video = load_start_latent_with_t2v_support(
    image_path=None,  # Triggers T2V mode
    device="cuda",
    H=512,
    W=512,
    T_frames=1,
    vae=vae_model,
    seed=42  # Deterministic seed
)

# Method 2: Use transparent PNG
start_latent, is_text_to_video = load_start_latent_with_t2v_support(
    image_path="transparent_image.png",  # Fully transparent PNG
    device="cuda",
    H=512,
    W=512,
    T_frames=1,
    vae=vae_model,
    seed=42  # Deterministic seed
)

if is_text_to_video:
    print("Using text-to-video mode with deterministic noise")
else:
    print("Using image-to-video mode with encoded image")
```

The function automatically:
- Detects transparent PNGs using img.getextrema()[3] == (0, 0)
- Generates deterministic noise with torch.Generator(device).manual_seed(seed)
- Returns both the latent tensor and a flag indicating the mode
""")

def example_tips_and_tricks():
    """
    Tips and best practices for using the new T2V functionality.
    """
    print("\nTips and Best Practices")
    print("=" * 50)
    
    print("""
1. REPRODUCIBLE RESULTS:
   - Always use the same seed for identical outputs
   - The noise generation is deterministic across runs
   - Same prompt + same seed = identical video

2. TRANSPARENT PNG CREATION:
   - Use RGBA mode with alpha=0 for all pixels
   - Any image editor can create these (Photoshop, GIMP, etc.)
   - Or use the provided create_transparent_png() function

3. SEED CONTROL:
   - Use positive integers for seeds (0 to 2^32-1)
   - Seed -1 will generate random seed (not deterministic)
   - Document your seeds for reproducible experiments

4. PERFORMANCE:
   - T2V mode skips image encoding (faster startup)
   - No need to load/process input images
   - Ideal for pure text-based generation workflows

5. INTEGRATION:
   - Works with both standard and F1 models
   - Compatible with LoRA, custom models, etc.
   - Maintains all existing FramePack features

6. DEBUGGING:
   - Check console output for mode confirmation
   - Look for "🎬 Running in pure text-to-video mode"
   - Verify seed values in output filenames
""")

def main():
    """Main example function."""
    print("FramePack Text-to-Video Integration Examples")
    print("=" * 60)
    
    # Show batch processing example
    example_batch_processing()
    
    # Show programmatic usage
    example_programmatic_usage()
    
    # Show tips and tricks
    example_tips_and_tricks()
    
    print("\n" + "=" * 60)
    print("Summary:")
    print("The new text-to-video functionality provides:")
    print("✅ Automatic transparent PNG detection")
    print("✅ Deterministic seed control with torch.Generator")
    print("✅ Pure text-to-video generation (no input image needed)")
    print("✅ Reproducible results across runs")
    print("✅ Full integration with existing FramePack features")
    print("\nReady to use with both batch scripts and programmatic integration!")

if __name__ == "__main__":
    main()
