@echo off
setlocal enabledelayedexpansion

:: File Backup Utility Launcher
:: Backs up py, json, bat, md, txt, ps1 files to timestamped backup folder

echo File Backup Utility
echo ==================

:: Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

:: Get the directory of this batch file
set "SCRIPT_DIR=%~dp0"
set "PYTHON_SCRIPT=%SCRIPT_DIR%file_backup.py"

:: Check if Python script exists
if not exist "%PYTHON_SCRIPT%" (
    echo Error: file_backup.py not found in the same directory as this batch file
    echo Expected location: %PYTHON_SCRIPT%
    pause
    exit /b 1
)

:: Check if a directory was dragged onto the batch file
if "%~1"=="" (
    echo No directory specified - backing up current directory
    echo Current directory: %CD%
    echo.
    python "%PYTHON_SCRIPT%"
) else (
    echo Directory specified: %~1
    echo.
    
    :: Check if the provided path is a directory
    if exist "%~1\" (
        python "%PYTHON_SCRIPT%" "%~1"
    ) else if exist "%~1" (
        :: If it's a file, backup the directory containing the file
        for %%F in ("%~1") do (
            echo File provided: %%F
            echo Backing up parent directory: %%~dpF
            echo.
            python "%PYTHON_SCRIPT%" "%%~dpF"
        )
    ) else (
        echo Error: Specified path does not exist: %~1
        pause
        exit /b 1
    )
)

:: Check the exit code from Python script
if errorlevel 1 (
    echo.
    echo Backup failed with errors
    pause
    exit /b 1
)

echo.
echo Backup completed successfully!
pause
