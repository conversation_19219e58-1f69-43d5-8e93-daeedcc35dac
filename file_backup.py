#!/usr/bin/env python3
"""
File Backup Utility
Backs up specified file types to a timestamped folder in the backups directory.
Preserves folder structure and recursively processes subdirectories.
"""

import os
import shutil
import sys
from datetime import datetime
from pathlib import Path

# File extensions to backup
BACKUP_EXTENSIONS = {'.py', '.json', '.bat', '.md', '.txt', '.ps1'}

# Directories to ignore during backup
IGNORE_DIRECTORIES = {'__pycache__', 'venv', 'outputs', 'temp', '.venv', 'node_modules', '.git'}

def create_timestamp():
    """Create a timestamp string for the backup folder."""
    return datetime.now().strftime("%Y%m%d_%H%M%S")

def should_backup_file(file_path):
    """Check if a file should be backed up based on its extension."""
    return file_path.suffix.lower() in BACKUP_EXTENSIONS

def copy_file_with_structure(source_file, source_root, backup_root):
    """
    Copy a file to the backup directory while preserving folder structure.
    
    Args:
        source_file: Path to the source file
        source_root: Root directory being backed up
        backup_root: Root backup directory
    """
    # Calculate relative path from source root
    relative_path = source_file.relative_to(source_root)
    
    # Create destination path
    dest_path = backup_root / relative_path
    
    # Create destination directory if it doesn't exist
    dest_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Copy the file
    try:
        shutil.copy2(source_file, dest_path)
        print(f"Backed up: {relative_path}")
        return True
    except Exception as e:
        print(f"Error backing up {relative_path}: {e}")
        return False

def backup_files(source_dir=None):
    """
    Main backup function that processes all files in the source directory.
    
    Args:
        source_dir: Directory to backup (defaults to current directory)
    """
    if source_dir is None:
        source_dir = Path.cwd()
    else:
        source_dir = Path(source_dir)
    
    if not source_dir.exists():
        print(f"Error: Source directory '{source_dir}' does not exist.")
        return False
    
    # Create backup directory structure
    backup_base = source_dir / "backups"
    timestamp = create_timestamp()
    backup_dir = backup_base / timestamp
    
    try:
        backup_dir.mkdir(parents=True, exist_ok=True)
        print(f"Created backup directory: {backup_dir}")
    except Exception as e:
        print(f"Error creating backup directory: {e}")
        return False
    
    # Track statistics
    files_backed_up = 0
    files_skipped = 0
    errors = 0
    
    print(f"\nStarting backup from: {source_dir}")
    print(f"Backup destination: {backup_dir}")
    print(f"File types to backup: {', '.join(sorted(BACKUP_EXTENSIONS))}")
    print(f"Ignoring directories: {', '.join(sorted(IGNORE_DIRECTORIES))}")
    print("-" * 60)
    
    # Walk through all files and subdirectories
    for root, dirs, files in os.walk(source_dir):
        root_path = Path(root)

        # Skip the backups directory itself
        if backup_base in root_path.parents or root_path == backup_base:
            continue

        # Skip ignored directories
        if root_path.name.lower() in {d.lower() for d in IGNORE_DIRECTORIES}:
            continue

        # Remove ignored directories from dirs list to prevent os.walk from entering them
        dirs[:] = [d for d in dirs if d.lower() not in {ignore.lower() for ignore in IGNORE_DIRECTORIES}]
        
        for file in files:
            file_path = root_path / file
            
            if should_backup_file(file_path):
                if copy_file_with_structure(file_path, source_dir, backup_dir):
                    files_backed_up += 1
                else:
                    errors += 1
            else:
                files_skipped += 1
    
    # Print summary
    print("-" * 60)
    print(f"Backup completed!")
    print(f"Files backed up: {files_backed_up}")
    print(f"Files skipped: {files_skipped}")
    print(f"Errors: {errors}")
    print(f"Backup location: {backup_dir}")
    
    return errors == 0

def main():
    """Main entry point."""
    print("File Backup Utility")
    print("=" * 50)
    
    # Check for command line arguments
    if len(sys.argv) > 1:
        source_directory = sys.argv[1]
        print(f"Backing up directory: {source_directory}")
    else:
        source_directory = None
        print("Backing up current directory")
    
    try:
        success = backup_files(source_directory)
        if success:
            print("\nBackup completed successfully!")
        else:
            print("\nBackup completed with errors.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\nBackup cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
    
    # Wait for user input before closing (when run from batch file)
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
