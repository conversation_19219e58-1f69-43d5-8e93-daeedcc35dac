#!/usr/bin/env python3
import os
import sys
from pathlib import Path

def create_symlinks(model_dir):
    print("Converting HuggingFace model files to proper symlinks...")
    print()
    
    model_path = Path(model_dir)
    if not model_path.exists():
        print(f"Error: Model directory does not exist: {model_dir}")
        return False
    
    blobs_dir = model_path / "blobs"
    snapshots_dir = model_path / "snapshots"
    
    if not blobs_dir.exists():
        print(f"Error: blobs directory not found in: {model_dir}")
        return False
    
    if not snapshots_dir.exists():
        print(f"Error: snapshots directory not found in: {model_dir}")
        return False
    
    print(f"Processing model directory: {model_dir}")
    print()
    
    # Get all safetensors files in snapshots directory
    safetensors_files = list(snapshots_dir.rglob("*.safetensors"))
    
    for snapshot_file in safetensors_files:
        print(f"Processing: {snapshot_file.name}")
        
        # Get size of snapshot file
        snapshot_size = snapshot_file.stat().st_size
        
        # Find matching blob by size
        matching_blob = None
        for blob_file in blobs_dir.iterdir():
            if blob_file.is_file() and blob_file.stat().st_size == snapshot_size:
                matching_blob = blob_file
                break
        
        if matching_blob:
            print(f"  Found matching blob: {matching_blob.name}")
            
            # Calculate relative path to blob
            relative_path = f"..\\..\\blobs\\{matching_blob.name}"
            
            # Remove the copied file
            snapshot_file.unlink()
            
            # Create symlink
            try:
                os.symlink(relative_path, str(snapshot_file))
                print("  ✓ Created symlink successfully")
            except Exception as e:
                print(f"  ✗ Error creating symlink: {e}")
        else:
            print(f"  Warning: No matching blob found (size: {snapshot_size} bytes)")
        
        print()
    
    print("Symlink conversion complete!")
    print()
    
    # Verify results
    print("Verifying symlinks...")
    safetensors_files = list(snapshots_dir.rglob("*.safetensors"))
    
    for file_path in safetensors_files:
        if file_path.is_symlink():
            print(f"✓ {file_path.name} is now a symlink")
        else:
            print(f"✗ {file_path.name} is still a regular file")
    
    print()
    print("Done! You can now use this model with proper symlinks.")
    return True

def main():
    if len(sys.argv) > 1:
        model_dir = sys.argv[1]
    else:
        print("Please enter the path to the model directory:")
        print("Example: merged_models\\models--lllyasviel--FramePack_F1_I2V_HY_20250503_pov-missionary-hunyuan-v1.0-vfx_ai_x0.8")
        model_dir = input("Model Directory: ").strip()
        
        if not model_dir:
            print("Error: No model directory specified")
            return 1
    
    success = create_symlinks(model_dir)
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
