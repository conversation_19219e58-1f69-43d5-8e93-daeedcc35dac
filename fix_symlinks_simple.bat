@echo off
setlocal enabledelayedexpansion

echo Converting HuggingFace model files to proper symlinks...
echo.

set "MODEL_DIR=%~1"
if "%MODEL_DIR%"=="" (
    echo Usage: fix_symlinks_simple.bat "path\to\model\directory"
    echo.
    echo Example: fix_symlinks_simple.bat "merged_models\models--lllyasviel--FramePack_F1_I2V_HY_20250503_pov-missionary-hunyuan-v1.0-vfx_ai_x0.8"
    pause
    exit /b 1
)

if not exist "%MODEL_DIR%" (
    echo Error: Model directory does not exist: %MODEL_DIR%
    pause
    exit /b 1
)

if not exist "%MODEL_DIR%\blobs" (
    echo Error: blobs directory not found in: %MODEL_DIR%
    pause
    exit /b 1
)

if not exist "%MODEL_DIR%\snapshots" (
    echo Error: snapshots directory not found in: %MODEL_DIR%
    pause
    exit /b 1
)

echo Processing model directory: %MODEL_DIR%
echo.

:: Create a temporary script to handle each file
echo @echo off > temp_process.bat
echo setlocal enabledelayedexpansion >> temp_process.bat
echo set "MODEL_DIR=%MODEL_DIR%" >> temp_process.bat
echo set "snapshot_file=%%1" >> temp_process.bat
echo set "filename=%%~nx1" >> temp_process.bat
echo echo Processing: %%filename%% >> temp_process.bat
echo for %%%%s in (%%1) do set "snapshot_size=%%%%~zs" >> temp_process.bat
echo set "found_match=0" >> temp_process.bat
echo for %%%%b in ("%%MODEL_DIR%%\blobs\*") do ( >> temp_process.bat
echo     for %%%%z in ("%%%%b") do set "blob_size=%%%%~zz" >> temp_process.bat
echo     set "blob_name=%%%%~nxb" >> temp_process.bat
echo     if "!snapshot_size!"=="!blob_size!" ( >> temp_process.bat
echo         echo   Found matching blob: !blob_name! >> temp_process.bat
echo         del %%1 2^>nul >> temp_process.bat
echo         mklink %%1 "..\..\blobs\!blob_name!" >> temp_process.bat
echo         if !errorlevel! equ 0 ( >> temp_process.bat
echo             echo   ✓ Created symlink successfully >> temp_process.bat
echo         ) else ( >> temp_process.bat
echo             echo   ✗ Error creating symlink >> temp_process.bat
echo         ) >> temp_process.bat
echo         set "found_match=1" >> temp_process.bat
echo         goto :done >> temp_process.bat
echo     ) >> temp_process.bat
echo ) >> temp_process.bat
echo if "!found_match!"=="0" echo   Warning: No matching blob found >> temp_process.bat
echo :done >> temp_process.bat
echo echo. >> temp_process.bat

:: Change to snapshots directory
cd /d "%MODEL_DIR%\snapshots"

:: Process each safetensors file
for /r . %%f in (*.safetensors) do (
    call "%~dp0temp_process.bat" "%%f"
)

:: Clean up
del "%~dp0temp_process.bat" 2>nul

echo Symlink conversion complete!
echo.

:: Verify results
echo Verifying symlinks...
for /r . %%f in (*.safetensors) do (
    set "filename=%%~nxf"
    dir "%%f" | find "<SYMLINK>" >nul
    if !errorlevel! equ 0 (
        echo ✓ !filename! is now a symlink
    ) else (
        echo ✗ !filename! is still a regular file
    )
)

echo.
echo Done! You can now use this model with proper symlinks.
pause
