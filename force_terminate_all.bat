@echo off
echo FramePack Force Terminate All Command
echo =====================================
echo.
echo WARNING: This will forcefully terminate ALL FramePack generation processes!
echo This is an emergency stop that will immediately kill all batch processes.
echo.
echo The GUI will remain running, but all background generation will be stopped.
echo.

set /p confirm="Are you sure you want to force terminate all processes? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo Operation cancelled.
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b
)

echo.
echo Force terminating all FramePack processes...
echo.

:: Method 1: Try to use Python script for precise termination
if exist force_terminate_all.py (
    echo Using Python script for precise process termination...
    python force_terminate_all.py
) else (
    echo Python script not found, using system commands...
    
    :: Method 2: Kill specific batch processes using taskkill
    echo Killing batch.py processes...
    taskkill /f /im python.exe /fi "WINDOWTITLE eq *batch.py*" 2>nul
    taskkill /f /im python.exe /fi "WINDOWTITLE eq *batch_f1*" 2>nul
    
    echo Killing temp batch files...
    taskkill /f /im cmd.exe /fi "WINDOWTITLE eq *temp_run_framepack*" 2>nul
    
    echo Killing any remaining FramePack batch processes...
    for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| findstr /i "batch"') do (
        taskkill /f /pid %%i 2>nul
    )
)

:: Clean up flag files
echo.
echo Cleaning up flag files...
if exist stop_framepack.flag del /q stop_framepack.flag
if exist skip_generation.flag del /q skip_generation.flag
if exist temp_run_framepack.bat del /q temp_run_framepack.bat
if exist framepack_completed.signal del /q framepack_completed.signal

echo.
echo ✓ Force terminate command completed!
echo All FramePack generation processes should now be stopped.
echo The GUI should remain running and responsive.
echo.
echo Press any key to exit...
pause >nul
