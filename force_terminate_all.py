#!/usr/bin/env python3
"""
FramePack Force Terminate All Command
Forcefully terminates all FramePack generation processes while preserving the GUI.
"""

import os
import sys
import time

def terminate_all_framepack_processes():
    """Terminate all running FramePack generation processes immediately (but keep GUI alive)"""
    try:
        print("FramePack Force Terminate All Command")
        print("=" * 45)
        print()
        print("WARNING: This will forcefully terminate ALL FramePack generation processes!")
        print("The GUI will remain running, but all background generation will be stopped.")
        print()
        
        confirm = input("Are you sure you want to continue? (Y/N): ").strip().upper()
        if confirm != 'Y':
            print("Operation cancelled.")
            return False
        
        print()
        print("Terminating FramePack generation processes...")
        terminated_processes = []

        # Method 1: Try to use psutil if available for precise process termination
        try:
            import psutil
            current_pid = os.getpid()  # Don't kill ourselves

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # Skip our own process
                    if proc.info['pid'] == current_pid:
                        continue

                    # Check if this is a FramePack-related process
                    cmdline = proc.info['cmdline'] or []
                    cmdline_str = ' '.join(cmdline).lower()

                    # IMPORTANT: Only target generation processes, NOT the GUI
                    # Look for specific batch processes and generation scripts
                    should_terminate = False

                    # Target batch.py processes (the actual generation)
                    if 'batch.py' in cmdline_str and 'framepack_gui.py' not in cmdline_str:
                        should_terminate = True
                    # Target temp_run_framepack.bat (the batch wrapper)
                    elif 'temp_run_framepack.bat' in cmdline_str:
                        should_terminate = True
                    # Target F1 batch processes
                    elif any(script in cmdline_str for script in ['batch_f1_lock.py', 'batch_f1_video.py']):
                        should_terminate = True
                    # Target cmd.exe processes running the batch file
                    elif proc.info['name'].lower() == 'cmd.exe' and 'temp_run_framepack.bat' in cmdline_str:
                        should_terminate = True

                    # NEVER terminate the GUI process or our own process
                    if 'framepack_gui.py' in cmdline_str or proc.info['pid'] == current_pid:
                        should_terminate = False
                        if 'framepack_gui.py' in cmdline_str:
                            print(f"Skipping GUI process PID {proc.info['pid']}: {cmdline_str}")
                        elif proc.info['pid'] == current_pid:
                            print(f"Skipping own process PID {proc.info['pid']}: {cmdline_str}")

                    if should_terminate:
                        print(f"Terminating generation process PID {proc.info['pid']}: {cmdline_str}")
                        proc.terminate()
                        terminated_processes.append(proc.info['pid'])

                        # Wait a moment for graceful termination
                        try:
                            proc.wait(timeout=2)
                        except psutil.TimeoutExpired:
                            # Force kill if it doesn't terminate gracefully
                            print(f"Force killing process PID {proc.info['pid']}")
                            proc.kill()

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            print(f"Terminated {len(terminated_processes)} generation processes using psutil")

        except ImportError:
            # Fallback method using system commands if psutil is not available
            print("psutil not available, using system commands for process termination")
            terminate_processes_fallback()

        # Force cleanup of all temporary files and locks
        force_cleanup_all_files()

        print("Process termination completed")
        return True

    except Exception as e:
        print(f"Error terminating FramePack processes: {e}")
        # Try fallback method even if psutil method failed
        try:
            terminate_processes_fallback()
        except Exception as fallback_error:
            print(f"Fallback termination also failed: {fallback_error}")
        return False

def terminate_processes_fallback():
    """Fallback method to terminate generation processes using system commands (preserves GUI)"""
    try:
        if os.name == 'nt':  # Windows
            # Kill cmd.exe processes with FramePack batch processing title
            try:
                print("Killing cmd.exe processes running FramePack batch files...")
                os.system('taskkill /f /im cmd.exe /fi "WINDOWTITLE eq *temp_run_framepack*" 2>nul')
            except Exception as e:
                print(f"Error killing cmd processes: {e}")

            # Kill Python processes running batch scripts (but not GUI)
            try:
                print("Killing Python batch processes...")
                # This is more targeted - only kills python processes with batch in the command line
                os.system('taskkill /f /im python.exe /fi "WINDOWTITLE eq *batch.py*" 2>nul')
                os.system('taskkill /f /im python.exe /fi "WINDOWTITLE eq *batch_f1*" 2>nul')
            except Exception as e:
                print(f"Error killing batch processes: {e}")

    except Exception as e:
        print(f"Error in fallback process termination: {e}")

def force_cleanup_all_files():
    """Clean up all temporary files and locks"""
    try:
        print("Cleaning up temporary files...")
        cleanup_files = [
            "temp_run_framepack.bat",
            "framepack_completed.signal", 
            "stop_framepack.flag",
            "skip_generation.flag",
            "temp_command_result.txt",
            "framepack_progress.txt"
        ]
        
        cleaned_count = 0
        for file_path in cleanup_files:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"Cleaned up: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"Could not clean up {file_path}: {e}")
        
        print(f"Cleaned up {cleaned_count} temporary files")
        
    except Exception as e:
        print(f"Error during cleanup: {e}")

if __name__ == "__main__":
    success = terminate_all_framepack_processes()
    print()
    if success:
        print("✓ Force terminate command completed successfully!")
        print("All FramePack generation processes should now be stopped.")
        print("The GUI should remain running and responsive.")
    else:
        print("✗ Force terminate command encountered errors!")
        print("Some processes may still be running.")
    
    print()
    input("Press Enter to exit...")
