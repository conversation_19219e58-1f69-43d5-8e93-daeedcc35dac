"""
This script applies a fix to the FramePack GUI to ensure drag and drop functionality works correctly.
It modifies the framepack_gui.py file to use a more reliable approach for drag and drop.
"""

import os
import sys
import re
import shutil
import tkinter as tk
from tkinter import ttk, messagebox

def backup_file(file_path):
    """Create a backup of the file"""
    backup_path = file_path + ".bak"
    try:
        shutil.copy2(file_path, backup_path)
        print(f"Created backup at {backup_path}")
        return True
    except Exception as e:
        print(f"Error creating backup: {e}")
        return False

def apply_dnd_fix():
    """Apply the drag and drop fix to framepack_gui.py"""
    file_path = "framepack_gui.py"
    
    # Check if the file exists
    if not os.path.exists(file_path):
        print(f"Error: {file_path} not found")
        return False
    
    # Create a backup
    if not backup_file(file_path):
        return False
    
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Apply the fixes
        
        # 1. Fix the import section
        import_pattern = r"try:\s+from tkinterdnd2 import TkinterDnD, DND_FILES\s+TKDND_AVAILABLE = True\s+except ImportError:"
        import_replacement = """try:
    from tkinterdnd2 import TkinterDnD, DND_FILES
    # Also import these additional constants
    from tkinterdnd2 import PRIVATE
    TKDND_AVAILABLE = True
except ImportError:"""
        
        content = re.sub(import_pattern, import_replacement, content)
        
        # 2. Fix the drop target registration
        dnd_pattern = r"# Add drag and drop support if TkinterDnD is available\s+if TKDND_AVAILABLE:.*?self\.drag_drop_label\.grid\(row=1, column=1, sticky=tk\.W, padx=5, pady=\(0, 5\)\)"
        
        dnd_replacement = """# Add drag and drop support if TkinterDnD is available
        if TKDND_AVAILABLE:
            # Register all relevant widgets as drop targets using a more direct approach
            
            # Make sure the listbox is a drop target
            self.files_listbox.drop_target_register(DND_FILES)
            self.files_listbox.dnd_bind('<<Drop>>', self.drop_files)
            
            # Make the container frame a drop target
            listbox_container.drop_target_register(DND_FILES)
            listbox_container.dnd_bind('<<Drop>>', self.drop_files)
            
            # Make the files frame a drop target
            self.files_frame.drop_target_register(DND_FILES)
            self.files_frame.dnd_bind('<<Drop>>', self.drop_files)
            
            # Store the listbox container for styling
            self.listbox_container = listbox_container
            
            # Add a label to indicate drag and drop capability
            self.drag_drop_label = ttk.Label(self.files_frame, text="Drag and drop files here", foreground="blue")
            self.drag_drop_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=(0, 5))"""
        
        content = re.sub(dnd_pattern, dnd_replacement, content, flags=re.DOTALL)
        
        # 3. Simplify the drag_enter and drag_leave methods
        drag_enter_pattern = r"def drag_enter\(self, _\):.*?self\.files_listbox\.config\(highlightthickness=2, highlightcolor=\"#0066cc\", highlightbackground=\"#0066cc\"\)"
        
        drag_enter_replacement = """def drag_enter(self, event):
        """Handle drag enter event - change appearance to indicate drop target"""
        if not TKDND_AVAILABLE:
            return
            
        # Simple visual feedback
        self.files_listbox.config(background="#e0f0ff")
        self.listbox_container.config(relief="groove", borderwidth=2)
        self.drag_drop_label.config(foreground="#0066cc", text="Drop files here to add them")"""
        
        content = re.sub(drag_enter_pattern, drag_enter_replacement, content, flags=re.DOTALL)
        
        drag_leave_pattern = r"def drag_leave\(self, _\):.*?self\.files_listbox\.config\(highlightthickness=1, highlightcolor=\"SystemWindowFrame\", highlightbackground=\"SystemWindowFrame\"\)"
        
        drag_leave_replacement = """def drag_leave(self, event):
        """Handle drag leave event - restore normal appearance"""
        if not TKDND_AVAILABLE:
            return
            
        # Restore normal appearance
        self.files_listbox.config(background="white")
        self.listbox_container.config(relief="solid", borderwidth=1)
        self.drag_drop_label.config(foreground="gray", text="Drag and drop files here")"""
        
        content = re.sub(drag_leave_pattern, drag_leave_replacement, content, flags=re.DOTALL)
        
        # 4. Simplify the drop_files method
        drop_files_pattern = r"def drop_files\(self, event\):.*?# Restore the normal appearance after drop.*?self\.files_listbox\.config\(highlightthickness=1, highlightcolor=\"SystemWindowFrame\", highlightbackground=\"SystemWindowFrame\"\)"
        
        drop_files_replacement = """def drop_files(self, event):
        """Handle files dropped onto the listbox or frame"""
        if not TKDND_AVAILABLE:
            return
            
        # Get the dropped data (file paths)
        file_paths = self.root.tk.splitlist(event.data)
        
        # Process the dropped files
        if file_paths:
            # Switch to individual files mode
            self.use_individual_files.set(True)
            self.toggle_input_mode()
            
            # Valid image extensions
            valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
            
            # Count how many new files we add
            added_count = 0
            skipped_count = 0
            
            # Add each valid file to the list
            for file_path in file_paths:
                # Normalize the path
                file_path = os.path.normpath(file_path)
                
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    ext = os.path.splitext(file_path)[1].lower()
                    if ext in valid_extensions:
                        if file_path not in self.selected_files:
                            self.selected_files.append(file_path)
                            self.files_listbox.insert(tk.END, os.path.basename(file_path))
                            added_count += 1
                    else:
                        print(f"Skipping {file_path} - not a supported image file")
                        skipped_count += 1
                else:
                    print(f"File not found: {file_path}")
                    skipped_count += 1
            
            # Show summary
            print(f"Added {added_count} files, skipped {skipped_count} files")
            
            # Restore normal appearance
            self.files_listbox.config(background="white")
            self.listbox_container.config(relief="solid", borderwidth=1)
            self.drag_drop_label.config(foreground="gray", text="Drag and drop files here")"""
        
        content = re.sub(drop_files_pattern, drop_files_replacement, content, flags=re.DOTALL)
        
        # Write the modified content back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Successfully applied drag and drop fix to {file_path}")
        return True
        
    except Exception as e:
        print(f"Error applying fix: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    # Create a simple GUI to apply the fix
    root = tk.Tk()
    root.title("FramePack Drag & Drop Fix")
    root.geometry("400x300")
    
    # Create main frame
    main_frame = ttk.Frame(root, padding=20)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # Add a title
    title_label = ttk.Label(main_frame, text="FramePack Drag & Drop Fix", font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 20))
    
    # Add description
    desc_label = ttk.Label(main_frame, text="This tool will fix drag and drop functionality in the FramePack GUI.\nA backup of the original file will be created before making changes.", wraplength=350, justify="center")
    desc_label.pack(pady=(0, 20))
    
    # Add a button to apply the fix
    def on_apply():
        if apply_dnd_fix():
            messagebox.showinfo("Success", "Drag and drop fix applied successfully!\nPlease restart the FramePack GUI to see the changes.")
            root.destroy()
        else:
            messagebox.showerror("Error", "Failed to apply the fix. Please check the console for details.")
    
    apply_button = ttk.Button(main_frame, text="Apply Fix", command=on_apply)
    apply_button.pack(pady=10)
    
    # Add a button to exit
    exit_button = ttk.Button(main_frame, text="Exit", command=root.destroy)
    exit_button.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    main()
