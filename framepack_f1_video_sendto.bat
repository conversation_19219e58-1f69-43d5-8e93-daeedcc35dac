@echo off
setlocal enabledelayedexpansion
title FramePack F1 Video-to-Video Processor

:: Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo ===============================================
echo      FRAMEPACK F1 VIDEO-TO-VIDEO PROCESSOR
echo ===============================================
echo.
echo This script processes video files using the FramePack F1 model
echo to extend videos with AI-generated content.
echo.

:: Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python not found. Please make sure Python is installed and in your PATH.
    goto :end
)

:: Activate virtual environment - make sure we're using the full path
echo Activating virtual environment...
call "%SCRIPT_DIR%venv\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo Error: Failed to activate virtual environment. Please make sure it exists.
    echo Current directory: %CD%
    echo Virtual environment path: %SCRIPT_DIR%venv\Scripts\activate.bat
    goto :end
)

echo Virtual environment activated successfully.

:: Process each file passed to the script
for %%F in (%*) do (
    echo.
    echo Processing: %%F
    echo.
    python "%SCRIPT_DIR%batch_f1_video.py" "%%F"
    echo.
    echo Completed: %%F
    echo.
)

:: Deactivate virtual environment
call "%SCRIPT_DIR%venv\Scripts\deactivate.bat"

echo.
echo ===============================================
echo    FRAMEPACK F1 VIDEO-TO-VIDEO COMPLETED
echo ===============================================
echo.
pause

:end
