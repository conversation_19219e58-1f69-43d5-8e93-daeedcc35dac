#!/usr/bin/env python
"""
framepack_filename_cleaner.py

This script scans a specified folder for files that don't match the FramePack
output filename conventions and prompts the user to remove them.

FramePack output filename conventions:
1. Timestamp format: DATE_TIME_XXX_YYYY(_ZZ)(_seed#######)(_XXs).mp4
   - Example: 250420_121919_242_3623_37.mp4
   - Example: 250420_121919_242_3623_37_24s.mp4
   - Example: 250426_085120_706_7278_19_seed357798872.mp4
   - Example: 250426_085120_706_7278_19_seed357798872_24s.mp4

2. Generic filename format: image_name(_seed#######)(_XXs).mp4
   - Example: image_name.mp4
   - Example: image_name_5s.mp4
   - Example: image_name_seed123456.mp4
   - Example: image_name_seed123456_5s.mp4

3. PNG files with _start.png or _end.png suffix
   - Example: 250420_121919_242_3623_start.png
   - Example: 250420_121919_242_3623_end.png

Usage:
    python framepack_filename_cleaner.py [--folder FOLDER] [--dry-run] [--auto-remove]

Arguments:
    --folder: Path to the folder to scan (default: current directory)
    --dry-run: Only list files without removing them
    --auto-remove: Automatically remove all non-conforming files without prompting
"""

import os
import re
import argparse
import sys
from pathlib import Path
from typing import List, Tuple


def is_valid_framepack_mp4(filename: str) -> bool:
    """
    Check if a filename matches the FramePack MP4 output conventions.

    Args:
        filename: The filename to check

    Returns:
        bool: True if the filename matches FramePack conventions, False otherwise
    """
    # Only check MP4 files
    if not filename.lower().endswith('.mp4'):
        return False

    # Timestamp format pattern
    # DATE_TIME_XXX_YYYY(_ZZ)(_seed#######)(_XXs).mp4
    timestamp_pattern = r'^\d+_\d+_\d+_\d+(?:_\d+)?(?:_seed\d+)?(?:_\d+s)?\.mp4$'

    # Generic filename format pattern
    # image_name(_seed#######)(_XXs).mp4
    generic_pattern = r'^.+?(?:_seed\d+)?(?:_\d+s)?\.mp4$'

    # Check if the filename matches either pattern
    if re.match(timestamp_pattern, filename) or re.match(generic_pattern, filename):
        return True

    return False


def is_valid_framepack_png(filename: str) -> bool:
    """
    Check if a filename matches the FramePack PNG output conventions.

    Args:
        filename: The filename to check

    Returns:
        bool: True if the filename matches FramePack conventions, False otherwise
    """
    # Only check PNG files
    if not filename.lower().endswith('.png'):
        return False

    # Check for _start.png or _end.png suffix
    if filename.endswith('_start.png') or filename.endswith('_end.png'):
        # Timestamp format with start/end suffix
        timestamp_pattern = r'^\d+_\d+_\d+_\d+(?:_\d+)?(?:_seed\d+)?_(?:start|end)\.png$'

        # Generic filename with start/end suffix
        generic_pattern = r'^.+?(?:_seed\d+)?_(?:start|end)\.png$'

        if re.match(timestamp_pattern, filename) or re.match(generic_pattern, filename):
            return True

    return False


def is_valid_framepack_file(filename: str) -> bool:
    """
    Check if a filename matches any FramePack output conventions.

    Args:
        filename: The filename to check

    Returns:
        bool: True if the filename matches FramePack conventions, False otherwise
    """
    # Check if it's a valid MP4 or PNG file
    return is_valid_framepack_mp4(filename) or is_valid_framepack_png(filename)


def scan_folder(folder_path: str) -> Tuple[List[Path], List[Path], List[str]]:
    """
    Scan a folder for files and separate them into valid and invalid FramePack files.

    Args:
        folder_path: Path to the folder to scan

    Returns:
        Tuple containing:
        - List of valid FramePack files
        - List of invalid files (not matching FramePack conventions)
        - List of reasons why each invalid file doesn't match conventions
    """
    folder = Path(folder_path)

    # Check if the folder exists
    if not folder.exists() or not folder.is_dir():
        print(f"Error: Folder '{folder_path}' does not exist or is not a directory.")
        sys.exit(1)

    valid_files = []
    invalid_files = []
    invalid_reasons = []

    # Scan all files in the folder
    for file_path in folder.iterdir():
        if file_path.is_file():
            # Only check MP4 and PNG files
            if file_path.suffix.lower() in ['.mp4', '.png']:
                if is_valid_framepack_file(file_path.name):
                    valid_files.append(file_path)
                else:
                    invalid_files.append(file_path)

                    # Determine the reason why it's invalid
                    if file_path.suffix.lower() == '.mp4':
                        invalid_reasons.append("MP4 file doesn't match FramePack naming pattern")
                    elif file_path.suffix.lower() == '.png':
                        if '_cancelled' in file_path.name:
                            invalid_reasons.append("PNG file has '_cancelled' suffix (not standard FramePack naming)")
                        elif not (file_path.name.endswith('_start.png') or file_path.name.endswith('_end.png')):
                            invalid_reasons.append("PNG file missing '_start' or '_end' suffix")
                        else:
                            invalid_reasons.append("PNG file doesn't match FramePack naming pattern")
                    else:
                        invalid_reasons.append("Unknown file type")

    return valid_files, invalid_files, invalid_reasons


def remove_files(files_to_remove: List[Path], reasons: List[str], dry_run: bool = False, auto_remove: bool = False) -> int:
    """
    Remove files after confirmation from the user.

    Args:
        files_to_remove: List of files to remove
        reasons: List of reasons why each file doesn't match conventions
        dry_run: If True, only simulate removal without actually deleting files
        auto_remove: If True, remove all files without prompting

    Returns:
        int: Number of files removed
    """
    if not files_to_remove:
        return 0

    removed_count = 0

    if auto_remove:
        print(f"\nAutomatically removing {len(files_to_remove)} files...")
        for i, file_path in enumerate(files_to_remove):
            print(f"Removing: {file_path} - Reason: {reasons[i]}")
            if not dry_run:
                try:
                    os.remove(file_path)
                    removed_count += 1
                except Exception as e:
                    print(f"Error removing {file_path}: {e}")
        return removed_count

    # Ask for each file individually
    print("\nThe following files don't match FramePack naming conventions:")
    for i, file_path in enumerate(files_to_remove):
        print(f"{i+1}. {file_path} - Reason: {reasons[i]}")

    if dry_run:
        print("\nDry run mode: No files will be removed.")
        return 0

    # Ask if the user wants to remove all files at once
    response = input("\nDo you want to remove all these files? (y/n/s) [s = selective removal]: ").lower()

    if response == 'y':
        # Remove all files
        for file_path in files_to_remove:
            try:
                os.remove(file_path)
                print(f"Removed: {file_path}")
                removed_count += 1
            except Exception as e:
                print(f"Error removing {file_path}: {e}")

    elif response == 's':
        # Selective removal
        for i, file_path in enumerate(files_to_remove):
            response = input(f"Remove {file_path}? (Reason: {reasons[i]}) (y/n): ").lower()
            if response == 'y':
                try:
                    os.remove(file_path)
                    print(f"Removed: {file_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"Error removing {file_path}: {e}")

    else:
        print("No files were removed.")

    return removed_count


def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description="Scan a folder for files that don't match FramePack naming conventions.")
    parser.add_argument("--folder", type=str, default=".",
                        help="Path to the folder to scan (default: current directory)")
    parser.add_argument("--dry-run", action="store_true",
                        help="Only list files without removing them")
    parser.add_argument("--auto-remove", action="store_true",
                        help="Automatically remove all non-conforming files without prompting")
    parser.add_argument("--filter", type=str, choices=["all", "cancelled", "no-suffix", "mp4"],
                        default="all", help="Filter which types of non-conforming files to process")

    args = parser.parse_args()

    print(f"Scanning folder: {args.folder}")
    valid_files, invalid_files, invalid_reasons = scan_folder(args.folder)

    print(f"Found {len(valid_files)} valid FramePack files.")
    print(f"Found {len(invalid_files)} files that don't match FramePack naming conventions.")

    if not invalid_files:
        print("All files match FramePack naming conventions. Nothing to remove.")
        return

    # Apply filter if specified
    if args.filter != "all":
        filtered_files = []
        filtered_reasons = []

        for i, file_path in enumerate(invalid_files):
            reason = invalid_reasons[i]
            include = False

            if args.filter == "cancelled" and "_cancelled" in file_path.name:
                include = True
            elif args.filter == "no-suffix" and file_path.suffix.lower() == '.png' and not (
                file_path.name.endswith('_start.png') or
                file_path.name.endswith('_end.png') or
                "_cancelled" in file_path.name
            ):
                include = True
            elif args.filter == "mp4" and file_path.suffix.lower() == '.mp4':
                include = True

            if include:
                filtered_files.append(file_path)
                filtered_reasons.append(reason)

        print(f"Filtered to {len(filtered_files)} files based on filter: {args.filter}")
        invalid_files = filtered_files
        invalid_reasons = filtered_reasons

        if not invalid_files:
            print(f"No files match the filter criteria '{args.filter}'. Nothing to remove.")
            return

    removed_count = remove_files(invalid_files, invalid_reasons, args.dry_run, args.auto_remove)

    if args.dry_run:
        print(f"\nDry run complete. {len(invalid_files)} files would be removed.")
    else:
        print(f"\nRemoval complete. {removed_count} files were removed.")


if __name__ == "__main__":
    main()
