import os
import sys
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import json
import shutil
import time
import datetime
import threading  # For thread locking in video preview
import glob  # For finding video files
import pyperclip  # For clipboard operations
from PIL import Image, ImageGrab  # For clipboard image operations

# Import our patch for tkVideoPlayer to fix the NoneType close error
try:
    from tkvideoplayer_patch import apply_patches
    # Apply the patches to fix the NoneType close error
    apply_patches()
except ImportError:
    print("tkvideoplayer_patch module not found, skipping patches")
except Exception as e:
    print(f"Error applying patches to tkVideoPlayer: {e}")

# Import our custom GIF player for latent previews
try:
    from tk_gif_player import TkGifPlayer
    GIF_PLAYER_AVAILABLE = True
    print("TkGifPlayer loaded successfully. GIF preview is enabled.")
except ImportError:
    print("TkGifPlayer not available. GIF preview will be disabled.")
    GIF_PLAYER_AVAILABLE = False

# Import TkinterVideo for output video preview
try:
    from tkVideoPlayer import TkinterVideo
    TKVIDEO_AVAILABLE = True
    print("TkinterVideo loaded successfully. Video preview is enabled.")
except ImportError:
    print("TkinterVideo not available. Video preview will be disabled.")
    print("To enable video preview, install tkvideoplayer: pip install tkvideoplayer")
    TKVIDEO_AVAILABLE = False
except Exception as e:
    print(f"Error importing TkinterVideo: {e}")
    print("Video preview will be disabled.")
    TKVIDEO_AVAILABLE = False

# add the extra constant
from tkinterdnd2 import TkinterDnD, DND_FILES, DND_TEXT


# Import TkinterDnD for drag and drop support
try:
    # Import the required components from tkinterdnd2
    from tkinterdnd2 import TkinterDnD, DND_FILES
    # PRIVATE constant is imported by TkinterDnD internally
    TKDND_AVAILABLE = True
    print("TkinterDnD2 loaded successfully. Drag and drop is enabled.")
except ImportError:
    print("TkinterDnD2 not available. Drag and drop will be disabled.")
    print("To enable drag and drop, install tkinterdnd2: pip install tkinterdnd2")
    TKDND_AVAILABLE = False

# Function to load quick prompts from JSON file
def load_quick_prompts_from_json():
    # Default prompts to use if JSON file doesn't exist or has issues
    default_prompts = [
        'The girl dances gracefully, with clear movements, full of charm.',
        'A character doing some simple body movements.',
    ]

    json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "quick_list.json")

    try:
        # Check if the JSON file exists
        if os.path.exists(json_path):
            print(f"Loading quick prompts from {json_path}")
            with open(json_path, 'r', encoding='utf-8') as f:
                try:
                    prompts_data = json.load(f)

                    # Check if it's a list format
                    if isinstance(prompts_data, list):
                        print(f"Loaded {len(prompts_data)} prompts from JSON file (list format)")
                        return prompts_data

                    # Check if it's a dict with a "prompts" field
                    if isinstance(prompts_data, dict) and "prompts" in prompts_data and isinstance(prompts_data["prompts"], list):
                        result = prompts_data["prompts"]
                        print(f"Loaded {len(result)} prompts from JSON file (prompts field format)")
                        return result

                    print(f"JSON format not recognized, using default prompts")
                except json.JSONDecodeError:
                    print(f"JSON file is invalid, using default prompts")

                    # Try to backup the corrupted file
                    try:
                        backup_path = json_path + ".bak"
                        shutil.copy2(json_path, backup_path)
                        print(f"Backed up corrupted JSON file to {backup_path}")
                    except Exception as backup_error:
                        print(f"Failed to backup corrupted JSON file: {backup_error}")
        else:
            print(f"Quick prompts JSON file not found at {json_path}, using default prompts")

            # Create a sample JSON file for the user
            sample_data = {
                "prompts": default_prompts
            }
            try:
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(sample_data, f, indent=2, ensure_ascii=False)
                print(f"Created sample quick_list.json file at {json_path}")
            except Exception as e:
                print(f"Failed to create sample JSON file: {e}")

        return default_prompts
    except Exception as e:
        print(f"Error loading quick prompts from JSON: {e}")
        return default_prompts

# Function to save a prompt to the quick list JSON file
def save_prompt_to_quick_list(prompt_text):
    if not prompt_text or prompt_text.strip() == "":
        print("Cannot save empty prompt to quick list")
        return False

    json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "quick_list.json")

    try:
        # Load existing prompts
        existing_prompts = load_quick_prompts_from_json()

        # Check if the prompt already exists (case-sensitive comparison)
        if prompt_text not in existing_prompts:
            # Add the new prompt to the list
            existing_prompts.append(prompt_text)

            # Save the updated list back to the JSON file
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump({"prompts": existing_prompts}, f, indent=2, ensure_ascii=False)

            print(f"Added prompt to quick list: {prompt_text}")
            return True
        else:
            print(f"Prompt already exists in quick list: {prompt_text}")
            return False
    except Exception as e:
        print(f"Error saving prompt to quick list: {e}")
        return False

# Function to generate a timestamped filename for clipboard images
def generate_clipboard_image_filename():
    """Generate a timestamped filename for clipboard images"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    return f"clipboard_image_{timestamp}.png"

# Function to ensure the temp directory exists
def ensure_temp_directory():
    """Ensure the temp directory exists, create it if it doesn't"""
    temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
    if not os.path.exists(temp_dir):
        try:
            os.makedirs(temp_dir)
            print(f"Created temp directory at {temp_dir}")
        except Exception as e:
            print(f"Error creating temp directory: {e}")
            return None
    return temp_dir

# Function to ensure the latent_previews directory exists
def ensure_latent_previews_directory():
    """Ensure the latent_previews directory exists, create it if it doesn't"""
    latent_previews_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "latent_previews")
    if not os.path.exists(latent_previews_dir):
        try:
            os.makedirs(latent_previews_dir)
            print(f"Created latent_previews directory at {latent_previews_dir}")
        except Exception as e:
            print(f"Error creating latent_previews directory: {e}")
            return None
    return latent_previews_dir

class FramePackGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("FramePack Batch GUI")
        # Don't set fixed geometry - we'll calculate it dynamically
        self.root.resizable(True, True)

        # Set icon if available
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # Create main frame with padding
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create a direct content frame without scrolling
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        self.content_frame = content_frame

        # Initialize variables
        self.init_variables()

        # Create UI elements
        self.create_ui_elements()

        # Load default settings if available
        self.load_default_settings()

        # Initialize prompt entry state
        self.toggle_prompt_entry()

        # Add keyboard binding for Ctrl+V to paste URL from clipboard
        self.root.bind("<Control-v>", self.paste_url_from_clipboard)
        # Also bind Ctrl+V to the listbox and its frame for better usability
        self.files_listbox.bind("<Control-v>", self.paste_url_from_clipboard)
        self.listbox_frame.bind("<Control-v>", self.paste_url_from_clipboard)

        # Calculate and set the window size based on content
        self.update_window_size()

        # Bind to Configure event to update window size when content changes
        self.content_frame.bind("<Configure>", lambda _: self.update_window_size())

    def init_variables(self):
        # Basic settings
        self.input_dir = tk.StringVar(value="input")
        self.output_dir = tk.StringVar(value="output")
        self.fallback_prompt = tk.StringVar(value="")
        self.seed = tk.IntVar(value=-1)
        self.video_length = tk.DoubleVar(value=5.0)
        self.steps = tk.IntVar(value=25)
        self.distilled_cfg = tk.DoubleVar(value=10.0)

        # Model selection
        self.model_type = tk.StringVar(value="standard")  # "standard" or "f1"

        # File selection
        self.selected_files = []  # List to store selected file paths
        self.input_mode = tk.StringVar(value="directory")  # Mode: "directory", "files", "urls", or "combined"

        # Video trimming
        self.file_trim_settings = {}  # Dictionary to store trim settings for each file

        # Quick prompts
        self.quick_prompts = load_quick_prompts_from_json()  # Load quick prompts from JSON file

        # Advanced settings
        self.use_teacache = tk.BooleanVar(value=True)
        self.gpu_memory = tk.DoubleVar(value=6.0)
        self.mp4_crf = tk.IntVar(value=16)  # MP4 compression quality (0-51, lower is better)
        self.randomize_order = tk.BooleanVar(value=False)  # Randomize processing order
        self.clear_processed_list = tk.BooleanVar(value=True)  # Clear processed files list after completion
        self.use_image_prompt = tk.BooleanVar(value=False)  # Changed to False by default
        self.overwrite = tk.BooleanVar(value=False)
        self.fix_encoding = tk.BooleanVar(value=True)
        self.use_prompt_list_file = tk.BooleanVar(value=False)
        self.prompt_list_file = tk.StringVar(value="prompt_list.txt")
        self.copy_to_input = tk.BooleanVar(value=True)
        self.allow_duplicates = tk.BooleanVar(value=False)  # Allow duplicate files to be processed
        self.apply_all_prompts = tk.BooleanVar(value=False)  # Apply each prompt to each image
        self.latent_window_size = tk.IntVar(value=9)  # Latent window size (F1 default is 9)

        # Queue system variables
        self.job_queue = []  # List to store queued jobs
        self.current_job = None  # Currently running job
        self.current_job_iteration = 0  # Current iteration of the current job
        self.is_processing_queue = False  # Flag to indicate if queue is being processed
        self.iterations_var = tk.IntVar(value=1)  # Number of iterations for the current job

        # Video preview variables
        self.show_latent_preview = tk.BooleanVar(value=True)  # Show latent preview animation
        self.show_output_preview = tk.BooleanVar(value=True)  # Show output video preview
        self.video_load_lock = threading.Lock()  # Lock for video loading operations
        self.output_video_load_lock = threading.Lock()  # Lock for output video loading operations
        self.preview_loading = False  # Flag to track if a latent preview is currently being loaded
        self.output_preview_loading = False  # Flag to track if an output preview is currently being loaded
        self.current_preview = None  # Path to the currently playing latent preview
        self.current_output_preview = None  # Path to the currently playing output preview
        self.playback_rate = tk.DoubleVar(value=100.0)  # Playback rate for latent preview (percentage)
        self.session_start_time = time.time()  # Track when this session started
        self.generation_started = False  # Flag to track if generation has started in this session
        self._restarting_preview = False  # Flag to prevent multiple restart attempts for latent preview
        self._restarting_output_preview = False  # Flag to prevent multiple restart attempts for output preview

        # Status message variables
        self.status_message = tk.StringVar(value="")  # Current status message
        self.status_fade_job = None  # Reference to the fade job for cancellation

    def create_ui_elements(self):
        # Create a main frame with padding
        main_frame = ttk.Frame(self.content_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create a three-column layout
        left_column = ttk.Frame(main_frame)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        right_column = ttk.Frame(main_frame)
        right_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 5))

        # Add a third column for previews
        preview_column = ttk.Frame(main_frame)
        preview_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # Create frames for each section
        frame = left_column  # For backward compatibility with existing code

        # Input settings
        input_frame = ttk.LabelFrame(frame, text="Input Settings")
        input_frame.pack(fill=tk.X, pady=5)

        # Input type label
        input_label_frame = ttk.Frame(input_frame)
        input_label_frame.grid(row=0, column=0, columnspan=3, sticky=tk.W, padx=5, pady=5)

        ttk.Label(input_label_frame, text="Unified Batch Queue:", font=("", 9, "bold")).pack(side=tk.LEFT)

        # Set default input mode to unified
        self.input_mode.set("unified")

        # We'll keep the directory frame for backward compatibility but it won't be used in the unified mode
        self.dir_frame = ttk.Frame(input_frame)
        self.dir_frame.grid(row=1, column=0, columnspan=3, sticky=tk.W+tk.E, padx=5, pady=5)
        self.dir_frame.grid_remove()  # Hide it by default

        # Unified batch queue input
        self.files_frame = ttk.Frame(input_frame)
        self.files_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W+tk.E, padx=5, pady=5)

        # Selected items label
        ttk.Label(self.files_frame, text="Batch Queue:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)

        # Files listbox with scrollbar - using a similar structure to the working test script
        files_list_frame = ttk.Frame(self.files_frame)
        files_list_frame.grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)

        # Create a frame with a visible border for the listbox (similar to test_listbox_dnd.py)
        self.listbox_frame = ttk.Frame(files_list_frame, borderwidth=2, relief="solid")
        self.listbox_frame.pack(fill=tk.BOTH, expand=True)

        # Create the listbox
        self.files_listbox = tk.Listbox(self.listbox_frame, width=50, height=8)  # Increased height for better visibility
        self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=1, pady=1)

        # Add a scrollbar
        files_scrollbar = ttk.Scrollbar(self.listbox_frame, orient="vertical", command=self.files_listbox.yview)
        files_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.files_listbox.configure(yscrollcommand=files_scrollbar.set)

        # Add a status label for drag and drop
        self.drag_drop_label = ttk.Label(self.files_frame, text="Ready for drag and drop", foreground="gray")
        self.drag_drop_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=(0, 5))

        # Add drag and drop support if TkinterDnD is available
        if TKDND_AVAILABLE:
            # Register the listbox as a drop target
            self.files_listbox.drop_target_register(DND_FILES, DND_TEXT)
            self.files_listbox.dnd_bind('<<Drop>>', self.drop_files)
            self.files_listbox.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.files_listbox.dnd_bind('<<DragLeave>>', self.drag_leave)

            # Also register the listbox frame
            self.listbox_frame.drop_target_register(DND_FILES, DND_TEXT)
            self.listbox_frame.dnd_bind('<<Drop>>', self.drop_files)
            self.listbox_frame.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.listbox_frame.dnd_bind('<<DragLeave>>', self.drag_leave)

            # Update the label text to indicate drag and drop is available
            self.drag_drop_label.config(text="Drag and drop files, directories, or URLs here", foreground="blue")
        else:
            # Indicate that drag and drop is not available
            self.drag_drop_label.config(text="Drag and drop not available", foreground="red")

        # Create two frames for buttons - one for adding items and one for managing the queue
        buttons_container = ttk.Frame(self.files_frame)
        buttons_container.grid(row=0, column=2, sticky=tk.N+tk.S, padx=5, pady=5)

        # Add items buttons frame
        add_buttons_frame = ttk.LabelFrame(buttons_container, text="Add Items")
        add_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        # Add Files button
        self.add_files_button = ttk.Button(add_buttons_frame, text="Add Files...", command=self.browse_files)
        self.add_files_button.pack(fill=tk.X, padx=5, pady=2)
        self.add_tooltip(self.add_files_button, "Add image or video files to the batch queue")

        # Add Directory button
        self.add_dir_button = ttk.Button(add_buttons_frame, text="Add Directory...", command=self.add_directory)
        self.add_dir_button.pack(fill=tk.X, padx=5, pady=2)
        self.add_tooltip(self.add_dir_button, "Add a directory of images or videos to the batch queue")

        # Add URL button
        self.add_url_button = ttk.Button(add_buttons_frame, text="Add URL...", command=self.add_url)
        self.add_url_button.pack(fill=tk.X, padx=5, pady=2)
        self.add_tooltip(self.add_url_button, "Add an image URL to the batch queue")

        # Add Paste URL/Image button
        self.paste_url_button = ttk.Button(add_buttons_frame, text="Paste from Clipboard", command=self.paste_url_from_clipboard)
        self.paste_url_button.pack(fill=tk.X, padx=5, pady=2)
        self.add_tooltip(self.paste_url_button, "Paste URL or image from clipboard (Ctrl+V)")

        # Queue management buttons frame
        queue_buttons_frame = ttk.LabelFrame(buttons_container, text="Manage Queue")
        queue_buttons_frame.pack(fill=tk.X)

        # Move Up button
        self.move_up_button = ttk.Button(queue_buttons_frame, text="Move Up", command=self.move_selected_up)
        self.move_up_button.pack(fill=tk.X, padx=5, pady=2)
        self.add_tooltip(self.move_up_button, "Move selected item up in the queue")

        # Move Down button
        self.move_down_button = ttk.Button(queue_buttons_frame, text="Move Down", command=self.move_selected_down)
        self.move_down_button.pack(fill=tk.X, padx=5, pady=2)
        self.add_tooltip(self.move_down_button, "Move selected item down in the queue")

        # Remove button
        self.remove_file_button = ttk.Button(queue_buttons_frame, text="Remove Selected", command=self.remove_selected_file)
        self.remove_file_button.pack(fill=tk.X, padx=5, pady=2)
        self.add_tooltip(self.remove_file_button, "Remove selected item from the queue")

        # Clear All button
        self.clear_files_button = ttk.Button(queue_buttons_frame, text="Clear All", command=self.clear_files)
        self.clear_files_button.pack(fill=tk.X, padx=5, pady=2)
        self.add_tooltip(self.clear_files_button, "Clear all items from the queue")

        # Output directory
        output_frame = ttk.Frame(input_frame)
        output_frame.grid(row=3, column=0, columnspan=3, sticky=tk.W+tk.E, padx=5, pady=5)

        ttk.Label(output_frame, text="Output Directory:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(output_frame, textvariable=self.output_dir, width=50).grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        ttk.Button(output_frame, text="Browse...", command=lambda: self.browse_directory(self.output_dir)).grid(row=0, column=2, padx=5, pady=5)

        # Initialize the input mode
        self.toggle_input_mode()

        # Prompt settings - Grouped together
        prompt_frame = ttk.LabelFrame(frame, text="Prompt Settings")
        prompt_frame.pack(fill=tk.X, pady=5)

        # Create a header frame for the title and info icon
        header_frame = ttk.Frame(prompt_frame)
        header_frame.grid(row=0, column=0, columnspan=4, sticky=tk.W, padx=5, pady=5)

        # Create a label for "Prompt Priority Order" with an info icon
        priority_label = ttk.Label(header_frame, text="Prompt Priority Order", font=("", 9, "bold"))
        priority_label.pack(side=tk.LEFT)

        # Create an info icon (using a label with a character)
        info_icon = ttk.Label(header_frame, text="ⓘ", font=("", 10), foreground="blue")
        info_icon.pack(side=tk.LEFT, padx=(5, 0))

        # Create tooltip text
        tooltip_text = "Priority Order:\n1. Prompt list file (if enabled)\n2. Per-image .txt file (if exists)\n3. Image metadata (if enabled)\n4. Fallback prompt (below)"

        # Create tooltip functionality
        def show_tooltip(event):
            tooltip = tk.Toplevel(self.root)
            tooltip.wm_overrideredirect(True)
            tooltip.geometry(f"+{event.x_root+10}+{event.y_root+10}")
            tooltip_label = ttk.Label(tooltip, text=tooltip_text, background="#FFFFCC", relief="solid", borderwidth=1, padding=5, justify=tk.LEFT)
            tooltip_label.pack()
            self.active_tooltip = tooltip

        def hide_tooltip(_):  # Using _ to indicate unused parameter
            if hasattr(self, 'active_tooltip') and self.active_tooltip:
                self.active_tooltip.destroy()
                self.active_tooltip = None

        # Bind tooltip events to the info icon
        info_icon.bind("<Enter>", show_tooltip)
        info_icon.bind("<Leave>", hide_tooltip)

        # Create a frame for the image metadata checkbox
        metadata_frame = ttk.Frame(prompt_frame)
        metadata_frame.grid(row=1, column=0, columnspan=4, sticky=tk.W, padx=5, pady=5)

        # Use image prompt checkbox
        image_prompt_cb = ttk.Checkbutton(
            metadata_frame,
            text="Use image metadata for prompt",
            variable=self.use_image_prompt
        )
        image_prompt_cb.pack(anchor=tk.W)
        self.add_tooltip(image_prompt_cb, "Extract and use prompts from image metadata if available.\nWorks with images generated by Stable Diffusion and similar tools\nthat store the generation prompt in the image metadata.")

        # Prompt list file row
        file_frame = ttk.Frame(prompt_frame)
        file_frame.grid(row=2, column=0, columnspan=4, sticky=tk.W+tk.E, padx=5, pady=5)

        ttk.Label(file_frame, text="Prompt List File:").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Entry(file_frame, textvariable=self.prompt_list_file, width=50).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(
            file_frame,
            text="Browse...",
            command=lambda: self.browse_file(self.prompt_list_file, [("Text Files", "*.txt"), ("All Files", "*.*")])
        ).pack(side=tk.LEFT)

        # Edit button for prompt list file
        ttk.Button(
            file_frame,
            text="Edit...",
            command=self.edit_prompt_list_file
        ).pack(side=tk.LEFT, padx=(5, 0))

        # Prompt list options frame
        prompt_list_options_frame = ttk.Frame(prompt_frame)
        prompt_list_options_frame.grid(row=3, column=0, columnspan=4, sticky=tk.W, padx=5, pady=5)

        # Use prompt list file checkbox
        self.prompt_list_checkbox = ttk.Checkbutton(
            prompt_list_options_frame,
            text="Use prompt list file",
            variable=self.use_prompt_list_file,
            command=self.toggle_prompt_entry
        )
        self.prompt_list_checkbox.pack(side=tk.LEFT, padx=(0, 15))
        self.add_tooltip(self.prompt_list_checkbox, "Use prompts from the specified prompt list file.\nEach line in the file will be treated as a separate prompt.\nWhen enabled, the fallback prompt will be ignored.")

        # Apply each prompt to each image checkbox
        apply_all_cb = ttk.Checkbutton(
            prompt_list_options_frame,
            text="Apply each prompt to each image",
            variable=self.apply_all_prompts
        )
        apply_all_cb.pack(side=tk.LEFT)
        self.add_tooltip(apply_all_cb, "Apply each prompt from the prompt list to each image.\nThis will generate multiple videos per image, one for each prompt.\nOnly works when 'Use prompt list file' is enabled.")

        # Fallback prompt row
        fallback_frame = ttk.Frame(prompt_frame)
        fallback_frame.grid(row=4, column=0, columnspan=4, sticky=tk.W+tk.E, padx=5, pady=5)

        ttk.Label(fallback_frame, text="Fallback Prompt:").pack(side=tk.LEFT, padx=(0, 5))

        # Create combobox with quick prompts
        self.fallback_prompt_combobox = ttk.Combobox(
            fallback_frame,
            textvariable=self.fallback_prompt,
            values=self.quick_prompts,
            width=55
        )
        self.fallback_prompt_combobox.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Add buttons for quick prompt management
        buttons_frame = ttk.Frame(prompt_frame)
        buttons_frame.grid(row=5, column=0, columnspan=4, sticky=tk.E, padx=5, pady=5)

        # Save to quick list button
        self.save_prompt_button = ttk.Button(
            buttons_frame,
            text="Save to Quick List",
            command=self.save_current_prompt,
            width=15
        )
        self.save_prompt_button.pack(side=tk.RIGHT, padx=5)

        # Refresh quick list button
        self.refresh_prompts_button = ttk.Button(
            buttons_frame,
            text="Refresh Quick List",
            command=self.refresh_quick_prompts,
            width=15
        )
        self.refresh_prompts_button.pack(side=tk.RIGHT, padx=5)

        # Add a spacer to push the action buttons to the bottom of the left column
        spacer_frame = ttk.Frame(frame)
        spacer_frame.pack(fill=tk.BOTH, expand=True)

        # Add a frame for the latent preview in the preview column
        latent_preview_frame = ttk.LabelFrame(preview_column, text="Latent Preview")
        latent_preview_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Create a frame to hold the video player
        self.video_frame = ttk.Frame(latent_preview_frame)
        self.video_frame.pack(fill=tk.BOTH, padx=5, pady=5, expand=True)

        # Initialize the latent preview player
        # First try our custom GIF player for better compatibility
        if GIF_PLAYER_AVAILABLE:
            try:
                # Create a TkGifPlayer widget to display the latent preview at 60% of full resolution
                # Assuming full resolution is around 640x480, 60% would be around 384x288
                self.latent_preview_player = TkGifPlayer(master=self.video_frame, width=384, height=288)
                self.latent_preview_player.pack(fill=tk.BOTH, expand=True)

                # Set a flag to indicate we're using the GIF player
                self.using_gif_player = True

                print("Using TkGifPlayer for latent previews")
            except Exception as e:
                print(f"Error initializing GIF player: {e}")
                self.latent_preview_player = None
                self.using_gif_player = False
        else:
            self.using_gif_player = False
            self.latent_preview_player = None

        # Fall back to TkinterVideo if GIF player is not available
        if not self.using_gif_player and TKVIDEO_AVAILABLE:
            try:
                # Create a TkinterVideo widget to display the latent preview at 60% of full resolution
                self.latent_preview_player = TkinterVideo(master=self.video_frame, scaled=True, keep_aspect=True)
                self.latent_preview_player.pack(fill=tk.BOTH, expand=True)

                # Set a height for the video player (60% of full resolution)
                self.latent_preview_player.config(height=288)  # 60% of 480 height

                # Bind click event to pause/resume the video
                self.latent_preview_player.bind("<Button-1>", self.toggle_preview_playback)

                print("Using TkinterVideo for latent previews")
            except Exception as e:
                print(f"Error initializing latent preview player: {e}")
                # Create a fallback label if video player fails to initialize
                self.latent_preview_player = None
                ttk.Label(self.video_frame, text="Video player initialization failed. Please restart the application.").pack(pady=10)
        elif not self.using_gif_player:
            # Create a fallback label if neither player is available
            self.latent_preview_player = None
            ttk.Label(self.video_frame, text="Video preview not available. Install required packages to enable.").pack(pady=10)

        # Create a label to show status
        self.latent_preview_label = ttk.Label(latent_preview_frame, text="No preview available - Start generation to see preview")
        self.latent_preview_label.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Add a frame for the playback rate slider (only if using GIF player)
        if hasattr(self, 'using_gif_player') and self.using_gif_player:
            self.rate_frame = ttk.Frame(latent_preview_frame)
            self.rate_frame.pack(fill=tk.X, padx=5, pady=2)

            # Playback rate label
            self.rate_label = ttk.Label(self.rate_frame, text="Playback Rate: 100%")
            self.rate_label.pack(side=tk.LEFT, padx=5)

            # Playback rate slider (5% to 200%)
            self.rate_slider = ttk.Scale(
                self.rate_frame,
                from_=5,
                to=200,
                orient=tk.HORIZONTAL,
                variable=self.playback_rate,
                command=self.update_playback_rate
            )
            self.rate_slider.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # Add a checkbox to enable/disable latent preview
        ttk.Checkbutton(
            latent_preview_frame,
            text="Show latent preview animation",
            variable=self.show_latent_preview
        ).pack(anchor=tk.W, padx=5, pady=5)

        # Add queue management section
        queue_frame = ttk.LabelFrame(frame, text="Queue Management")
        queue_frame.pack(fill=tk.X, pady=5)

        # Queue controls
        queue_controls_frame = ttk.Frame(queue_frame)
        queue_controls_frame.pack(fill=tk.X, padx=5, pady=5)

        # Iterations control
        ttk.Label(queue_controls_frame, text="Iterations:").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Spinbox(queue_controls_frame, from_=1, to=100, textvariable=self.iterations_var, width=5).pack(side=tk.LEFT, padx=(0, 10))

        # Add to queue button
        self.add_to_queue_button = ttk.Button(
            queue_controls_frame,
            text="Add to Queue",
            command=self.add_current_settings_to_queue,
            width=15
        )
        self.add_to_queue_button.pack(side=tk.LEFT, padx=5)

        # Clear queue button
        self.clear_queue_button = ttk.Button(
            queue_controls_frame,
            text="Clear Queue",
            command=self.clear_queue,
            width=15
        )
        self.clear_queue_button.pack(side=tk.LEFT, padx=5)

        # Queue status
        queue_status_frame = ttk.Frame(queue_frame)
        queue_status_frame.pack(fill=tk.X, padx=5, pady=5)

        # Queue status label
        self.queue_status_label = ttk.Label(queue_status_frame, text="Queue: Empty")
        self.queue_status_label.pack(side=tk.LEFT, padx=5)

        # Queue progress label
        self.queue_progress_label = ttk.Label(queue_status_frame, text="")
        self.queue_progress_label.pack(side=tk.RIGHT, padx=5)

        # Queue list frame
        queue_list_frame = ttk.Frame(queue_frame)
        queue_list_frame.pack(fill=tk.BOTH, padx=5, pady=5, expand=True)

        # Queue listbox with scrollbar
        self.queue_listbox = tk.Listbox(queue_list_frame, height=4)
        self.queue_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        queue_scrollbar = ttk.Scrollbar(queue_list_frame, orient="vertical", command=self.queue_listbox.yview)
        queue_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.queue_listbox.configure(yscrollcommand=queue_scrollbar.set)

        # Add action buttons at the bottom of the left column
        action_frame = ttk.Frame(frame)
        action_frame.pack(fill=tk.X, pady=10)

        # Combined Run/Start Queue button
        self.run_button = ttk.Button(
            action_frame,
            text="Run FramePack",
            command=self.run_or_start_queue,
            width=15
        )
        self.run_button.pack(side=tk.RIGHT, padx=5)

        # Stop button (initially disabled)
        self.stop_button = ttk.Button(
            action_frame,
            text="Stop Generation",
            command=self.stop_framepack,
            width=15,
            state="disabled"
        )
        self.stop_button.pack(side=tk.RIGHT, padx=5)

        # Stop Queue button (initially disabled)
        self.stop_queue_button = ttk.Button(
            action_frame,
            text="Stop Queue",
            command=self.stop_queue,
            width=15,
            state="disabled"
        )
        self.stop_queue_button.pack(side=tk.RIGHT, padx=5)

        # Reset UI button
        self.reset_button = ttk.Button(
            action_frame,
            text="Reset UI State",
            command=self.reset_ui_state,
            width=15
        )
        self.reset_button.pack(side=tk.RIGHT, padx=5)

        # Save settings button
        save_button = ttk.Button(
            action_frame,
            text="Save Settings",
            command=self.save_settings,
            width=15
        )
        save_button.pack(side=tk.RIGHT, padx=5)

        # Load settings button
        load_button = ttk.Button(
            action_frame,
            text="Load Settings",
            command=self.load_settings,
            width=15
        )
        load_button.pack(side=tk.RIGHT, padx=5)

        # Add status message label at the bottom of the window
        status_frame = ttk.Frame(self.root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X)

        self.status_label = ttk.Label(
            status_frame,
            textvariable=self.status_message,
            font=("", 10),
            foreground="black",
            background="#f0f0f0",
            relief="sunken",
            anchor="w",
            padding=(5, 3)
        )
        self.status_label.pack(fill=tk.X, side=tk.BOTTOM)

        # ===== RIGHT COLUMN SETTINGS =====

        # Model selection
        model_frame = ttk.LabelFrame(right_column, text="Model Selection")
        model_frame.pack(fill=tk.X, pady=5)

        # Create radio buttons for model selection
        model_radio_frame = ttk.Frame(model_frame)
        model_radio_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Radiobutton(
            model_radio_frame,
            text="Standard FramePack",
            variable=self.model_type,
            value="standard"
        ).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Radiobutton(
            model_radio_frame,
            text="FramePack F1",
            variable=self.model_type,
            value="f1"
        ).pack(side=tk.LEFT)

        # Video settings
        video_frame = ttk.LabelFrame(right_column, text="Video Generation Settings")
        video_frame.pack(fill=tk.X, pady=5)

        # Create a more compact grid layout for video settings
        settings_grid = ttk.Frame(video_frame)
        settings_grid.pack(fill=tk.X, padx=5, pady=5)

        # Row 0: Seed
        ttk.Label(settings_grid, text="Seed (-1 for random):").grid(row=0, column=0, sticky=tk.W, padx=2, pady=2)
        seed_frame = ttk.Frame(settings_grid)
        seed_frame.grid(row=0, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Entry(seed_frame, textvariable=self.seed, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(seed_frame, text="Random", command=lambda: self.seed.set(-1), width=8).pack(side=tk.LEFT)

        # Row 1: Video Length
        ttk.Label(settings_grid, text="Video Length (sec):").grid(row=1, column=0, sticky=tk.W, padx=2, pady=2)
        length_frame = ttk.Frame(settings_grid)
        length_frame.grid(row=1, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Spinbox(length_frame, from_=1, to=120, textvariable=self.video_length, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(length_frame, text="(1-120)").pack(side=tk.LEFT)

        # Row 2: Steps
        ttk.Label(settings_grid, text="Steps:").grid(row=2, column=0, sticky=tk.W, padx=2, pady=2)
        steps_frame = ttk.Frame(settings_grid)
        steps_frame.grid(row=2, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Spinbox(steps_frame, from_=1, to=100, textvariable=self.steps, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(steps_frame, text="(1-100)").pack(side=tk.LEFT)

        # Row 3: Distilled CFG
        ttk.Label(settings_grid, text="Distilled CFG:").grid(row=3, column=0, sticky=tk.W, padx=2, pady=2)
        cfg_frame = ttk.Frame(settings_grid)
        cfg_frame.grid(row=3, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Spinbox(cfg_frame, from_=1.0, to=32.0, increment=0.5, textvariable=self.distilled_cfg, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(cfg_frame, text="(1.0-32.0)").pack(side=tk.LEFT)

        # Row 4: Latent Window Size (for F1 model)
        ttk.Label(settings_grid, text="Latent Window Size:").grid(row=4, column=0, sticky=tk.W, padx=2, pady=2)
        latent_frame = ttk.Frame(settings_grid)
        latent_frame.grid(row=4, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Spinbox(latent_frame, from_=1, to=20, textvariable=self.latent_window_size, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(latent_frame, text="(F1 model only)").pack(side=tk.LEFT)

        # Video trimming frame
        trim_frame = ttk.LabelFrame(right_column, text="Video Trimming (F1 Video Only)")
        trim_frame.pack(fill=tk.X, pady=5)

        # Create a frame for the trim controls
        trim_controls = ttk.Frame(trim_frame)
        trim_controls.pack(fill=tk.X, padx=5, pady=5)

        # Initialize trim variables
        self.trim_seconds = tk.DoubleVar(value=0.0)
        self.trim_from_beginning = tk.BooleanVar(value=False)

        # Trim seconds
        ttk.Label(trim_controls, text="Trim Seconds:").grid(row=0, column=0, sticky=tk.W, padx=2, pady=2)
        trim_seconds_frame = ttk.Frame(trim_controls)
        trim_seconds_frame.grid(row=0, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Spinbox(trim_seconds_frame, from_=0, to=120, increment=0.5, textvariable=self.trim_seconds, width=8).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(trim_seconds_frame, text="(0 = no trim)").pack(side=tk.LEFT)

        # Trim direction
        ttk.Label(trim_controls, text="Trim Direction:").grid(row=1, column=0, sticky=tk.W, padx=2, pady=2)
        trim_direction_frame = ttk.Frame(trim_controls)
        trim_direction_frame.grid(row=1, column=1, sticky=tk.W, padx=2, pady=2)
        ttk.Radiobutton(trim_direction_frame, text="From End", variable=self.trim_from_beginning, value=False).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(trim_direction_frame, text="From Beginning", variable=self.trim_from_beginning, value=True).pack(side=tk.LEFT)

        # Apply trim button and selected file info
        trim_apply_frame = ttk.Frame(trim_frame)
        trim_apply_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Apply trim button
        self.apply_trim_button = ttk.Button(trim_apply_frame, text="Apply Trim to Selected Video", command=self.apply_trim_to_selected)
        self.apply_trim_button.pack(side=tk.LEFT, padx=(0, 5))

        # Selected file info label
        self.trim_info_label = ttk.Label(trim_apply_frame, text="No video selected")
        self.trim_info_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Bind selection event to update the trim info label
        self.files_listbox.bind("<<ListboxSelect>>", self.update_trim_info)

        # Performance settings
        perf_frame = ttk.LabelFrame(right_column, text="Performance Settings")
        perf_frame.pack(fill=tk.X, pady=5)

        # Create a more compact layout for performance settings
        perf_grid = ttk.Frame(perf_frame)
        perf_grid.pack(fill=tk.X, padx=5, pady=5)

        # Left column of checkboxes
        perf_left = ttk.Frame(perf_grid)
        perf_left.grid(row=0, column=0, sticky=tk.NW)

        # TeaCache
        teacache_cb = ttk.Checkbutton(perf_left, text="Use TeaCache", variable=self.use_teacache)
        teacache_cb.pack(anchor=tk.W, pady=1)
        self.add_tooltip(teacache_cb, "Enable TeaCache for faster processing.\nMay affect hand quality in some cases.\nDisable if you notice issues with hands.")

        # Randomize Order
        randomize_cb = ttk.Checkbutton(perf_left, text="Randomize order", variable=self.randomize_order)
        randomize_cb.pack(anchor=tk.W, pady=1)
        self.add_tooltip(randomize_cb, "Randomize the order of image processing.\nUseful when processing a large batch of files\nto get a variety of results sooner.")

        # Right column of checkboxes
        perf_right = ttk.Frame(perf_grid)
        perf_right.grid(row=0, column=1, sticky=tk.NW)

        # Clear Processed List
        clear_list_cb = ttk.Checkbutton(perf_right, text="Clear processed list", variable=self.clear_processed_list)
        clear_list_cb.pack(anchor=tk.W, pady=1)
        self.add_tooltip(clear_list_cb, "Clear the list of processed files after completion.\nIf disabled, files that have been processed\nwill be skipped in future runs unless overwrite is enabled.")

        # GPU and CRF settings
        perf_settings = ttk.Frame(perf_frame)
        perf_settings.pack(fill=tk.X, padx=5, pady=(0, 5))

        # GPU Memory
        gpu_frame = ttk.Frame(perf_settings)
        gpu_frame.pack(fill=tk.X, pady=1)
        ttk.Label(gpu_frame, text="GPU Memory (GB):").pack(side=tk.LEFT, padx=(0, 2))
        ttk.Spinbox(gpu_frame, from_=6.0, to=128.0, increment=0.5, textvariable=self.gpu_memory, width=6).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(gpu_frame, text="(6-128)").pack(side=tk.LEFT)

        # MP4 Compression (CRF)
        crf_frame = ttk.Frame(perf_settings)
        crf_frame.pack(fill=tk.X, pady=1)
        ttk.Label(crf_frame, text="MP4 Compression:").pack(side=tk.LEFT, padx=(0, 2))
        ttk.Spinbox(crf_frame, from_=0, to=51, increment=1, textvariable=self.mp4_crf, width=6).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Label(crf_frame, text="(0-51, lower is better)").pack(side=tk.LEFT)

        # Output Video Preview
        output_preview_frame = ttk.LabelFrame(preview_column, text="Output Video Preview")
        output_preview_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Create a frame to hold the output video player
        self.output_video_frame = ttk.Frame(output_preview_frame)
        self.output_video_frame.pack(fill=tk.BOTH, padx=5, pady=5, expand=True)

        # Initialize the output video player with a try-except block to handle potential errors
        if TKVIDEO_AVAILABLE:
            try:
                # Create a TkinterVideo widget to display the output preview at full resolution
                # Keep scaled=True to prevent division by zero errors, but use full resolution
                self.output_preview_player = TkinterVideo(master=self.output_video_frame, scaled=True, keep_aspect=True)
                self.output_preview_player.pack(fill=tk.BOTH, expand=True)

                # Set height for full resolution display (standard 480p height)
                # This prevents division by zero errors in the TkinterVideo library
                self.output_preview_player.config(height=480)  # Full size for output preview

                # Bind click event to pause/resume the video
                self.output_preview_player.bind("<Button-1>", self.toggle_output_preview_playback)
            except Exception as e:
                print(f"Error initializing output preview player: {e}")
                # Create a fallback label if video player fails to initialize
                self.output_preview_player = None
                ttk.Label(self.output_video_frame, text="Video player initialization failed. Please restart the application.").pack(pady=10)
        else:
            # Create a fallback label if TkinterVideo is not available
            self.output_preview_player = None
            ttk.Label(self.output_video_frame, text="Video preview not available. Install tkvideoplayer to enable.").pack(pady=10)

        # Create a label to show status
        self.output_preview_label = ttk.Label(output_preview_frame, text="No output video available - Complete generation to see result")
        self.output_preview_label.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Add a checkbox to enable/disable output preview
        ttk.Checkbutton(
            output_preview_frame,
            text="Show output video preview",
            variable=self.show_output_preview
        ).pack(anchor=tk.W, padx=5, pady=5)

        # Output settings
        output_frame = ttk.LabelFrame(right_column, text="Output Settings")
        output_frame.pack(fill=tk.X, pady=5)

        # Create a more compact layout with two columns of checkboxes
        output_grid = ttk.Frame(output_frame)
        output_grid.pack(fill=tk.X, padx=5, pady=5)

        # Left column
        output_left = ttk.Frame(output_grid)
        output_left.grid(row=0, column=0, sticky=tk.NW)

        # Overwrite
        overwrite_cb = ttk.Checkbutton(output_left, text="Overwrite existing files", variable=self.overwrite)
        overwrite_cb.pack(anchor=tk.W, pady=1)
        self.add_tooltip(overwrite_cb, "Overwrite existing output files if they exist.\nIf disabled, existing files will be preserved\nand new files will have unique timestamps.")

        # Allow duplicates
        duplicates_cb = ttk.Checkbutton(output_left, text="Allow duplicate processing", variable=self.allow_duplicates)
        duplicates_cb.pack(anchor=tk.W, pady=1)
        self.add_tooltip(duplicates_cb, "Process the same file multiple times\nand ignore the processed files tracking.\nUseful for testing different settings on the same image.")

        # Right column
        output_right = ttk.Frame(output_grid)
        output_right.grid(row=0, column=1, sticky=tk.NW)

        # Fix encoding
        fix_encoding_cb = ttk.Checkbutton(output_right, text="Fix video encoding", variable=self.fix_encoding)
        fix_encoding_cb.pack(anchor=tk.W, pady=1)
        self.add_tooltip(fix_encoding_cb, "Re-encode videos for better web compatibility.\nSlightly increases processing time but ensures\nvideos play correctly in browsers and media players.")

        # Copy to input
        copy_input_cb = ttk.Checkbutton(output_right, text="Copy to input folder", variable=self.copy_to_input)
        copy_input_cb.pack(anchor=tk.W, pady=1)
        self.add_tooltip(copy_input_cb, "Copy the generated video to the input folder.\nMakes it easier to find your results\nwithout navigating to the output folder.")

        # Initialize the prompt entry state based on the checkbox
        self.toggle_prompt_entry()

    def browse_directory(self, var):
        directory = filedialog.askdirectory(initialdir=var.get())
        if directory:
            var.set(directory)

    def browse_file(self, var, filetypes):
        filename = filedialog.askopenfilename(initialdir=os.path.dirname(var.get()), filetypes=filetypes)
        if filename:
            var.set(filename)

    def edit_prompt_list_file(self):
        """Open the prompt list file in Notepad for editing"""
        file_path = self.prompt_list_file.get()
        if not file_path:
            self.show_status("Please select a prompt list file first.", "warning")
            return

        # Check if the file exists, create it if it doesn't
        if not os.path.exists(file_path):
            try:
                # Create an empty file
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("# Enter one prompt per line in this file\n")
                print(f"Created new prompt list file: {file_path}")
                self.show_status(f"Created new prompt list file: {file_path}", "success")
            except Exception as e:
                self.show_status(f"Failed to create prompt list file: {e}", "error")
                return

        try:
            # Launch Notepad to edit the file
            subprocess.Popen(['notepad.exe', file_path])
            print(f"Opened {file_path} in Notepad")
            self.show_status(f"Opened {file_path} in Notepad", "info")
        except Exception as e:
            self.show_status(f"Failed to open Notepad: {e}", "error")
            print(f"Error opening Notepad: {e}")

    def browse_files(self):
        """Browse for image and video files to add to the batch queue"""
        # Browse for image and video files
        filetypes = [
            ("All Supported Files", "*.jpg *.jpeg *.png *.bmp *.webp *.mp4 *.avi *.mov *.webm *.mkv"),
            ("Image Files", "*.jpg *.jpeg *.png *.bmp *.webp"),
            ("Video Files", "*.mp4 *.avi *.mov *.webm *.mkv"),
            ("JPEG Files", "*.jpg *.jpeg"),
            ("PNG Files", "*.png"),
            ("BMP Files", "*.bmp"),
            ("WebP Files", "*.webp"),
            ("MP4 Files", "*.mp4"),
            ("AVI Files", "*.avi"),
            ("MOV Files", "*.mov"),
            ("WebM Files", "*.webm"),
            ("MKV Files", "*.mkv"),
            ("All Files", "*.*")
        ]

        # Get initial directory from input_dir if no files selected yet, otherwise use directory of first file
        if self.selected_files and os.path.exists(self.selected_files[0]):
            initialdir = os.path.dirname(self.selected_files[0])
        else:
            initialdir = self.input_dir.get()

        filenames = filedialog.askopenfilenames(
            initialdir=initialdir,
            filetypes=filetypes,
            title="Select Images or Videos to Process"
        )

        if filenames:
            # Add files to the list (allowing duplicates)
            for filename in filenames:
                self.selected_files.append(filename)
                self.files_listbox.insert(tk.END, os.path.basename(filename))

            # Show a status message
            self.show_status(f"Added {len(filenames)} file(s) to the batch queue", "success")

            # Update window size after adding files
            self.root.after(100, self.update_window_size)

    def add_directory(self):
        """Add a directory of images and videos to the batch queue"""
        # Browse for directory
        directory = filedialog.askdirectory(
            initialdir=self.input_dir.get(),
            title="Select Directory with Images or Videos"
        )

        if not directory:
            return

        # Store the directory path with a special prefix to identify it as a directory
        dir_path = f"DIR:{directory}"
        self.selected_files.append(dir_path)

        # Display the directory in the listbox with a folder icon
        display_name = f"📁 {os.path.basename(directory) or directory}"
        self.files_listbox.insert(tk.END, display_name)

        # Show a status message
        self.show_status(f"Added directory: {directory}", "success")

        # Update window size after adding directory
        self.root.after(100, self.update_window_size)

    def add_url(self):
        """Add a URL to the batch queue"""
        # Prompt for URL
        url = simpledialog.askstring("Enter URL", "Enter image URL:")
        if url:
            # Validate URL format
            if not url.startswith(('http://', 'https://')):
                self.show_status("Invalid URL format. URLs must start with http:// or https://", "error")
                return

            # Add the URL to the list
            self.selected_files.append(url)

            # Display the URL or a shortened version if it's too long
            display_name = f"🌐 {url}"
            if len(url) > 50:
                display_name = f"🌐 {url[:47]}..."
            self.files_listbox.insert(tk.END, display_name)

            # Show a status message
            self.show_status(f"Added URL: {url}", "success")

            # Update window size after adding URL
            self.root.after(100, self.update_window_size)

    def remove_selected_file(self):
        """Remove the selected item from the batch queue"""
        selected_indices = self.files_listbox.curselection()
        if not selected_indices:
            self.show_status("No item selected to remove", "warning")
            return

        # Remove in reverse order to avoid index shifting
        for index in sorted(selected_indices, reverse=True):
            del self.selected_files[index]
            self.files_listbox.delete(index)

        # Show a status message
        if len(selected_indices) == 1:
            self.show_status("Item removed from the batch queue", "info")
        else:
            self.show_status(f"{len(selected_indices)} items removed from the batch queue", "info")

        # Update window size after removing files
        self.root.after(100, self.update_window_size)

    def move_selected_up(self):
        """Move the selected item up in the batch queue"""
        selected_indices = self.files_listbox.curselection()
        if not selected_indices:
            self.show_status("No item selected to move", "warning")
            return

        # Only handle single selection for now
        if len(selected_indices) > 1:
            self.show_status("Please select only one item to move", "warning")
            return

        index = selected_indices[0]

        # Can't move up if already at the top
        if index == 0:
            self.show_status("Item is already at the top of the queue", "info")
            return

        # Get the item data and display text
        item = self.selected_files[index]
        item_text = self.files_listbox.get(index)

        # Remove from current position
        del self.selected_files[index]
        self.files_listbox.delete(index)

        # Insert at new position
        new_index = index - 1
        self.selected_files.insert(new_index, item)
        self.files_listbox.insert(new_index, item_text)

        # Select the item at its new position
        self.files_listbox.selection_clear(0, tk.END)
        self.files_listbox.selection_set(new_index)
        self.files_listbox.see(new_index)

        # Show a status message
        self.show_status("Item moved up in the queue", "info")

    def move_selected_down(self):
        """Move the selected item down in the batch queue"""
        selected_indices = self.files_listbox.curselection()
        if not selected_indices:
            self.show_status("No item selected to move", "warning")
            return

        # Only handle single selection for now
        if len(selected_indices) > 1:
            self.show_status("Please select only one item to move", "warning")
            return

        index = selected_indices[0]

        # Can't move down if already at the bottom
        if index == len(self.selected_files) - 1:
            self.show_status("Item is already at the bottom of the queue", "info")
            return

        # Get the item data and display text
        item = self.selected_files[index]
        item_text = self.files_listbox.get(index)

        # Remove from current position
        del self.selected_files[index]
        self.files_listbox.delete(index)

        # Insert at new position
        new_index = index + 1
        self.selected_files.insert(new_index, item)
        self.files_listbox.insert(new_index, item_text)

        # Select the item at its new position
        self.files_listbox.selection_clear(0, tk.END)
        self.files_listbox.selection_set(new_index)
        self.files_listbox.see(new_index)

        # Show a status message
        self.show_status("Item moved down in the queue", "info")

    def clear_files(self):
        """Clear all items from the batch queue"""
        if not self.selected_files:
            self.show_status("Batch queue is already empty", "info")
            return

        # Ask for confirmation if there are items in the queue
        if len(self.selected_files) > 0:
            if not messagebox.askyesno("Confirm Clear", f"Are you sure you want to clear all {len(self.selected_files)} items from the batch queue?"):
                return

        # Clear the queue
        item_count = len(self.selected_files)
        self.selected_files = []
        self.files_listbox.delete(0, tk.END)

        # Show a status message
        self.show_status(f"Cleared {item_count} items from the batch queue", "info")

        # Update window size after clearing files
        self.root.after(100, self.update_window_size)

    def paste_image_from_clipboard(self):
        """Paste image from clipboard, save it to temp folder, and add it to the batch queue"""
        try:
            # Try to get an image from the clipboard
            clipboard_image = ImageGrab.grabclipboard()

            # Check if we got an image
            if clipboard_image is None or not isinstance(clipboard_image, Image.Image):
                print("No image found in clipboard")
                return False

            # Ensure the temp directory exists
            temp_dir = ensure_temp_directory()
            if not temp_dir:
                self.show_status("Failed to create temp directory for clipboard image", "error")
                return False

            # Generate a filename for the clipboard image
            image_filename = generate_clipboard_image_filename()
            image_path = os.path.join(temp_dir, image_filename)

            # Save the image as PNG with high quality
            clipboard_image.save(image_path, "PNG")
            print(f"Saved clipboard image to {image_path}")

            # Add the image to the batch queue
            self.selected_files.append(image_path)

            # Display the image name with an icon
            display_name = f"📷 {image_filename}"
            self.files_listbox.insert(tk.END, display_name)

            # Flash the listbox to indicate success
            self.files_listbox.config(background="#e0ffe0")  # Light green
            self.root.after(200, lambda: self.files_listbox.config(background="white"))

            # Show a status message
            self.show_status(f"Added image from clipboard: {image_filename}", "success")

            # Update window size after adding image
            self.root.after(300, self.update_window_size)

            return True
        except Exception as e:
            print(f"Error pasting image from clipboard: {e}")
            self.show_status(f"Failed to paste image from clipboard: {e}", "error")
            return False

    def paste_url_from_clipboard(self, _=None):
        """Paste content from clipboard and add it to the batch queue if it's valid"""
        try:
            # First try to paste an image from the clipboard
            if self.paste_image_from_clipboard():
                return

            # If no image was found, try to get text from clipboard
            clipboard_text = pyperclip.paste().strip()

            if not clipboard_text:
                self.show_status("Clipboard is empty", "warning")
                return

            print(f"Clipboard content: {clipboard_text}")

            # Check if it's a URL
            if clipboard_text.startswith(('http://', 'https://')):
                # Add the URL to the list (allowing duplicates)
                self.selected_files.append(clipboard_text)

                # Display the URL with an icon and a shortened version if it's too long
                display_name = f"🌐 {clipboard_text}"
                if len(clipboard_text) > 50:
                    display_name = f"🌐 {clipboard_text[:47]}..."

                self.files_listbox.insert(tk.END, display_name)
                print(f"Added URL from clipboard: {clipboard_text}")

                # Flash the listbox to indicate success
                self.files_listbox.config(background="#e0ffe0")  # Light green
                self.root.after(200, lambda: self.files_listbox.config(background="white"))

                # Show a status message
                self.show_status(f"Added URL from clipboard", "success")

                # Update window size after adding URL
                self.root.after(300, self.update_window_size)
            else:
                # Check if it might be a file path
                if os.path.exists(clipboard_text):
                    if os.path.isdir(clipboard_text):
                        # It's a directory
                        dir_path = f"DIR:{clipboard_text}"
                        self.selected_files.append(dir_path)

                        # Display the directory with a folder icon
                        display_name = f"📁 {os.path.basename(clipboard_text) or clipboard_text}"
                        self.files_listbox.insert(tk.END, display_name)

                        # Flash the listbox to indicate success
                        self.files_listbox.config(background="#e0ffe0")  # Light green
                        self.root.after(200, lambda: self.files_listbox.config(background="white"))

                        # Show a status message
                        self.show_status(f"Added directory from clipboard: {clipboard_text}", "success")
                    else:
                        # It's a file, check if it's a supported file type
                        ext = os.path.splitext(clipboard_text)[1].lower()
                        valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp', '.mp4', '.avi', '.mov', '.webm', '.mkv']

                        if ext in valid_extensions:
                            # Add the file to the list
                            self.selected_files.append(clipboard_text)

                            # Display the file name
                            self.files_listbox.insert(tk.END, os.path.basename(clipboard_text))

                            # Flash the listbox to indicate success
                            self.files_listbox.config(background="#e0ffe0")  # Light green
                            self.root.after(200, lambda: self.files_listbox.config(background="white"))

                            # Show a status message
                            self.show_status(f"Added file from clipboard: {os.path.basename(clipboard_text)}", "success")
                        else:
                            self.show_status(f"Unsupported file type: {ext}. Must be an image or video file.", "warning")
                else:
                    self.show_status("Clipboard content is not a valid URL, directory, or file path", "warning")

                # Update window size after processing clipboard content
                self.root.after(300, self.update_window_size)
        except Exception as e:
            print(f"Error pasting from clipboard: {e}")
            self.show_status(f"Failed to paste from clipboard: {e}", "error")

    def drop_files(self, event):
        """Handle files, directories, or URLs dropped onto the widget"""
        if not TKDND_AVAILABLE:
            return

        try:
            # Get the dropped data
            dropped_data = self.root.tk.splitlist(event.data)
            print(f"Received drop event with data: {event.data}")

            # Count how many new items we add
            added_count = 0
            skipped_count = 0

            # Valid file extensions
            valid_image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
            valid_video_extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
            valid_extensions = valid_image_extensions + valid_video_extensions

            # Process each dropped item
            for item in dropped_data:
                # Remove quotes if present (Windows sometimes adds them)
                if item.startswith('"') and item.endswith('"'):
                    item = item[1:-1]

                # Check if it's a URL
                if item.startswith(('http://', 'https://')):
                    # Handle URL
                    self.selected_files.append(item)

                    # Display the URL with an icon and a shortened version if it's too long
                    display_name = f"🌐 {item}"
                    if len(item) > 50:
                        display_name = f"🌐 {item[:47]}..."

                    self.files_listbox.insert(tk.END, display_name)
                    added_count += 1
                    print(f"Added URL: {item}")
                else:
                    # Handle file or directory
                    # Normalize path
                    file_path = os.path.normpath(item)
                    print(f"Processing dropped item: {file_path}")

                    if os.path.exists(file_path):
                        if os.path.isdir(file_path):
                            # It's a directory
                            dir_path = f"DIR:{file_path}"
                            self.selected_files.append(dir_path)

                            # Display the directory with a folder icon
                            display_name = f"📁 {os.path.basename(file_path) or file_path}"
                            self.files_listbox.insert(tk.END, display_name)
                            added_count += 1
                            print(f"Added directory: {file_path}")
                        elif os.path.isfile(file_path):
                            # It's a file, check if it's a supported file type
                            ext = os.path.splitext(file_path)[1].lower()
                            if ext in valid_extensions:
                                # Add the file to the list
                                self.selected_files.append(file_path)
                                self.files_listbox.insert(tk.END, os.path.basename(file_path))
                                added_count += 1

                                # Log whether it's an image or video file
                                if ext in valid_image_extensions:
                                    print(f"Added image file: {file_path}")
                                elif ext in valid_video_extensions:
                                    print(f"Added video file: {file_path}")
                            else:
                                print(f"Warning: Skipping {file_path} - not a supported file type (valid extensions: {valid_extensions})")
                                skipped_count += 1
                        else:
                            print(f"Warning: Not a file or directory: {file_path}")
                            skipped_count += 1
                    else:
                        print(f"Warning: Path not found: {file_path}")
                        skipped_count += 1

            # Show a summary message in the status label
            if added_count > 0:
                self.drag_drop_label.config(text=f"Added {added_count} items", foreground="green")
                self.show_status(f"Added {added_count} items to the batch queue", "success")
            else:
                self.drag_drop_label.config(text="No new items were added", foreground="red")
                if skipped_count > 0:
                    self.show_status(f"No items added. Skipped {skipped_count} unsupported items.", "warning")
                else:
                    self.show_status("No items added to the batch queue", "warning")
        except Exception as e:
            print(f"Error during drop: {str(e)}")
            import traceback
            traceback.print_exc()
            self.drag_drop_label.config(text=f"Error: {str(e)}", foreground="red")
            self.show_status(f"Error processing dropped items: {str(e)}", "error")
        finally:
            # Restore normal appearance
            self.files_listbox.config(background="white")
            self.listbox_frame.config(relief="solid", borderwidth=2)

            # Update window size after adding files
            self.root.after(100, self.update_window_size)

    def drag_enter(self, event):
        """Handle drag enter event"""
        if not TKDND_AVAILABLE:
            return

        try:
            print(f"Drag enter event on {event.widget}")

            # Change appearance to indicate it's a drop target
            self.files_listbox.config(background="#e0f0ff")  # Light blue background
            self.listbox_frame.config(relief="groove", borderwidth=3)

            # Update the label to indicate drop action
            self.drag_drop_label.config(text="Drop files, directories, or URLs here!", foreground="#0066cc")
        except Exception as e:
            print(f"Error in drag_enter: {str(e)}")

    def drag_leave(self, event):
        """Handle drag leave event"""
        if not TKDND_AVAILABLE:
            return

        try:
            print(f"Drag leave event on {event.widget}")

            # Restore normal appearance
            self.files_listbox.config(background="white")
            self.listbox_frame.config(relief="solid", borderwidth=2)

            # Restore the default label text
            self.drag_drop_label.config(text="Drag and drop files, directories, or URLs here", foreground="blue")
        except Exception as e:
            print(f"Error in drag_leave: {str(e)}")

    def update_window_size(self):
        """Calculate and set the window size based on the content"""
        # Update the UI to ensure all widgets are properly laid out
        self.root.update_idletasks()

        # Get the required width and height of the content frame
        content_width = self.content_frame.winfo_reqwidth()
        content_height = self.content_frame.winfo_reqheight()

        # Add padding for window borders, etc.
        width_padding = 30
        height_padding = 80  # Extra padding for the status bar at the bottom

        # Calculate the required window size
        window_width = content_width + width_padding
        window_height = content_height + height_padding

        # Get the screen dimensions
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Limit the window size to 90% of the screen size
        max_width = int(screen_width * 0.9)
        max_height = int(screen_height * 0.9)

        window_width = min(window_width, max_width)
        window_height = min(window_height, max_height)

        # Ensure minimum size for usability
        window_width = max(window_width, 800)
        window_height = max(window_height, 700)

        # Set the window size
        self.root.geometry(f"{window_width}x{window_height}")

        # Print the calculated size for debugging
        print(f"Window size updated to {window_width}x{window_height}")

    def update_trim_info(self, event=None):
        """Update the trim info label when a file is selected in the listbox"""
        # We no longer reset the trim controls when selecting a file
        # This allows the user to keep editing the values without them being reset

        selected_indices = self.files_listbox.curselection()
        if not selected_indices:
            self.trim_info_label.config(text="No video selected")
            return

        # Get the selected file
        index = selected_indices[0]
        if index >= len(self.selected_files):
            self.trim_info_label.config(text="Invalid selection")
            return

        file_path = self.selected_files[index]

        # Check if it's a video file
        is_video = False
        if isinstance(file_path, str):
            if file_path.startswith("DIR:"):
                self.trim_info_label.config(text="Cannot trim directories")
                return
            elif file_path.startswith(("http://", "https://")):
                self.trim_info_label.config(text="Cannot trim URLs directly")
                return
            else:
                # Check file extension
                ext = os.path.splitext(file_path)[1].lower()
                valid_video_extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
                is_video = ext in valid_video_extensions

        if not is_video:
            self.trim_info_label.config(text="Not a video file")
            return

        # Check if this file has trim settings
        if file_path in self.file_trim_settings:
            trim_settings = self.file_trim_settings[file_path]
            trim_seconds = trim_settings.get('trim_seconds', 0)
            trim_from_beginning = trim_settings.get('trim_from_beginning', False)
            trim_direction = "beginning" if trim_from_beginning else "end"

            # Only update the trim info label, don't change the control values
            # This allows the user to keep editing the values without them being reset
            self.trim_info_label.config(text=f"Trim: {trim_seconds}s from {trim_direction}")
        else:
            # Only update the label, don't reset the controls
            self.trim_info_label.config(text="No trim settings for this file")

    def apply_trim_to_selected(self):
        """Apply trim settings to the selected video file"""
        selected_indices = self.files_listbox.curselection()
        if not selected_indices:
            self.show_status("No video selected", "warning")
            return

        # Get the selected file
        index = selected_indices[0]
        if index >= len(self.selected_files):
            self.show_status("Invalid selection", "error")
            return

        file_path = self.selected_files[index]

        # Check if it's a video file
        is_video = False
        if isinstance(file_path, str):
            if file_path.startswith("DIR:"):
                self.show_status("Cannot trim directories", "warning")
                return
            elif file_path.startswith(("http://", "https://")):
                self.show_status("Cannot trim URLs directly", "warning")
                return
            else:
                # Check file extension
                ext = os.path.splitext(file_path)[1].lower()
                valid_video_extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
                is_video = ext in valid_video_extensions

        if not is_video:
            self.show_status("Not a video file", "warning")
            return

        # Get trim settings
        trim_seconds = self.trim_seconds.get()
        trim_from_beginning = self.trim_from_beginning.get()

        # If trim seconds is 0, remove any existing trim settings
        if trim_seconds <= 0:
            if file_path in self.file_trim_settings:
                del self.file_trim_settings[file_path]
                self.show_status(f"Trim settings removed for {os.path.basename(file_path)}", "info")

                # Update the display in the listbox
                display_name = os.path.basename(file_path)
                self.files_listbox.delete(index)
                self.files_listbox.insert(index, display_name)
                self.files_listbox.selection_set(index)

                # Update the trim info label
                self.trim_info_label.config(text="No trim settings")
            else:
                self.show_status("No trim settings to remove", "info")
            return

        # Store the trim settings
        self.file_trim_settings[file_path] = {
            'trim_seconds': trim_seconds,
            'trim_from_beginning': trim_from_beginning
        }

        # Update the display in the listbox to show trim indicator
        trim_direction = "beginning" if trim_from_beginning else "end"
        display_name = f"{os.path.basename(file_path)} [Trim: {trim_seconds}s from {trim_direction}]"
        self.files_listbox.delete(index)
        self.files_listbox.insert(index, display_name)
        self.files_listbox.selection_set(index)

        # Update the trim info label
        self.trim_info_label.config(text=f"Trim: {trim_seconds}s from {trim_direction}")

        # Show a status message
        self.show_status(f"Trim settings applied: {trim_seconds}s from {trim_direction}", "success")

    def show_status(self, message, message_type="info", duration=3000):
        """
        Show a status message at the bottom of the window

        Args:
            message (str): The message to display
            message_type (str): Type of message - "info", "success", "warning", or "error"
            duration (int): How long to display the message in milliseconds
        """
        # Cancel any existing fade job
        if self.status_fade_job is not None:
            self.root.after_cancel(self.status_fade_job)
            self.status_fade_job = None

        # Set message color based on type
        if message_type == "info":
            self.status_label.configure(foreground="black", background="#e0f0ff")  # Light blue
        elif message_type == "success":
            self.status_label.configure(foreground="dark green", background="#e0ffe0")  # Light green
        elif message_type == "warning":
            self.status_label.configure(foreground="dark orange", background="#fff8e0")  # Light yellow
        elif message_type == "error":
            self.status_label.configure(foreground="dark red", background="#ffe0e0")  # Light red
        else:
            self.status_label.configure(foreground="black", background="#f0f0f0")  # Default gray

        # Set the message
        self.status_message.set(message)

        # Schedule the fade
        self.status_fade_job = self.root.after(duration, self.fade_status)

    def fade_status(self):
        """Gradually fade out the status message"""
        # Reset the status label appearance
        self.status_label.configure(foreground="gray", background="#f0f0f0")

        # Clear the message after a short delay
        self.status_fade_job = self.root.after(500, lambda: self.status_message.set(""))

    def add_tooltip(self, widget, text):
        """Add a tooltip to a widget"""
        tooltip = tk.Label(self.root, text=text, background="#FFFFCC",
                          relief="solid", borderwidth=1, justify=tk.LEFT,
                          font=("", 9), padx=5, pady=3)
        tooltip.pack_forget()  # Hide initially

        def show_tooltip(event):
            # Position the tooltip at the mouse cursor position
            # Use event coordinates for multi-monitor support
            x = event.x_root + 15
            y = event.y_root + 10
            tooltip.lift()
            tooltip.place(x=x, y=y)

        def hide_tooltip(_):
            tooltip.place_forget()

        # Bind events to show/hide the tooltip
        widget.bind("<Enter>", show_tooltip)
        widget.bind("<Leave>", hide_tooltip)

    def toggle_input_mode(self):
        """Handle input mode changes - now simplified for unified mode"""
        # In unified mode, we always show the files_frame
        self.dir_frame.grid_remove()
        self.files_frame.grid()

        # Set the drag and drop label text
        if TKDND_AVAILABLE:
            self.drag_drop_label.config(text="Drag and drop files, directories, or URLs here", foreground="blue")

        # Update the window size after changing the mode
        self.root.after(100, self.update_window_size)

    def toggle_prompt_entry(self):
        """Enable or disable the fallback prompt entry based on the use_prompt_list_file checkbox"""
        if self.use_prompt_list_file.get():
            # Disable fallback prompt controls when using prompt list file
            self.fallback_prompt_combobox.configure(state="disabled")
            self.save_prompt_button.configure(state="disabled")
            self.refresh_prompts_button.configure(state="disabled")
        else:
            # Enable fallback prompt controls when not using prompt list file
            self.fallback_prompt_combobox.configure(state="normal")
            self.save_prompt_button.configure(state="normal")
            self.refresh_prompts_button.configure(state="normal")

        # Update the window size after changing the prompt entry state
        self.root.after(100, self.update_window_size)

    # Removed duplicate tooltip methods as they're replaced by the new tooltip system

    def reset_ui_state(self):
        """Force reset the UI state to allow running FramePack again"""
        try:
            # Check if there's a stop flag file and remove it
            stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
            if os.path.exists(stop_flag_path):
                try:
                    os.remove(stop_flag_path)
                    print(f"Removed stop flag file: {stop_flag_path}")
                except Exception as e:
                    print(f"Error removing stop flag file: {e}")

            # Reset button states
            self.run_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.stop_queue_button.config(state="disabled")

            # Reset status check counter
            self.status_check_count = 0

            # Cancel any scheduled safety stop
            if hasattr(self, 'safety_stop_job') and self.safety_stop_job is not None:
                self.root.after_cancel(self.safety_stop_job)
                self.safety_stop_job = None
                print("Cancelled scheduled safety stop")

            self.show_status("UI state has been reset. You can now run FramePack again.", "success")
        except Exception as e:
            print(f"Error resetting UI state: {e}")
            self.show_status(f"Failed to reset UI state: {e}", "error")

    def save_current_prompt(self):
        """Save the current prompt to the quick list"""
        current_prompt = self.fallback_prompt.get().strip()

        if not current_prompt:
            self.show_status("Cannot save an empty prompt to the quick list.", "warning")
            return

        if save_prompt_to_quick_list(current_prompt):
            # Refresh the combobox values
            self.refresh_quick_prompts()
            self.show_status(f"Prompt added to quick list: {current_prompt[:50]}{'...' if len(current_prompt) > 50 else ''}", "success")
        else:
            # If the prompt already exists or there was an error
            self.show_status("Prompt already exists in the quick list or there was an error saving it.", "info")

    def refresh_quick_prompts(self):
        """Reload the quick prompts from the JSON file"""
        # Reload the prompts
        self.quick_prompts = load_quick_prompts_from_json()

        # Update the combobox values
        self.fallback_prompt_combobox['values'] = self.quick_prompts

        # Show a message
        print(f"Quick prompts refreshed. Loaded {len(self.quick_prompts)} prompts.")

    def save_settings(self):
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")],
            initialdir=".",
            title="Save Settings"
        )

        if not filename:
            return

        settings = {
            "input_dir": self.input_dir.get(),
            "output_dir": self.output_dir.get(),
            "fallback_prompt": self.fallback_prompt.get(),
            "seed": self.seed.get(),
            "video_length": self.video_length.get(),
            "steps": self.steps.get(),
            "distilled_cfg": self.distilled_cfg.get(),
            "model_type": self.model_type.get(),
            "latent_window_size": self.latent_window_size.get(),
            "use_teacache": self.use_teacache.get(),
            "gpu_memory": self.gpu_memory.get(),
            "mp4_crf": self.mp4_crf.get(),
            "randomize_order": self.randomize_order.get(),
            "clear_processed_list": self.clear_processed_list.get(),
            "use_image_prompt": self.use_image_prompt.get(),
            "overwrite": self.overwrite.get(),
            "fix_encoding": self.fix_encoding.get(),
            "use_prompt_list_file": self.use_prompt_list_file.get(),
            "prompt_list_file": self.prompt_list_file.get(),
            "copy_to_input": self.copy_to_input.get(),
            "apply_all_prompts": self.apply_all_prompts.get(),
            "input_mode": self.input_mode.get(),
            "selected_files": self.selected_files
        }

        try:
            with open(filename, 'w') as f:
                json.dump(settings, f, indent=4)
            self.show_status(f"Settings saved to {filename}", "success")
        except Exception as e:
            self.show_status(f"Failed to save settings: {str(e)}", "error")

    def load_settings(self):
        filename = filedialog.askopenfilename(
            filetypes=[("JSON Files", "*.json"), ("All Files", "*.*")],
            initialdir=".",
            title="Load Settings"
        )

        if not filename:
            return

        try:
            with open(filename, 'r') as f:
                settings = json.load(f)

            # Apply settings
            self.input_dir.set(settings.get("input_dir", "input"))
            self.output_dir.set(settings.get("output_dir", "output"))
            self.fallback_prompt.set(settings.get("fallback_prompt", ""))
            self.seed.set(settings.get("seed", -1))
            self.video_length.set(settings.get("video_length", 5.0))
            self.steps.set(settings.get("steps", 25))
            self.distilled_cfg.set(settings.get("distilled_cfg", 10.0))
            self.model_type.set(settings.get("model_type", "standard"))
            self.latent_window_size.set(settings.get("latent_window_size", 9))
            self.use_teacache.set(settings.get("use_teacache", True))
            self.gpu_memory.set(settings.get("gpu_memory", 6.0))
            self.mp4_crf.set(settings.get("mp4_crf", 16))
            self.randomize_order.set(settings.get("randomize_order", False))
            self.clear_processed_list.set(settings.get("clear_processed_list", True))
            self.use_image_prompt.set(settings.get("use_image_prompt", True))
            self.overwrite.set(settings.get("overwrite", False))
            self.fix_encoding.set(settings.get("fix_encoding", True))
            self.use_prompt_list_file.set(settings.get("use_prompt_list_file", False))
            self.prompt_list_file.set(settings.get("prompt_list_file", "prompt_list.txt"))
            self.copy_to_input.set(settings.get("copy_to_input", True))
            self.allow_duplicates.set(settings.get("allow_duplicates", False))
            self.apply_all_prompts.set(settings.get("apply_all_prompts", False))

            # Load input mode setting (convert from old format if needed)
            if "input_mode" in settings:
                self.input_mode.set(settings.get("input_mode", "directory"))
            else:
                # Convert from old format (use_individual_files boolean)
                old_mode = settings.get("use_individual_files", False)
                self.input_mode.set("files" if old_mode else "directory")

            # Load selected files
            self.selected_files = settings.get("selected_files", [])

            # Update the files listbox
            self.files_listbox.delete(0, tk.END)
            for file_path in self.selected_files:
                # Check if the file still exists
                if os.path.exists(file_path):
                    self.files_listbox.insert(tk.END, os.path.basename(file_path))
                else:
                    print(f"Warning: File {file_path} no longer exists")

            # Update UI based on mode
            self.toggle_input_mode()

            # Update window size after loading settings
            self.root.after(100, self.update_window_size)

            self.show_status(f"Settings loaded from {filename}", "success")
        except Exception as e:
            self.show_status(f"Failed to load settings: {str(e)}", "error")

    def load_default_settings(self):
        default_settings_path = "framepack_default_settings.json"
        if os.path.exists(default_settings_path):
            try:
                with open(default_settings_path, 'r') as f:
                    settings = json.load(f)

                # Apply settings
                self.input_dir.set(settings.get("input_dir", "input"))
                self.output_dir.set(settings.get("output_dir", "output"))
                self.fallback_prompt.set(settings.get("fallback_prompt", ""))
                self.seed.set(settings.get("seed", -1))
                self.video_length.set(settings.get("video_length", 5.0))
                self.steps.set(settings.get("steps", 25))
                self.distilled_cfg.set(settings.get("distilled_cfg", 10.0))
                self.model_type.set(settings.get("model_type", "standard"))
                self.latent_window_size.set(settings.get("latent_window_size", 9))
                self.use_teacache.set(settings.get("use_teacache", True))
                self.gpu_memory.set(settings.get("gpu_memory", 6.0))
                self.mp4_crf.set(settings.get("mp4_crf", 16))
                self.randomize_order.set(settings.get("randomize_order", False))
                self.clear_processed_list.set(settings.get("clear_processed_list", True))
                self.use_image_prompt.set(settings.get("use_image_prompt", True))
                self.overwrite.set(settings.get("overwrite", False))
                self.fix_encoding.set(settings.get("fix_encoding", True))
                self.use_prompt_list_file.set(settings.get("use_prompt_list_file", False))
                self.prompt_list_file.set(settings.get("prompt_list_file", "prompt_list.txt"))
                self.copy_to_input.set(settings.get("copy_to_input", True))
                self.allow_duplicates.set(settings.get("allow_duplicates", False))
                self.apply_all_prompts.set(settings.get("apply_all_prompts", False))

                # Load input mode setting (convert from old format if needed)
                if "input_mode" in settings:
                    self.input_mode.set(settings.get("input_mode", "directory"))
                else:
                    # Convert from old format (use_individual_files boolean)
                    old_mode = settings.get("use_individual_files", False)
                    self.input_mode.set("files" if old_mode else "directory")

                # Load selected files
                self.selected_files = settings.get("selected_files", [])

                # Load trim settings if they exist
                if "file_trim_settings" in settings:
                    self.file_trim_settings = settings.get("file_trim_settings", {})

                # Update the files listbox
                self.files_listbox.delete(0, tk.END)
                for file_path in self.selected_files:
                    # Check if the file still exists
                    if os.path.exists(file_path) or file_path.startswith(("DIR:", "http://", "https://")):
                        # Check if this file has trim settings
                        if file_path in self.file_trim_settings:
                            trim_settings = self.file_trim_settings[file_path]
                            trim_seconds = trim_settings.get('trim_seconds', 0)
                            trim_from_beginning = trim_settings.get('trim_from_beginning', False)

                            if trim_seconds > 0:
                                trim_direction = "beginning" if trim_from_beginning else "end"
                                display_name = f"{os.path.basename(file_path)} [Trim: {trim_seconds}s from {trim_direction}]"
                                self.files_listbox.insert(tk.END, display_name)
                                continue

                        # No trim settings or trim seconds is 0
                        if file_path.startswith("DIR:"):
                            # It's a directory
                            dir_path = file_path[4:]  # Remove the DIR: prefix
                            display_name = f"📁 {os.path.basename(dir_path) or dir_path}"
                            self.files_listbox.insert(tk.END, display_name)
                        elif file_path.startswith(("http://", "https://")):
                            # It's a URL, add icon and truncate if needed
                            display_name = f"🌐 {file_path}"
                            if len(file_path) > 50:
                                display_name = f"🌐 {file_path[:47]}..."
                            self.files_listbox.insert(tk.END, display_name)
                        else:
                            # It's a regular file
                            self.files_listbox.insert(tk.END, os.path.basename(file_path))
                    else:
                        # Remove non-existent files from the list
                        self.selected_files.remove(file_path)
                        # Also remove from trim settings if present
                        if file_path in self.file_trim_settings:
                            del self.file_trim_settings[file_path]
                        print(f"Warning: File {file_path} no longer exists and was removed from the list")

                # Update UI based on mode
                self.toggle_input_mode()

                # Update window size after loading default settings
                self.root.after(100, self.update_window_size)
            except Exception as e:
                print(f"Failed to load default settings: {str(e)}")

    def stop_queue(self):
        """Stop the current generation and cancel all queue items"""
        try:
            # Create a stop flag file that batch.py will check for
            stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
            with open(stop_flag_path, 'w') as f:
                f.write(f"Stop queue requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")

            # Update button states
            self.stop_button.config(state="disabled")
            self.stop_queue_button.config(state="disabled")

            # Cancel any scheduled safety stop
            if hasattr(self, 'safety_stop_job') and self.safety_stop_job is not None:
                self.root.after_cancel(self.safety_stop_job)
                self.safety_stop_job = None
                print("Cancelled scheduled safety stop")

            # Stop the queue processing
            self.is_processing_queue = False
            self.current_job = None
            self.current_job_iteration = 0
            self.show_status("Queue processing has been stopped. Current generation will stop after the current frame completes.", "warning")

            # Schedule a check to re-enable the run button once processing is complete
            self.start_checking_status()
        except Exception as e:
            print(f"Error stopping queue: {e}")
            self.show_status(f"Failed to stop queue: {e}", "error")

    def stop_framepack(self):
        """Stop the currently running FramePack process by creating a stop flag file"""
        try:
            # Create a stop flag file that batch.py will check for
            stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
            with open(stop_flag_path, 'w') as f:
                f.write(f"Stop requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")

            # Update button states
            self.stop_button.config(state="disabled")

            # Cancel any scheduled safety stop
            if hasattr(self, 'safety_stop_job') and self.safety_stop_job is not None:
                self.root.after_cancel(self.safety_stop_job)
                self.safety_stop_job = None
                print("Cancelled scheduled safety stop")

            # If we're processing a queue, ask if the user wants to stop the entire queue
            if self.is_processing_queue:
                if messagebox.askyesno("Stop Queue",
                                      "You are currently processing a queue. Do you want to stop the entire queue processing?"):
                    self.is_processing_queue = False
                    self.current_job = None
                    self.current_job_iteration = 0
                    self.show_status("Queue processing has been stopped.", "warning")
                else:
                    self.show_status("Stop signal sent. Processing will stop after the current frame completes. Queue will continue.", "info")
            else:
                self.show_status("Stop signal sent. Processing will stop after the current frame completes.", "info")

            # Schedule a check to re-enable the run button once processing is complete
            self.start_checking_status()
        except Exception as e:
            print(f"Error stopping FramePack: {e}")
            self.show_status(f"Failed to stop FramePack: {e}", "error")

    def start_checking_status(self):
        """Start checking for processing status and latent preview updates"""
        # Initialize or reset status check counter and max checks
        self.status_check_count = 0
        self.max_status_checks = 100  # 100 checks * 3 seconds = 5 minutes timeout

        # Start checking processing status with a longer initial delay (10 seconds)
        # This gives the process more time to start up properly and begin processing
        self.root.after(10000, self.check_processing_status)

        # Start video preview monitoring
        if TKVIDEO_AVAILABLE:
            # Make sure the video players are created
            if (not hasattr(self, 'latent_preview_player') or self.latent_preview_player is None or
                not hasattr(self, 'output_preview_player') or self.output_preview_player is None):
                # Try to create the video players
                self.create_video_players()

                # Bind click events to toggle play/pause
                if hasattr(self, 'latent_preview_player') and self.latent_preview_player is not None:
                    self.latent_preview_player.bind("<Button-1>", self.toggle_preview_playback)

                if hasattr(self, 'output_preview_player') and self.output_preview_player is not None:
                    self.output_preview_player.bind("<Button-1>", self.toggle_output_preview_playback)

            # Start monitoring for latent previews
            if hasattr(self, 'latent_preview_player') and self.latent_preview_player is not None:
                print("Starting latent preview monitoring")
                self.root.after(1000, self.update_latent_preview)

            # Start monitoring for output videos
            if hasattr(self, 'output_preview_player') and self.output_preview_player is not None:
                print("Starting output video monitoring")
                self.root.after(2000, self.update_output_preview)

    def toggle_preview_playback(self, event=None):
        """Toggle play/pause of the latent preview video when clicked"""
        try:
            if hasattr(self, 'latent_preview_player') and self.latent_preview_player is not None:
                try:
                    # Handle both GIF player and TkinterVideo
                    if hasattr(self, 'using_gif_player') and self.using_gif_player:
                        # Using our custom GIF player
                        if self.latent_preview_player.is_paused():
                            self.latent_preview_player.play()
                            self.latent_preview_label.config(text="Playing preview...")
                        else:
                            self.latent_preview_player.pause()
                            self.latent_preview_label.config(text="Paused - Click to resume")
                    else:
                        # Using TkinterVideo
                        if self.latent_preview_player.is_paused():
                            self.latent_preview_player.play()
                            self.latent_preview_label.config(text="Playing preview...")
                        else:
                            self.latent_preview_player.pause()
                            self.latent_preview_label.config(text="Paused - Click to resume")
                except Exception as e:
                    print(f"Error toggling latent preview playback: {e}")
        except Exception as e:
            print(f"Unexpected error in toggle_preview_playback: {e}")

    def toggle_output_preview_playback(self, event=None):
        """Toggle play/pause of the output preview video when clicked"""
        try:
            if hasattr(self, 'output_preview_player') and self.output_preview_player is not None:
                try:
                    if self.output_preview_player.is_paused():
                        self.output_preview_player.play()
                        self.output_preview_label.config(text="Playing output video...")
                    else:
                        self.output_preview_player.pause()
                        self.output_preview_label.config(text="Paused - Click to resume")
                except Exception as e:
                    print(f"Error toggling output preview playback: {e}")
        except Exception as e:
            print(f"Unexpected error in toggle_output_preview_playback: {e}")

    def restart_preview(self, event=None):
        """Restart the latent preview video when it ends"""
        try:
            # Check if we're already in the process of restarting
            if self._restarting_preview:
                return

            # Set the flag to prevent multiple restart attempts
            self._restarting_preview = True

            # Schedule the restart with a short delay to avoid issues
            def delayed_restart():
                try:
                    if hasattr(self, 'latent_preview_player') and self.latent_preview_player is not None:
                        # Handle both GIF player and TkinterVideo
                        if hasattr(self, 'using_gif_player') and self.using_gif_player:
                            # Using our custom GIF player

                            try:
                                # Apply the current playback rate before restarting
                                if hasattr(self, 'playback_rate') and hasattr(self.latent_preview_player, 'set_playback_rate'):
                                    rate = float(self.playback_rate.get()) / 100.0
                                    self.latent_preview_player.set_playback_rate(rate)
                                    print(f"Applied playback rate of {rate:.2f}x when restarting GIF")

                                # Add a small delay before seeking and playing
                                time.sleep(0.1)

                                # Force garbage collection
                                import gc
                                gc.collect()

                                if hasattr(self.latent_preview_player, 'seek'):
                                    self.latent_preview_player.seek(0)  # Seek to the beginning

                                if hasattr(self.latent_preview_player, 'play'):
                                    self.latent_preview_player.play()   # Start playing
                            except Exception as restart_error:
                                print(f"Error during GIF restart: {restart_error}")
                                # Try again with a delay
                                time.sleep(0.5)
                                try:
                                    if hasattr(self.latent_preview_player, 'seek'):
                                        self.latent_preview_player.seek(0)
                                    if hasattr(self.latent_preview_player, 'play'):
                                        self.latent_preview_player.play()
                                except Exception as retry_error:
                                    print(f"Error during GIF restart retry: {retry_error}")
                        else:
                            # Using TkinterVideo
                            try:
                                # Add a small delay before seeking and playing
                                time.sleep(0.1)

                                # Force garbage collection
                                import gc
                                gc.collect()

                                if hasattr(self.latent_preview_player, 'seek'):
                                    self.latent_preview_player.seek(0)  # Seek to the beginning

                                if hasattr(self.latent_preview_player, 'play'):
                                    self.latent_preview_player.play()   # Start playing
                            except Exception as restart_error:
                                print(f"Error during TkinterVideo restart: {restart_error}")
                                # Try again with a delay
                                time.sleep(0.5)
                                try:
                                    if hasattr(self.latent_preview_player, 'seek'):
                                        self.latent_preview_player.seek(0)
                                    if hasattr(self.latent_preview_player, 'play'):
                                        self.latent_preview_player.play()
                                except Exception as retry_error:
                                    print(f"Error during TkinterVideo restart retry: {retry_error}")
                except Exception as e:
                    print(f"Error restarting latent preview: {e}")
                finally:
                    # Reset the flag
                    self._restarting_preview = False

            # Use a delay of 200ms before restarting
            self.root.after(200, delayed_restart)
        except Exception as e:
            print(f"Unexpected error in restart_preview: {e}")
            self._restarting_preview = False

    def restart_output_preview(self, event=None):
        """Restart the output preview video when it ends"""
        try:
            # Check if we're already in the process of restarting
            if self._restarting_output_preview:
                return

            # Set the flag to prevent multiple restart attempts
            self._restarting_output_preview = True

            # Schedule the restart with a short delay to avoid issues
            def delayed_restart():
                try:
                    if hasattr(self, 'output_preview_player') and self.output_preview_player is not None:
                        try:
                            # Add a small delay before seeking and playing
                            time.sleep(0.1)

                            # Force garbage collection
                            import gc
                            gc.collect()

                            if hasattr(self.output_preview_player, 'seek'):
                                self.output_preview_player.seek(0)  # Seek to the beginning

                            if hasattr(self.output_preview_player, 'play'):
                                self.output_preview_player.play()   # Start playing
                        except Exception as restart_error:
                            print(f"Error during output video restart: {restart_error}")
                            # Try again with a delay
                            time.sleep(0.5)
                            try:
                                if hasattr(self.output_preview_player, 'seek'):
                                    self.output_preview_player.seek(0)
                                if hasattr(self.output_preview_player, 'play'):
                                    self.output_preview_player.play()
                            except Exception as retry_error:
                                print(f"Error during output video restart retry: {retry_error}")
                except Exception as e:
                    print(f"Error restarting output preview: {e}")
                finally:
                    # Reset the flag
                    self._restarting_output_preview = False

            # Use a delay of 200ms before restarting
            self.root.after(200, delayed_restart)
        except Exception as e:
            print(f"Unexpected error in restart_output_preview: {e}")
            self._restarting_output_preview = False

    def create_video_players(self):
        """Safely create the video players with error handling"""
        try:
            # Create the latent preview player
            if hasattr(self, 'video_frame'):
                # First check if we can use the GIF player
                try:
                    # Try to import PIL to check if it's available
                    from PIL import Image, ImageTk

                    # Create a TkGifPlayer widget to display the latent preview at 60% of full resolution
                    self.latent_preview_player = TkGifPlayer(master=self.video_frame, width=384, height=288)
                    self.latent_preview_player.pack(fill=tk.BOTH, expand=True)

                    # Set the flag to indicate we're using the GIF player
                    self.using_gif_player = True

                    print("Using GIF player for latent preview")
                except (ImportError, NameError, AttributeError) as e:
                    print(f"Error creating GIF player: {e}")
                    print("Falling back to TkinterVideo for latent preview")

                    # Create a TkinterVideo widget to display the latent preview
                    self.latent_preview_player = TkinterVideo(master=self.video_frame, scaled=True, keep_aspect=True)
                    self.latent_preview_player.pack(fill=tk.BOTH, expand=True)

                    # Set a height for the video player (60% of full resolution)
                    self.latent_preview_player.config(height=288)  # 60% of 480 height

                    # Set the flag to indicate we're not using the GIF player
                    self.using_gif_player = False

            # Create the output preview player
            if hasattr(self, 'output_video_frame'):
                # Create a TkinterVideo widget to display the output preview at full resolution
                self.output_preview_player = TkinterVideo(master=self.output_video_frame, scaled=True, keep_aspect=True)
                self.output_preview_player.pack(fill=tk.BOTH, expand=True)

                # Set height for full resolution display (standard 480p height)
                self.output_preview_player.config(height=480)  # Full size for output preview

            # Initialize video load counters
            self.latent_video_load_count = 0
            self.output_video_load_count = 0

            return True
        except Exception as e:
            print(f"Error creating video players: {e}")
            return False

    def reset_video_players(self):
        """Completely reset both video players when a critical error occurs"""
        print("Performing complete reset of video players...")

        try:
            # Stop and destroy both players
            for player_name in ['latent_preview_player', 'output_preview_player']:
                if hasattr(self, player_name):
                    player = getattr(self, player_name)

                    # Stop the player if possible
                    if hasattr(player, 'stop'):
                        try:
                            player.stop()
                        except Exception as e:
                            print(f"Error stopping {player_name}: {e}")

                    # Destroy the player if possible
                    if hasattr(player, 'destroy'):
                        try:
                            player.destroy()
                        except Exception as e:
                            print(f"Error destroying {player_name}: {e}")

                    # Remove the player from the frame
                    if hasattr(player, 'pack_forget'):
                        try:
                            player.pack_forget()
                        except Exception as e:
                            print(f"Error removing {player_name}: {e}")

            # Force aggressive garbage collection
            import gc
            for _ in range(5):  # Run GC multiple times
                gc.collect()

            # Wait for resources to be released
            time.sleep(3.0)

            # Recreate the players
            self.create_video_players()

            # Bind click events
            if hasattr(self, 'latent_preview_player') and self.latent_preview_player is not None:
                self.latent_preview_player.bind("<Button-1>", self.toggle_preview_playback)

            if hasattr(self, 'output_preview_player') and self.output_preview_player is not None:
                self.output_preview_player.bind("<Button-1>", self.toggle_output_preview_playback)

            # Reset video load counters
            self.latent_video_load_count = 0
            self.output_video_load_count = 0

            print("Video players reset successfully")
            return True
        except Exception as e:
            print(f"Error resetting video players: {e}")
            return False

    def handle_video_load_error(self, player, file_path, error_msg):
        """Handle errors when loading videos, with special handling for common errors"""
        print(f"Error loading video: {error_msg}")

        error_str = str(error_msg).lower()

        # Check for specific error patterns
        is_nonetype_error = "'nonetype' object has no attribute 'streams'" in error_str
        is_nonetype_close_error = "'nonetype' object has no attribute 'close'" in error_str
        is_assertion_error = "assertion" in error_str and "pthread_frame" in error_str
        is_ffmpeg_error = "ffmpeg" in error_str

        # If any of the known errors occur, try the recovery procedure
        if is_nonetype_error or is_nonetype_close_error or is_assertion_error or is_ffmpeg_error:
            print(f"Detected video loading error: {error_str}")
            print("Attempting advanced recovery procedure with player recreation...")

            try:
                # Force aggressive garbage collection
                import gc
                for _ in range(3):  # Run GC multiple times
                    gc.collect()

                # Stop the player
                if hasattr(player, 'stop'):
                    try:
                        player.stop()
                    except Exception as e:
                        print(f"Error stopping player: {e}")

                # Wait for resources to be released
                time.sleep(3.0)

                # Check if the file exists and is accessible
                if not os.path.exists(file_path):
                    print(f"Warning: Video file not found during recovery: {file_path}")
                    return False

                # Check if the file is not empty
                if os.path.getsize(file_path) == 0:
                    print(f"Warning: Video file is empty during recovery: {file_path}")
                    return False

                # Wait for the file to be fully written
                last_size = os.path.getsize(file_path)
                time.sleep(1.0)
                current_size = os.path.getsize(file_path)

                # If the file size is still changing, wait longer
                retry_count = 0
                while current_size != last_size and retry_count < 5:
                    print(f"File size still changing ({last_size} -> {current_size}), waiting...")
                    last_size = current_size
                    time.sleep(2.0)
                    current_size = os.path.getsize(file_path)
                    retry_count += 1

                # For assertion errors, recreate the player instead of just reloading
                if is_assertion_error or is_ffmpeg_error:
                    print(f"Recreating video player for file: {file_path}")

                    # Determine which player we're dealing with
                    if player == self.latent_preview_player:
                        # Destroy the old player if possible
                        if hasattr(self.latent_preview_player, 'destroy'):
                            try:
                                self.latent_preview_player.destroy()
                            except Exception as e:
                                print(f"Error destroying latent preview player: {e}")

                        # Remove the player from the frame
                        if hasattr(self.latent_preview_player, 'pack_forget'):
                            try:
                                self.latent_preview_player.pack_forget()
                            except Exception as e:
                                print(f"Error removing latent preview player: {e}")

                        # Force garbage collection again
                        for _ in range(3):
                            gc.collect()

                        # Wait for resources to be released
                        time.sleep(2.0)

                        # Create a new player
                        if hasattr(self, 'using_gif_player') and self.using_gif_player:
                            self.latent_preview_player = TkGifPlayer(master=self.video_frame, width=384, height=288)
                        else:
                            self.latent_preview_player = TkinterVideo(master=self.video_frame, scaled=True, keep_aspect=True)
                            self.latent_preview_player.config(height=288)

                        self.latent_preview_player.pack(fill=tk.BOTH, expand=True)

                        # Bind click event
                        self.latent_preview_player.bind("<Button-1>", self.toggle_preview_playback)

                        # Wait before loading
                        time.sleep(1.0)

                        # Load the video
                        self.latent_preview_player.load(file_path)

                        # Bind ended event
                        self.latent_preview_player.bind("<<Ended>>", self.restart_preview)

                    elif player == self.output_preview_player:
                        # Destroy the old player if possible
                        if hasattr(self.output_preview_player, 'destroy'):
                            try:
                                self.output_preview_player.destroy()
                            except Exception as e:
                                print(f"Error destroying output preview player: {e}")

                        # Remove the player from the frame
                        if hasattr(self.output_preview_player, 'pack_forget'):
                            try:
                                self.output_preview_player.pack_forget()
                            except Exception as e:
                                print(f"Error removing output preview player: {e}")

                        # Force garbage collection again
                        for _ in range(3):
                            gc.collect()

                        # Wait for resources to be released
                        time.sleep(2.0)

                        # Create a new player
                        self.output_preview_player = TkinterVideo(master=self.output_video_frame, scaled=True, keep_aspect=True)
                        self.output_preview_player.config(height=480)
                        self.output_preview_player.pack(fill=tk.BOTH, expand=True)

                        # Bind click event
                        self.output_preview_player.bind("<Button-1>", self.toggle_output_preview_playback)

                        # Wait before loading
                        time.sleep(1.0)

                        # Load the video
                        self.output_preview_player.load(file_path)

                        # Bind ended event
                        self.output_preview_player.bind("<<Ended>>", self.restart_output_preview)

                    return True
                else:
                    # For other errors, just try reloading
                    print(f"Attempting to reload video: {file_path}")
                    player.load(file_path)
                    return True

            except Exception as recovery_error:
                print(f"Recovery attempt failed: {recovery_error}")
                return False

        # For other errors, just return False
        return False

    def update_playback_rate(self, value=None):
        """Update the playback rate for the latent preview GIF"""
        try:
            # Only proceed if we're using the GIF player
            if not hasattr(self, 'using_gif_player') or not self.using_gif_player:
                return

            # Get the current value from the variable if not provided
            if value is None:
                value = self.playback_rate.get()

            # Convert from percentage (5-200) to float (0.05-2.0)
            rate = float(value) / 100.0

            # Update the label if it exists
            if hasattr(self, 'rate_label'):
                self.rate_label.config(text=f"Playback Rate: {int(float(value))}%")

            # Update the playback rate in the GIF player
            if (hasattr(self, 'latent_preview_player') and
                self.latent_preview_player is not None and
                hasattr(self.latent_preview_player, 'set_playback_rate')):
                try:
                    # Try to acquire the lock to prevent conflicts with video loading
                    if not self.video_load_lock.acquire(blocking=False):
                        # If we can't acquire the lock, schedule another attempt later
                        self.root.after(100, lambda: self.update_playback_rate(value))
                        return

                    try:
                        # Set the playback rate
                        self.latent_preview_player.set_playback_rate(rate)
                    finally:
                        # Release the lock
                        self.video_load_lock.release()
                except Exception as e:
                    print(f"Error setting playback rate: {e}")
        except Exception as e:
            print(f"Unexpected error in update_playback_rate: {e}")

    def hide_preview(self):
        """Hide the latent preview when no generation is running"""
        try:
            # Check if the video player is available
            if not hasattr(self, 'latent_preview_player') or self.latent_preview_player is None:
                # If the video player is not available, just return
                return

            # Try to acquire the lock to prevent conflicts with video loading
            if not self.video_load_lock.acquire(blocking=False):
                # If we can't acquire the lock, schedule another attempt later
                self.root.after(500, self.hide_preview)
                return

            try:
                # Set the loading flag to prevent other operations
                self.preview_loading = True

                # Stop the video if it's playing
                try:
                    self.latent_preview_player.stop()
                except Exception as e:
                    print(f"Error stopping latent preview: {e}")

                # Update the label
                self.latent_preview_label.config(text="No preview available - Start generation to see preview")
            finally:
                # Reset the loading flag
                self.preview_loading = False
                # Release the lock
                self.video_load_lock.release()
        except Exception as e:
            print(f"Unexpected error in hide_preview: {e}")

    def hide_output_preview(self):
        """Hide the output preview when disabled"""
        try:
            # Check if the video player is available
            if not hasattr(self, 'output_preview_player') or self.output_preview_player is None:
                # If the video player is not available, just return
                return

            # Try to acquire the lock to prevent conflicts with video loading
            if not self.output_video_load_lock.acquire(blocking=False):
                # If we can't acquire the lock, schedule another attempt later
                self.root.after(500, self.hide_output_preview)
                return

            try:
                # Set the loading flag to prevent other operations
                self.output_preview_loading = True

                # Stop the video if it's playing
                try:
                    self.output_preview_player.stop()
                except Exception as e:
                    print(f"Error stopping output preview: {e}")

                # Update the label
                self.output_preview_label.config(text="No output video available - Complete generation to see result")
            finally:
                # Reset the loading flag
                self.output_preview_loading = False
                # Release the lock
                self.output_video_load_lock.release()
        except Exception as e:
            print(f"Unexpected error in hide_output_preview: {e}")

    def update_latent_preview(self):
        """Check for and display the latest latent preview animation"""
        try:
            # Check if the video player is available
            if not hasattr(self, 'latent_preview_player') or self.latent_preview_player is None:
                # If the video player is not available, just schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            if not self.show_latent_preview.get():
                # If preview is disabled, hide any existing preview
                self.hide_preview()
                # Schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            # If we're already in the process of loading a video, don't try to load another one
            if self.preview_loading:
                # Schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            # Get the temp directory
            temp_dir = ensure_temp_directory()
            if not temp_dir:
                # Schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            # Get the latent_previews directory
            latent_previews_dir = ensure_latent_previews_directory()
            if not latent_previews_dir:
                # Schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            # Look for the latest latent preview file (mp4 format)
            try:
                # First check in the latent_previews directory
                preview_files = glob.glob(os.path.join(latent_previews_dir, "*latent_preview_*.mp4"))

                # If no files found in latent_previews, check temp directory
                if not preview_files:
                    preview_files = glob.glob(os.path.join(temp_dir, "*latent_preview_*.mp4"))

                # If still no files found, check for GIF format as fallback (better compatibility)
                if not preview_files:
                    preview_files = glob.glob(os.path.join(latent_previews_dir, "*latent_preview_*.gif"))
                    if not preview_files:
                        preview_files = glob.glob(os.path.join(temp_dir, "*latent_preview_*.gif"))

                # If still no files found, check for webm format as fallback
                if not preview_files:
                    preview_files = glob.glob(os.path.join(latent_previews_dir, "*latent_preview_*.webm"))
                    if not preview_files:
                        preview_files = glob.glob(os.path.join(temp_dir, "*latent_preview_*.webm"))
            except Exception as e:
                print(f"Error searching for preview files: {e}")
                # Schedule the next update and return
                self.root.after(1000, self.update_latent_preview)
                return

            # Process the preview files if we found any
            if preview_files:
                try:
                    # Sort by modification time (newest first)
                    preview_files.sort(key=os.path.getmtime, reverse=True)

                    # Filter out files that were created before this session started
                    # Only show files from the current generation session
                    if self.generation_started:
                        current_session_files = []
                        for file_path in preview_files:
                            try:
                                file_mtime = os.path.getmtime(file_path)
                                if file_mtime >= self.session_start_time:
                                    current_session_files.append(file_path)
                            except Exception as e:
                                print(f"Error checking file time: {e}")

                        # Use only files from current session
                        if current_session_files:
                            preview_files = current_session_files
                        else:
                            # No files from current session, don't show anything
                            self.hide_preview()
                            self.root.after(1000, self.update_latent_preview)
                            return
                    elif not self.generation_started:
                        # If generation hasn't started yet, don't show any previews
                        self.hide_preview()
                        self.root.after(1000, self.update_latent_preview)
                        return

                    latest_preview = preview_files[0]

                    # Check if this is a new preview
                    if self.current_preview != latest_preview:
                        print(f"Found new latent preview: {latest_preview}")

                        # Try to acquire the lock
                        if not self.video_load_lock.acquire(blocking=False):
                            # If we can't acquire the lock, schedule another attempt later
                            self.root.after(1000, self.update_latent_preview)
                            return

                        # Set the loading flag
                        self.preview_loading = True

                        # Define a function to load the new video in a separate thread
                        def load_new_video():
                            try:
                                # Update the current preview path
                                self.current_preview = latest_preview

                                # Stop any currently playing video
                                try:
                                    # Make sure to completely stop the current preview before loading a new one
                                    if hasattr(self.latent_preview_player, 'stop'):
                                        self.latent_preview_player.stop()

                                    # Add a longer delay to ensure the player is fully stopped and resources are released
                                    time.sleep(0.5)

                                    # Force garbage collection to free up resources
                                    import gc
                                    gc.collect()

                                    # Print debug info
                                    print(f"Stopped current preview before loading new one: {self.current_preview}")
                                except Exception as e:
                                    print(f"Error stopping current preview: {e}")
                                    # Add a longer delay to recover from errors
                                    time.sleep(1.0)

                                try:
                                    # Handle both GIF player and TkinterVideo
                                    if hasattr(self, 'using_gif_player') and self.using_gif_player:
                                        # Using our custom GIF player

                                        # Check if the file is a GIF
                                        is_gif = latest_preview.lower().endswith('.gif')

                                        if is_gif:
                                            # It's a GIF, load it directly
                                            try:
                                                # First stop any currently playing video
                                                if hasattr(self.latent_preview_player, 'stop'):
                                                    self.latent_preview_player.stop()

                                                # Add a delay to ensure the player is fully stopped
                                                time.sleep(0.3)

                                                # Force garbage collection
                                                import gc
                                                gc.collect()

                                                # Check if the file exists and is accessible
                                                if not os.path.exists(latest_preview):
                                                    print(f"Warning: GIF file not found: {latest_preview}")
                                                    raise Exception(f"GIF file not found: {latest_preview}")

                                                # Check if the file is not empty
                                                if os.path.getsize(latest_preview) == 0:
                                                    print(f"Warning: GIF file is empty: {latest_preview}")
                                                    raise Exception(f"GIF file is empty: {latest_preview}")

                                                # Load the GIF with error handling
                                                try:
                                                    load_success = self.latent_preview_player.load(latest_preview)
                                                except Exception as load_error:
                                                    print(f"Error loading GIF: {load_error}")
                                                    # Try one more time after a delay
                                                    time.sleep(0.5)
                                                    load_success = self.latent_preview_player.load(latest_preview)

                                                if not load_success:
                                                    raise Exception("Failed to load GIF preview")

                                                # Set up a video ended event handler to restart the video (looping)
                                                self.latent_preview_player.bind("<<Ended>>", self.restart_preview)

                                                # Apply the current playback rate
                                                if hasattr(self, 'playback_rate') and hasattr(self.latent_preview_player, 'set_playback_rate'):
                                                    rate = float(self.playback_rate.get()) / 100.0
                                                    self.latent_preview_player.set_playback_rate(rate)
                                                    print(f"Applied playback rate of {rate:.2f}x to GIF")

                                                # Play the GIF
                                                self.latent_preview_player.play()
                                            except Exception as e:
                                                print(f"Error loading GIF preview: {e}")
                                                raise e
                                        else:
                                            # It's not a GIF, try to convert it first
                                            print(f"Converting MP4 to GIF for better compatibility: {latest_preview}")

                                            # Try to import the GIF converter
                                            try:
                                                from generate_gif_preview import convert_mp4_to_gif

                                                # Convert the MP4 to GIF
                                                gif_path = convert_mp4_to_gif(latest_preview)

                                                if gif_path and os.path.exists(gif_path):
                                                    # Successfully converted, load the GIF
                                                    if hasattr(self.latent_preview_player, 'stop'):
                                                        self.latent_preview_player.stop()

                                                    # Add a delay to ensure the player is fully stopped
                                                    time.sleep(0.3)

                                                    # Force garbage collection
                                                    import gc
                                                    gc.collect()

                                                    # Check if the file exists and is accessible
                                                    if not os.path.exists(gif_path):
                                                        print(f"Warning: Converted GIF file not found: {gif_path}")
                                                        raise Exception(f"Converted GIF file not found: {gif_path}")

                                                    # Check if the file is not empty
                                                    if os.path.getsize(gif_path) == 0:
                                                        print(f"Warning: Converted GIF file is empty: {gif_path}")
                                                        raise Exception(f"Converted GIF file is empty: {gif_path}")

                                                    # Load the GIF with error handling
                                                    try:
                                                        load_success = self.latent_preview_player.load(gif_path)
                                                    except Exception as load_error:
                                                        print(f"Error loading converted GIF: {load_error}")
                                                        # Try one more time after a delay
                                                        time.sleep(0.5)
                                                        load_success = self.latent_preview_player.load(gif_path)

                                                    if not load_success:
                                                        raise Exception("Failed to load converted GIF preview")

                                                    # Set up a video ended event handler to restart the video (looping)
                                                    self.latent_preview_player.bind("<<Ended>>", self.restart_preview)

                                                    # Apply the current playback rate
                                                    if hasattr(self, 'playback_rate') and hasattr(self.latent_preview_player, 'set_playback_rate'):
                                                        rate = float(self.playback_rate.get()) / 100.0
                                                        self.latent_preview_player.set_playback_rate(rate)
                                                        print(f"Applied playback rate of {rate:.2f}x to converted GIF")

                                                    # Play the GIF
                                                    self.latent_preview_player.play()
                                                else:
                                                    # Conversion failed, show an error
                                                    raise Exception("Failed to convert MP4 to GIF")
                                            except ImportError:
                                                # GIF converter not available, show an error
                                                raise Exception("GIF converter not available")
                                            except Exception as e:
                                                print(f"Error converting MP4 to GIF: {e}")
                                                raise e
                                    else:
                                        # Using TkinterVideo
                                        load_success = False

                                        # Try to load the video with a timeout
                                        try:
                                            # First stop any currently playing video
                                            if hasattr(self.latent_preview_player, 'stop'):
                                                self.latent_preview_player.stop()

                                            # Add a longer delay to ensure the player is fully stopped
                                            time.sleep(0.5)

                                            # Force garbage collection
                                            import gc
                                            gc.collect()

                                            # Check if the file exists and is accessible
                                            if not os.path.exists(latest_preview):
                                                print(f"Warning: Video file not found: {latest_preview}")
                                                raise Exception(f"Video file not found: {latest_preview}")

                                            # Check if the file is not empty
                                            if os.path.getsize(latest_preview) == 0:
                                                print(f"Warning: Video file is empty: {latest_preview}")
                                                raise Exception(f"Video file is empty: {latest_preview}")

                                            # Check if the file is still being written by checking if it's locked
                                            def is_file_locked(file_path):
                                                try:
                                                    # Try to open the file in exclusive mode
                                                    with open(file_path, 'r+b') as f:
                                                        # If we can open it, it's not locked
                                                        return False
                                                except IOError:
                                                    # If we can't open it, it's probably locked
                                                    return True

                                            # Wait for the file to be fully written and unlocked
                                            max_wait_attempts = 5
                                            wait_attempt = 0
                                            while wait_attempt < max_wait_attempts and is_file_locked(latest_preview):
                                                print(f"Latent preview file is locked, waiting... (attempt {wait_attempt+1}/{max_wait_attempts})")
                                                time.sleep(1.0)
                                                wait_attempt += 1

                                            # Check file size stability to ensure it's fully written
                                            last_size = os.path.getsize(latest_preview)
                                            time.sleep(0.5)
                                            current_size = os.path.getsize(latest_preview)

                                            # If the file size is still changing, wait a bit more
                                            if current_size != last_size:
                                                print(f"Latent preview file size is still changing, waiting...")
                                                time.sleep(2.0)

                                            # Load the video with a timeout
                                            print(f"Loading latent preview video: {latest_preview}")
                                            try:
                                                # Before loading, check if we need to recreate the player
                                                # This is a preventive measure to avoid the FFmpeg assertion error
                                                if hasattr(self, 'latent_video_load_count') and self.latent_video_load_count > 3:
                                                    # After a few loads, recreate the player to prevent memory issues
                                                    print("Preventively recreating latent video player to avoid assertion errors")

                                                    # Destroy the old player if possible
                                                    if hasattr(self.latent_preview_player, 'destroy'):
                                                        try:
                                                            self.latent_preview_player.destroy()
                                                        except Exception as e:
                                                            print(f"Error destroying latent preview player: {e}")

                                                    # Remove the player from the frame
                                                    if hasattr(self.latent_preview_player, 'pack_forget'):
                                                        try:
                                                            self.latent_preview_player.pack_forget()
                                                        except Exception as e:
                                                            print(f"Error removing latent preview player: {e}")

                                                    # Force garbage collection
                                                    for _ in range(3):
                                                        gc.collect()

                                                    # Wait for resources to be released
                                                    time.sleep(2.0)

                                                    # Create a new player
                                                    if hasattr(self, 'using_gif_player') and self.using_gif_player:
                                                        self.latent_preview_player = TkGifPlayer(master=self.video_frame, width=384, height=288)
                                                    else:
                                                        self.latent_preview_player = TkinterVideo(master=self.video_frame, scaled=True, keep_aspect=True)
                                                        self.latent_preview_player.config(height=288)

                                                    self.latent_preview_player.pack(fill=tk.BOTH, expand=True)

                                                    # Bind click event
                                                    self.latent_preview_player.bind("<Button-1>", self.toggle_preview_playback)

                                                    # Reset the counter
                                                    self.latent_video_load_count = 0
                                                else:
                                                    # Increment the counter
                                                    if hasattr(self, 'latent_video_load_count'):
                                                        self.latent_video_load_count += 1
                                                    else:
                                                        self.latent_video_load_count = 1

                                                # Now load the video
                                                self.latent_preview_player.load(latest_preview)
                                                load_success = True
                                            except (AttributeError, Exception) as error:
                                                # Use our specialized error handler for any error
                                                if self.handle_video_load_error(self.latent_preview_player, latest_preview, error):
                                                    load_success = True
                                                else:
                                                    raise error
                                        except Exception as e:
                                            print(f"Error in initial load attempt for latent preview: {e}")
                                            # If loading fails, try one more time after a longer delay
                                            time.sleep(1.0)
                                            try:
                                                # Force garbage collection again
                                                gc.collect()

                                                print(f"Retrying load of latent preview video: {latest_preview}")
                                                try:
                                                    self.latent_preview_player.load(latest_preview)
                                                    load_success = True
                                                except AttributeError as attr_error:
                                                    # Use our specialized error handler for the retry as well
                                                    if self.handle_video_load_error(self.latent_preview_player, latest_preview, attr_error):
                                                        load_success = True
                                                    else:
                                                        raise attr_error
                                            except Exception as e2:
                                                print(f"Error in second load attempt for latent preview: {e2}")
                                                # Don't raise the error, just log it and continue
                                                load_success = False

                                        if not load_success:
                                            raise Exception("Failed to load latent preview video after multiple attempts")

                                        # Get video info to set appropriate playback settings
                                        try:
                                            video_info = self.latent_preview_player.video_info()
                                            # If the video is very short, increase the delay before restart
                                            if video_info.get("duration", 0) < 1.0:
                                                self._restart_delay = 300  # longer delay for short videos
                                            else:
                                                self._restart_delay = 200  # normal delay
                                        except Exception as e:
                                            print(f"Error getting video info: {e}")
                                            self._restart_delay = 200  # default delay

                                        # Set up a video ended event handler to restart the video (looping)
                                        self.latent_preview_player.bind("<<Ended>>", self.restart_preview)

                                        # Play the video
                                        self.latent_preview_player.play()
                                except Exception as e:
                                    print(f"Critical error loading latent preview: {e}")
                                    # If we can't load the video, show an error message
                                    self.latent_preview_label.config(text=f"Error loading preview: {str(e)}")

                                    # Try to reset the player
                                    try:
                                        # First try to stop the player
                                        if hasattr(self.latent_preview_player, 'stop'):
                                            self.latent_preview_player.stop()

                                        # If this is a critical error, try a complete reset of both players
                                        if "assertion" in str(e).lower() or "pthread_frame" in str(e).lower() or "ffmpeg" in str(e).lower():
                                            print("Critical FFmpeg error detected, performing complete player reset")
                                            self.root.after(1000, self.reset_video_players)
                                    except Exception as reset_error:
                                        print(f"Error during player reset: {reset_error}")

                                # Update the label
                                self.latent_preview_label.config(text=f"Playing: {os.path.basename(latest_preview)}")
                            except Exception as e:
                                print(f"Error loading new preview: {e}")
                                self.latent_preview_label.config(text=f"Error loading preview: {str(e)}")
                            finally:
                                # Reset the loading flag
                                self.preview_loading = False
                                # Release the lock
                                self.video_load_lock.release()

                        # Schedule the video loading with a longer delay for better stability
                        self.root.after(1000, load_new_video)  # Increased delay to 1 second

                except Exception as e:
                    print(f"Error displaying latent preview: {e}")
                    self.latent_preview_label.config(text=f"Error loading preview: {str(e)}")
                    # Reset the loading flag
                    self.preview_loading = False
                    # Release the lock
                    if self.video_load_lock._is_owned():
                        self.video_load_lock.release()

            # Schedule the next update
            self.root.after(1000, self.update_latent_preview)

        except Exception as e:
            print(f"Unexpected error in update_latent_preview: {e}")
            # Schedule the next update even if an error occurred
            self.root.after(1000, self.update_latent_preview)

    def update_output_preview(self):
        """Check for and display the latest output video"""
        try:
            # Check if the video player is available
            if not hasattr(self, 'output_preview_player') or self.output_preview_player is None:
                # If the video player is not available, just schedule the next update and return
                self.root.after(2000, self.update_output_preview)
                return

            if not self.show_output_preview.get():
                # If preview is disabled, hide any existing preview
                self.hide_output_preview()
                # Schedule the next update and return
                self.root.after(2000, self.update_output_preview)
                return

            # If we're already in the process of loading a video, don't try to load another one
            if self.output_preview_loading:
                # Schedule the next update and return
                self.root.after(2000, self.update_output_preview)
                return

            # Get the output directory
            output_dir = self.output_dir.get()
            if not os.path.exists(output_dir):
                # Schedule the next update and return
                self.root.after(2000, self.update_output_preview)
                return

            # Look for the latest output video file (mp4 format)
            try:
                output_files = glob.glob(os.path.join(output_dir, "*.mp4"))

                # If no files found in output directory, check for other formats
                if not output_files:
                    output_files = glob.glob(os.path.join(output_dir, "*.webm"))
                    if not output_files:
                        output_files = glob.glob(os.path.join(output_dir, "*.avi"))
                        if not output_files:
                            output_files = glob.glob(os.path.join(output_dir, "*.mov"))
            except Exception as e:
                print(f"Error searching for output files: {e}")
                # Schedule the next update and return
                self.root.after(2000, self.update_output_preview)
                return

            # Process the output files if we found any
            if output_files:
                try:
                    # Sort by modification time (newest first)
                    output_files.sort(key=os.path.getmtime, reverse=True)

                    # Filter out files that were created before this session started
                    # Only show files from the current generation session
                    if self.generation_started:
                        current_session_files = []
                        for file_path in output_files:
                            try:
                                file_mtime = os.path.getmtime(file_path)
                                if file_mtime >= self.session_start_time:
                                    current_session_files.append(file_path)
                            except Exception as e:
                                print(f"Error checking file time: {e}")

                        # Use only files from current session
                        if current_session_files:
                            output_files = current_session_files
                        else:
                            # No files from current session, don't show anything
                            self.hide_output_preview()
                            self.root.after(2000, self.update_output_preview)
                            return
                    elif not self.generation_started:
                        # If generation hasn't started yet, don't show any videos
                        self.hide_output_preview()
                        self.root.after(2000, self.update_output_preview)
                        return

                    latest_output = output_files[0]

                    # Check if this is a new output video
                    if self.current_output_preview != latest_output:
                        print(f"Found new output video: {latest_output}")

                        # Try to acquire the lock
                        if not self.output_video_load_lock.acquire(blocking=False):
                            # If we can't acquire the lock, schedule another attempt later
                            self.root.after(2000, self.update_output_preview)
                            return

                        # Set the loading flag
                        self.output_preview_loading = True

                        # Define a function to load the new video in a separate thread
                        def load_new_output_video():
                            try:
                                # Update the current output preview path
                                self.current_output_preview = latest_output

                                # Stop any currently playing video
                                try:
                                    # Make sure to completely stop the current preview before loading a new one
                                    if hasattr(self.output_preview_player, 'stop'):
                                        self.output_preview_player.stop()

                                    # Add a longer delay to ensure the player is fully stopped and resources are released
                                    time.sleep(0.5)

                                    # Force garbage collection to free up resources
                                    import gc
                                    gc.collect()

                                    # Print debug info
                                    print(f"Stopped current output preview before loading new one: {self.current_output_preview}")
                                except Exception as e:
                                    print(f"Error stopping current output preview: {e}")
                                    # Add a longer delay to recover from errors
                                    time.sleep(1.0)

                                try:
                                    # Load the video into the TkinterVideo widget with a timeout
                                    load_success = False

                                    # Try to load the video with a timeout
                                    try:
                                        # First stop any currently playing video
                                        if hasattr(self.output_preview_player, 'stop'):
                                            self.output_preview_player.stop()

                                        # Add a longer delay to ensure the player is fully stopped
                                        time.sleep(0.5)

                                        # Force garbage collection
                                        import gc
                                        gc.collect()

                                        # Check if the file exists and is accessible
                                        if not os.path.exists(latest_output):
                                            print(f"Warning: Output video file not found: {latest_output}")
                                            raise Exception(f"Output video file not found: {latest_output}")

                                        # Check if the file is not empty
                                        if os.path.getsize(latest_output) == 0:
                                            print(f"Warning: Output video file is empty: {latest_output}")
                                            raise Exception(f"Output video file is empty: {latest_output}")

                                        # Check if the file is still being written by checking if it's locked
                                        def is_file_locked(file_path):
                                            try:
                                                # Try to open the file in exclusive mode
                                                with open(file_path, 'r+b') as f:
                                                    # If we can open it, it's not locked
                                                    return False
                                            except IOError:
                                                # If we can't open it, it's probably locked
                                                return True

                                        # Wait for the file to be fully written and unlocked
                                        max_wait_attempts = 5
                                        wait_attempt = 0
                                        while wait_attempt < max_wait_attempts and is_file_locked(latest_output):
                                            print(f"Output video file is locked, waiting... (attempt {wait_attempt+1}/{max_wait_attempts})")
                                            time.sleep(1.0)
                                            wait_attempt += 1

                                        # Check file size stability to ensure it's fully written
                                        last_size = os.path.getsize(latest_output)
                                        time.sleep(0.5)
                                        current_size = os.path.getsize(latest_output)

                                        # If the file size is still changing, wait a bit more
                                        if current_size != last_size:
                                            print(f"Output video file size is still changing, waiting...")
                                            time.sleep(2.0)

                                        # Load the video with a timeout
                                        print(f"Loading output preview video: {latest_output}")
                                        try:
                                            # Before loading, check if we need to recreate the player
                                            # This is a preventive measure to avoid the FFmpeg assertion error
                                            if hasattr(self, 'output_video_load_count') and self.output_video_load_count > 3:
                                                # After a few loads, recreate the player to prevent memory issues
                                                print("Preventively recreating output video player to avoid assertion errors")

                                                # Destroy the old player if possible
                                                if hasattr(self.output_preview_player, 'destroy'):
                                                    try:
                                                        self.output_preview_player.destroy()
                                                    except Exception as e:
                                                        print(f"Error destroying output preview player: {e}")

                                                # Remove the player from the frame
                                                if hasattr(self.output_preview_player, 'pack_forget'):
                                                    try:
                                                        self.output_preview_player.pack_forget()
                                                    except Exception as e:
                                                        print(f"Error removing output preview player: {e}")

                                                # Force garbage collection
                                                for _ in range(3):
                                                    gc.collect()

                                                # Wait for resources to be released
                                                time.sleep(2.0)

                                                # Create a new player
                                                self.output_preview_player = TkinterVideo(master=self.output_video_frame, scaled=True, keep_aspect=True)
                                                self.output_preview_player.config(height=480)
                                                self.output_preview_player.pack(fill=tk.BOTH, expand=True)

                                                # Bind click event
                                                self.output_preview_player.bind("<Button-1>", self.toggle_output_preview_playback)

                                                # Reset the counter
                                                self.output_video_load_count = 0
                                            else:
                                                # Increment the counter
                                                if hasattr(self, 'output_video_load_count'):
                                                    self.output_video_load_count += 1
                                                else:
                                                    self.output_video_load_count = 1

                                            # Now load the video
                                            self.output_preview_player.load(latest_output)
                                            load_success = True
                                        except (AttributeError, Exception) as error:
                                            # Use our specialized error handler for any error
                                            if self.handle_video_load_error(self.output_preview_player, latest_output, error):
                                                load_success = True
                                            else:
                                                raise error
                                    except Exception as e:
                                        print(f"Error in initial load attempt for output preview: {e}")
                                        # If loading fails, try one more time after a longer delay
                                        time.sleep(1.0)
                                        try:
                                            # Force garbage collection again
                                            gc.collect()

                                            print(f"Retrying load of output preview video: {latest_output}")
                                            try:
                                                self.output_preview_player.load(latest_output)
                                                load_success = True
                                            except AttributeError as attr_error:
                                                # Use our specialized error handler for the retry as well
                                                if self.handle_video_load_error(self.output_preview_player, latest_output, attr_error):
                                                    load_success = True
                                                else:
                                                    raise attr_error
                                        except Exception as e2:
                                            print(f"Error in second load attempt for output preview: {e2}")
                                            # Don't raise the error, just log it and continue
                                            load_success = False

                                    if not load_success:
                                        raise Exception("Failed to load output preview video after multiple attempts")

                                    # Set up a video ended event handler to restart the video (looping)
                                    self.output_preview_player.bind("<<Ended>>", self.restart_output_preview)

                                    # Play the video
                                    self.output_preview_player.play()
                                except Exception as e:
                                    print(f"Critical error loading output preview: {e}")
                                    # If we can't load the video, show an error message
                                    self.output_preview_label.config(text=f"Error loading output preview: {str(e)}")

                                    # Try to reset the player
                                    try:
                                        # First try to stop the player
                                        if hasattr(self.output_preview_player, 'stop'):
                                            self.output_preview_player.stop()

                                        # If this is a critical error, try a complete reset of both players
                                        if "assertion" in str(e).lower() or "pthread_frame" in str(e).lower() or "ffmpeg" in str(e).lower():
                                            print("Critical FFmpeg error detected, performing complete player reset")
                                            self.root.after(1000, self.reset_video_players)
                                    except Exception as reset_error:
                                        print(f"Error during player reset: {reset_error}")

                                # Update the label
                                self.output_preview_label.config(text=f"Playing: {os.path.basename(latest_output)}")
                            except Exception as e:
                                print(f"Error loading new output preview: {e}")
                                self.output_preview_label.config(text=f"Error loading output preview: {str(e)}")
                            finally:
                                # Reset the loading flag
                                self.output_preview_loading = False
                                # Release the lock
                                self.output_video_load_lock.release()

                        # Schedule the video loading with a longer delay for better stability
                        self.root.after(1000, load_new_output_video)  # Increased delay to 1 second

                except Exception as e:
                    print(f"Error displaying output preview: {e}")
                    self.output_preview_label.config(text=f"Error loading output preview: {str(e)}")
                    # Reset the loading flag
                    self.output_preview_loading = False
                    # Release the lock
                    if self.output_video_load_lock._is_owned():
                        self.output_video_load_lock.release()

            # Schedule the next update (less frequent than latent preview)
            self.root.after(2000, self.update_output_preview)

        except Exception as e:
            print(f"Unexpected error in update_output_preview: {e}")
            # Schedule the next update even if an error occurred
            self.root.after(2000, self.update_output_preview)

    def check_processing_status(self):
        """Check if processing has stopped and update button states accordingly"""
        stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
        completion_signal_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "framepack_completed.signal")
        temp_batch_file = "temp_run_framepack.bat"

        # Initialize the check counter if it doesn't exist
        if not hasattr(self, 'status_check_count'):
            self.status_check_count = 0

        # During the first few checks, be more patient and don't interfere with the process
        if self.status_check_count < 5:  # First 5 checks (about 15 seconds)
            # If the batch file exists, assume the process is still starting up
            if os.path.exists(temp_batch_file):
                self.status_check_count += 1
                self.root.after(3000, self.check_processing_status)
                return

        # Check for completion signal file first (highest priority)
        if os.path.exists(completion_signal_path):
            try:
                # Read the signal file to see if it was successful or not
                with open(completion_signal_path, 'r') as f:
                    status = f.read().strip()

                # Handle different status types
                if status == "MISSING_FILES":
                    self.show_status("Missing files detected. Continuing to next item.", "warning")
                elif status == "FALLBACK":
                    self.show_status("Fallback completion detected. Continuing to next item.", "warning")
                elif status == "ERROR":
                    self.show_status("Error detected. Continuing to next item.", "error")
                elif status == "SUCCESS":
                    self.show_status("Processing completed successfully.", "success")
                else:
                    self.show_status(f"Unknown status: {status}. Continuing to next item.", "warning")

                # Remove the signal file
                os.remove(completion_signal_path)

                # Create or ensure stop flag exists
                # This ensures the process is properly stopped
                if not os.path.exists(stop_flag_path):
                    try:
                        with open(stop_flag_path, 'w') as f:
                            f.write(f"Auto-stop requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
                    except Exception as e:
                        pass
            except Exception as e:
                pass

            # Reset button states
            self.run_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.stop_queue_button.config(state="disabled")
            self.status_check_count = 0

            # Cancel any scheduled safety stop
            if hasattr(self, 'safety_stop_job') and self.safety_stop_job is not None:
                self.root.after_cancel(self.safety_stop_job)
                self.safety_stop_job = None

            # If we're processing a queue, continue with the next item
            if self.is_processing_queue:
                # Use a shorter delay to make queue processing more responsive
                self.root.after(500, self.process_next_queue_item)
            else:
                pass

            return

        # Check if the batch file still exists (second priority)
        # If it doesn't exist, the process has likely completed
        if not os.path.exists(temp_batch_file):
            # Try to remove the stop flag if it exists
            if os.path.exists(stop_flag_path):
                try:
                    os.remove(stop_flag_path)
                except Exception as e:
                    pass

            # Reset button states
            self.run_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.stop_queue_button.config(state="disabled")
            self.status_check_count = 0

            # Cancel any scheduled safety stop
            if hasattr(self, 'safety_stop_job') and self.safety_stop_job is not None:
                self.root.after_cancel(self.safety_stop_job)
                self.safety_stop_job = None

            # If we're processing a queue, continue with the next item
            if self.is_processing_queue:
                # Use a shorter delay to make queue processing more responsive
                self.root.after(500, self.process_next_queue_item)

            return

        # If the stop flag still exists, check again later (third priority)
        if os.path.exists(stop_flag_path):
            self.status_check_count += 1

            # Check if we've reached the timeout limit
            if self.status_check_count >= self.max_status_checks:
                try:
                    # Try to remove the stop flag file
                    os.remove(stop_flag_path)
                except Exception as e:
                    pass

                # Reset button states
                self.run_button.config(state="normal")
                self.stop_button.config(state="disabled")
                self.stop_queue_button.config(state="disabled")
                self.status_check_count = 0

                # Cancel any scheduled safety stop
                if hasattr(self, 'safety_stop_job') and self.safety_stop_job is not None:
                    self.root.after_cancel(self.safety_stop_job)
                    self.safety_stop_job = None

                # If we're processing a queue, continue with the next item
                if self.is_processing_queue:
                    self.root.after(500, self.process_next_queue_item)

                return

            # Continue checking (every 3 seconds to reduce overhead)
            self.root.after(3000, self.check_processing_status)
        else:
            # If we get here, the process is still running (no completion signal, batch file exists, no stop flag)
            # Continue checking
            self.status_check_count += 1

            # Check if we've reached the timeout limit (safety check)
            if self.status_check_count >= self.max_status_checks * 3:  # Triple timeout for this case (15 minutes total)
                # Reset button states
                self.run_button.config(state="normal")
                self.stop_button.config(state="disabled")
                self.stop_queue_button.config(state="disabled")
                self.status_check_count = 0

                # Cancel any scheduled safety stop
                if hasattr(self, 'safety_stop_job') and self.safety_stop_job is not None:
                    self.root.after_cancel(self.safety_stop_job)
                    self.safety_stop_job = None

                # If we're processing a queue, continue with the next item
                if self.is_processing_queue:
                    self.root.after(500, self.process_next_queue_item)

                return

            # Continue checking (every 3 seconds to reduce overhead)
            self.root.after(3000, self.check_processing_status)

    def run_framepack(self, from_queue=False):
        current_mode = self.input_mode.get()

        # Check if we have items in the batch queue
        # Skip this check if we're running from the queue since we've already validated
        if not from_queue and not self.selected_files:
            self.show_status("No items in the batch queue. Please add at least one item to process.", "error")
            return

        # If we're not processing from the queue and there are items in the queue,
        # we'll now automatically start processing the queue (handled by run_or_start_queue)
        # This code is kept for backward compatibility with other parts of the code

        # Remove any existing stop flag file and completion signal file
        stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
        completion_signal_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "framepack_completed.signal")

        if os.path.exists(stop_flag_path):
            try:
                os.remove(stop_flag_path)
                print(f"Removed existing stop flag file: {stop_flag_path}")
            except Exception as e:
                print(f"Warning: Could not remove existing stop flag file: {e}")

        if os.path.exists(completion_signal_path):
            try:
                os.remove(completion_signal_path)
                print(f"Removed existing completion signal file: {completion_signal_path}")
            except Exception as e:
                print(f"Warning: Could not remove existing completion signal file: {e}")

        # Determine which batch script to use based on model selection
        if self.model_type.get() == "f1":
            cmd = [sys.executable, "batch_f1.py"]
            print("Using FramePack F1 model")
        else:
            cmd = [sys.executable, "batch.py"]
            print("Using standard FramePack model")

        # In unified mode, we need to separate directories, files, and URLs
        dir_list = []
        file_list = []
        url_list = []

        for item in self.selected_files:
            if item.startswith('DIR:'):
                # It's a directory
                dir_path = item[4:]  # Remove the DIR: prefix
                dir_list.append(dir_path)
            elif item.startswith(('http://', 'https://')):
                # It's a URL
                url_list.append(item)
            else:
                # It's a file
                file_list.append(item)

        # Create a combined list file with all items
        temp_file_list = "temp_combined_list.txt"

        # We don't delete the existing temp_combined_list.txt file anymore
        # to allow it to persist between app restarts

        try:
            with open(temp_file_list, 'w', encoding='utf-8') as f:
                # Write directories with a special prefix
                for dir_path in dir_list:
                    f.write(f"DIR:{dir_path}\n")

                # Write files as is
                for item in file_list:
                    f.write(f"{item}\n")

                # Write URLs with a special prefix to handle special characters
                for url in url_list:
                    # Add URL: prefix to indicate this is a URL that may contain special characters
                    f.write(f"URL:{url}\n")

            # Verify the file was created and has content
            if os.path.exists(temp_file_list) and os.path.getsize(temp_file_list) > 0:
                print(f"Successfully created temp file list with {len(dir_list) + len(file_list) + len(url_list)} items")
                # Add the unified-list parameter
                cmd.extend(["--unified-list", temp_file_list])
            else:
                raise Exception("Temp file list was not created or is empty")

            # Log what we're processing
            print(f"Processing {len(dir_list)} directories, {len(file_list)} files, and {len(url_list)} URLs")
        except Exception as e:
            print(f"Error creating unified list: {e}")
            self.show_status(f"Failed to create unified list: {e}", "error")
            return

        # Output directory is always needed
        cmd.extend(["--output_dir", self.output_dir.get()])

        # Handle prompt - pass it directly as a command line argument
        # The batch file will add quotes around it
        if self.fallback_prompt.get():
            cmd.extend(["--prompt", self.fallback_prompt.get()])

        cmd.extend(["--seed", str(self.seed.get())])
        cmd.extend(["--video_length", str(self.video_length.get())])
        cmd.extend(["--steps", str(self.steps.get())])
        cmd.extend(["--distilled_cfg", str(self.distilled_cfg.get())])
        cmd.extend(["--gpu_memory", str(self.gpu_memory.get())])
        cmd.extend(["--mp4_crf", str(self.mp4_crf.get())])

        # Add latent window size parameter for F1 model
        if self.model_type.get() == "f1":
            cmd.extend(["--latent_window_size", str(self.latent_window_size.get())])

        if self.use_teacache.get():
            cmd.append("--use_teacache")

        # Add randomize_order for all modes
        if self.randomize_order.get():
            cmd.append("--randomize_order")

        # Only add clear_processed_list in directory mode
        if current_mode == "directory" and self.clear_processed_list.get():
            cmd.append("--clear_processed_list")

        # Handle image prompt option
        if self.use_image_prompt.get():
            cmd.append("--use_image_prompt")
        else:
            # Explicitly disable image prompt extraction when not checked
            # This ensures the fallback prompt is used instead
            cmd.append("--no_image_prompt")

        if self.overwrite.get():
            cmd.append("--overwrite")

        if self.allow_duplicates.get():
            cmd.append("--allow_duplicates")

        if self.fix_encoding.get():
            cmd.append("--fix_encoding")
        else:
            cmd.append("--no_fix_encoding")

        if self.use_prompt_list_file.get():
            cmd.append("--use_prompt_list_file")
            cmd.extend(["--prompt_list_file", self.prompt_list_file.get()])

        if self.copy_to_input.get():
            cmd.append("--copy_to_input")
        else:
            cmd.append("--no_copy_to_input")

        if self.apply_all_prompts.get():
            cmd.append("--apply_all_prompts")

        # Add video trim settings for selected files
        # We only need to add these parameters if we're processing a single file with trim settings
        if len(self.selected_files) == 1 and self.selected_files[0] in self.file_trim_settings:
            file_path = self.selected_files[0]
            trim_settings = self.file_trim_settings[file_path]
            trim_seconds = trim_settings.get('trim_seconds', 0)
            trim_from_beginning = trim_settings.get('trim_from_beginning', False)

            if trim_seconds > 0:
                cmd.extend(["--trim_seconds", str(trim_seconds)])
                if trim_from_beginning:
                    cmd.append("--trim_from_beginning")
                else:
                    cmd.append("--no_trim_from_beginning")
                print(f"Adding trim settings: {trim_seconds}s from {'beginning' if trim_from_beginning else 'end'}")

        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir.get(), exist_ok=True)

        # Create input directory if in directory mode and it doesn't exist
        if current_mode == "directory":
            os.makedirs(self.input_dir.get(), exist_ok=True)

        # Save current settings as default
        self.save_default_settings()

        # Set the generation_started flag to true
        self.generation_started = True

        # Reset the session start time to now
        self.session_start_time = time.time()

        # Show command that will be executed
        command_str = " ".join(cmd)
        print(f"Executing: {command_str}")

        # If we're using F1 model, use batch_f1_lock.py which will handle both image and video files
        if self.model_type.get() == "f1":
            print("Using F1 model with batch_f1_lock.py for automatic file type detection")

            # Replace the script name in the command with batch_f1_lock.py
            # This script will automatically detect video files and route them to batch_f1_video.py
            # or use batch_f1.py for image files
            cmd[1] = "batch_f1_lock.py"

            # Log the command that will be used
            print(f"Using command: {' '.join(cmd)}")

            # No need for separate handling of image and video files
            # The lock script will handle this automatically

        # Create a temporary batch file to run the command and keep the window open
        temp_batch_file = "temp_run_framepack.bat"
        with open(temp_batch_file, 'w') as f:
            f.write('@echo off\n')
            f.write('setlocal enabledelayedexpansion\n')
            f.write('title FramePack Batch Processing\n')
            f.write('color 0A\n')  # Green text on black background for better visibility
            f.write('echo ===============================================\n')
            f.write('echo             FRAMEPACK BATCH PROCESSING         \n')
            f.write('echo ===============================================\n')
            f.write('echo.\n')
            f.write('echo Running FramePack with the following settings:\n')
            f.write('echo.\n')

            # Write settings summary
            current_mode = self.input_mode.get()
            if current_mode == "directory":
                f.write(f'echo Mode:                Directory\n')
                f.write(f'echo Input Directory:    {self.input_dir.get()}\n')
                f.write(f'echo Randomize Order:    {"Yes" if self.randomize_order.get() else "No"}\n')
                f.write(f'echo Clear Processed:    {"Yes" if self.clear_processed_list.get() else "No"}\n')
            elif current_mode == "files":
                f.write(f'echo Mode:                Individual Files\n')
                f.write(f'echo Number of Files:     {len(self.selected_files)}\n')

                # List the first few files (up to 5)
                max_files_to_show = min(5, len(self.selected_files))
                for i in range(max_files_to_show):
                    f.write(f'echo File {i+1}:             {os.path.basename(self.selected_files[i])}\n')

                # Indicate if there are more files
                if len(self.selected_files) > max_files_to_show:
                    f.write(f'echo                    ... and {len(self.selected_files) - max_files_to_show} more files\n')
            elif current_mode == "urls":
                f.write(f'echo Mode:                URLs\n')
                f.write(f'echo Number of URLs:      {len(self.selected_files)}\n')

                # List the first few URLs (up to 5)
                max_urls_to_show = min(5, len(self.selected_files))
                for i in range(max_urls_to_show):
                    # Truncate long URLs for display
                    url = self.selected_files[i]
                    if len(url) > 50:
                        display_url = url[:47] + "..."
                    else:
                        display_url = url
                    f.write(f'echo URL {i+1}:              {display_url}\n')

                # Indicate if there are more URLs
                if len(self.selected_files) > max_urls_to_show:
                    f.write(f'echo                    ... and {len(self.selected_files) - max_urls_to_show} more URLs\n')
            else:  # Combined mode
                # Count files and URLs
                file_count = sum(1 for item in self.selected_files if not item.startswith(('http://', 'https://')))
                url_count = sum(1 for item in self.selected_files if item.startswith(('http://', 'https://')))

                f.write(f'echo Mode:                Combined (Files and URLs)\n')
                f.write(f'echo Number of Items:     {len(self.selected_files)} ({file_count} files, {url_count} URLs)\n')

                # List the first few items (up to 5)
                max_items_to_show = min(5, len(self.selected_files))
                for i in range(max_items_to_show):
                    item = self.selected_files[i]
                    if item.startswith(('http://', 'https://')):
                        # It's a URL, truncate if needed
                        if len(item) > 50:
                            display_item = item[:47] + "..."
                        else:
                            display_item = item
                        f.write(f'echo URL {i+1}:              {display_item}\n')
                    else:
                        # It's a file
                        f.write(f'echo File {i+1}:             {os.path.basename(item)}\n')

                # Indicate if there are more items
                if len(self.selected_files) > max_items_to_show:
                    f.write(f'echo                    ... and {len(self.selected_files) - max_items_to_show} more items\n')

            f.write(f'echo Output Directory:   {self.output_dir.get()}\n')
            f.write(f'echo Fallback Prompt:    {self.fallback_prompt.get() if self.fallback_prompt.get() else "(None)"}\n')
            f.write(f'echo Model Type:         {"FramePack F1" if self.model_type.get() == "f1" else "Standard FramePack"}\n')
            f.write(f'echo Seed:               {self.seed.get()} {"(Random)" if self.seed.get() == -1 else ""}\n')
            f.write(f'echo Video Length:       {self.video_length.get()} seconds\n')
            f.write(f'echo Steps:              {self.steps.get()}\n')
            f.write(f'echo Distilled CFG:      {self.distilled_cfg.get()}\n')
            f.write(f'echo GPU Memory:         {self.gpu_memory.get()} GB\n')
            f.write(f'echo MP4 Compression:    {self.mp4_crf.get()} (0-51, lower is better)\n')
            if self.model_type.get() == "f1":
                f.write(f'echo Latent Window Size: {self.latent_window_size.get()}\n')
            f.write(f'echo Use TeaCache:       {"Yes" if self.use_teacache.get() else "No"}\n')
            f.write(f'echo Use Image Prompt:   {"Yes" if self.use_image_prompt.get() else "No"}\n')
            f.write(f'echo Use Prompt List:    {"Yes - " + self.prompt_list_file.get() if self.use_prompt_list_file.get() else "No"}\n')
            f.write(f'echo Fix Encoding:       {"Yes" if self.fix_encoding.get() else "No"}\n')
            f.write(f'echo Copy to Input:      {"Yes" if self.copy_to_input.get() else "No"}\n')
            f.write(f'echo Overwrite Existing: {"Yes" if self.overwrite.get() else "No"}\n')
            f.write(f'echo Allow Duplicates:   {"Yes" if self.allow_duplicates.get() else "No"}\n')
            f.write(f'echo Apply All Prompts:  {"Yes" if self.apply_all_prompts.get() else "No"}\n')
            f.write('echo.\n')
            f.write('echo ===============================================\n')
            f.write('echo.\n')

            # Activate virtual environment and run the command
            f.write('echo Activating virtual environment...\n')
            f.write('call venv\\Scripts\\activate.bat\n')
            f.write('if %ERRORLEVEL% NEQ 0 (\n')
            f.write('    color 0C\n')
            f.write('    echo ERROR: Failed to activate virtual environment.\n')
            f.write('    echo Please make sure the venv directory exists and is properly set up.\n')
            f.write('    echo.\n')
            f.write('    pause\n')
            f.write('    exit /b 1\n')
            f.write(')\n')
            f.write('echo Virtual environment activated successfully.\n')
            f.write('echo.\n')
            # Format the command with proper quoting for the batch file
            formatted_cmd = []
            i = 0
            while i < len(cmd):
                if cmd[i] == "--prompt" and i + 1 < len(cmd):
                    # Add quotes around the prompt value
                    formatted_cmd.append(f'{cmd[i]} "{cmd[i+1]}"')
                    i += 2  # Skip the next item since we've included it
                else:
                    formatted_cmd.append(cmd[i])
                    i += 1

            formatted_cmd_str = " ".join(formatted_cmd)
            f.write('echo Running command: ' + formatted_cmd_str + '\n')
            f.write('echo.\n')

            # Check if the temp_combined_list.txt file exists before running the command
            f.write('REM Check if the temp_combined_list.txt file exists\n')
            f.write('if not exist "temp_combined_list.txt" (\n')
            f.write('    echo Error: temp_combined_list.txt file not found\n')
            f.write('    echo Creating empty file to prevent further errors\n')
            f.write('    echo. > temp_combined_list.txt\n')
            f.write(')\n')
            f.write('echo.\n')

            # Add error handling for the case where files might be missing
            f.write('REM Run the command with error handling\n')
            f.write('set COMMAND_RESULT=0\n')
            # Run the command normally to show real-time output
            f.write('echo Running command with real-time output...\n')
            f.write('call ' + formatted_cmd_str + '\n')  # Use 'call' to prevent batch file termination on error
            f.write('set COMMAND_RESULT=%ERRORLEVEL%\n')
            f.write('echo Command completed with exit code: %COMMAND_RESULT%\n')

            # Create a simple log file for error detection
            f.write('echo Creating error detection log...\n')
            f.write('echo Command result: %COMMAND_RESULT% > temp_command_result.txt\n')

            # Check for specific error conditions based on exit code
            f.write('REM Check for specific error conditions\n')
            f.write('set MISSING_FILES=0\n')

            # Check if the command failed with a non-zero exit code
            f.write('if %COMMAND_RESULT% NEQ 0 (\n')
            f.write('    REM Check if this is likely a missing files error\n')
            f.write('    if exist "temp_file_list.txt" (\n')
            f.write('        echo Checking temp file list content...\n')
            f.write('        find /c /v "" temp_file_list.txt > temp_line_count.txt 2>nul\n')
            f.write('        if exist temp_line_count.txt (\n')
            f.write('            set /p LINE_COUNT=<temp_line_count.txt\n')
            f.write('            if "!LINE_COUNT!"=="0" set MISSING_FILES=1\n')
            f.write('            del temp_line_count.txt 2>nul\n')
            f.write('        ) else (\n')
            f.write('            echo Warning: Could not create line count file\n')
            f.write('            set MISSING_FILES=1\n')
            f.write('        )\n')
            f.write('    ) else (\n')
            f.write('        echo Temp file list not found, assuming missing files error...\n')
            f.write('        set MISSING_FILES=1\n')
            f.write('    )\n')
            f.write(')\n')

            # Handle the result based on error conditions
            f.write('echo.\n')
            f.write('if %MISSING_FILES% EQU 1 (\n')
            f.write('    color 0E\n')  # Yellow text for warnings
            f.write('    echo ===============================================\n')
            f.write('    echo WARNING: Missing files or file list detected\n')
            f.write('    echo ===============================================\n')
            f.write('    echo.\n')
            f.write('    echo This is likely due to a temporary file being deleted too early.\n')
            f.write('    echo The queue will continue to the next item.\n')
            f.write('    echo.\n')
            f.write('    echo Creating completion signal file...\n')
            f.write('    echo MISSING_FILES > framepack_completed.signal\n')
            f.write('    echo Creating stop flag file...\n')
            f.write('    echo "Auto-stop after missing files at %DATE% %TIME%" > stop_framepack.flag\n')
            f.write('    echo Window will close in 5 seconds...\n')
            f.write('    timeout /t 5 > nul\n')
            f.write(') else if %COMMAND_RESULT% NEQ 0 (\n')
            f.write('    color 0C\n')  # Red text for errors
            f.write('    echo ===============================================\n')
            f.write('    echo ERROR: Processing failed with error code %COMMAND_RESULT%\n')
            f.write('    echo ===============================================\n')
            f.write('    echo.\n')
            f.write('    echo Please check the error messages above for details.\n')
            f.write('    echo.\n')
            f.write('    echo Creating completion signal file...\n')
            f.write('    echo ERROR > framepack_completed.signal\n')
            f.write('    echo Creating stop flag file...\n')
            f.write('    echo "Auto-stop after error at %DATE% %TIME%" > stop_framepack.flag\n')
            f.write('    echo Window will close in 10 seconds...\n')
            f.write('    timeout /t 10 > nul\n')
            f.write(') else (\n')
            f.write('    echo ===============================================\n')
            f.write('    echo Processing completed successfully!\n')
            f.write('    echo ===============================================\n')
            f.write('    echo.\n')
            f.write('    echo Creating completion signal file...\n')
            f.write('    echo SUCCESS > framepack_completed.signal\n')
            f.write('    echo Creating stop flag file...\n')
            f.write('    echo "Auto-stop after success at %DATE% %TIME%" > stop_framepack.flag\n')
            f.write('    echo Window will close in 10 seconds...\n')
            f.write('    timeout /t 10 > nul\n')
            f.write(')\n')

            # Add cleanup for the temporary files
            f.write('\n')
            f.write('REM Clean up the temporary files\n')

            # Clean up all temporary files
            f.write('echo Cleaning up temporary files...\n')
            f.write('if exist "temp_command_output.txt" del "temp_command_output.txt" 2>nul\n')
            f.write('if exist "temp_command_result.txt" del "temp_command_result.txt" 2>nul\n')
            f.write('if exist "temp_line_count.txt" del "temp_line_count.txt" 2>nul\n')

            # We don't clean up the temp_combined_list.txt file anymore to allow it to persist between app restarts
            # if current_mode != "directory":
            #     f.write(f'if exist "{temp_file_list}" del "{temp_file_list}" 2>nul\n')

            # Final check to ensure a completion signal file exists
            f.write('REM Final check to ensure a completion signal file exists\n')
            f.write('if not exist "framepack_completed.signal" (\n')
            f.write('    echo Creating completion signal file as a fallback...\n')
            f.write('    echo FALLBACK > framepack_completed.signal\n')
            f.write('    echo Creating stop flag file...\n')
            f.write('    echo "Auto-stop fallback at %DATE% %TIME%" > stop_framepack.flag\n')
            f.write(')\n')

            # Add a delay to ensure the completion signal file is detected
            f.write('\n')
            f.write('REM Add a delay to ensure the completion signal file is detected\n')
            f.write('timeout /t 5 > nul\n')

            # Add cleanup for the batch file itself (self-delete)
            f.write('\n')
            f.write('REM Clean up the batch file itself\n')
            f.write('(goto) 2>nul & del "%~f0"\n')

        # Run the batch file
        subprocess.Popen(temp_batch_file, shell=True)

        # Update button states
        self.run_button.config(state="disabled")
        self.stop_button.config(state="normal")

        # Enable the stop queue button if we're processing a queue
        if self.is_processing_queue:
            self.stop_queue_button.config(state="normal")

        # Start checking for processing status
        self.start_checking_status()

    def add_current_settings_to_queue(self):
        """Add current settings as a job to the queue"""
        # Check if we have items in the batch queue
        if not self.selected_files:
            self.show_status("No items in the batch queue. Please add at least one item to process.", "error")
            return

        # Create a job with current settings
        job = {
            "input_mode": "unified",  # Always use unified mode
            "selected_files": self.selected_files.copy(),  # Copy the current batch queue
            "output_dir": self.output_dir.get(),
            "fallback_prompt": self.fallback_prompt.get(),
            "seed": self.seed.get(),
            "video_length": self.video_length.get(),
            "steps": self.steps.get(),
            "distilled_cfg": self.distilled_cfg.get(),
            "model_type": self.model_type.get(),
            "latent_window_size": self.latent_window_size.get(),
            "use_teacache": self.use_teacache.get(),
            "gpu_memory": self.gpu_memory.get(),
            "mp4_crf": self.mp4_crf.get(),
            "randomize_order": self.randomize_order.get(),
            "clear_processed_list": self.clear_processed_list.get(),
            "use_image_prompt": self.use_image_prompt.get(),
            "overwrite": self.overwrite.get(),
            "fix_encoding": self.fix_encoding.get(),
            "use_prompt_list_file": self.use_prompt_list_file.get(),
            "prompt_list_file": self.prompt_list_file.get(),
            "copy_to_input": self.copy_to_input.get(),
            "allow_duplicates": self.allow_duplicates.get(),
            "apply_all_prompts": self.apply_all_prompts.get(),
            "iterations": self.iterations_var.get(),
            "file_trim_settings": self.file_trim_settings.copy()  # Copy the trim settings
        }

        # Add job to queue
        self.job_queue.append(job)

        # Update queue display
        self.update_queue_display()

        # Show confirmation
        self.show_status(f"Job added to queue with {job['iterations']} iteration(s).", "success")

    def run_or_start_queue(self):
        """Combined function to either run FramePack directly or start queue processing"""
        # If there are items in the queue, start processing the queue
        if self.job_queue:
            self.start_queue_processing()
        else:
            # If no queue items, run FramePack directly with the current settings
            # and use the iterations value
            iterations = self.iterations_var.get()
            if iterations > 1:
                # Create a temporary job with current settings and add to queue
                self.add_current_settings_to_queue()
                # Start processing the queue
                self.start_queue_processing()
            else:
                # Just run once with current settings
                self.run_framepack()

    def start_queue_processing(self):
        """Start processing the queue"""
        if not self.job_queue:
            self.show_status("The queue is empty. Add jobs to the queue first.", "warning")
            return

        if self.is_processing_queue:
            self.show_status("The queue is already being processed.", "info")
            return

        # Start queue processing
        self.is_processing_queue = True
        self.current_job = None
        self.current_job_iteration = 0

        # Enable the Stop Queue button
        self.stop_queue_button.config(state="normal")

        # Update the queue display
        self.update_queue_display()

        # Start processing the first job
        self.process_next_queue_item()

        self.show_status("Queue processing has started.", "success")

    def clear_queue(self):
        """Clear all jobs from the queue"""
        if not self.job_queue:
            self.show_status("The queue is already empty.", "info")
            return

        if messagebox.askyesno("Confirm Clear", "Are you sure you want to clear the entire queue?"):
            self.job_queue = []
            self.update_queue_display()
            self.show_status("All jobs have been removed from the queue.", "success")

    def update_queue_display(self):
        """Update the queue listbox and status label"""
        # Clear the listbox
        self.queue_listbox.delete(0, tk.END)

        # Add each job to the listbox
        for i, job in enumerate(self.job_queue):
            # Create a descriptive string for the job
            if job["input_mode"] == "unified":
                # Count directories, files, and URLs
                dir_count = sum(1 for item in job["selected_files"] if item.startswith('DIR:'))
                url_count = sum(1 for item in job["selected_files"] if item.startswith(('http://', 'https://')) and not item.startswith('DIR:'))
                file_count = len(job["selected_files"]) - dir_count - url_count

                # Create a description based on what's in the job
                parts = []
                if dir_count > 0:
                    parts.append(f"{dir_count} director{'ies' if dir_count > 1 else 'y'}")
                if file_count > 0:
                    parts.append(f"{file_count} file{'s' if file_count > 1 else ''}")
                if url_count > 0:
                    parts.append(f"{url_count} URL{'s' if url_count > 1 else ''}")

                desc = f"Job {i+1}: " + ", ".join(parts)
            elif job["input_mode"] == "directory":
                desc = f"Job {i+1}: Directory '{job['input_dir']}'"
            elif job["input_mode"] == "files":
                file_count = len(job["selected_files"])
                desc = f"Job {i+1}: {file_count} file(s)"
            elif job["input_mode"] == "urls":
                url_count = len(job["selected_files"])
                desc = f"Job {i+1}: {url_count} URL(s)"
            else:  # combined
                file_count = sum(1 for item in job["selected_files"] if not item.startswith(('http://', 'https://')))
                url_count = sum(1 for item in job["selected_files"] if item.startswith(('http://', 'https://')))
                desc = f"Job {i+1}: {file_count} file(s), {url_count} URL(s)"

            # Add iterations info
            desc += f" - {job['iterations']} iteration(s)"

            # Highlight the current job
            if self.is_processing_queue and self.current_job and job == self.current_job:
                desc = f"▶ {desc} (Current)"

            # Add to listbox
            self.queue_listbox.insert(tk.END, desc)

        # Update status label
        if not self.job_queue:
            self.queue_status_label.config(text="Queue: Empty")
        else:
            self.queue_status_label.config(text=f"Queue: {len(self.job_queue)} job(s)")

        # Update progress label
        if self.is_processing_queue and self.current_job:
            # Find the current job index
            try:
                current_job_index = self.job_queue.index(self.current_job)
            except ValueError:
                # If the current job is not in the queue (shouldn't happen), use -1
                current_job_index = -1

            if current_job_index >= 0:
                # Update the progress label with current job and iteration
                progress_text = f"Processing job {current_job_index+1}/{len(self.job_queue)}, " \
                               f"iteration {self.current_job_iteration}/{self.current_job['iterations']}"

                # Add more details about the current job
                if self.current_job["input_mode"] == "directory":
                    progress_text += f" - Directory: {self.current_job['input_dir']}"
                else:
                    file_count = len(self.current_job["selected_files"])
                    progress_text += f" - Files: {file_count}"

                self.queue_progress_label.config(text=progress_text)

                # Also print to console for debugging
                print(f"Queue progress: {progress_text}")
        else:
            self.queue_progress_label.config(text="")

    def process_next_queue_item(self):
        """Process the next item in the queue"""
        # Clean up any temporary files from previous runs
        self.cleanup_temp_files()

        # If queue is empty, we're done
        if not self.job_queue:
            self.is_processing_queue = False
            self.current_job = None
            self.current_job_iteration = 0
            self.update_queue_display()
            self.show_status("Queue processing completed.", "success")
            return

        # If we're not currently processing a job, get the first one
        if not self.current_job:
            self.current_job = self.job_queue[0]
            self.current_job_iteration = 1
        else:
            # Check if we need to do another iteration of the current job
            if self.current_job_iteration < self.current_job["iterations"]:
                # Do another iteration of the current job
                self.current_job_iteration += 1
            else:
                # Move to the next job
                try:
                    current_job_index = self.job_queue.index(self.current_job)

                    if current_job_index < len(self.job_queue) - 1:
                        # There's another job in the queue
                        self.current_job = self.job_queue[current_job_index + 1]
                        self.current_job_iteration = 1
                    else:
                        # We've processed all jobs
                        self.is_processing_queue = False
                        self.current_job = None
                        self.current_job_iteration = 0
                        self.update_queue_display()
                        self.show_status("Queue processing completed - all jobs finished.", "success")
                        return
                except ValueError:
                    # If the current job is not in the queue (shouldn't happen), start from the beginning
                    self.current_job = self.job_queue[0]
                    self.current_job_iteration = 1

        # Update the queue display
        self.update_queue_display()

        # Apply the current job settings to the UI
        self.apply_job_settings(self.current_job)

        # Get the current job index for the status message
        try:
            current_job_index = self.job_queue.index(self.current_job)
        except ValueError:
            current_job_index = 0

        # Show a status message
        status_msg = f"Starting job {current_job_index + 1}/{len(self.job_queue)}, iteration {self.current_job_iteration}/{self.current_job['iterations']}"
        self.show_status(status_msg, "info")

        # Add a small delay before starting the next job to ensure any cleanup from previous job is complete
        self.root.after(3000, self.run_framepack_with_current_settings)

    def cleanup_temp_files(self):
        """Clean up temporary files that might be left over from previous runs"""
        temp_files = [
            # "temp_combined_list.txt" - Keep this file for batch list persistence
            "temp_command_output.txt",
            "temp_command_result.txt",
            "temp_line_count.txt",
            "temp_image_list.txt",
            "temp_video_list.txt",
            "framepack_completed.signal",
            "stop_framepack.flag"
        ]

        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    print(f"Cleaned up temporary file: {temp_file}")
                except Exception as e:
                    print(f"Warning: Could not remove temporary file {temp_file}: {e}")

    def apply_job_settings(self, job):
        """Apply the settings from a job to the UI"""
        # Set input mode
        self.input_mode.set(job["input_mode"])
        self.toggle_input_mode()

        # Set selected files
        self.selected_files = job["selected_files"].copy()

        # Update the files listbox
        self.files_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            if file_path.startswith('DIR:'):
                # It's a directory
                dir_path = file_path[4:]  # Remove the DIR: prefix
                display_name = f"📁 {os.path.basename(dir_path) or dir_path}"
                self.files_listbox.insert(tk.END, display_name)
            elif file_path.startswith(('http://', 'https://')):
                # It's a URL, add icon and truncate if needed
                display_name = f"🌐 {file_path}"
                if len(file_path) > 50:
                    display_name = f"🌐 {file_path[:47]}..."
                self.files_listbox.insert(tk.END, display_name)
            else:
                # It's a file
                self.files_listbox.insert(tk.END, os.path.basename(file_path))

        # Set other settings
        self.output_dir.set(job["output_dir"])
        self.fallback_prompt.set(job["fallback_prompt"])
        self.seed.set(job["seed"])
        self.video_length.set(job["video_length"])
        self.steps.set(job["steps"])
        self.distilled_cfg.set(job["distilled_cfg"])
        self.model_type.set(job["model_type"])
        self.latent_window_size.set(job["latent_window_size"])
        self.use_teacache.set(job["use_teacache"])
        self.gpu_memory.set(job["gpu_memory"])
        self.mp4_crf.set(job["mp4_crf"])
        self.randomize_order.set(job["randomize_order"])
        self.clear_processed_list.set(job["clear_processed_list"])
        self.use_image_prompt.set(job["use_image_prompt"])
        self.overwrite.set(job["overwrite"])
        self.fix_encoding.set(job["fix_encoding"])
        self.use_prompt_list_file.set(job["use_prompt_list_file"])
        self.prompt_list_file.set(job["prompt_list_file"])
        self.copy_to_input.set(job["copy_to_input"])
        self.allow_duplicates.set(job["allow_duplicates"])
        self.apply_all_prompts.set(job["apply_all_prompts"])

        # Restore trim settings if they exist in the job
        if "file_trim_settings" in job:
            self.file_trim_settings = job["file_trim_settings"].copy()

            # Update the display in the listbox to show trim indicators
            for i, file_path in enumerate(self.selected_files):
                if file_path in self.file_trim_settings:
                    trim_settings = self.file_trim_settings[file_path]
                    trim_seconds = trim_settings.get('trim_seconds', 0)
                    trim_from_beginning = trim_settings.get('trim_from_beginning', False)

                    if trim_seconds > 0:
                        trim_direction = "beginning" if trim_from_beginning else "end"
                        display_name = f"{os.path.basename(file_path)} [Trim: {trim_seconds}s from {trim_direction}]"
                        self.files_listbox.delete(i)
                        self.files_listbox.insert(i, display_name)

        # Update prompt entry state
        self.toggle_prompt_entry()

    def run_framepack_with_current_settings(self):
        """Run FramePack with the current UI settings"""
        # This is the same as run_framepack but without the checks
        # since we've already validated the settings
        self.run_framepack(from_queue=True)

        # Schedule a safety check to automatically send a stop signal after a long timeout
        # This helps in cases where the completion signal might not be detected
        self.schedule_safety_stop()

    def schedule_safety_stop(self):
        """Schedule a safety stop after a long timeout (30 minutes)"""
        # Calculate a reasonable timeout based on the video length and steps
        # Minimum 30 minutes for longer generations
        base_timeout = 30 * 60 * 1000  # 30 minutes in milliseconds

        # For longer videos or higher step counts, add more time
        try:
            video_length = self.video_length.get()
            steps = self.steps.get()

            # Add 1 minute per second of video length
            additional_time = video_length * 60 * 1000

            # Add 30 seconds per step
            additional_time += steps * 30 * 1000

            # Cap the additional time at 30 minutes
            additional_time = min(additional_time, 30 * 60 * 1000)

            # Add the additional time to the base timeout
            total_timeout = base_timeout + additional_time
        except:
            # If there's an error calculating the timeout, use the base timeout
            total_timeout = base_timeout

        # Cancel any existing safety stop
        if hasattr(self, 'safety_stop_job') and self.safety_stop_job is not None:
            self.root.after_cancel(self.safety_stop_job)

        # Schedule a new safety stop
        # Convert total_timeout to integer to avoid Tkinter error
        total_timeout_int = int(total_timeout)
        self.safety_stop_job = self.root.after(total_timeout_int, self.send_safety_stop)

    def send_safety_stop(self):
        """Send a stop signal as a safety measure if the process is still running"""
        # Check if the batch file still exists
        temp_batch_file = "temp_run_framepack.bat"
        if not os.path.exists(temp_batch_file):
            return

        # Check if the stop flag already exists
        stop_flag_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "stop_framepack.flag")
        if os.path.exists(stop_flag_path):
            return

        # Only send the stop signal if we're still processing
        if self.stop_button.cget("state") == "normal":
            self.stop_framepack()
            self.show_status("Safety timeout reached. Sending stop signal to continue queue.", "warning")

    def save_default_settings(self):
        settings = {
            "input_dir": self.input_dir.get(),
            "output_dir": self.output_dir.get(),
            "fallback_prompt": self.fallback_prompt.get(),
            "seed": self.seed.get(),
            "video_length": self.video_length.get(),
            "steps": self.steps.get(),
            "distilled_cfg": self.distilled_cfg.get(),
            "model_type": self.model_type.get(),
            "latent_window_size": self.latent_window_size.get(),
            "use_teacache": self.use_teacache.get(),
            "gpu_memory": self.gpu_memory.get(),
            "mp4_crf": self.mp4_crf.get(),
            "randomize_order": self.randomize_order.get(),
            "clear_processed_list": self.clear_processed_list.get(),
            "use_image_prompt": self.use_image_prompt.get(),
            "overwrite": self.overwrite.get(),
            "fix_encoding": self.fix_encoding.get(),
            "use_prompt_list_file": self.use_prompt_list_file.get(),
            "prompt_list_file": self.prompt_list_file.get(),
            "copy_to_input": self.copy_to_input.get(),
            "allow_duplicates": self.allow_duplicates.get(),
            "apply_all_prompts": self.apply_all_prompts.get(),
            "input_mode": self.input_mode.get(),
            "selected_files": self.selected_files,
            "file_trim_settings": self.file_trim_settings
        }

        try:
            with open("framepack_default_settings.json", 'w') as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            print(f"Failed to save default settings: {str(e)}")

def main():
    # Parse command line arguments for files
    import argparse
    parser = argparse.ArgumentParser(description="FramePack GUI")
    parser.add_argument("files", nargs="*", help="Image or video files to process")
    parser.add_argument("--file-list", help="Path to a text file containing a list of image or video files to process")
    args = parser.parse_args()

    # Use TkinterDnD.Tk() if available, otherwise fallback to standard tk.Tk()
    if TKDND_AVAILABLE:
        root = TkinterDnD.Tk()
    else:
        root = tk.Tk()

    app = FramePackGUI(root)  # Create the application

    # Start video preview monitoring if available
    if TKVIDEO_AVAILABLE:
        # Start monitoring for latent previews
        if hasattr(app, 'latent_preview_player') and app.latent_preview_player is not None:
            print("Starting latent preview monitoring")
            root.after(1000, app.update_latent_preview)

        # Start monitoring for output videos
        if hasattr(app, 'output_preview_player') and app.output_preview_player is not None:
            print("Starting output video monitoring")
            root.after(2000, app.update_output_preview)

    # List to store file paths
    file_paths = []

    # If a file list was provided, read files from it
    if args.file_list and os.path.exists(args.file_list):
        print(f"Reading file list from {args.file_list}")
        try:
            with open(args.file_list, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        # Print the path for debugging
                        print(f"Found file path: {line}")
                        # Check if the file exists
                        if os.path.exists(line):
                            file_paths.append(line)
                        else:
                            print(f"Warning: File does not exist: {line}")
            print(f"Read {len(file_paths)} valid file paths from list file")
        except Exception as e:
            print(f"Error reading file list: {e}")

    # If files were provided as direct arguments, add them too
    if args.files and len(args.files) > 0:
        print(f"Processing {len(args.files)} files from command line arguments")
        for file_path in args.files:
            abs_path = os.path.abspath(file_path)
            print(f"Processing file: {abs_path}")
            if os.path.exists(abs_path):
                file_paths.append(abs_path)
            else:
                print(f"Warning: File does not exist: {abs_path}")

    # Process the file paths if we have any
    if file_paths:
        # Filter for valid image and video files
        valid_image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
        valid_video_extensions = ['.mp4', '.avi', '.mov', '.webm', '.mkv']
        valid_extensions = valid_image_extensions + valid_video_extensions
        valid_files = []

        # Clear existing files in the list
        app.selected_files = []
        app.files_listbox.delete(0, tk.END)
        print("Cleared existing files from the list")

        for file_path in file_paths:
            if os.path.exists(file_path) and os.path.isfile(file_path):
                ext = os.path.splitext(file_path)[1].lower()
                if ext in valid_extensions:
                    valid_files.append(file_path)
                    app.files_listbox.insert(tk.END, os.path.basename(file_path))
                else:
                    print(f"Warning: Skipping {file_path} - not a supported file type")
            else:
                print(f"Warning: File not found or not a file: {file_path}")

        # If we have valid files, add them to the batch queue
        if valid_files:
            app.selected_files = valid_files
            app.input_mode.set("unified")
            app.toggle_input_mode()
            print(f"Loaded {len(valid_files)} files to the batch queue")

    root.mainloop()

if __name__ == "__main__":
    main()



