#!/usr/bin/env python
"""
framepack_gui.py

This script provides a GUI for the FramePack batch processing scripts.
It allows users to select input files, set processing parameters, and run the batch scripts.
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import json
import shutil
import time
import datetime
import pyperclip  # For clipboard operations
from PIL import Image, ImageGrab  # For clipboard image operations
# add the extra constant
from tkinterdnd2 import TkinterDnD, DND_FILES, DND_TEXT

# Check if TkinterDnD is available
try:
    from tkinterdnd2 import TkinterDnD, DND_FILES, DND_TEXT
    TKDND_AVAILABLE = True
except ImportError:
    print("TkinterDnD2 not available. Drag and drop functionality will be disabled.")
    TKDND_AVAILABLE = False

# Function to load quick prompts from JSON file
def load_quick_prompts_from_json():
    """Load quick prompts from the quick_list.json file"""
    try:
        json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "quick_list.json")
        if os.path.exists(json_path):
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    return data
                else:
                    print("Warning: quick_list.json should contain a list of prompts")
                    return []
        else:
            print(f"Warning: quick_list.json not found at {json_path}")
            return []
    except Exception as e:
        print(f"Error loading quick prompts: {e}")
        return []

# Function to generate a timestamped filename for clipboard images
def generate_clipboard_image_filename():
    """Generate a timestamped filename for clipboard images"""
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    return f"clipboard_image_{timestamp}.png"

# Function to ensure the temp directory exists
def ensure_temp_directory():
    """Ensure the temp directory exists, create it if it doesn't"""
    temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
    if not os.path.exists(temp_dir):
        try:
            os.makedirs(temp_dir)
            print(f"Created temp directory at {temp_dir}")
        except Exception as e:
            print(f"Error creating temp directory: {e}")
            return None
    return temp_dir
