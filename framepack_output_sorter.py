#!/usr/bin/env python
"""
framepack_output_sorter.py

This script scans the current directory for MP4 files,
groups them based on patterns in their filenames, and copies the longest
video from each group to a 'sorted' subfolder.

Pattern recognized:
1. DATE_TIME_XXX_YYYY(_ZZ)(_seed#######)(_XXs).mp4
2. image_name(_seed#######)(_XXs).mp4

Files are grouped by their base identifier and seed value.
The _XXs suffix (if present) indicates the video duration in seconds.
Only MP4 files are processed.

By default, the script selects the longest video in each group based on actual
video duration. If the --use-size flag is provided, it will select the largest
file by file size instead.

IMPORTANT: This script preserves all original files in the current directory.
It copies files to a 'sorted' subfolder without deleting any files.
"""

import os
import re
import shutil
import subprocess
import argparse
import sys
import traceback
from collections import defaultdict


def parse_filename(filename):
    """
    Parse a filename to extract its base group identifier and seed value.
    Examples:
    - 250420_121919_242_3623_37.mp4 -> (250420_121919_242_3623, None)
    - 250420_121919_242_3623_37_24s.mp4 -> (250420_121919_242_3623, None)
    - 250426_085120_706_7278_19_seed357798872.mp4 -> (250426_085120_706_7278, 357798872)
    - 250426_085120_706_7278_19_seed357798872_24s.mp4 -> (250426_085120_706_7278, 357798872)
    - image_name_5s.mp4 -> (image_name, None)
    - image_name_seed123456_5s.mp4 -> (image_name, 123456)

    Returns a tuple of (base_id, seed) or None if the filename doesn't match the pattern.
    """
    # Only match MP4 files
    if not filename.lower().endswith('.mp4'):
        return None

    # Pattern matches various formats:
    # 1. DATE_TIME_XXX_YYYY(_ZZ).mp4
    # 2. DATE_TIME_XXX_YYYY(_ZZ_seed#######).mp4
    # 3. DATE_TIME_XXX_YYYY(_###).mp4
    # 4. DATE_TIME_XXX_YYYY(_###_seed#######).mp4
    # 5. Any of the above with _XXs.mp4 suffix (where XX is duration in seconds)
    # 6. image_name(_seed#######)(_XXs).mp4 - For non-timestamp filenames

    # Extract the base identifier and seed if available
    # First try timestamp pattern
    pattern = r'^(\d+_\d+_\d+_\d+)(?:_\d+)?(?:_seed(\d+))?(?:_\d+s)?\.mp4$'
    match = re.match(pattern, filename)

    if match:
        base_id = match.group(1)
        seed = match.group(2) if match.group(2) else None
        return (base_id, seed)

    # If timestamp pattern doesn't match, try generic filename pattern
    # This handles filenames like "image_name_seed123456_5s.mp4"
    pattern2 = r'^(.+?)(?:_seed(\d+))?(?:_\d+s)?\.mp4$'
    match2 = re.match(pattern2, filename)

    if match2:
        base_id = match2.group(1)
        seed = match2.group(2) if match2.group(2) else None
        return (base_id, seed)

    return None


def extract_file_info(filename):
    """
    Extract detailed information from a filename.
    Returns a dictionary with extracted information.
    """
    info = {
        "filename": filename,
        "base_id": None,
        "suffix": None,
        "seed": None,
        "duration": None
    }

    # Extract base_id, suffix, seed, and duration
    # First try timestamp pattern
    base_match = re.match(r'^(\d+_\d+_\d+_\d+)(?:_(\d+))?(?:_seed(\d+))?(?:_(\d+)s)?\.mp4$', filename)
    if base_match:
        info["base_id"] = base_match.group(1)
        info["suffix"] = base_match.group(2) if base_match.group(2) else ""
        info["seed"] = base_match.group(3) if base_match.group(3) else ""
        info["duration"] = base_match.group(4) if base_match.group(4) else ""
        return info

    # If timestamp pattern doesn't match, try generic filename pattern
    # This handles filenames like "image_name_seed123456_5s.mp4"
    base_match2 = re.match(r'^(.+?)(?:_seed(\d+))?(?:_(\d+)s)?\.mp4$', filename)
    if base_match2:
        info["base_id"] = base_match2.group(1)
        info["suffix"] = ""  # No suffix in this pattern
        info["seed"] = base_match2.group(2) if base_match2.group(2) else ""
        info["duration"] = base_match2.group(3) if base_match2.group(3) else ""

    return info


def group_files(file_paths):
    """
    Group files based on their base identifier and seed value.
    Returns a dictionary where keys are (base_id, seed) tuples and values are lists of file paths.
    Files with the same base_id and seed are considered duplicates.
    """
    groups = defaultdict(list)

    for file_path in file_paths:
        if not os.path.isfile(file_path):
            continue

        filename = os.path.basename(file_path)
        result = parse_filename(filename)

        if result:
            base_id, seed = result
            # Use a tuple of (base_id, seed) as the key
            # This groups files with the same base_id and seed together
            group_key = (base_id, seed)
            groups[group_key].append(file_path)

    return groups


def get_largest_file(file_paths):
    """
    Return the path to the largest file in the given list.
    Includes detailed logging to help diagnose size comparison issues.
    """
    if not file_paths:
        return None

    # Get file sizes and sort by size (largest first)
    files_with_sizes = [(path, os.path.getsize(path)) for path in file_paths]

    # Sort by size in descending order
    sorted_files = sorted(files_with_sizes, key=lambda x: x[1], reverse=True)

    # Print detailed information about each file
    print("  Detailed file size comparison:")
    for path, size in sorted_files:
        filename = os.path.basename(path)
        has_seconds = "_s.mp4" in filename.lower()
        print(f"    {filename} - {size} bytes - {'Has seconds indicator' if has_seconds else 'No seconds indicator'}")

    # Return the path of the largest file
    largest_file = sorted_files[0][0]
    largest_filename = os.path.basename(largest_file)
    print(f"  Selected largest file: {largest_filename} - {sorted_files[0][1]} bytes")

    return largest_file


def get_video_duration(file_path):
    """
    Get the duration of a video file in seconds using FFmpeg.

    Args:
        file_path: Path to the video file

    Returns:
        The duration in seconds as a float, or 0 if the duration couldn't be determined
    """
    try:
        # Use FFmpeg to get the duration
        cmd = ['ffmpeg', '-i', file_path, '-hide_banner']
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)

        # Parse the output to find the duration
        duration_match = re.search(r'Duration: (\d+):(\d+):(\d+\.\d+)', result.stderr)
        if duration_match:
            hours = int(duration_match.group(1))
            minutes = int(duration_match.group(2))
            seconds = float(duration_match.group(3))
            total_seconds = hours * 3600 + minutes * 60 + seconds
            return total_seconds

        # If we couldn't find the duration, try another method
        cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', file_path]
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
        if result.stdout.strip():
            return float(result.stdout.strip())

        # If we still couldn't find the duration, return 0
        return 0
    except Exception as e:
        print(f"  ⚠️ Error getting duration for {os.path.basename(file_path)}: {str(e)}")
        return 0


def get_longest_file(file_paths):
    """
    Return the path to the longest video file in the given list.

    Args:
        file_paths: List of paths to video files

    Returns:
        The path to the longest video file, or None if no files were provided
    """
    if not file_paths:
        return None

    # Get durations for all files
    files_with_durations = []
    for path in file_paths:
        filename = os.path.basename(path)
        size = os.path.getsize(path)
        duration = get_video_duration(path)
        files_with_durations.append((path, duration, size, filename))

    # Sort by duration (longest first)
    sorted_files = sorted(files_with_durations, key=lambda x: (x[1], x[2]), reverse=True)

    # Print detailed information about each file
    print("  Detailed video duration comparison:")
    for path, duration, size, filename in sorted_files:
        has_seconds = bool(re.search(r'_\d+s\.mp4$', filename.lower()))
        seconds_info = f" - Has seconds indicator" if has_seconds else ""
        print(f"    {filename} - {duration:.2f} seconds - {size} bytes{seconds_info}")

    # Return the path of the longest file
    longest_file = sorted_files[0][0]
    longest_filename = os.path.basename(longest_file)
    print(f"  Selected longest file: {longest_filename} - {sorted_files[0][1]:.2f} seconds - {sorted_files[0][2]} bytes")

    return longest_file


def process_files(directory=".", use_size=False):
    """
    Process files in the specified directory:
    1. Group them based on filename patterns and seed values
    2. For each group, find the longest video (or largest file if use_size=True)
    3. Copy the selected file to the 'sorted' subfolder

    Args:
        directory: Directory to process files in
        use_size: If True, use file size instead of video duration to determine which file to keep

    Returns:
        Number of files copied to the sorted directory
    """
    print(f"\n=== Starting Automatic Sorting ===")
    print(f"Processing files in: {directory}")
    print(f"Selection method: {'File size' if use_size else 'Video duration'}")

    # Create the sorted directory if it doesn't exist
    sorted_dir = os.path.join(directory, "sorted")
    os.makedirs(sorted_dir, exist_ok=True)

    # Get all MP4 files in the directory (excluding the sorted directory and script files)
    file_paths = []
    for file in os.listdir(directory):
        # Skip the sorted directory and script files
        if file == "sorted" or file.startswith("framepack_"):
            continue

        # Only process MP4 files
        if not file.lower().endswith('.mp4'):
            continue

        full_path = os.path.join(directory, file)
        if os.path.isfile(full_path):
            file_paths.append(full_path)

    # Check if we found any files to process
    if not file_paths:
        print("No MP4 files found to process in the directory.")
        return 0

    print(f"Found {len(file_paths)} MP4 files to process")

    # Group the files by both base_id and seed
    groups = group_files(file_paths)

    # Check if we have any groups after parsing
    if not groups:
        print("No valid file groups found. Check if filenames match the expected pattern.")
        return 0

    print(f"Grouped into {len(groups)} distinct groups based on base_id and seed")

    # Print detailed information about the grouping
    print(f"\nDetailed grouping information:")
    for group_key, files_in_group in groups.items():
        base_id, seed = group_key
        seed_info = f" (seed: {seed})" if seed else ""
        print(f"  Group: {base_id}{seed_info} - {len(files_in_group)} files")
        for file_path in files_in_group:
            filename = os.path.basename(file_path)
            has_seconds = "_s.mp4" in filename.lower()
            print(f"    {filename} - {'Has seconds indicator' if has_seconds else 'No seconds indicator'}")

    # Process each group
    copied_files = []
    skipped_files = []

    print("\nProcessing file groups:")
    for group_key, files_in_group in groups.items():
        base_id, seed = group_key
        seed_info = f" (seed: {seed})" if seed else ""
        print(f"\nGroup: {base_id}{seed_info} - {len(files_in_group)} files")

        # Get the best file (longest or largest)
        if use_size:
            best_file = get_largest_file(files_in_group)
            print(f"  Using largest file by size")
        else:
            best_file = get_longest_file(files_in_group)
            print(f"  Using longest file by duration")

        if best_file:
            dest_filename = os.path.basename(best_file)
            dest_path = os.path.join(sorted_dir, dest_filename)

            if os.path.exists(dest_path):
                skipped_files.append(dest_filename)
                print(f"  Skipped copying {dest_filename} (already exists in destination)")
            else:
                shutil.copy2(best_file, dest_path)
                copied_files.append(dest_filename)
                print(f"  Copied {dest_filename} to sorted directory")

    # Print summary
    print(f"\nProcessed {len(groups)} groups of files.")
    print(f"Copied {len(copied_files)} files to {sorted_dir}")

    if copied_files:
        print("\nFiles copied:")
        for file in copied_files:
            print(f"  - {file}")

    if skipped_files:
        print(f"\nSkipped {len(skipped_files)} files (already exist in destination):")
        for file in skipped_files:
            print(f"  - {file}")

    print("\n=== Automatic Sorting Completed ===")
    return len(copied_files)


def check_non_matching_files(directory="."):
    """
    Check for files that don't match the expected naming schemes.
    Specifically looks for MP4 files without the seconds indicator.

    Args:
        directory: Directory to process files in

    Returns:
        Tuple containing:
        - List of files that don't match any naming scheme
        - List of files that match a naming scheme but don't have seconds indicator
    """
    print(f"\n=== Checking for Non-Matching Files ===")
    print(f"Scanning directory: {directory}")

    # Get all MP4 files in the directory (excluding the sorted directory and script files)
    file_paths = []
    for file in os.listdir(directory):
        # Skip the sorted directory and script files
        if file == "sorted" or file.startswith("framepack_"):
            continue

        # Only process MP4 files
        if not file.lower().endswith('.mp4'):
            continue

        full_path = os.path.join(directory, file)
        if os.path.isfile(full_path):
            file_paths.append(full_path)

    # Check if we found any files to process
    if not file_paths:
        print("No MP4 files found to check in the directory.")
        return [], []

    print(f"Found {len(file_paths)} MP4 files to check")

    # Lists to store results
    non_matching_files = []
    missing_seconds_files = []

    # Check each file
    for file_path in file_paths:
        filename = os.path.basename(file_path)

        # Check if the file matches any naming scheme
        result = parse_filename(filename)

        if not result:
            # File doesn't match any naming scheme
            non_matching_files.append(file_path)
            continue

        # Check if the file has a seconds indicator
        has_seconds = bool(re.search(r'_\d+s\.mp4$', filename.lower()))
        if not has_seconds:
            missing_seconds_files.append(file_path)

    # Print results
    if non_matching_files:
        print(f"\nFound {len(non_matching_files)} files that don't match any naming scheme:")
        for file_path in non_matching_files:
            print(f"  - {os.path.basename(file_path)}")
    else:
        print("\nAll files match a valid naming scheme.")

    if missing_seconds_files:
        print(f"\nFound {len(missing_seconds_files)} files that match a naming scheme but don't have seconds indicator:")
        for file_path in missing_seconds_files:
            print(f"  - {os.path.basename(file_path)}")
    else:
        print("\nAll valid files have seconds indicators.")

    print("\n=== File Check Completed ===")
    return non_matching_files, missing_seconds_files


def main():
    """
    Main function to parse arguments and process files.
    """
    parser = argparse.ArgumentParser(description="Sort FramePack output files by grouping them and selecting the best file from each group")
    parser.add_argument("--directory", "-d", type=str, default=".",
                        help="Directory to process files in (default: current directory)")
    parser.add_argument("--use-size", "-s", action="store_true",
                        help="Use file size instead of video duration to determine which file to keep")
    parser.add_argument("--check-only", "-c", action="store_true",
                        help="Only check for files that don't match naming schemes, don't perform sorting")

    args = parser.parse_args()

    print("Framepack Output Sorter")
    print("======================")
    print(f"Scanning directory: {args.directory}")

    try:
        if args.check_only:
            # Run in check-only mode
            print("Running in check-only mode (no files will be sorted)")
            non_matching, missing_seconds = check_non_matching_files(args.directory)

            # Print summary
            total_issues = len(non_matching) + len(missing_seconds)
            if total_issues > 0:
                print(f"\nFound {total_issues} files with naming issues.")
                print("Consider using framepack_add_seconds_label.py to add seconds indicators to files.")
            else:
                print("\nNo issues found with file naming conventions.")
        else:
            # Process files in the specified directory
            num_copied = process_files(args.directory, args.use_size)

            if num_copied > 0:
                print("\nOperation completed successfully!")
            else:
                print("\nNo new files were copied.")
    except Exception as e:
        print(f"\nError: {str(e)}")
        traceback.print_exc()
        return 1

    # Keep console window open if running directly
    if sys.stdout.isatty():
        input("\nPress Enter to exit...")

    return 0


if __name__ == "__main__":
    sys.exit(main())
