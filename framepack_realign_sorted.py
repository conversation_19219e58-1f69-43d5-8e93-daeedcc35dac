#!/usr/bin/env python
"""
framepack_realign_sorted.py

This script realigns the files in the sorted folder to ensure that only the longest
video from each group is present. It uses the same grouping logic as the auto_sorter
module but prioritizes video duration over file size.

Usage:
    python framepack_realign_sorted.py [--outputs-dir OUTPUTS_DIR] [--clean] [--use-size]

Arguments:
    --outputs-dir: Path to the outputs directory (default: ./outputs/)
    --clean: Remove files from the sorted folder that aren't the longest in their group
    --use-size: Use file size instead of video duration to determine which file to keep
"""

import os
import sys
import shutil
import argparse
import subprocess
import re
from collections import defaultdict
from auto_sorter import group_files, get_largest_file

def get_video_duration(file_path):
    """
    Get the duration of a video file in seconds using FFmpeg.

    Args:
        file_path: Path to the video file

    Returns:
        The duration in seconds as a float, or 0 if the duration couldn't be determined
    """
    try:
        # Use FFmpeg to get the duration
        cmd = ['ffmpeg', '-i', file_path, '-hide_banner']
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)

        # Parse the output to find the duration
        duration_match = re.search(r'Duration: (\d+):(\d+):(\d+\.\d+)', result.stderr)
        if duration_match:
            hours = int(duration_match.group(1))
            minutes = int(duration_match.group(2))
            seconds = float(duration_match.group(3))
            total_seconds = hours * 3600 + minutes * 60 + seconds
            return total_seconds

        # If we couldn't find the duration, try another method
        cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', file_path]
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
        if result.stdout.strip():
            return float(result.stdout.strip())

        # If we still couldn't find the duration, return 0
        return 0
    except Exception as e:
        print(f"  ⚠️ Error getting duration for {os.path.basename(file_path)}: {str(e)}")
        return 0

def get_longest_file(file_paths):
    """
    Return the path to the longest video file in the given list.

    Args:
        file_paths: List of paths to video files

    Returns:
        The path to the longest video file, or None if no files were provided
    """
    if not file_paths:
        return None

    # Get durations for all files
    files_with_durations = []
    for path in file_paths:
        filename = os.path.basename(path)
        size = os.path.getsize(path)
        duration = get_video_duration(path)
        files_with_durations.append((path, duration, size, filename))

    # Sort by duration (longest first)
    sorted_files = sorted(files_with_durations, key=lambda x: (x[1], x[2]), reverse=True)

    # Print detailed information about each file
    print("  Detailed video duration comparison:")
    for path, duration, size, filename in sorted_files:
        has_seconds = bool(re.search(r'_\d+s\.mp4$', filename.lower()))
        seconds_info = f" - Has seconds indicator" if has_seconds else ""
        print(f"    {filename} - {duration:.2f} seconds - {size} bytes{seconds_info}")

    # Return the path of the longest file
    longest_file = sorted_files[0][0]
    longest_filename = os.path.basename(longest_file)
    print(f"  Selected longest file: {longest_filename} - {sorted_files[0][1]:.2f} seconds - {sorted_files[0][2]} bytes")

    return longest_file

def realign_sorted_folder(outputs_folder, clean=False, use_size=False):
    """
    Realign the files in the sorted folder to ensure that only the longest
    video from each group is present.

    Args:
        outputs_folder: Path to the outputs folder
        clean: If True, remove files from the sorted folder that aren't the longest in their group
        use_size: If True, use file size instead of video duration to determine which file to keep

    Returns:
        A tuple of (num_copied, num_removed)
    """
    print(f"\n=== Starting Sorted Folder Realignment ===")
    print(f"Processing files in: {outputs_folder}")
    print(f"Selection method: {'File size' if use_size else 'Video duration'}")

    # Create the sorted directory if it doesn't exist
    sorted_dir = os.path.join(outputs_folder, "sorted")
    os.makedirs(sorted_dir, exist_ok=True)

    # Get all MP4 files in the outputs directory (excluding the sorted directory and script files)
    output_files = []
    for file in os.listdir(outputs_folder):
        # Skip the sorted directory and script files
        if file == "sorted" or file.startswith("framepack_"):
            continue

        # Only process MP4 files
        if not file.lower().endswith('.mp4'):
            continue

        full_path = os.path.join(outputs_folder, file)
        if os.path.isfile(full_path):
            output_files.append(full_path)

    # Check if we found any files to process
    if not output_files:
        print("No MP4 files found to process in the outputs directory.")
        return 0, 0

    print(f"Found {len(output_files)} MP4 files in the outputs directory")

    # Group the files by both base_id and seed
    groups = group_files(output_files)

    # Check if we have any groups after parsing
    if not groups:
        print("No valid file groups found. Check if filenames match the expected pattern.")
        return 0, 0

    print(f"Grouped into {len(groups)} distinct groups based on base_id and seed")

    # Get all MP4 files in the sorted directory
    sorted_files = []
    for file in os.listdir(sorted_dir):
        # Only process MP4 files
        if not file.lower().endswith('.mp4'):
            continue

        full_path = os.path.join(sorted_dir, file)
        if os.path.isfile(full_path):
            sorted_files.append(full_path)

    print(f"Found {len(sorted_files)} MP4 files in the sorted directory")

    # Group the sorted files by both base_id and seed
    sorted_groups = group_files(sorted_files)

    # Process each group
    copied_files = []
    removed_files = []

    print("\nProcessing file groups:")
    for group_key, files_in_group in groups.items():
        base_id, seed = group_key
        seed_info = f" (seed: {seed})" if seed else ""
        print(f"\nGroup: {base_id}{seed_info} - {len(files_in_group)} files")

        # Get the best file (longest or largest)
        if use_size:
            best_file = get_largest_file(files_in_group)
            selection_method = "largest"
        else:
            best_file = get_longest_file(files_in_group)
            selection_method = "longest"

        if not best_file:
            print(f"  No valid files found in this group")
            continue

        best_filename = os.path.basename(best_file)
        print(f"  Selected {selection_method} file: {best_filename}")

        # Check if the best file is already in the sorted directory
        dest_path = os.path.join(sorted_dir, best_filename)
        if os.path.exists(dest_path):
            print(f"  ✅ Already in sorted directory")
        else:
            # Copy the best file to the sorted directory
            shutil.copy2(best_file, dest_path)
            copied_files.append(best_filename)
            print(f"  ✅ Copied to sorted directory")

        # If clean is True, remove files from the sorted directory that aren't the best
        if clean and group_key in sorted_groups:
            sorted_files_in_group = sorted_groups[group_key]
            for sorted_file in sorted_files_in_group:
                sorted_filename = os.path.basename(sorted_file)
                if sorted_filename != best_filename:
                    try:
                        os.remove(sorted_file)
                        removed_files.append(sorted_filename)
                        print(f"  ❌ Removed from sorted directory: {sorted_filename}")
                    except Exception as e:
                        print(f"  ⚠️ Error removing {sorted_filename}: {str(e)}")

    # Print summary
    print(f"\nRealignment Summary:")
    print(f"Processed {len(groups)} groups of files")
    print(f"Copied {len(copied_files)} files to the sorted directory")
    if clean:
        print(f"Removed {len(removed_files)} files from the sorted directory")

    print("\n=== Sorted Folder Realignment Completed ===")
    return len(copied_files), len(removed_files)

def main():
    """Main function to parse arguments and run the realignment."""
    parser = argparse.ArgumentParser(description="Realign the files in the sorted folder")
    parser.add_argument("--outputs-dir", type=str, default="./outputs/",
                        help="Path to the outputs directory (default: ./outputs/)")
    parser.add_argument("--clean", action="store_true",
                        help="Remove files from the sorted folder that aren't the longest in their group")
    parser.add_argument("--use-size", action="store_true",
                        help="Use file size instead of video duration to determine which file to keep")

    args = parser.parse_args()

    # Run the realignment
    num_copied, num_removed = realign_sorted_folder(args.outputs_dir, args.clean, args.use_size)

    # Print final summary
    if num_copied > 0 or num_removed > 0:
        print(f"\nRealignment completed successfully!")
        print(f"- {num_copied} files copied to the sorted directory")
        if args.clean:
            print(f"- {num_removed} files removed from the sorted directory")
    else:
        print(f"\nNo changes were made. The sorted directory is already up to date.")

    return 0

if __name__ == "__main__":
    sys.exit(main())
