@echo off
:: framepack_sorted_deduper.bat
:: Launcher for framepack_sorted_deduper.py
:: Identifies and removes duplicate MP4 files in the current directory

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Python is not installed or not in the PATH.
    echo Please install Python and try again.
    pause
    exit /b 1
)

:: Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"

:: Execute the Python script
python "%SCRIPT_DIR%framepack_sorted_deduper.py"

exit /b 0
