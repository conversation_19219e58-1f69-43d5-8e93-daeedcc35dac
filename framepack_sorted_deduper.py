#!/usr/bin/env python
"""
framepack_sorted_deduper.py

This script scans the current directory for MP4 files,
identifies duplicates with the same base identifier, and
deletes the smaller files upon confirmation (default is 'y').

Pattern recognized: DATE_TIME_XXX_YYYY(_ZZ)(_XXs).mp4
Files are grouped by their base identifier (everything before the optional _ZZ suffix).
The _XXs suffix (if present) indicates the video duration in seconds.
Only MP4 files are processed.
"""

import os
import re
from collections import defaultdict


def parse_filename(filename):
    """
    Parse a filename to extract its base group identifier.
    Examples:
    - 250420_121919_242_3623_37.mp4 -> 250420_121919_242_3623
    - 250420_121919_242_3623_37_24s.mp4 -> 250420_121919_242_3623
    """
    # Pattern matches: DATE_TIME_XXX_YYYY(_ZZ)(_XXs).mp4
    # For example: 250420_121919_242_3623_37.mp4 -> base_id is 250420_121919_242_3623
    # Or: 250420_121919_242_3623_37_24s.mp4 -> base_id is 250420_121919_242_3623
    # Only match MP4 files
    pattern = r'^(\d+_\d+_\d+_\d+)(?:_\d+)?(?:_\d+s)?\.mp4$'
    match = re.match(pattern, filename)
    if match:
        base_id = match.group(1)
        return base_id
    return None


def group_files(file_paths):
    """
    Group files based on their base identifier.
    Returns a dictionary where keys are base identifiers and values are lists of file paths.
    """
    groups = defaultdict(list)

    for file_path in file_paths:
        if not os.path.isfile(file_path):
            continue

        filename = os.path.basename(file_path)
        base_id = parse_filename(filename)

        if base_id:
            groups[base_id].append(file_path)

    return groups


def get_largest_file(file_paths):
    """
    Return the path to the largest file in the given list.
    """
    if not file_paths:
        return None

    return max(file_paths, key=os.path.getsize)


def ask_confirmation(prompt, default="y"):
    """
    Ask for user confirmation with a default value.
    """
    if default.lower() == "y":
        prompt = f"{prompt} [Y/n]: "
    else:
        prompt = f"{prompt} [y/N]: "

    response = input(prompt).strip().lower()

    if not response:
        return default.lower() == "y"

    return response.startswith("y")


def process_duplicates(directory="."):
    """
    Process files in the specified directory:
    1. Group them based on filename patterns
    2. For each group with multiple files, identify duplicates
    3. Delete smaller duplicates upon confirmation
    """
    # Get all MP4 files in the directory
    file_paths = []
    for file in os.listdir(directory):
        # Skip script files
        if file.startswith("framepack_"):
            continue

        # Only process MP4 files
        if not file.lower().endswith('.mp4'):
            continue

        full_path = os.path.join(directory, file)
        if os.path.isfile(full_path):
            file_paths.append(full_path)

    # Check if we found any files to process
    if not file_paths:
        print("No MP4 files found to process in the current directory.")
        return 0

    # Group the files
    groups = group_files(file_paths)

    # Check if we have any groups after parsing
    if not groups:
        print("No valid file groups found. Check if filenames match the expected pattern.")
        return 0

    # Find groups with multiple files (duplicates)
    duplicate_groups = {base_id: files for base_id, files in groups.items() if len(files) > 1}

    if not duplicate_groups:
        print("No duplicate files found.")
        return 0

    # Process each group with duplicates
    deleted_files = []
    skipped_groups = []

    print(f"\nFound {len(duplicate_groups)} groups with duplicate files:")

    for base_id, files in duplicate_groups.items():
        print(f"\nGroup: {base_id}")

        # Sort files by size (largest first)
        sorted_files = sorted(files, key=os.path.getsize, reverse=True)

        # Display files with their sizes and duration if available
        for i, file_path in enumerate(sorted_files):
            size = os.path.getsize(file_path)
            filename = os.path.basename(file_path)

            # Extract duration if present in filename
            duration_match = re.search(r'_(\d+)s\.mp4$', filename)
            duration_info = f" ({duration_match.group(1)}s)" if duration_match else ""

            status = "KEEP (largest)" if i == 0 else "DELETE (smaller)"
            print(f"  {i+1}. {filename} - {size} bytes{duration_info} - {status}")

        # Ask for confirmation to delete smaller files
        if ask_confirmation(f"Delete {len(sorted_files)-1} smaller files in this group?"):
            # Keep the largest file, delete the rest
            for file_path in sorted_files[1:]:
                try:
                    os.remove(file_path)
                    deleted_files.append(os.path.basename(file_path))
                    print(f"  Deleted: {os.path.basename(file_path)}")
                except Exception as e:
                    print(f"  Error deleting {os.path.basename(file_path)}: {str(e)}")
        else:
            skipped_groups.append(base_id)
            print("  Skipped: No files deleted in this group")

    # Print summary
    print("\n--- Summary ---")
    print(f"Processed {len(duplicate_groups)} groups with duplicates")
    print(f"Deleted {len(deleted_files)} duplicate files")

    if skipped_groups:
        print(f"Skipped {len(skipped_groups)} groups")

    return len(deleted_files)


def main():
    """
    Main function to process files in the current directory.
    """
    print("Framepack Sorted Deduper")
    print("=======================")
    print("Scanning for duplicate MP4 files...")

    try:
        # Get the current directory
        current_dir = os.getcwd()
        print(f"Working in: {current_dir}")

        # Process duplicates in the current directory
        num_deleted = process_duplicates(current_dir)

        if num_deleted > 0:
            print("\nOperation completed successfully!")
        else:
            print("\nNo files were deleted.")
    except Exception as e:
        print(f"\nError: {str(e)}")

    # Keep console window open
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
