#!/usr/bin/env python3
"""
FramePack Sync Tool
Syncs files from A:/AI/FramePack and A:/AI/FramePack/utils to B:/AI/FramePack
Only copies files that are different or older than the source.
"""

import os
import shutil
import hashlib
import sys
from pathlib import Path
from datetime import datetime

def calculate_file_hash(file_path):
    """Calculate MD5 hash of a file for comparison."""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"Error calculating hash for {file_path}: {e}")
        return None

def should_copy_file(source_path, dest_path):
    """Determine if a file should be copied based on existence, modification time, and content."""
    # Skip framepack_default_settings.json
    if source_path.name == "framepack_default_settings.json":
        return False, "Excluded file (framepack_default_settings.json)"

    # Skip .crdownload files (Chrome partial downloads)
    if source_path.suffix.lower() == ".crdownload":
        return False, "Excluded file (.crdownload)"

    if not dest_path.exists():
        return True, "File doesn't exist at destination"

    # Compare modification times
    source_mtime = source_path.stat().st_mtime
    dest_mtime = dest_path.stat().st_mtime

    if source_mtime > dest_mtime:
        return True, "Source file is newer"

    # If destination is newer, skip without hash calculation to avoid network activity
    if dest_mtime > source_mtime:
        return False, "Destination file is newer"

    # If times are exactly the same, compare file sizes first
    # This avoids expensive hash calculation for identical files
    source_size = source_path.stat().st_size
    dest_size = dest_path.stat().st_size

    if source_size != dest_size:
        return True, "File size is different"

    # Only if times AND sizes are the same, assume files are identical
    # Skip hash calculation to avoid network activity for large files
    return False, "File is up to date (same time and size)"

def sync_directory(source_dir, dest_dir, relative_path=""):
    """Sync files from source directory to destination directory."""
    source_path = Path(source_dir)
    dest_path = Path(dest_dir)
    
    if not source_path.exists():
        print(f"Source directory does not exist: {source_path}")
        return 0, 0
    
    # Create destination directory if it doesn't exist
    dest_path.mkdir(parents=True, exist_ok=True)
    
    copied_count = 0
    skipped_count = 0
    
    print(f"\nSyncing: {source_path} -> {dest_path}")
    print("-" * 60)
    
    # Process all files in the source directory
    for item in source_path.rglob('*'):
        if item.is_file():
            # Calculate relative path from source root
            rel_path = item.relative_to(source_path)
            dest_file = dest_path / rel_path

            should_copy, reason = should_copy_file(item, dest_file)

            if should_copy:
                try:
                    # Create parent directories only when we actually need to copy
                    dest_file.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, dest_file)
                    print(f"✓ Copied: {rel_path} ({reason})")
                    copied_count += 1
                except Exception as e:
                    print(f"✗ Error copying {rel_path}: {e}")
            else:
                print(f"- Skipped: {rel_path} ({reason})")
                skipped_count += 1
    
    return copied_count, skipped_count

def main():
    """Main sync function."""
    print("FramePack Sync Tool")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Define source and destination paths
    source_base = Path("A:/AI/FramePack")
    dest_base = Path("B:/AI/FramePack")
    
    total_copied = 0
    total_skipped = 0
    
    # Sync main FramePack directory (files only, not subdirectories except utils)
    if source_base.exists():
        print(f"\nSyncing main directory files...")
        for item in source_base.iterdir():
            if item.is_file():
                dest_file = dest_base / item.name
                dest_base.mkdir(parents=True, exist_ok=True)
                
                should_copy, reason = should_copy_file(item, dest_file)
                
                if should_copy:
                    try:
                        shutil.copy2(item, dest_file)
                        print(f"✓ Copied: {item.name} ({reason})")
                        total_copied += 1
                    except Exception as e:
                        print(f"✗ Error copying {item.name}: {e}")
                else:
                    print(f"- Skipped: {item.name} ({reason})")
                    total_skipped += 1
    else:
        print(f"Source directory does not exist: {source_base}")
        return
    
    # Sync utils subdirectory
    utils_source = source_base / "utils"
    utils_dest = dest_base / "utils"

    if utils_source.exists():
        copied, skipped = sync_directory(utils_source, utils_dest)
        total_copied += copied
        total_skipped += skipped
    else:
        print(f"Utils directory does not exist: {utils_source}")

    # Sync lora subdirectory
    lora_source = source_base / "lora"
    lora_dest = dest_base / "lora"

    if lora_source.exists():
        copied, skipped = sync_directory(lora_source, lora_dest)
        total_copied += copied
        total_skipped += skipped
    else:
        print(f"Lora directory does not exist: {lora_source}")

    # Sync fp-files subdirectory
    fp_files_source = source_base / "A:\AI\FP-Files"
    fp_files_dest = dest_base / "B:\AI\FP-Files"

    if fp_files_source.exists():
        copied, skipped = sync_directory(fp_files_source, fp_files_dest)
        total_copied += copied
        total_skipped += skipped
    else:
        print(f"FP-files directory does not exist: {fp_files_source}")

    # Summary
    print("\n" + "=" * 50)
    print("SYNC SUMMARY")
    print("=" * 50)
    print(f"Files copied: {total_copied}")
    print(f"Files skipped: {total_skipped}")
    print(f"Total files processed: {total_copied + total_skipped}")
    print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if len(sys.argv) == 1:  # No command line arguments, wait for user input
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
