"""
Generate GIF latent previews for better compatibility with Tkinter.
This script creates animated GIFs from latent preview frames for better compatibility.
"""

import os
import sys
import glob
import datetime
from PIL import Image, ImageDraw, ImageFont

def ensure_directory(directory):
    """Ensure the directory exists, creating it if necessary"""
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)
    return directory

def generate_gif_from_frames(frame_pattern, output_path, fps=10, loop=0):
    """Generate an animated GIF from a sequence of frames
    
    Args:
        frame_pattern: Glob pattern to match frame files (e.g., "temp/latent_frame_*.png")
        output_path: Path to save the output GIF
        fps: Frames per second (default: 10)
        loop: Number of times to loop (0 = infinite)
    
    Returns:
        Path to the generated GIF
    """
    print(f"Generating GIF from frames matching {frame_pattern}...")
    
    # Find all frames matching the pattern
    frames = sorted(glob.glob(frame_pattern))
    
    if not frames:
        print(f"No frames found matching pattern: {frame_pattern}")
        return None
    
    print(f"Found {len(frames)} frames")
    
    # Calculate duration in milliseconds (1000 / fps)
    duration = int(1000 / fps)
    
    # Load all frames
    images = []
    for frame_path in frames:
        try:
            img = Image.open(frame_path)
            images.append(img.copy())  # Make a copy to avoid reference issues
        except Exception as e:
            print(f"Error loading frame {frame_path}: {e}")
    
    if not images:
        print("No valid frames loaded")
        return None
    
    # Save as animated GIF
    try:
        # Save the first frame
        images[0].save(
            output_path,
            save_all=True,
            append_images=images[1:],
            optimize=False,
            duration=duration,
            loop=loop
        )
        print(f"GIF created successfully: {output_path}")
        return output_path
    except Exception as e:
        print(f"Error creating GIF: {e}")
        return None

def convert_mp4_to_gif(input_path, output_path=None, fps=10):
    """Convert an MP4 latent preview to GIF format for better compatibility
    
    Args:
        input_path: Path to the input MP4 file
        output_path: Path to save the output GIF (default: same as input with .gif extension)
        fps: Frames per second (default: 10)
    
    Returns:
        Path to the generated GIF
    """
    if not os.path.exists(input_path):
        print(f"Input file not found: {input_path}")
        return None
    
    # If output_path is not specified, use the same name with .gif extension
    if output_path is None:
        output_path = os.path.splitext(input_path)[0] + ".gif"
    
    print(f"Converting {input_path} to GIF...")
    
    # Create temp directory for frames
    temp_dir = ensure_directory("temp")
    
    # Extract frames using ffmpeg
    import subprocess
    
    # Build the ffmpeg command to extract frames
    ffmpeg_cmd = [
        "ffmpeg",
        "-y",  # Overwrite output files if they exist
        "-i", input_path,  # Input file
        "-vf", "fps=10",  # Extract at 10 fps
        os.path.join(temp_dir, "frame_%04d.png")  # Output pattern
    ]
    
    try:
        # Run ffmpeg
        print(f"Extracting frames with command: {' '.join(ffmpeg_cmd)}")
        subprocess.run(ffmpeg_cmd, check=True)
        
        # Generate GIF from extracted frames
        frame_pattern = os.path.join(temp_dir, "frame_*.png")
        return generate_gif_from_frames(frame_pattern, output_path, fps)
    except Exception as e:
        print(f"Error converting MP4 to GIF: {e}")
        return None

def convert_all_latent_previews():
    """Convert all MP4 latent previews to GIF format"""
    print("Converting all latent previews to GIF format...")
    
    # Ensure the latent_previews directory exists
    latent_dir = ensure_directory("latent_previews")
    
    # Find all MP4 latent previews
    mp4_files = glob.glob(os.path.join(latent_dir, "*latent_preview_*.mp4"))
    
    if not mp4_files:
        print("No latent preview MP4 files found")
        return
    
    print(f"Found {len(mp4_files)} latent preview MP4 files")
    
    # Convert each MP4 to GIF
    for mp4_file in mp4_files:
        gif_file = os.path.splitext(mp4_file)[0] + ".gif"
        
        # Skip if GIF already exists
        if os.path.exists(gif_file):
            print(f"GIF already exists: {gif_file}")
            continue
        
        # Convert MP4 to GIF
        convert_mp4_to_gif(mp4_file, gif_file)
    
    # Create a copy of the latest GIF as latest_latent_preview.gif
    gif_files = glob.glob(os.path.join(latent_dir, "*latent_preview_*.gif"))
    if gif_files:
        # Sort by modification time (newest first)
        gif_files.sort(key=os.path.getmtime, reverse=True)
        latest_gif = gif_files[0]
        
        # Copy to latest_latent_preview.gif
        latest_path = os.path.join(latent_dir, "latest_latent_preview.gif")
        try:
            import shutil
            shutil.copy2(latest_gif, latest_path)
            print(f"Copied to latest_latent_preview.gif")
        except Exception as e:
            print(f"Error copying to latest_latent_preview.gif: {e}")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Generate GIF latent previews for better compatibility with Tkinter")
    parser.add_argument("--input", help="Input MP4 file to convert to GIF")
    parser.add_argument("--output", help="Output GIF file (default: same as input with .gif extension)")
    parser.add_argument("--fps", type=int, default=10, help="Frames per second (default: 10)")
    parser.add_argument("--all", action="store_true", help="Convert all MP4 latent previews to GIF")
    
    args = parser.parse_args()
    
    if args.all:
        convert_all_latent_previews()
    elif args.input:
        convert_mp4_to_gif(args.input, args.output, args.fps)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
