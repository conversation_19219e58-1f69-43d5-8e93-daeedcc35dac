@echo off
REM Generate sample preview videos for testing the dual video preview system in FramePack

echo Generating sample preview videos for testing...

REM Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is not installed or not in the PATH.
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Check if ffmpeg is installed
ffmpeg -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: ffmpeg is not installed or not in the PATH.
    echo Please install ffmpeg and try again.
    echo You can download ffmpeg from https://ffmpeg.org/download.html
    pause
    exit /b 1
)

REM Install Pillow if not already installed
echo Checking for Pillow...
python -c "import PIL" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Installing Pillow...
    pip install pillow
    if %ERRORLEVEL% NEQ 0 (
        echo Error: Failed to install Pillow.
        echo Please try installing it manually: pip install pillow
        pause
        exit /b 1
    )
    echo Pillow installed successfully.
) else (
    echo Pillow is already installed.
)

REM Create latent_previews directory if it doesn't exist
if not exist "latent_previews" (
    echo Creating latent_previews directory...
    mkdir latent_previews
)

REM Create outputs directory if it doesn't exist
if not exist "outputs" (
    echo Creating outputs directory...
    mkdir outputs
)

REM Create temp directory if it doesn't exist
if not exist "temp" (
    echo Creating temp directory...
    mkdir temp
)

REM Run the generator script
echo Running generate_sample_previews.py...
python generate_sample_previews.py

echo Sample preview videos generated successfully.
echo You can now run test_video_previews.py to test the video preview functionality.
pause
