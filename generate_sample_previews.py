"""
Generate sample preview videos for testing the dual video preview system in FramePack.
This script creates sample latent preview and output videos to test the video preview functionality.
"""

import os
import sys
import time
import datetime
import subprocess
import numpy as np
from PIL import Image, ImageDraw, ImageFont

def ensure_directory(directory):
    """Ensure the directory exists, creating it if necessary"""
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)
    return directory

def generate_frame(frame_number, width, height, is_latent=True):
    """Generate a single frame with frame number and timestamp"""
    # Create a new image with a gradient background
    if is_latent:
        # For latent preview, use a blue gradient
        r = int(64 + (frame_number % 10) * 5)
        g = int(64 + (frame_number % 15) * 5)
        b = int(128 + (frame_number % 20) * 5)
        color = (r, g, b)
        bg_color = (20, 20, 40)
    else:
        # For output preview, use a green gradient
        r = int(64 + (frame_number % 20) * 5)
        g = int(128 + (frame_number % 15) * 5)
        b = int(64 + (frame_number % 10) * 5)
        color = (r, g, b)
        bg_color = (20, 40, 20)

    # Create the image
    image = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(image)

    # Draw a rectangle with the gradient color
    draw.rectangle([(10, 10), (width-10, height-10)], fill=color)

    # Add text with frame number and timestamp
    try:
        # Try to use a TrueType font if available
        font = ImageFont.truetype("arial.ttf", 14)
    except IOError:
        # Fall back to default font
        font = ImageFont.load_default()

    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    if is_latent:
        text = f"Latent Preview\nFrame: {frame_number}\nTime: {timestamp}"
    else:
        text = f"Output Preview\nFrame: {frame_number}\nTime: {timestamp}"

    # Draw text with a shadow for better visibility
    draw.text((width//2-50+1, height//2-20+1), text, fill=(0, 0, 0), font=font)
    draw.text((width//2-50, height//2-20), text, fill=(255, 255, 255), font=font)

    return image

def generate_latent_preview(num_frames=30, fps=10):
    """Generate a sample latent preview video"""
    print("Generating sample latent preview video...")

    # Ensure the latent_previews directory exists
    latent_dir = ensure_directory("latent_previews")
    temp_dir = ensure_directory("temp")

    # Create a timestamp for the filename
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

    # Define the frame size (small for latent preview)
    width, height = 64, 96

    # Generate frames
    frames = []
    frame_paths = []

    for i in range(num_frames):
        # Generate the frame
        frame = generate_frame(i, width, height, is_latent=True)
        frames.append(frame)

        # Save the frame to the temp directory
        frame_path = os.path.join(temp_dir, f"latent_frame_{i:04d}.png")
        frame.save(frame_path)
        frame_paths.append(frame_path)

        # Print progress
        sys.stdout.write(f"\rGenerating frames: {i+1}/{num_frames}")
        sys.stdout.flush()

    print("\nFrames generated successfully.")

    # Create the video using ffmpeg
    output_path = os.path.join(latent_dir, f"latent_preview_{timestamp}.mp4")

    # Build the ffmpeg command - using libx264 for better compatibility with TkVideoPlayer
    ffmpeg_cmd = [
        "ffmpeg",
        "-y",  # Overwrite output file if it exists
        "-framerate", str(fps),  # Set the frame rate
        "-i", os.path.join(temp_dir, "latent_frame_%04d.png"),  # Input pattern
        "-c:v", "libx264",  # Use H.264 codec for better compatibility
        "-preset", "ultrafast",  # Use ultrafast preset for quick encoding
        "-tune", "animation",  # Optimize for animation content
        "-crf", "18",  # High quality
        "-pix_fmt", "yuv420p",  # Pixel format for compatibility
        "-vf", "scale=128:192",  # Scale up for better visibility (2x original size)
        output_path  # Output file
    ]

    # Run ffmpeg
    print(f"Creating video with command: {' '.join(ffmpeg_cmd)}")
    subprocess.run(ffmpeg_cmd, check=True)

    print(f"Latent preview video created at: {output_path}")

    # Create a copy as latest_latent_preview.mp4
    latest_path = os.path.join(latent_dir, "latest_latent_preview.mp4")
    try:
        import shutil
        shutil.copy2(output_path, latest_path)
        print(f"Copied to latest_latent_preview.mp4")
    except Exception as e:
        print(f"Error copying to latest_latent_preview.mp4: {e}")

    return output_path

def generate_output_video(num_frames=60, fps=30):
    """Generate a sample output video"""
    print("Generating sample output video...")

    # Ensure the outputs directory exists
    output_dir = ensure_directory("outputs")
    temp_dir = ensure_directory("temp")

    # Create a timestamp for the filename
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

    # Define the frame size (larger for output video)
    width, height = 320, 480

    # Generate frames
    frames = []
    frame_paths = []

    for i in range(num_frames):
        # Generate the frame
        frame = generate_frame(i, width, height, is_latent=False)
        frames.append(frame)

        # Save the frame to the temp directory
        frame_path = os.path.join(temp_dir, f"output_frame_{i:04d}.png")
        frame.save(frame_path)
        frame_paths.append(frame_path)

        # Print progress
        sys.stdout.write(f"\rGenerating frames: {i+1}/{num_frames}")
        sys.stdout.flush()

    print("\nFrames generated successfully.")

    # Create the video using ffmpeg
    output_path = os.path.join(output_dir, f"sample_output_{timestamp}.mp4")

    # Build the ffmpeg command
    ffmpeg_cmd = [
        "ffmpeg",
        "-y",  # Overwrite output file if it exists
        "-framerate", str(fps),  # Set the frame rate
        "-i", os.path.join(temp_dir, "output_frame_%04d.png"),  # Input pattern
        "-c:v", "libx264",  # Use H.264 codec
        "-crf", "18",  # High quality
        "-pix_fmt", "yuv420p",  # Pixel format for compatibility
        output_path  # Output file
    ]

    # Run ffmpeg
    print(f"Creating video with command: {' '.join(ffmpeg_cmd)}")
    subprocess.run(ffmpeg_cmd, check=True)

    print(f"Output video created at: {output_path}")

    return output_path

def main():
    """Main function to generate sample preview videos"""
    print("Generating sample preview videos for testing...")

    # Generate a latent preview video
    latent_preview = generate_latent_preview(num_frames=30, fps=10)

    # Generate an output video
    output_video = generate_output_video(num_frames=60, fps=30)

    print("\nSample preview videos generated successfully.")
    print(f"Latent preview: {latent_preview}")
    print(f"Output video: {output_video}")

    print("\nYou can now run test_video_previews.py to test the video preview functionality.")

if __name__ == "__main__":
    main()
