#!/usr/bin/env python
"""
Image Background Remover

Removes backgrounds from images using rembg AI model.
Supports drag-and-drop and "Send To" functionality.

Usage:
    python image_blank.py [image_file1] [image_file2] ...

    If no files are provided, the script will prompt for input.
"""
import os
import sys
from pathlib import Path
from PIL import Image

# Try to import rembg for actual background removal
try:
    from rembg import remove, new_session
    REMBG_AVAILABLE = True
    print("rembg library loaded successfully. Background removal is enabled.")
except ImportError:
    REMBG_AVAILABLE = False
    print("rembg library not available. Install with: pip install rembg")
    print("Falling back to transparent image creation.")


def create_transparent_image(input_path):
    """
    Remove background from image using rembg AI model, or create transparent image as fallback.

    Args:
        input_path: Path to the input image file

    Returns:
        Path to the created image with removed background
    """
    try:
        path = Path(input_path)

        if REMBG_AVAILABLE:
            # Use rembg to remove background
            print(f"Removing background from {input_path} using rembg...")

            # Open the input image
            with Image.open(input_path) as input_img:
                # Convert to RGB if necessary (rembg works better with RGB)
                if input_img.mode != 'RGB':
                    input_img = input_img.convert('RGB')

                # Remove background using rembg
                output_img = remove(input_img)

                # Determine output path
                output_path = path.parent / f"{path.stem}_nobg.png"

                # Save the image with removed background
                output_img.save(output_path, format='PNG')

                print(f"Background removed successfully: {output_path}")
                return output_path
        else:
            # Fallback: Create a transparent image with the same dimensions
            print(f"Creating transparent image for {input_path} (rembg not available)...")

            # Open the input image to get its dimensions
            with Image.open(input_path) as img:
                width, height = img.size

            # Create a new transparent image with the same dimensions
            transparent_img = Image.new('RGBA', (width, height), (0, 0, 0, 0))

            # Determine output path
            output_path = path.parent / f"{path.stem}_transparent.png"

            # Save the transparent image
            transparent_img.save(output_path, format='PNG')

            print(f"Created transparent image: {output_path}")
            return output_path

    except Exception as e:
        print(f"Error processing {input_path}: {str(e)}")
        return None


def main():
    # Check if any files were provided
    if len(sys.argv) < 2:
        print("No files provided.")
        if REMBG_AVAILABLE:
            print("Usage: python image_blank.py image1.png image2.jpg ...")
            print("This will remove backgrounds from the images using AI.")
        else:
            print("Usage: python image_blank.py image1.png image2.jpg ...")
            print("This will create transparent versions of the images.")
            print("For AI background removal, install rembg: pip install rembg")

        # Prompt for file input
        input_file = input("Enter the path to an image file (or press Enter to exit): ").strip()
        if not input_file:
            return

        # Process the single file
        if os.path.isfile(input_file):
            create_transparent_image(input_file)
        else:
            print(f"File not found: {input_file}")

        input("Press Enter to exit...")
        return

    # Process all provided files
    processed_files = []
    for image_path in sys.argv[1:]:
        # Remove quotes if present (common when dragging files)
        image_path = image_path.strip('"\'')

        # Check if file exists
        if not os.path.isfile(image_path):
            print(f"File not found: {image_path}")
            continue

        # Process the image
        output_path = create_transparent_image(image_path)
        if output_path:
            processed_files.append(output_path)

    # Summary
    if processed_files:
        if REMBG_AVAILABLE:
            print(f"\nSuccessfully removed backgrounds from {len(processed_files)} image(s).")
        else:
            print(f"\nSuccessfully created {len(processed_files)} transparent image(s).")
    else:
        print("\nNo images were processed.")

    # If run from command line, wait for user input before closing
    if len(sys.argv) < 2:
        input("Press Enter to exit...")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Unhandled error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
