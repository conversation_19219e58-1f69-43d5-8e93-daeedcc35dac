#!/usr/bin/env python
"""
Image Crop Test App

A GUI application for testing the image cropping functionality from FramePack.
Allows users to load images via dialog or drag-and-drop, adjust cropping settings,
and preview the results in real-time.

Features:
- Load images via file dialog or drag-and-drop
- All FramePack cropping controls (fill percentage, padding, skip options)
- Real-time preview of original and cropped images
- Apply button to test cropping with current settings
- Status indicators for multi-face and no-face conditions
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
from PIL import Image, ImageTk
import threading
import cv2

# Try to import TkinterDnD for drag and drop support
try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    TKDND_AVAILABLE = True
except ImportError:
    TKDND_AVAILABLE = False
    print("TkinterDnD not available - drag and drop disabled")

class ImageCropTestApp:
    def __init__(self):
        # Initialize the main window
        if TKDND_AVAILABLE:
            self.root = TkinterDnD.Tk()
        else:
            self.root = tk.Tk()
        
        self.root.title("Image Crop Test App")
        self.root.geometry("1200x800")
        
        # Initialize variables
        self.current_image_path = None
        self.original_image = None
        self.cropped_image = None
        
        # Face cropping settings (matching FramePack GUI)
        self.face_fill_percentage = tk.IntVar(value=60)  # Face fill percentage (10-95, default: 60)
        self.face_padding_pixels = tk.IntVar(value=64)  # Padding pixels to add before cropping (default: 64)
        self.face_padding_percent = tk.IntVar(value=25)  # Padding percentage (1-100, default: 25)
        self.face_padding_side = tk.StringVar(value="Top")  # Which side(s) to add padding to (default: Top)
        self.face_padding_enabled = tk.BooleanVar(value=True)  # Enable/disable padding feature
        self.face_padding_mode = tk.StringVar(value="Pixels")  # "Pixels" or "Percent"
        
        # Skip options for face cropping
        self.skip_no_face = tk.BooleanVar(value=False)  # Skip generation if no face is found
        self.skip_multiple_faces = tk.BooleanVar(value=False)  # Skip entire image when multiple faces are detected
        self.no_crop_multiple_faces = tk.BooleanVar(value=False)  # Skip cropping but process original image when multiple faces are detected
        
        # Status variables
        self.crop_status = tk.StringVar(value="No image loaded")
        self.face_count = tk.IntVar(value=0)
        self.detection_method = tk.StringVar(value="None")
        
        self.setup_ui()
        
        # Add drag and drop support if available
        if TKDND_AVAILABLE:
            self.root.drop_target_register(DND_FILES)
            self.root.dnd_bind('<<Drop>>', self.on_drop)

    def on_padding_mode_change(self, event):
        """Handle padding mode change between Pixels and Percent"""
        mode = self.face_padding_mode.get()
        if mode == "Pixels":
            self.pixels_frame.pack(fill=tk.X, pady=2)
            self.percent_frame.pack_forget()
        else:  # Percent
            self.percent_frame.pack(fill=tk.X, pady=2)
            self.pixels_frame.pack_forget()

    def calculate_percentage_padding(self, image_size, face_bbox, face_center, padding_percent, padding_side):
        """
        Calculate padding in pixels based on percentage of available space.

        Args:
            image_size: (width, height) of the image
            face_bbox: (x, y, width, height) of the detected face
            face_center: (center_x, center_y) of the face
            padding_percent: Percentage of available space to use as padding
            padding_side: Which side(s) to apply padding to

        Returns:
            Padding in pixels
        """
        img_width, img_height = image_size
        face_x, face_y, face_width, face_height = face_bbox
        face_center_x, face_center_y = face_center

        # Calculate available space in each direction from face center
        available_left = face_center_x
        available_right = img_width - face_center_x
        available_top = face_center_y
        available_bottom = img_height - face_center_y

        # Calculate padding based on the selected side
        if padding_side == "Top":
            available_space = available_top
        elif padding_side == "Bottom":
            available_space = available_bottom
        elif padding_side == "Left":
            available_space = available_left
        elif padding_side == "Right":
            available_space = available_right
        elif padding_side == "Top and Bottom":
            available_space = min(available_top, available_bottom)
        elif padding_side == "Left and Right":
            available_space = min(available_left, available_right)
        else:
            available_space = min(available_top, available_bottom, available_left, available_right)

        # Calculate padding as percentage of available space
        padding_pixels = int((padding_percent / 100.0) * available_space)

        print(f"Percentage padding calculation:")
        print(f"  Available space in {padding_side.lower()} direction: {available_space}px")
        print(f"  {padding_percent}% of {available_space}px = {padding_pixels}px")

        return padding_pixels
    
    def setup_ui(self):
        """Set up the user interface"""
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create three-column layout
        # Left column: Controls
        controls_frame = ttk.LabelFrame(main_frame, text="Cropping Controls", padding="10")
        controls_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # Middle column: Original image preview
        original_frame = ttk.LabelFrame(main_frame, text="Original Image", padding="10")
        original_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Right column: Cropped image preview
        cropped_frame = ttk.LabelFrame(main_frame, text="Cropped Image", padding="10")
        cropped_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.setup_controls(controls_frame)
        self.setup_image_previews(original_frame, cropped_frame)
    
    def setup_controls(self, parent):
        """Set up the cropping controls"""
        # Load Image section
        load_section = ttk.LabelFrame(parent, text="Load Image")
        load_section.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(load_section, text="Browse for Image...", command=self.browse_image).pack(fill=tk.X, pady=2)
        
        if TKDND_AVAILABLE:
            ttk.Label(load_section, text="or drag & drop image here", font=("", 8)).pack(pady=2)
        
        # Face Fill Percentage
        fill_frame = ttk.Frame(parent)
        fill_frame.pack(fill=tk.X, pady=2)
        ttk.Label(fill_frame, text="Face Fill %:").pack(side=tk.LEFT)
        ttk.Spinbox(fill_frame, from_=10, to=95, textvariable=self.face_fill_percentage, width=8).pack(side=tk.RIGHT)
        
        # Padding controls
        padding_section = ttk.LabelFrame(parent, text="Padding Settings")
        padding_section.pack(fill=tk.X, pady=(10, 0))

        # Padding enable checkbox
        ttk.Checkbutton(padding_section, text="Enable Padding", variable=self.face_padding_enabled).pack(anchor=tk.W, pady=2)

        # Padding mode selection
        mode_frame = ttk.Frame(padding_section)
        mode_frame.pack(fill=tk.X, pady=2)
        ttk.Label(mode_frame, text="Mode:").pack(side=tk.LEFT)
        mode_combo = ttk.Combobox(mode_frame, textvariable=self.face_padding_mode, width=10, state="readonly")
        mode_combo['values'] = ("Pixels", "Percent")
        mode_combo.pack(side=tk.RIGHT)
        mode_combo.bind('<<ComboboxSelected>>', self.on_padding_mode_change)

        # Padding pixels
        self.pixels_frame = ttk.Frame(padding_section)
        self.pixels_frame.pack(fill=tk.X, pady=2)
        ttk.Label(self.pixels_frame, text="Pixels:").pack(side=tk.LEFT)
        ttk.Spinbox(self.pixels_frame, from_=0, to=500, textvariable=self.face_padding_pixels, width=8).pack(side=tk.RIGHT)

        # Padding percent
        self.percent_frame = ttk.Frame(padding_section)
        self.percent_frame.pack(fill=tk.X, pady=2)
        ttk.Label(self.percent_frame, text="Percent:").pack(side=tk.LEFT)
        ttk.Spinbox(self.percent_frame, from_=1, to=100, textvariable=self.face_padding_percent, width=8).pack(side=tk.RIGHT)

        # Padding side
        side_frame = ttk.Frame(padding_section)
        side_frame.pack(fill=tk.X, pady=2)
        ttk.Label(side_frame, text="Side:").pack(side=tk.LEFT)
        side_combo = ttk.Combobox(side_frame, textvariable=self.face_padding_side, width=15, state="readonly")
        side_combo['values'] = ("Top", "Bottom", "Left", "Right", "Top and Bottom", "Left and Right")
        side_combo.pack(side=tk.RIGHT)

        # Initially show pixels mode
        self.on_padding_mode_change(None)
        
        # Skip options
        skip_section = ttk.LabelFrame(parent, text="Skip Options")
        skip_section.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Checkbutton(skip_section, text="Skip no face", variable=self.skip_no_face).pack(anchor=tk.W, pady=1)
        ttk.Checkbutton(skip_section, text="Skip multi-face", variable=self.skip_multiple_faces).pack(anchor=tk.W, pady=1)
        ttk.Checkbutton(skip_section, text="No crop multi-face", variable=self.no_crop_multiple_faces).pack(anchor=tk.W, pady=1)
        
        # Apply button
        ttk.Button(parent, text="Apply Crop", command=self.apply_crop, style="Accent.TButton").pack(fill=tk.X, pady=(20, 10))
        
        # Status section
        status_section = ttk.LabelFrame(parent, text="Status")
        status_section.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(status_section, textvariable=self.crop_status, wraplength=200).pack(pady=2)
        
        # Face detection info
        info_frame = ttk.Frame(status_section)
        info_frame.pack(fill=tk.X, pady=2)
        ttk.Label(info_frame, text="Faces:").pack(side=tk.LEFT)
        ttk.Label(info_frame, textvariable=self.face_count).pack(side=tk.RIGHT)
        
        method_frame = ttk.Frame(status_section)
        method_frame.pack(fill=tk.X, pady=2)
        ttk.Label(method_frame, text="Method:").pack(side=tk.LEFT)
        ttk.Label(method_frame, textvariable=self.detection_method, wraplength=100).pack(side=tk.RIGHT)
    
    def setup_image_previews(self, original_frame, cropped_frame):
        """Set up the image preview areas"""
        # Original image preview (smaller)
        self.original_canvas = tk.Canvas(original_frame, width=300, height=300, bg="white", relief="sunken", borderwidth=1)
        self.original_canvas.pack(expand=True, fill=tk.BOTH, padx=5, pady=5)
        
        # Cropped image preview (larger)
        self.cropped_canvas = tk.Canvas(cropped_frame, width=400, height=400, bg="white", relief="sunken", borderwidth=1)
        self.cropped_canvas.pack(expand=True, fill=tk.BOTH, padx=5, pady=5)
        
        # Add labels
        ttk.Label(original_frame, text="Original (smaller preview)").pack()
        ttk.Label(cropped_frame, text="Cropped Result (larger preview)").pack()
    
    def browse_image(self):
        """Open file dialog to select an image"""
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[
                ("Image files", "*.jpg *.jpeg *.png *.bmp *.gif *.tiff *.webp"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.load_image(file_path)
    
    def on_drop(self, event):
        """Handle drag and drop events"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]
            # Check if it's an image file
            image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp')
            if file_path.lower().endswith(image_extensions):
                self.load_image(file_path)
            else:
                messagebox.showerror("Error", "Please drop an image file")
    
    def load_image(self, file_path):
        """Load an image and display it in the original preview"""
        try:
            self.current_image_path = file_path
            self.original_image = Image.open(file_path)
            
            # Display in original canvas
            self.display_image(self.original_image, self.original_canvas)
            
            # Clear cropped preview
            self.cropped_canvas.delete("all")
            self.cropped_image = None
            
            # Update status
            self.crop_status.set(f"Loaded: {os.path.basename(file_path)}")
            self.face_count.set(0)
            self.detection_method.set("None")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load image: {e}")
            self.crop_status.set("Failed to load image")
    
    def display_image(self, image, canvas):
        """Display an image in a canvas, scaled to fit"""
        if not image:
            return
        
        # Get canvas dimensions
        canvas.update_idletasks()
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            canvas_width = 300
            canvas_height = 300
        
        # Calculate scaling to fit canvas
        img_width, img_height = image.size
        scale_x = canvas_width / img_width
        scale_y = canvas_height / img_height
        scale = min(scale_x, scale_y, 1.0)  # Don't upscale
        
        # Resize image
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)
        resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Convert to PhotoImage and display
        photo = ImageTk.PhotoImage(resized_image)
        canvas.delete("all")
        canvas.create_image(canvas_width//2, canvas_height//2, image=photo, anchor=tk.CENTER)
        
        # Keep a reference to prevent garbage collection
        canvas.image = photo
    
    def apply_crop(self):
        """Apply cropping with current settings"""
        if not self.current_image_path:
            messagebox.showwarning("Warning", "Please load an image first")
            return
        
        # Run cropping in a separate thread to prevent GUI freezing
        threading.Thread(target=self._apply_crop_thread, daemon=True).start()
    
    def _apply_crop_thread(self):
        """Apply cropping in a separate thread"""
        try:
            # Update status
            self.root.after(0, lambda: self.crop_status.set("Processing..."))
            
            # Import the crop_face function with multiple fallback paths
            import importlib.util

            # Try multiple possible locations for the image_face_cropper module
            possible_paths = [
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "image_face_cropper.py"),  # Same directory as script
                os.path.join(os.getcwd(), "image_face_cropper.py"),  # Current working directory
                os.path.join(os.path.dirname(__file__), "image_face_cropper.py"),  # Relative to script
                "image_face_cropper.py"  # Just the filename
            ]

            cropper_module = None
            successful_path = None

            # Try each path until we find the module
            for path in possible_paths:
                if os.path.exists(path):
                    try:
                        spec = importlib.util.spec_from_file_location("image_face_cropper", path)
                        cropper_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(cropper_module)
                        successful_path = path
                        break
                    except Exception as e:
                        continue

            if cropper_module is None:
                # Last resort: try direct import
                try:
                    import image_face_cropper as cropper_module
                    successful_path = "direct import"
                except ImportError:
                    raise ImportError(f"image_face_cropper.py not found. Tried paths: {possible_paths}")

            print(f"Successfully loaded image_face_cropper from: {successful_path}")
            crop_face = cropper_module.crop_face
            
            # Get settings
            fill_percentage = self.face_fill_percentage.get()
            padding_side = self.face_padding_side.get()

            # Convert GUI padding side to the format expected by crop_face function
            padding_side_map = {
                "Top": "top",
                "Bottom": "bottom",
                "Left": "left",
                "Right": "right",
                "Top and Bottom": "top_bottom",
                "Left and Right": "left_right"
            }
            padding_side_param = padding_side_map.get(padding_side, "top")

            # Calculate padding based on mode
            if not self.face_padding_enabled.get():
                padding_pixels = 0
            elif self.face_padding_mode.get() == "Pixels":
                padding_pixels = self.face_padding_pixels.get()
            else:  # Percent mode
                # We need to do a preliminary face detection to get the face info for percentage calculation
                import importlib.util

                # Try multiple possible locations for the image_face_cropper module
                possible_paths = [
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), "image_face_cropper.py"),
                    os.path.join(os.getcwd(), "image_face_cropper.py"),
                    os.path.join(os.path.dirname(__file__), "image_face_cropper.py"),
                    "image_face_cropper.py"
                ]

                cropper_module = None

                # Try each path until we find the module
                for path in possible_paths:
                    if os.path.exists(path):
                        try:
                            spec = importlib.util.spec_from_file_location("image_face_cropper", path)
                            cropper_module = importlib.util.module_from_spec(spec)
                            spec.loader.exec_module(cropper_module)
                            break
                        except Exception as e:
                            continue

                if cropper_module is None:
                    # Last resort: try direct import
                    try:
                        import image_face_cropper as cropper_module
                    except ImportError:
                        raise ImportError(f"image_face_cropper.py not found for percentage padding. Tried paths: {possible_paths}")

                # Get the face detection functions
                detect_faces_mediapipe = cropper_module.detect_faces_mediapipe
                detect_faces_face_recognition = cropper_module.detect_faces_face_recognition
                detect_faces_haar_improved = cropper_module.detect_faces_haar_improved
                # Note: detect_faces_haar_original doesn't exist, using detect_faces_dnn as fallback
                detect_faces_dnn = cropper_module.detect_faces_dnn
                import cv2

                # Load image for face detection
                image = cv2.imread(self.current_image_path)
                if image is None:
                    raise Exception("Could not load image for face detection")

                # Try to detect face using the same cascade as crop_face
                faces = []
                if len(faces) == 0:
                    faces = detect_faces_mediapipe(image)
                if len(faces) == 0:
                    faces = detect_faces_face_recognition(image)
                if len(faces) == 0:
                    faces = detect_faces_haar_improved(image, 5)
                if len(faces) == 0:
                    faces = detect_faces_dnn(image)

                if len(faces) == 0:
                    raise Exception("No face detected for percentage padding calculation")

                # Use the largest face
                largest_face = max(faces, key=lambda f: f[2] * f[3])
                face_x, face_y, face_w, face_h = largest_face
                face_center_x = face_x + face_w // 2
                face_center_y = face_y + face_h // 2

                # Calculate percentage-based padding
                image_size = (image.shape[1], image.shape[0])  # (width, height)
                face_bbox = (face_x, face_y, face_w, face_h)
                face_center = (face_center_x, face_center_y)

                padding_pixels = self.calculate_percentage_padding(
                    image_size, face_bbox, face_center,
                    self.face_padding_percent.get(), padding_side
                )
            
            # Create temp output path
            temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
            os.makedirs(temp_dir, exist_ok=True)
            
            original_filename = os.path.basename(self.current_image_path)
            base_name, ext = os.path.splitext(original_filename)
            temp_output_path = os.path.join(temp_dir, f"{base_name}_test_crop{ext}")
            
            # Apply cropping
            result = crop_face(
                input_path=self.current_image_path,
                output_path=temp_output_path,
                strength=5,  # Default strength
                output_size=512,  # Default output size
                fill_percentage=fill_percentage,
                padding_pixels=padding_pixels,
                padding_side=padding_side_param,
                skip_multiple_faces=self.skip_multiple_faces.get(),
                no_crop_multiple_faces=self.no_crop_multiple_faces.get()
            )
            
            # Update UI based on result
            self.root.after(0, lambda: self._handle_crop_result(result, temp_output_path))
            
        except ImportError as e:
            error_msg = str(e)
            self.root.after(0, lambda: self.crop_status.set("Face cropper module not found"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"image_face_cropper.py not found: {error_msg}"))
        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda: self.crop_status.set(f"Error: {error_msg}"))
            self.root.after(0, lambda: messagebox.showerror("Error", f"Cropping failed: {error_msg}"))
    
    def _handle_crop_result(self, result, temp_output_path):
        """Handle the result of the cropping operation"""
        if result is False:
            # skip_multiple_faces was triggered
            self.crop_status.set("Skipped - multiple faces detected")
            self.face_count.set(0)
            self.detection_method.set("Multiple faces")
        elif isinstance(result, dict):
            # New detailed return format
            if result["status"] == "single_face_cropped":
                # Load and display the cropped image
                try:
                    self.cropped_image = Image.open(temp_output_path)
                    self.display_image(self.cropped_image, self.cropped_canvas)
                    self.crop_status.set(f"Success - cropped with {result['face_count']} face")
                    self.face_count.set(result['face_count'])
                    self.detection_method.set("Single face")
                except Exception as e:
                    self.crop_status.set(f"Error loading result: {e}")
            elif result["status"] == "multiple_faces_original":
                # Multiple faces, using original image
                try:
                    self.cropped_image = Image.open(temp_output_path)
                    self.display_image(self.cropped_image, self.cropped_canvas)
                    self.crop_status.set(f"Multiple faces ({result['face_count']}) - using original")
                    self.face_count.set(result['face_count'])
                    self.detection_method.set("Multiple faces")
                except Exception as e:
                    self.crop_status.set(f"Error loading result: {e}")
            elif result["status"] == "no_face_found":
                self.crop_status.set("No face detected")
                self.face_count.set(0)
                self.detection_method.set("No face")
        else:
            # Legacy return format or other result
            if result:
                try:
                    self.cropped_image = Image.open(temp_output_path)
                    self.display_image(self.cropped_image, self.cropped_canvas)
                    self.crop_status.set("Cropping completed")
                except Exception as e:
                    self.crop_status.set(f"Error loading result: {e}")
            else:
                self.crop_status.set("No face detected")
                self.face_count.set(0)
                self.detection_method.set("No face")

def main():
    app = ImageCropTestApp()
    app.root.mainloop()

if __name__ == "__main__":
    main()
