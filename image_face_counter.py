#!/usr/bin/env python
"""
Image Face Counter

Scans images in a folder or processes a list of image paths and categorizes them based on the number of faces detected.
Creates text files listing image paths by face count: no_faces.txt, single_faces.txt, two_faces.txt, multiple_faces.txt

Usage:
    python image_face_counter.py [folder_path]
    python image_face_counter.py --strength 10 [folder_path]
    python image_face_counter.py --list [list_file.txt]
    python image_face_counter.py --list --strength 5 [list_file.txt]

    Options:
        folder_path   Path to folder to scan (default: current directory)
        list_file.txt Path to text file containing image paths (one per line)
        --list        Enable list processing mode for text files with image paths
        --strength    Face detection strength (1-10, default: 10)
                     1-2: Very sensitive (may detect false positives)
                     3-4: Sensitive
                     5-6: Balanced
                     7-8: Strict
                     9-10: Very strict (default - only clear faces)

    Directory Mode Output:
        Output files created in the scanned folder

    List Mode Output:
        Output files created in the same directory as the input .txt file

    Output files:
        no_faces.txt        - Images with 0 faces
        single_faces.txt    - Images with exactly 1 face
        two_faces.txt       - Images with exactly 2 faces
        multiple_faces.txt  - Images with 3 or more faces
"""
import os
import sys
import argparse

# Check for required dependencies
try:
    import cv2
except ImportError as e:
    print(f"Missing required dependency: {e}")
    print("Please install required packages with:")
    print("pip install opencv-python pillow numpy")
    sys.exit(1)

def count_faces_in_image(image_path, strength=10):
    """
    Count the number of faces in an image using OpenCV face detection.

    Args:
        image_path (str): Path to the input image
        strength (int): Face detection strength (1-10). Higher values are more strict. Default: 10.

    Returns:
        int: Number of faces detected, or -1 if error occurred
    """
    try:
        # Map strength to minNeighbors parameter for face detection
        # Higher strength = higher minNeighbors = more strict detection
        strength = max(1, min(10, strength))  # Clamp to 1-10 range
        if strength <= 2:
            min_neighbors = 3
        elif strength <= 4:
            min_neighbors = 4
        elif strength <= 6:
            min_neighbors = 5
        elif strength <= 8:
            min_neighbors = 6 + (strength - 7)  # 6 or 7
        else:  # 9-10
            min_neighbors = 8 + (strength - 9)  # 8 or 9

        # Read the image
        image = cv2.imread(image_path)
        if image is None:
            print(f"Warning: Could not read image: {image_path}")
            return -1

        # Load OpenCV's pre-trained face detection model
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

        # Convert to grayscale for face detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Detect faces using the strength-based minNeighbors parameter
        faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=min_neighbors, minSize=(30, 30))

        return len(faces)

    except Exception as e:
        print(f"Error processing image {image_path}: {e}")
        return -1

def scan_folder_for_images(folder_path):
    """
    Scan a folder for supported image files.

    Args:
        folder_path (str): Path to the folder to scan

    Returns:
        list: List of image file paths
    """
    # Supported image formats based on existing apps
    supported_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.tif', '.webp')

    image_files = []

    try:
        # Use os.listdir instead of glob to avoid issues with special characters in paths
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)

            # Check if it's a file (not a directory)
            if os.path.isfile(file_path):
                # Check if the file has a supported extension (case-insensitive)
                file_lower = filename.lower()
                if any(file_lower.endswith(ext) for ext in supported_extensions):
                    image_files.append(file_path)

    except OSError as e:
        print(f"Error accessing folder {folder_path}: {e}")
        return []

    # Sort the files
    image_files.sort()

    return image_files

def read_image_list_from_file(list_file_path):
    """
    Read image file paths from a text file.

    Args:
        list_file_path (str): Path to the text file containing image paths (one per line)

    Returns:
        list: List of image file paths
    """
    image_files = []

    try:
        with open(list_file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                # Strip whitespace and skip empty lines
                image_path = line.strip()
                if not image_path:
                    continue

                # Convert to absolute path if it's relative
                if not os.path.isabs(image_path):
                    # If relative path, make it relative to the list file's directory
                    list_dir = os.path.dirname(os.path.abspath(list_file_path))
                    image_path = os.path.join(list_dir, image_path)

                # Check if the file exists
                if not os.path.exists(image_path):
                    print(f"Warning: Image file not found (line {line_num}): {image_path}")
                    continue

                # Check if it's actually a file
                if not os.path.isfile(image_path):
                    print(f"Warning: Path is not a file (line {line_num}): {image_path}")
                    continue

                # Check if it's a supported image format
                supported_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.tif', '.webp')
                file_lower = os.path.basename(image_path).lower()
                if not any(file_lower.endswith(ext) for ext in supported_extensions):
                    print(f"Warning: Unsupported file format (line {line_num}): {image_path}")
                    continue

                image_files.append(image_path)

    except FileNotFoundError:
        print(f"Error: List file not found: {list_file_path}")
        return []
    except Exception as e:
        print(f"Error reading list file {list_file_path}: {e}")
        return []

    return image_files

def write_results_to_files(folder_path, no_faces, single_faces, two_faces, multiple_faces):
    """
    Write the categorized image lists to text files.

    Args:
        folder_path (str): Path to the folder where output files will be created
        no_faces (list): List of image paths with 0 faces
        single_faces (list): List of image paths with 1 face
        two_faces (list): List of image paths with 2 faces
        multiple_faces (list): List of image paths with 3+ faces
    """
    # Define output file paths
    no_faces_file = os.path.join(folder_path, "no_faces.txt")
    single_file = os.path.join(folder_path, "single_faces.txt")
    two_file = os.path.join(folder_path, "two_faces.txt")
    multiple_file = os.path.join(folder_path, "multiple_faces.txt")

    # Write no faces
    with open(no_faces_file, 'w', encoding='utf-8') as f:
        for image_path in sorted(no_faces):
            f.write(f"{image_path}\n")

    # Write single faces
    with open(single_file, 'w', encoding='utf-8') as f:
        for image_path in sorted(single_faces):
            f.write(f"{image_path}\n")

    # Write two faces
    with open(two_file, 'w', encoding='utf-8') as f:
        for image_path in sorted(two_faces):
            f.write(f"{image_path}\n")

    # Write multiple faces
    with open(multiple_file, 'w', encoding='utf-8') as f:
        for image_path in sorted(multiple_faces):
            f.write(f"{image_path}\n")

    print(f"\nResults written to:")
    print(f"  No faces ({len(no_faces)} images): {no_faces_file}")
    print(f"  Single faces ({len(single_faces)} images): {single_file}")
    print(f"  Two faces ({len(two_faces)} images): {two_file}")
    print(f"  Multiple faces ({len(multiple_faces)} images): {multiple_file}")

def main():
    try:
        parser = argparse.ArgumentParser(description='Count faces in images and categorize them')
        parser.add_argument('input_path', nargs='?', default='.',
                          help='Folder path to scan for images or text file with image paths (default: current directory)')
        parser.add_argument('--list', action='store_true',
                          help='Enable list processing mode for text files with image paths')
        parser.add_argument('--strength', type=int, default=10, choices=range(1, 11),
                          help='Face detection strength (1-10, default: 10). Higher values are more strict.')

        args = parser.parse_args()

        input_path = args.input_path
        list_mode = args.list
        strength = args.strength

        # Auto-detect list mode if input is a .txt file
        if input_path.lower().endswith('.txt') and os.path.isfile(input_path):
            list_mode = True
            print(f"Auto-detected list mode for .txt file: {input_path}")

        if list_mode:
            # List processing mode
            if not os.path.isfile(input_path):
                print(f"Error: List file does not exist: {input_path}")
                return 1

            print(f"Processing image list from: {input_path}")
            print(f"Face detection strength: {strength}")

            # Read image files from list
            image_files = read_image_list_from_file(input_path)

            if not image_files:
                print("No valid image files found in the list.")
                return 0

            # Determine output directory from the input .txt file location
            output_folder = os.path.dirname(os.path.abspath(input_path))
            print(f"Output files will be saved to: {output_folder}")

        else:
            # Directory processing mode
            # If a file was dropped/passed, use its parent directory
            if os.path.isfile(input_path):
                folder_path = os.path.dirname(input_path)
                print(f"File detected: {os.path.basename(input_path)}")
                print(f"Using parent directory: {folder_path}")
            else:
                folder_path = input_path

            folder_path = os.path.abspath(folder_path)
            output_folder = folder_path

            # Validate folder path
            if not os.path.exists(folder_path):
                print(f"Error: Folder does not exist: {folder_path}")
                return 1

            if not os.path.isdir(folder_path):
                print(f"Error: Path is not a directory: {folder_path}")
                return 1

            print(f"Scanning folder: {folder_path}")
            print(f"Face detection strength: {strength}")

            # Find all image files
            image_files = scan_folder_for_images(folder_path)
        
        if not image_files:
            if list_mode:
                print("No valid image files found in the list.")
            else:
                print("No supported image files found in the folder.")
                print("Supported formats: .png, .jpg, .jpeg, .bmp, .gif, .tiff, .tif, .webp")
            return 0

        print(f"Found {len(image_files)} image files to process...")

        # Initialize lists for categorization
        single_faces = []
        two_faces = []
        multiple_faces = []
        no_faces = []
        errors = []

        # Process each image
        for i, image_path in enumerate(image_files, 1):
            print(f"Processing {i}/{len(image_files)}: {os.path.basename(image_path)}", end="")

            face_count = count_faces_in_image(image_path, strength)

            if face_count == -1:
                errors.append(image_path)
                print(" - ERROR")
            elif face_count == 0:
                no_faces.append(image_path)
                print(" - 0 faces")
            elif face_count == 1:
                single_faces.append(image_path)
                print(" - 1 face")
            elif face_count == 2:
                two_faces.append(image_path)
                print(" - 2 faces")
            else:
                multiple_faces.append(image_path)
                print(f" - {face_count} faces")

        # Write results to files
        write_results_to_files(output_folder, no_faces, single_faces, two_faces, multiple_faces)
        
        # Print summary
        print(f"\n=== SUMMARY ===")
        print(f"Total images processed: {len(image_files)}")
        print(f"Images with 0 faces: {len(no_faces)}")
        print(f"Images with 1 face: {len(single_faces)}")
        print(f"Images with 2 faces: {len(two_faces)}")
        print(f"Images with 3+ faces: {len(multiple_faces)}")
        if errors:
            print(f"Errors encountered: {len(errors)}")
        
        return 0

    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
