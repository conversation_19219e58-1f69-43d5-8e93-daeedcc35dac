import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from PIL import Image, ImageTk, ImageGrab
import os
import sys
import subprocess
import tempfile
import io
import win32clipboard
from tkinterdnd2 import DND_FILES, TkinterDnD
import traceback
from pathlib import Path
import math
import time
import json

class DraggableImage:
    """Represents a draggable and resizable image on the canvas"""

    def __init__(self, canvas, image_path, x, y, is_background=False):
        self.canvas = canvas
        self.image_path = image_path
        self.is_background = is_background
        self.x = x
        self.y = y
        self.scale = 1.0
        self.rotation = 0.0  # Rotation angle in degrees
        self.canvas_id = None
        self.handles = []
        self.rotation_handle = None
        self.handle_size = 8
        self.selected = False

        # Rotation tracking for relative rotation
        self.rotation_start_angle = 0.0  # Initial rotation when rotation starts
        self.rotation_start_mouse_angle = 0.0  # Initial mouse angle when rotation starts

        # Load and process the image
        self.original_image = Image.open(image_path)
        if not is_background:
            # Process with rembg for foreground images
            self.processed_image = self.remove_background()
        else:
            # Keep original for background images
            self.processed_image = self.original_image.copy()

        self.current_image = self.processed_image.copy()
        self.photo = None

        # Create the image on canvas
        self.create_canvas_image()

    def remove_background(self):
        """Remove background using rembg with improved strength control"""
        try:
            from rembg import remove, new_session
            import numpy as np

            print(f"Processing background removal for: {self.image_path}")

            # Get strength from app if available
            strength = 0.5  # default
            if hasattr(self.canvas, '_app_instance'):
                try:
                    strength = float(self.canvas._app_instance.rembg_strength_var.get())
                    strength = max(0.0, min(2.0, strength))  # Allow values up to 2.0 for extra strength
                except (ValueError, AttributeError):
                    strength = 0.5

            print(f"Using REMBG strength: {strength}")

            # Create a session with the default model
            session = new_session("u2net")

            # Remove the background directly
            output_img = remove(self.original_image, session=session)

            # Apply strength adjustment - now with improved control
            if strength != 1.0:  # Apply adjustment for any value other than exactly 1.0
                # Convert to numpy arrays for processing
                original_array = np.array(self.original_image.convert('RGBA'))
                removed_array = np.array(output_img.convert('RGBA'))

                # Get the alpha channels
                original_alpha = original_array[:, :, 3].astype(float) / 255.0
                removed_alpha = removed_array[:, :, 3].astype(float) / 255.0

                # NEW IMPROVED APPROACH: More intuitive strength control
                # 0.0 = keep original (no removal)
                # 0.5 = conservative removal
                # 1.0 = standard rembg removal (baseline)
                # 1.5 = aggressive removal
                # 2.0 = very aggressive removal (catches fine details like hair strands)

                if strength < 1.0:
                    # Conservative mode: Keep more of the original
                    # Linear interpolation between original and rembg result
                    final_alpha = original_alpha * (1.0 - strength) + removed_alpha * strength
                else:
                    # Aggressive mode: Remove more than standard rembg
                    # Use progressively lower thresholds to catch more background

                    # Calculate how much more aggressive than standard (1.0)
                    extra_strength = strength - 1.0  # 0.0 to 1.0

                    # Create an aggressive threshold that gets lower with higher strength
                    # At strength 1.0: threshold = 0.5 (standard)
                    # At strength 1.5: threshold = 0.25 (aggressive)
                    # At strength 2.0: threshold = 0.05 (very aggressive, catches hair strands)
                    base_threshold = 0.5
                    aggressive_reduction = extra_strength * 0.45  # Reduce threshold by up to 0.45
                    removal_threshold = base_threshold - aggressive_reduction

                    print(f"Aggressive mode: threshold = {removal_threshold:.3f}")

                    # Apply the threshold: if rembg alpha is below threshold, FULLY remove
                    final_alpha = np.where(
                        removed_alpha < removal_threshold,  # Where rembg detected background
                        0.0,  # FULLY transparent (complete removal)
                        original_alpha  # Keep original alpha (solid foreground)
                    )

                # Use the original RGB colors (no color blending)
                result_array = original_array.copy()
                result_array[:, :, 3] = (final_alpha * 255).astype(np.uint8)

                output_img = Image.fromarray(result_array, 'RGBA')

            print("Background removal successful")
            return output_img

        except ImportError as e:
            print(f"REMBG not available: {e}")
            print("Using original image without background removal")
            return self.original_image.copy()
        except Exception as e:
            print(f"Background removal failed: {e}")
            traceback.print_exc()
            print("Using original image without background removal")
            return self.original_image.copy()

    def create_canvas_image(self):
        """Create the image on the canvas"""
        # Get canvas zoom factor if available
        canvas_scale = 1.0
        if hasattr(self.canvas, '_app_instance'):
            canvas_scale = self.canvas._app_instance.canvas_scale

        # Calculate display size with image scale and canvas zoom
        display_width = int(self.processed_image.size[0] * self.scale * canvas_scale)
        display_height = int(self.processed_image.size[1] * self.scale * canvas_scale)

        if display_width > 0 and display_height > 0:
            # Resize the image
            resized_image = self.processed_image.resize(
                (display_width, display_height), Image.Resampling.LANCZOS
            )

            # Apply rotation if needed
            if self.rotation != 0:
                resized_image = resized_image.rotate(
                    -self.rotation,  # Negative for clockwise rotation
                    expand=True,
                    fillcolor=(0, 0, 0, 0)  # Transparent fill
                )

            self.current_image = resized_image
            self.photo = ImageTk.PhotoImage(self.current_image)

            if self.canvas_id:
                self.canvas.delete(self.canvas_id)

            # Apply canvas zoom to position
            canvas_offset_x = 0
            canvas_offset_y = 0
            if hasattr(self.canvas, '_app_instance'):
                canvas_offset_x = self.canvas._app_instance.canvas_offset_x
                canvas_offset_y = self.canvas._app_instance.canvas_offset_y

            display_x = self.x * canvas_scale + canvas_offset_x
            display_y = self.y * canvas_scale + canvas_offset_y

            self.canvas_id = self.canvas.create_image(
                display_x, display_y, image=self.photo, anchor=tk.CENTER
            )

            # Set layer order
            if self.is_background:
                self.canvas.tag_lower(self.canvas_id)
            else:
                self.canvas.tag_raise(self.canvas_id)

    def get_bounds(self):
        """Get the bounding box of the image (display coordinates with zoom)"""
        if not self.current_image:
            return None

        # Get canvas zoom factor if available
        canvas_scale = 1.0
        canvas_offset_x = 0
        canvas_offset_y = 0
        if hasattr(self.canvas, '_app_instance'):
            canvas_scale = self.canvas._app_instance.canvas_scale
            canvas_offset_x = self.canvas._app_instance.canvas_offset_x
            canvas_offset_y = self.canvas._app_instance.canvas_offset_y

        half_width = self.current_image.size[0] // 2
        half_height = self.current_image.size[1] // 2

        # Apply canvas zoom to position
        display_x = self.x * canvas_scale + canvas_offset_x
        display_y = self.y * canvas_scale + canvas_offset_y

        return (
            display_x - half_width,
            display_y - half_height,
            display_x + half_width,
            display_y + half_height
        )

    def get_original_bounds(self):
        """Get the bounding box of the image in original coordinates (no zoom)"""
        if not self.processed_image:
            return None

        # Calculate original size with image scale only (no canvas zoom)
        original_width = int(self.processed_image.size[0] * self.scale)
        original_height = int(self.processed_image.size[1] * self.scale)

        # Account for rotation expansion
        if self.rotation != 0:
            # Create a temporary rotated image to get actual bounds
            temp_image = self.processed_image.resize(
                (original_width, original_height), Image.Resampling.LANCZOS
            )
            rotated_temp = temp_image.rotate(
                -self.rotation, expand=True, fillcolor=(0, 0, 0, 0)
            )
            original_width = rotated_temp.size[0]
            original_height = rotated_temp.size[1]

        half_width = original_width // 2
        half_height = original_height // 2

        return (
            self.x - half_width,
            self.y - half_height,
            self.x + half_width,
            self.y + half_height
        )

    def get_export_image(self):
        """Get the image for export (original size with scale and rotation, no zoom)"""
        # Calculate display size with image scale only (no canvas zoom)
        display_width = int(self.processed_image.size[0] * self.scale)
        display_height = int(self.processed_image.size[1] * self.scale)

        if display_width > 0 and display_height > 0:
            # Resize the image
            resized_image = self.processed_image.resize(
                (display_width, display_height), Image.Resampling.LANCZOS
            )

            # Apply rotation if needed
            if self.rotation != 0:
                resized_image = resized_image.rotate(
                    -self.rotation,  # Negative for clockwise rotation
                    expand=True,
                    fillcolor=(0, 0, 0, 0)  # Transparent fill
                )

            return resized_image

        return None

    def is_point_inside(self, px, py):
        """Check if a point is inside the image bounds"""
        bounds = self.get_bounds()
        if not bounds:
            return False

        return (bounds[0] <= px <= bounds[2] and
                bounds[1] <= py <= bounds[3])

    def get_handle_at(self, px, py):
        """Get handle index if point is over a handle, None otherwise"""
        if not self.selected:
            return None

        bounds = self.get_bounds()
        if not bounds:
            return None

        # Check rotation handle first (special case)
        center_x = (bounds[0] + bounds[2]) // 2
        center_y = bounds[1] - 30  # 30 pixels above the top edge
        rotation_radius = 6

        if ((px - center_x) ** 2 + (py - center_y) ** 2) <= (rotation_radius + 2) ** 2:
            return 'rotation'

        # Handle positions: 0=TL, 1=TR, 2=BR, 3=BL
        handle_positions = [
            (bounds[0], bounds[1]),  # Top-left
            (bounds[2], bounds[1]),  # Top-right
            (bounds[2], bounds[3]),  # Bottom-right
            (bounds[0], bounds[3])   # Bottom-left
        ]

        for i, (hx, hy) in enumerate(handle_positions):
            if (abs(px - hx) <= self.handle_size and
                abs(py - hy) <= self.handle_size):
                return i

        return None

    def draw_handles(self):
        """Draw resize handles if selected"""
        # Clear existing handles
        for handle in self.handles:
            self.canvas.delete(handle)
        self.handles.clear()

        if self.rotation_handle:
            self.canvas.delete(self.rotation_handle)
            self.rotation_handle = None

        if not self.selected:
            return

        bounds = self.get_bounds()
        if not bounds:
            return

        # Draw corner handles
        handle_positions = [
            (bounds[0], bounds[1]),  # Top-left
            (bounds[2], bounds[1]),  # Top-right
            (bounds[2], bounds[3]),  # Bottom-right
            (bounds[0], bounds[3])   # Bottom-left
        ]

        for hx, hy in handle_positions:
            handle = self.canvas.create_rectangle(
                hx - self.handle_size//2, hy - self.handle_size//2,
                hx + self.handle_size//2, hy + self.handle_size//2,
                fill="yellow", outline="black", width=1
            )
            self.handles.append(handle)
            self.canvas.tag_raise(handle)

        # Draw rotation handle (circle above the image)
        center_x = (bounds[0] + bounds[2]) // 2
        center_y = bounds[1] - 30  # 30 pixels above the top edge
        rotation_radius = 6

        self.rotation_handle = self.canvas.create_oval(
            center_x - rotation_radius, center_y - rotation_radius,
            center_x + rotation_radius, center_y + rotation_radius,
            fill="lightblue", outline="blue", width=2
        )
        self.canvas.tag_raise(self.rotation_handle)

    def update_display(self):
        """Optimized display update - only recreates image if necessary"""
        self.create_canvas_image()
        self.draw_handles()

    def move(self, dx, dy):
        """Move the image by the given offset"""
        self.x += dx
        self.y += dy
        self.update_display()

    def resize_from_handle(self, handle_index, new_x, new_y):
        """Resize image from a specific handle while maintaining aspect ratio"""
        bounds = self.get_bounds()
        if not bounds:
            return

        # Get canvas zoom factor if available
        canvas_scale = 1.0
        canvas_offset_x = 0
        canvas_offset_y = 0
        if hasattr(self.canvas, '_app_instance'):
            canvas_scale = self.canvas._app_instance.canvas_scale
            canvas_offset_x = self.canvas._app_instance.canvas_offset_x
            canvas_offset_y = self.canvas._app_instance.canvas_offset_y

        # Get current image center in canvas coordinates
        center_x = self.x * canvas_scale + canvas_offset_x
        center_y = self.y * canvas_scale + canvas_offset_y

        # Calculate current distance from center to handle
        current_handle_positions = [
            (bounds[0], bounds[1]),  # Top-left
            (bounds[2], bounds[1]),  # Top-right
            (bounds[2], bounds[3]),  # Bottom-right
            (bounds[0], bounds[3])   # Bottom-left
        ]

        current_handle_x, current_handle_y = current_handle_positions[handle_index]
        current_distance = ((current_handle_x - center_x) ** 2 + (current_handle_y - center_y) ** 2) ** 0.5

        # Calculate new distance from center to mouse
        new_distance = ((new_x - center_x) ** 2 + (new_y - center_y) ** 2) ** 0.5

        # Calculate scale factor based on distance ratio
        if current_distance > 0:
            scale_factor = new_distance / current_distance
        else:
            scale_factor = 1.0

        # Apply scale factor to current scale
        new_scale = self.scale * scale_factor

        # Minimum and maximum scale limits
        if new_scale < 0.1:
            new_scale = 0.1
        elif new_scale > 10.0:
            new_scale = 10.0

        self.scale = new_scale

        self.update_display()

    def start_rotation(self, mouse_x, mouse_y):
        """Start rotation tracking - call this when rotation begins"""
        bounds = self.get_bounds()
        if not bounds:
            return

        # Get canvas zoom factor if available
        canvas_scale = 1.0
        canvas_offset_x = 0
        canvas_offset_y = 0
        if hasattr(self.canvas, '_app_instance'):
            canvas_scale = self.canvas._app_instance.canvas_scale
            canvas_offset_x = self.canvas._app_instance.canvas_offset_x
            canvas_offset_y = self.canvas._app_instance.canvas_offset_y

        # Get current image center in canvas coordinates
        center_x = self.x * canvas_scale + canvas_offset_x
        center_y = self.y * canvas_scale + canvas_offset_y

        # Calculate initial mouse angle from center
        dx = mouse_x - center_x
        dy = mouse_y - center_y
        initial_mouse_angle = math.degrees(math.atan2(dy, dx)) - 90

        # Normalize angle to 0-360 range
        while initial_mouse_angle < 0:
            initial_mouse_angle += 360
        while initial_mouse_angle >= 360:
            initial_mouse_angle -= 360

        # Store initial state
        self.rotation_start_angle = self.rotation
        self.rotation_start_mouse_angle = initial_mouse_angle

    def rotate_from_handle(self, new_x, new_y):
        """Rotate image based on rotation handle position - uses relative rotation"""
        bounds = self.get_bounds()
        if not bounds:
            return

        # Get canvas zoom factor if available
        canvas_scale = 1.0
        canvas_offset_x = 0
        canvas_offset_y = 0
        if hasattr(self.canvas, '_app_instance'):
            canvas_scale = self.canvas._app_instance.canvas_scale
            canvas_offset_x = self.canvas._app_instance.canvas_offset_x
            canvas_offset_y = self.canvas._app_instance.canvas_offset_y

        # Get current image center in canvas coordinates
        center_x = self.x * canvas_scale + canvas_offset_x
        center_y = self.y * canvas_scale + canvas_offset_y

        # Calculate current mouse angle from center
        dx = new_x - center_x
        dy = new_y - center_y
        current_mouse_angle = math.degrees(math.atan2(dy, dx)) - 90

        # Normalize angle to 0-360 range
        while current_mouse_angle < 0:
            current_mouse_angle += 360
        while current_mouse_angle >= 360:
            current_mouse_angle -= 360

        # Calculate the relative rotation from the starting position
        angle_delta = current_mouse_angle - self.rotation_start_mouse_angle

        # Handle angle wrapping (e.g., going from 350° to 10°)
        if angle_delta > 180:
            angle_delta -= 360
        elif angle_delta < -180:
            angle_delta += 360

        # Apply the relative rotation to the starting angle
        new_rotation = self.rotation_start_angle + angle_delta

        # Normalize final angle to 0-360 range
        while new_rotation < 0:
            new_rotation += 360
        while new_rotation >= 360:
            new_rotation -= 360

        self.rotation = new_rotation

        self.update_display()

    def erase_at_point(self, x, y, brush_size):
        """Erase pixels at the given point with specified brush size"""
        try:
            from PIL import ImageDraw
            import numpy as np

            # Convert image to RGBA if not already
            if self.processed_image.mode != 'RGBA':
                self.processed_image = self.processed_image.convert('RGBA')

            # Create a mask for the eraser brush
            mask = Image.new('L', self.processed_image.size, 0)
            mask_draw = ImageDraw.Draw(mask)

            # Convert canvas coordinates to image coordinates
            bounds = self.get_bounds()
            if not bounds:
                print("Eraser: No bounds available")
                return False

            # Calculate relative position within the image
            rel_x = (x - bounds[0]) / (bounds[2] - bounds[0])
            rel_y = (y - bounds[1]) / (bounds[3] - bounds[1])

            # Debug output
            print(f"Eraser: Canvas coords ({x}, {y}), Bounds {bounds}, Relative ({rel_x:.3f}, {rel_y:.3f})")

            # Clamp coordinates to image bounds for erasing
            rel_x = max(0, min(1, rel_x))
            rel_y = max(0, min(1, rel_y))

            # Convert to image pixel coordinates
            img_x = int(rel_x * self.processed_image.size[0])
            img_y = int(rel_y * self.processed_image.size[1])

            # Calculate brush radius in image coordinates
            scale_factor = self.processed_image.size[0] / (bounds[2] - bounds[0])
            brush_radius = int(brush_size * scale_factor / 2)

            print(f"Eraser: Image coords ({img_x}, {img_y}), brush_radius {brush_radius}")

            # Ensure minimum brush size
            if brush_radius < 1:
                brush_radius = 1

            # Draw circle on mask
            mask_draw.ellipse([
                img_x - brush_radius, img_y - brush_radius,
                img_x + brush_radius, img_y + brush_radius
            ], fill=255)

            # Apply mask to alpha channel (erase by setting alpha to 0)
            img_array = np.array(self.processed_image)
            mask_array = np.array(mask)

            # Where mask is white (255), set alpha to 0 (transparent)
            img_array[:, :, 3] = np.where(mask_array > 0, 0, img_array[:, :, 3])

            # Update the processed image
            self.processed_image = Image.fromarray(img_array, 'RGBA')

            # Refresh the canvas display
            self.create_canvas_image()

            print("Eraser: Successfully applied")
            return True

        except Exception as e:
            print(f"Eraser failed: {e}")
            import traceback
            traceback.print_exc()
            return False

class CanvasBoundingBox:
    """Bounding box for defining export area"""

    def __init__(self, canvas, width=832, height=832):
        self.canvas = canvas
        self.rect_id = None
        self.handles = []
        self.handle_size = 10
        self.color = "red"

        # Calculate center position
        canvas_width = canvas.winfo_width() or 800
        canvas_height = canvas.winfo_height() or 600

        center_x = canvas_width // 2
        center_y = canvas_height // 2

        # Set initial coordinates
        self.x1 = center_x - width // 2
        self.y1 = center_y - height // 2
        self.x2 = center_x + width // 2
        self.y2 = center_y + height // 2

        self.create_box()

    def create_box(self):
        """Create the bounding box on canvas"""
        if self.rect_id:
            self.canvas.delete(self.rect_id)

        # Apply canvas zoom and offset
        canvas_scale = 1.0
        canvas_offset_x = 0
        canvas_offset_y = 0
        if hasattr(self.canvas, '_app_instance'):
            canvas_scale = self.canvas._app_instance.canvas_scale
            canvas_offset_x = self.canvas._app_instance.canvas_offset_x
            canvas_offset_y = self.canvas._app_instance.canvas_offset_y

        display_x1 = self.x1 * canvas_scale + canvas_offset_x
        display_y1 = self.y1 * canvas_scale + canvas_offset_y
        display_x2 = self.x2 * canvas_scale + canvas_offset_x
        display_y2 = self.y2 * canvas_scale + canvas_offset_y

        self.rect_id = self.canvas.create_rectangle(
            display_x1, display_y1, display_x2, display_y2,
            outline=self.color, width=2, fill="", stipple="gray25"
        )

        self.draw_handles()

    def draw_handles(self):
        """Draw resize handles"""
        # Clear existing handles
        for handle in self.handles:
            self.canvas.delete(handle)
        self.handles.clear()

        # Apply canvas zoom and offset
        canvas_scale = 1.0
        canvas_offset_x = 0
        canvas_offset_y = 0
        if hasattr(self.canvas, '_app_instance'):
            canvas_scale = self.canvas._app_instance.canvas_scale
            canvas_offset_x = self.canvas._app_instance.canvas_offset_x
            canvas_offset_y = self.canvas._app_instance.canvas_offset_y

        # Corner handle positions
        handle_positions = [
            (self.x1, self.y1),  # Top-left
            (self.x2, self.y1),  # Top-right
            (self.x2, self.y2),  # Bottom-right
            (self.x1, self.y2)   # Bottom-left
        ]

        for hx, hy in handle_positions:
            display_x = hx * canvas_scale + canvas_offset_x
            display_y = hy * canvas_scale + canvas_offset_y

            handle = self.canvas.create_rectangle(
                display_x - self.handle_size//2, display_y - self.handle_size//2,
                display_x + self.handle_size//2, display_y + self.handle_size//2,
                fill=self.color, outline="black", width=1
            )
            self.handles.append(handle)
            self.canvas.tag_raise(handle)

    def get_handle_at(self, px, py):
        """Get handle index if point is over a handle"""
        # Apply canvas zoom and offset
        canvas_scale = 1.0
        canvas_offset_x = 0
        canvas_offset_y = 0
        if hasattr(self.canvas, '_app_instance'):
            canvas_scale = self.canvas._app_instance.canvas_scale
            canvas_offset_x = self.canvas._app_instance.canvas_offset_x
            canvas_offset_y = self.canvas._app_instance.canvas_offset_y

        handle_positions = [
            (self.x1, self.y1),  # Top-left
            (self.x2, self.y1),  # Top-right
            (self.x2, self.y2),  # Bottom-right
            (self.x1, self.y2)   # Bottom-left
        ]

        for i, (hx, hy) in enumerate(handle_positions):
            display_x = hx * canvas_scale + canvas_offset_x
            display_y = hy * canvas_scale + canvas_offset_y

            if (abs(px - display_x) <= self.handle_size and
                abs(py - display_y) <= self.handle_size):
                return i

        return None

    def is_point_inside(self, px, py):
        """Check if point is inside the bounding box"""
        return (self.x1 <= px <= self.x2 and self.y1 <= py <= self.y2)

    def update_from_handle(self, handle_index, new_x, new_y):
        """Update bounding box from handle drag"""
        # Convert canvas coordinates to original coordinates
        canvas_scale = 1.0
        canvas_offset_x = 0
        canvas_offset_y = 0
        if hasattr(self.canvas, '_app_instance'):
            canvas_scale = self.canvas._app_instance.canvas_scale
            canvas_offset_x = self.canvas._app_instance.canvas_offset_x
            canvas_offset_y = self.canvas._app_instance.canvas_offset_y

        # Convert mouse position to original coordinate space
        original_x = (new_x - canvas_offset_x) / canvas_scale
        original_y = (new_y - canvas_offset_y) / canvas_scale

        if handle_index == 0:  # Top-left
            self.x1 = original_x
            self.y1 = original_y
        elif handle_index == 1:  # Top-right
            self.x2 = original_x
            self.y1 = original_y
        elif handle_index == 2:  # Bottom-right
            self.x2 = original_x
            self.y2 = original_y
        elif handle_index == 3:  # Bottom-left
            self.x1 = original_x
            self.y2 = original_y

        # Ensure minimum size
        if self.x2 - self.x1 < 64:
            if handle_index in [0, 3]:  # Left handles
                self.x1 = self.x2 - 64
            else:  # Right handles
                self.x2 = self.x1 + 64

        if self.y2 - self.y1 < 64:
            if handle_index in [0, 1]:  # Top handles
                self.y1 = self.y2 - 64
            else:  # Bottom handles
                self.y2 = self.y1 + 64

        self.create_box()

    def move(self, dx, dy):
        """Move the entire bounding box"""
        # Convert canvas movement to original coordinate space
        canvas_scale = 1.0
        if hasattr(self.canvas, '_app_instance'):
            canvas_scale = self.canvas._app_instance.canvas_scale

        # Scale the movement to original coordinates
        original_dx = dx / canvas_scale
        original_dy = dy / canvas_scale

        self.x1 += original_dx
        self.y1 += original_dy
        self.x2 += original_dx
        self.y2 += original_dy
        self.create_box()

    def set_aspect_ratio(self, ratio, longest_side=832):
        """Set bounding box to specific aspect ratio"""
        center_x = (self.x1 + self.x2) // 2
        center_y = (self.y1 + self.y2) // 2

        if ratio >= 1:  # Landscape or square
            width = longest_side
            height = int(longest_side / ratio)
        else:  # Portrait
            height = longest_side
            width = int(longest_side * ratio)

        self.x1 = center_x - width // 2
        self.y1 = center_y - height // 2
        self.x2 = center_x + width // 2
        self.y2 = center_y + height // 2

        self.create_box()

    def get_coords(self):
        """Get bounding box coordinates"""
        return (self.x1, self.y1, self.x2, self.y2)

    def get_dimensions(self):
        """Get width and height"""
        return (self.x2 - self.x1, self.y2 - self.y1)

class PhotoBashingApp:
    """Main application for photo bashing and image composition"""

    def __init__(self, root):
        self.root = root
        self.root.title("Image Photo Bashing")

        # Application state
        self.images = []  # List of DraggableImage objects
        self.background_image = None  # Special background image
        self.bounding_box = None
        self.selected_image = None
        self.dragging = False
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.active_handle = None
        self.handle_type = None  # 'image' or 'bbox'

        # Quick elements configuration
        self.quick_elements = {"element1": "", "element2": "", "element3": ""}
        self.config_file = "photo_bashing_config.json"

        # Load configuration
        self.load_config()

        # Window setup
        window_width = 1400
        window_height = 900
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        root.minsize(1000, 700)

        self.setup_ui()
        self.setup_drag_drop()

    def setup_ui(self):
        """Setup the user interface"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel container with scrolling
        left_container = ttk.Frame(main_frame, width=250)
        left_container.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        left_container.pack_propagate(False)

        # Create scrollable left panel
        self.setup_scrollable_left_panel(left_container)

        # Canvas frame
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # Setup canvas
        self.canvas = tk.Canvas(canvas_frame, bg='lightgray', cursor='crosshair')
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # Set app instance reference for zoom functionality
        self.canvas._app_instance = self

        # Canvas instructions - store ID so we can hide it when images are loaded
        self.canvas_instructions_id = self.canvas.create_text(
            400, 300,
            text="Drop images here to start compositing\n\n" +
                 "• Drop background images in the Background Zone (left panel)\n" +
                 "• Drop foreground images directly on canvas\n" +
                 "• Drag images from Discord, browsers, or file explorer\n" +
                 "• Press Ctrl+V to paste images from clipboard\n" +
                 "• Click images to select and resize\n" +
                 "• Drag images to reposition\n" +
                 "• Right-click and drag to pan the canvas view\n" +
                 "• Use mouse wheel to scroll left panel and zoom canvas\n" +
                 "• Use red bounding box to define export area",
            fill="gray", font=('Arial', 12), justify=tk.CENTER
        )

        # Bind canvas events
        self.canvas.bind('<Button-1>', self.on_canvas_click)
        self.canvas.bind('<B1-Motion>', self.on_canvas_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_canvas_release)
        self.canvas.bind('<Button-3>', self.on_canvas_right_click)
        self.canvas.bind('<B3-Motion>', self.on_canvas_right_drag)
        self.canvas.bind('<ButtonRelease-3>', self.on_canvas_right_release)
        self.canvas.bind('<Configure>', self.on_canvas_configure)
        self.canvas.bind('<MouseWheel>', self.on_mouse_wheel)

        # Bind keyboard events (need to set focus)
        self.root.bind('<Delete>', self.on_delete_key)
        self.root.bind('<KeyPress>', self.on_key_press)
        self.root.bind('<Control-v>', self.on_paste_clipboard)
        self.root.focus_set()  # Allow root to receive key events

        # Canvas zoom and pan state
        self.canvas_scale = 1.0
        self.canvas_offset_x = 0
        self.canvas_offset_y = 0

        # Canvas dragging state
        self.canvas_dragging = False
        self.canvas_drag_start_x = 0
        self.canvas_drag_start_y = 0

        # Eraser tool state
        self.eraser_mode = False
        self.eraser_brush_size = 20
        self.eraser_reticle = None
        self.erasing = False

    def setup_scrollable_left_panel(self, container):
        """Setup scrollable left panel with controls"""
        # Create a simple scrollable frame using Scrollbar and Frame
        # Create main frame for scrollable content
        self.controls_frame = ttk.Frame(container)
        self.controls_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Create scrollbar
        scrollbar = ttk.Scrollbar(container, orient=tk.VERTICAL)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Create canvas for scrolling
        scroll_canvas = tk.Canvas(container, yscrollcommand=scrollbar.set, highlightthickness=0)
        scroll_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Configure scrollbar
        scrollbar.config(command=scroll_canvas.yview)

        # Create inner frame for controls
        inner_frame = ttk.Frame(scroll_canvas)
        canvas_window = scroll_canvas.create_window((0, 0), window=inner_frame, anchor=tk.NW)

        # Bind mouse wheel for scrolling
        def on_mousewheel(event):
            scroll_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

        # Bind mouse wheel to canvas and all child widgets
        def bind_mousewheel_recursive(widget):
            widget.bind("<MouseWheel>", on_mousewheel)
            for child in widget.winfo_children():
                bind_mousewheel_recursive(child)

        # Update scroll region when content changes
        def configure_scroll_region(event=None):
            scroll_canvas.configure(scrollregion=scroll_canvas.bbox("all"))
            # Make the inner frame fill the canvas width
            canvas_width = scroll_canvas.winfo_width()
            if canvas_width > 1:  # Avoid issues during initialization
                scroll_canvas.itemconfig(canvas_window, width=canvas_width)

        inner_frame.bind("<Configure>", configure_scroll_region)
        scroll_canvas.bind("<Configure>", configure_scroll_region)

        # Apply mouse wheel binding
        bind_mousewheel_recursive(scroll_canvas)
        bind_mousewheel_recursive(inner_frame)

        # Store references
        self.scroll_canvas = scroll_canvas
        self.scrollbar = scrollbar
        self.controls_frame = inner_frame  # Use inner frame for controls

        # Setup controls in the scrollable frame
        self.setup_controls(self.controls_frame)

    def setup_controls(self, parent):
        """Setup the control panel"""
        # Background drop zone
        bg_frame = ttk.LabelFrame(parent, text="Background Image", padding=10)
        bg_frame.pack(fill=tk.X, pady=(0, 10))

        self.bg_drop_label = tk.Label(
            bg_frame,
            text="Drop background\nimage here\n(no background removal)",
            bg='lightblue',
            relief=tk.RAISED,
            height=4,
            font=('Arial', 10)
        )
        self.bg_drop_label.pack(fill=tk.X)

        # Background control buttons
        bg_buttons_frame = ttk.Frame(bg_frame)
        bg_buttons_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(bg_buttons_frame, text="Clear Background",
                  command=self.clear_background).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        ttk.Button(bg_buttons_frame, text="Copy as REMBG",
                  command=self.copy_background_as_rembg).pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 0))

        # Quick Elements section
        elements_frame = ttk.LabelFrame(parent, text="Quick Elements", padding=10)
        elements_frame.pack(fill=tk.X, pady=(0, 10))

        # Element 1
        element1_frame = ttk.Frame(elements_frame)
        element1_frame.pack(fill=tk.X, pady=2)

        self.element1_button = ttk.Button(element1_frame, text="Element 1: (Empty)",
                                         command=lambda: self.add_quick_element("element1"))
        self.element1_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))

        ttk.Button(element1_frame, text="Load", width=6,
                  command=lambda: self.load_element("element1")).pack(side=tk.LEFT, padx=1)
        ttk.Button(element1_frame, text="Clear", width=6,
                  command=lambda: self.clear_element("element1")).pack(side=tk.LEFT, padx=1)

        # Element 2
        element2_frame = ttk.Frame(elements_frame)
        element2_frame.pack(fill=tk.X, pady=2)

        self.element2_button = ttk.Button(element2_frame, text="Element 2: (Empty)",
                                         command=lambda: self.add_quick_element("element2"))
        self.element2_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))

        ttk.Button(element2_frame, text="Load", width=6,
                  command=lambda: self.load_element("element2")).pack(side=tk.LEFT, padx=1)
        ttk.Button(element2_frame, text="Clear", width=6,
                  command=lambda: self.clear_element("element2")).pack(side=tk.LEFT, padx=1)

        # Element 3
        element3_frame = ttk.Frame(elements_frame)
        element3_frame.pack(fill=tk.X, pady=2)

        self.element3_button = ttk.Button(element3_frame, text="Element 3: (Empty)",
                                         command=lambda: self.add_quick_element("element3"))
        self.element3_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))

        ttk.Button(element3_frame, text="Load", width=6,
                  command=lambda: self.load_element("element3")).pack(side=tk.LEFT, padx=1)
        ttk.Button(element3_frame, text="Clear", width=6,
                  command=lambda: self.clear_element("element3")).pack(side=tk.LEFT, padx=1)

        # Update button texts based on loaded config
        self.update_element_buttons()

        # REMBG strength control
        rembg_frame = ttk.LabelFrame(parent, text="Background Removal", padding=10)
        rembg_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(rembg_frame, text="REMBG Strength (0.0-2.0):").pack()
        ttk.Label(rembg_frame, text="0.0=keep all, 1.0=standard, 2.0=very aggressive",
                 font=('Arial', 8), foreground='gray').pack()

        self.rembg_strength_var = tk.StringVar(value="1.0")
        strength_entry = ttk.Entry(rembg_frame, textvariable=self.rembg_strength_var, width=10)
        strength_entry.pack(pady=2)

        # Strength slider for better control
        self.rembg_slider_var = tk.DoubleVar(value=1.0)
        strength_slider = ttk.Scale(
            rembg_frame,
            from_=0.0,
            to=2.0,
            orient=tk.HORIZONTAL,
            variable=self.rembg_slider_var,
            command=self.update_rembg_strength
        )
        strength_slider.pack(fill=tk.X, pady=2)

        # Quick preset buttons
        preset_frame1 = ttk.Frame(rembg_frame)
        preset_frame1.pack(fill=tk.X, pady=(5, 2))

        ttk.Button(preset_frame1, text="Keep (0.0)", width=7,
                  command=lambda: self.set_rembg_strength(0.0)).pack(side=tk.LEFT, padx=1)
        ttk.Button(preset_frame1, text="Light (0.5)", width=7,
                  command=lambda: self.set_rembg_strength(0.5)).pack(side=tk.LEFT, padx=1)
        ttk.Button(preset_frame1, text="Normal (1.0)", width=7,
                  command=lambda: self.set_rembg_strength(1.0)).pack(side=tk.LEFT, padx=1)

        preset_frame2 = ttk.Frame(rembg_frame)
        preset_frame2.pack(fill=tk.X, pady=(0, 0))

        ttk.Button(preset_frame2, text="Strong (1.5)", width=7,
                  command=lambda: self.set_rembg_strength(1.5)).pack(side=tk.LEFT, padx=1)
        ttk.Button(preset_frame2, text="Max (2.0)", width=7,
                  command=lambda: self.set_rembg_strength(2.0)).pack(side=tk.LEFT, padx=1)

        # Re-process button
        ttk.Button(rembg_frame, text="Re-process Selected Image",
                  command=self.reprocess_selected_image).pack(fill=tk.X, pady=(5, 0))

        # Aspect ratio presets
        ratio_frame = ttk.LabelFrame(parent, text="Canvas Aspect Ratios", padding=10)
        ratio_frame.pack(fill=tk.X, pady=(0, 10))

        ratios = [
            ("Square (1:1)", 1.0),
            ("Portrait (3:4)", 3/4),
            ("Landscape (4:3)", 4/3),
            ("Wide (16:9)", 16/9),
            ("Ultra Wide (21:9)", 21/9),
            ("Phone (9:16)", 9/16)
        ]

        for name, ratio in ratios:
            ttk.Button(ratio_frame, text=name,
                      command=lambda r=ratio: self.set_canvas_ratio(r)).pack(fill=tk.X, pady=1)

        # Canvas size control
        size_frame = ttk.LabelFrame(parent, text="Canvas Size", padding=10)
        size_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(size_frame, text="Longest Side:").pack()
        self.size_var = tk.StringVar(value="832")
        size_entry = ttk.Entry(size_frame, textvariable=self.size_var, width=10)
        size_entry.pack(pady=2)
        size_entry.bind('<Return>', self.update_canvas_size)

        ttk.Button(size_frame, text="Update Size",
                  command=self.update_canvas_size).pack(fill=tk.X, pady=(5, 0))

        # Zoom controls
        zoom_frame = ttk.LabelFrame(parent, text="View Controls", padding=10)
        zoom_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(zoom_frame, text="Reset Zoom (100%)",
                  command=self.reset_zoom).pack(fill=tk.X, pady=2)

        self.zoom_label = ttk.Label(zoom_frame, text="Zoom: 100%", font=('Arial', 9))
        self.zoom_label.pack(pady=2)

        # Eraser tool controls
        eraser_frame = ttk.LabelFrame(parent, text="Eraser Tool", padding=10)
        eraser_frame.pack(fill=tk.X, pady=(0, 10))

        # Eraser toggle button
        self.eraser_var = tk.BooleanVar()
        self.eraser_button = ttk.Checkbutton(
            eraser_frame,
            text="Enable Eraser Mode",
            variable=self.eraser_var,
            command=self.toggle_eraser_mode
        )
        self.eraser_button.pack(pady=2)

        # Brush size controls
        ttk.Label(eraser_frame, text="Brush Size:").pack()

        # Brush size slider
        self.brush_size_var = tk.IntVar(value=20)
        brush_slider = ttk.Scale(
            eraser_frame,
            from_=5,
            to=100,
            orient=tk.HORIZONTAL,
            variable=self.brush_size_var,
            command=self.update_brush_size
        )
        brush_slider.pack(fill=tk.X, pady=2)

        # Brush size indicator
        self.brush_size_label = ttk.Label(eraser_frame, text="Size: 20px", font=('Arial', 9))
        self.brush_size_label.pack(pady=2)

        # Rotation controls
        rotation_frame = ttk.LabelFrame(parent, text="Rotation", padding=10)
        rotation_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(rotation_frame, text="Selected Image Rotation:").pack()

        # Rotation angle display
        self.rotation_var = tk.StringVar(value="0°")
        rotation_entry = ttk.Entry(rotation_frame, textvariable=self.rotation_var, width=10)
        rotation_entry.pack(pady=2)
        rotation_entry.bind('<Return>', self.set_rotation_from_entry)

        # Rotation buttons
        rotation_buttons_frame = ttk.Frame(rotation_frame)
        rotation_buttons_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(rotation_buttons_frame, text="↺ -90°", width=6,
                  command=lambda: self.rotate_selected(-90)).pack(side=tk.LEFT, padx=1)
        ttk.Button(rotation_buttons_frame, text="↻ +90°", width=6,
                  command=lambda: self.rotate_selected(90)).pack(side=tk.LEFT, padx=1)
        ttk.Button(rotation_buttons_frame, text="Reset", width=6,
                  command=lambda: self.rotate_selected(0, absolute=True)).pack(side=tk.LEFT, padx=1)

        # Export controls
        export_frame = ttk.LabelFrame(parent, text="Export", padding=10)
        export_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(export_frame, text="Export Bounding Box",
                  command=self.export_image).pack(fill=tk.X, pady=2)
        ttk.Button(export_frame, text="Export Full Canvas",
                  command=self.export_full_canvas).pack(fill=tk.X, pady=2)
        ttk.Button(export_frame, text="Copy to Clipboard",
                  command=self.copy_to_clipboard).pack(fill=tk.X, pady=2)
        ttk.Button(export_frame, text="Copy Path",
                  command=self.copy_path).pack(fill=tk.X, pady=2)
        ttk.Button(export_frame, text="Send to FramePack",
                  command=self.send_to_framepack).pack(fill=tk.X, pady=2)

        # Image management
        manage_frame = ttk.LabelFrame(parent, text="Image Management", padding=10)
        manage_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(manage_frame, text="Clear All Images",
                  command=self.clear_all_images).pack(fill=tk.X, pady=2)
        ttk.Button(manage_frame, text="Delete Selected",
                  command=self.delete_selected).pack(fill=tk.X, pady=2)

        # Status
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(parent, textvariable=self.status_var,
                               font=('Arial', 9), foreground='blue')
        status_label.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))

    def setup_drag_drop(self):
        """Setup drag and drop functionality"""
        try:
            from tkinterdnd2 import DND_TEXT, DND_ALL

            # Canvas drag and drop - support files and other data
            self.canvas.drop_target_register(DND_FILES)
            self.canvas.drop_target_register(DND_TEXT)
            self.canvas.drop_target_register(DND_ALL)
            self.canvas.dnd_bind('<<Drop>>', self.on_canvas_drop)

            # Background zone drag and drop - support files and other data
            self.bg_drop_label.drop_target_register(DND_FILES)
            self.bg_drop_label.drop_target_register(DND_TEXT)
            self.bg_drop_label.drop_target_register(DND_ALL)
            self.bg_drop_label.dnd_bind('<<Drop>>', self.on_background_drop)

            # Visual feedback for drag enter/leave
            self.canvas.dnd_bind('<<DragEnter>>', self.on_drag_enter_canvas)
            self.canvas.dnd_bind('<<DragLeave>>', self.on_drag_leave_canvas)
            self.bg_drop_label.dnd_bind('<<DragEnter>>', self.on_drag_enter_bg)
            self.bg_drop_label.dnd_bind('<<DragLeave>>', self.on_drag_leave_bg)

        except ImportError:
            # Fallback to basic file drop if extended DnD not available
            self.canvas.drop_target_register(DND_FILES)
            self.canvas.dnd_bind('<<Drop>>', self.on_canvas_drop)
            self.bg_drop_label.drop_target_register(DND_FILES)
            self.bg_drop_label.dnd_bind('<<Drop>>', self.on_background_drop)
            self.canvas.dnd_bind('<<DragEnter>>', self.on_drag_enter_canvas)
            self.canvas.dnd_bind('<<DragLeave>>', self.on_drag_leave_canvas)
            self.bg_drop_label.dnd_bind('<<DragEnter>>', self.on_drag_enter_bg)
            self.bg_drop_label.dnd_bind('<<DragLeave>>', self.on_drag_leave_bg)

    def on_drag_enter_canvas(self, event):
        """Visual feedback when dragging over canvas"""
        self.canvas.configure(bg='lightgreen')

    def on_drag_leave_canvas(self, event):
        """Remove visual feedback when leaving canvas"""
        self.canvas.configure(bg='lightgray')

    def on_drag_enter_bg(self, event):
        """Visual feedback when dragging over background zone"""
        self.bg_drop_label.configure(bg='lightgreen')

    def on_drag_leave_bg(self, event):
        """Remove visual feedback when leaving background zone"""
        self.bg_drop_label.configure(bg='lightblue')

    def on_canvas_drop(self, event):
        """Handle files dropped on canvas (foreground images)"""
        self.canvas.configure(bg='lightgray')

        # Try to handle the drop data
        success_count = 0

        # print(f"Canvas drop data: {repr(event.data)}")  # Debug output

        # First check if it's a URL (from Chrome, etc.)
        if event.data.startswith(('http://', 'https://')):
            if self.handle_image_data_drop(event.data, is_background=False):
                success_count = 1
        else:
            # Try to handle as regular files
            files = self.parse_drop_files(event.data)
            for file_path in files:
                if self.is_image_file(file_path):
                    try:
                        self.add_foreground_image(file_path)
                        success_count += 1
                    except Exception as e:
                        print(f"Failed to add image {file_path}: {e}")

            # If no files worked, try to handle as image data from clipboard/apps like Discord
            if success_count == 0:
                if self.handle_image_data_drop(event.data, is_background=False):
                    success_count = 1

        if success_count > 0:
            self.update_status(f"Added {success_count} foreground image(s)")
        else:
            self.update_status("No valid images found in drop data")

    def on_background_drop(self, event):
        """Handle files dropped on background zone"""
        self.bg_drop_label.configure(bg='lightblue')

        # print(f"Background drop data: {repr(event.data)}")  # Debug output

        # First check if it's a URL (from Chrome, etc.)
        if event.data.startswith(('http://', 'https://')):
            if self.handle_image_data_drop(event.data, is_background=True):
                self.update_status("Background image set from URL")
            else:
                self.update_status("Failed to download image from URL")
            return

        # Try to handle as regular files
        files = self.parse_drop_files(event.data)
        if files and self.is_image_file(files[0]):
            try:
                self.set_background_image(files[0])
                self.update_status("Background image set")
                return
            except Exception as e:
                print(f"Failed to set background image {files[0]}: {e}")

        # If no files worked, try to handle as image data from clipboard/apps
        if self.handle_image_data_drop(event.data, is_background=True):
            self.update_status("Background image set from dropped data")
        else:
            self.update_status("No valid image found in drop data")

    def handle_image_data_drop(self, data, is_background=False):
        """Handle image data dropped from apps like Discord"""
        try:
            # Check if data looks like it could be image data
            if not data or len(data) < 5:
                return False

            # print(f"Trying to handle as image data: {repr(data[:100])}")  # Debug

            # Try to handle different types of dropped data
            image = None
            temp_path = None

            # Case 1: Data is a URL (Discord often provides URLs)
            if data.startswith(('http://', 'https://')):
                try:
                    import urllib.request

                    print(f"Attempting to download from URL: {data}")

                    # Create request with proper headers for Discord and other sites
                    req = urllib.request.Request(data)
                    req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
                    req.add_header('Accept', 'image/webp,image/apng,image/*,*/*;q=0.8')
                    req.add_header('Accept-Language', 'en-US,en;q=0.9')
                    req.add_header('Cache-Control', 'no-cache')
                    req.add_header('Pragma', 'no-cache')

                    # Download the image
                    response = urllib.request.urlopen(req, timeout=10)
                    image_data = response.read()

                    # Create temporary file with proper extension based on content type
                    content_type = response.headers.get('content-type', '').lower()
                    if 'jpeg' in content_type or 'jpg' in content_type:
                        suffix = '.jpg'
                    elif 'png' in content_type:
                        suffix = '.png'
                    elif 'webp' in content_type:
                        suffix = '.webp'
                    elif 'gif' in content_type:
                        suffix = '.gif'
                    else:
                        # Try to detect from URL
                        if data.lower().endswith(('.jpg', '.jpeg')):
                            suffix = '.jpg'
                        elif data.lower().endswith('.webp'):
                            suffix = '.webp'
                        elif data.lower().endswith('.gif'):
                            suffix = '.gif'
                        else:
                            suffix = '.png'  # Default fallback

                    temp_path = tempfile.mktemp(suffix=suffix)
                    with open(temp_path, 'wb') as f:
                        f.write(image_data)

                    # Load as PIL image and convert to ensure compatibility
                    image = Image.open(temp_path)

                    # Convert to RGBA for consistency (handles various formats)
                    if image.mode not in ('RGBA', 'RGB'):
                        image = image.convert('RGBA')

                    # Save as PNG for consistent handling
                    final_temp_path = tempfile.mktemp(suffix='.png')
                    image.save(final_temp_path, 'PNG')
                    temp_path = final_temp_path

                    print("Successfully downloaded and loaded image from URL")

                except Exception as e:
                    print(f"Failed to download image from URL: {e}")
                    # If URL download fails, try clipboard as fallback
                    print("Trying clipboard as fallback for URL drop...")
                    try:
                        clipboard_image = ImageGrab.grabclipboard()
                        if clipboard_image:
                            image = clipboard_image
                            temp_path = tempfile.mktemp(suffix='.png')
                            image.save(temp_path)
                            print("Successfully got image from clipboard as URL fallback")
                        else:
                            return False
                    except Exception as e2:
                        print(f"Clipboard fallback also failed: {e2}")
                        return False

            # Case 2: Only try clipboard if data doesn't look like a file path
            elif not (data.startswith(('/', '\\', 'C:', 'D:', 'E:', 'F:', 'G:')) or
                     self.is_image_file(data) or
                     len(data) > 500):  # Don't try clipboard for long strings
                try:
                    print("Attempting to get image from clipboard")
                    # Try to get image from clipboard
                    clipboard_image = ImageGrab.grabclipboard()
                    if clipboard_image:
                        image = clipboard_image
                        # Save to temporary file
                        temp_path = tempfile.mktemp(suffix='.png')
                        image.save(temp_path)
                        print("Successfully got image from clipboard")
                    else:
                        print("No image found in clipboard")
                        return False

                except Exception as e:
                    print(f"Failed to get image from clipboard: {e}")
                    return False
            else:
                # Data looks like a file path or other non-image data
                return False

            # If we got an image, add it to the canvas
            if image and temp_path:
                if is_background:
                    self.set_background_image(temp_path)
                else:
                    self.add_foreground_image(temp_path)
                return True

            return False

        except Exception as e:
            print(f"Error handling image data drop: {e}")
            return False

    def parse_drop_files(self, data):
        """Parse dropped file paths"""
        # Handle multiple files and clean up paths
        files = []
        if data.startswith('{') and data.endswith('}'):
            # Multiple files
            data = data[1:-1]  # Remove braces
            files = [f.strip() for f in data.split('} {')]
        else:
            # Single file
            files = [data.strip()]

        return [f.strip('{}') for f in files if f.strip()]

    def is_image_file(self, file_path):
        """Check if file is a supported image format"""
        extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp')
        return file_path.lower().endswith(extensions)

    def add_foreground_image(self, file_path):
        """Add a foreground image to the canvas"""
        try:
            # Validate file path
            if not file_path or not os.path.exists(file_path):
                raise ValueError(f"Invalid file path: {file_path}")

            # Calculate position (center of canvas with slight offset for multiple images)
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # Offset each new image slightly
            offset = len(self.images) * 20
            x = canvas_width // 2 + offset
            y = canvas_height // 2 + offset

            # Create draggable image
            img = DraggableImage(self.canvas, file_path, x, y, is_background=False)
            self.images.append(img)

            # Create bounding box if this is the first image
            if not self.bounding_box:
                self.create_initial_bounding_box()

            # Update canvas instructions visibility
            self.update_canvas_instructions_visibility()

            self.update_status(f"Added: {os.path.basename(file_path)}")

        except Exception as e:
            error_msg = f"Failed to load image: {str(e)}"
            print(error_msg)  # Console output for debugging
            messagebox.showerror("Error", error_msg)
            self.update_status("Failed to load image")

    def set_background_image(self, file_path):
        """Set the background image"""
        try:
            # Validate file path
            if not file_path or not os.path.exists(file_path):
                raise ValueError(f"Invalid file path: {file_path}")

            # Remove existing background
            if self.background_image:
                self.canvas.delete(self.background_image.canvas_id)

            # Calculate center position
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            x = canvas_width // 2
            y = canvas_height // 2

            # Create background image (no background removal)
            self.background_image = DraggableImage(
                self.canvas, file_path, x, y, is_background=True
            )

            # Update background drop label
            filename = os.path.basename(file_path)
            self.bg_drop_label.configure(text=f"Background:\n{filename[:20]}...")

            # Update canvas instructions visibility
            self.update_canvas_instructions_visibility()

            self.update_status(f"Background set: {filename}")

        except Exception as e:
            error_msg = f"Failed to set background: {str(e)}"
            print(error_msg)  # Console output for debugging
            messagebox.showerror("Error", error_msg)
            self.update_status("Failed to set background image")

    def create_initial_bounding_box(self):
        """Create the initial bounding box"""
        if not self.bounding_box:
            # Wait for canvas to be properly sized
            self.root.update_idletasks()
            self.bounding_box = CanvasBoundingBox(self.canvas)

    def update_status(self, message):
        """Update status message"""
        self.status_var.set(message)
        self.root.after(3000, lambda: self.status_var.set("Ready"))

    def update_canvas_instructions_visibility(self):
        """Show or hide canvas instructions based on whether images are loaded"""
        has_images = len(self.images) > 0 or self.background_image is not None

        if has_images:
            # Hide instructions when images are present
            self.canvas.itemconfig(self.canvas_instructions_id, state='hidden')
        else:
            # Show instructions when no images are present
            self.canvas.itemconfig(self.canvas_instructions_id, state='normal')

    def on_canvas_click(self, event):
        """Handle canvas mouse click"""
        # Handle eraser mode
        if self.eraser_mode:
            # Check if clicking on selected image for erasing
            if self.selected_image and self.selected_image.is_point_inside(event.x, event.y):
                self.erasing = True
                self.selected_image.erase_at_point(event.x, event.y, self.eraser_brush_size)
                return
            else:
                # Try to select an image under the cursor first
                clicked_image = None
                for img in reversed(self.images):  # Check top images first
                    if img.is_point_inside(event.x, event.y):
                        clicked_image = img
                        break

                # Check background image if no foreground image clicked
                if not clicked_image and self.background_image:
                    if self.background_image.is_point_inside(event.x, event.y):
                        clicked_image = self.background_image

                if clicked_image:
                    # Select the image and start erasing
                    self.select_image(clicked_image)
                    self.erasing = True
                    clicked_image.erase_at_point(event.x, event.y, self.eraser_brush_size)
                    return
                else:
                    self.update_status("Click on an image to erase")
                    return

        # Check if clicking on image handles first (highest priority)
        for img in reversed(self.images):  # Check top images first
            if img.selected:
                handle = img.get_handle_at(event.x, event.y)
                if handle is not None:
                    self.active_handle = handle
                    self.handle_type = 'image'
                    self.dragging = True
                    self.drag_start_x = event.x
                    self.drag_start_y = event.y
                    self.selected_image = img
                    # Start rotation tracking if this is the rotation handle
                    if handle == 'rotation':
                        img.start_rotation(event.x, event.y)
                    return

        # Check background image handles if selected
        if self.background_image and self.background_image.selected:
            handle = self.background_image.get_handle_at(event.x, event.y)
            if handle is not None:
                self.active_handle = handle
                self.handle_type = 'image'
                self.dragging = True
                self.drag_start_x = event.x
                self.drag_start_y = event.y
                self.selected_image = self.background_image
                # Start rotation tracking if this is the rotation handle
                if handle == 'rotation':
                    self.background_image.start_rotation(event.x, event.y)
                return

        # Check if clicking on bounding box handles
        if self.bounding_box:
            handle = self.bounding_box.get_handle_at(event.x, event.y)
            if handle is not None:
                self.active_handle = handle
                self.handle_type = 'bbox'
                self.dragging = True
                self.drag_start_x = event.x
                self.drag_start_y = event.y
                return

        # Check if clicking on images (for selection and moving)
        clicked_image = None
        for img in reversed(self.images):  # Check top images first
            if img.is_point_inside(event.x, event.y):
                clicked_image = img
                break

        # Check background image if no foreground image clicked
        if not clicked_image and self.background_image:
            if self.background_image.is_point_inside(event.x, event.y):
                clicked_image = self.background_image

        # Update selection
        self.select_image(clicked_image)

        # Start dragging if image selected
        if clicked_image:
            self.active_handle = 'move'
            self.handle_type = 'image'
            self.dragging = True
            self.drag_start_x = event.x
            self.drag_start_y = event.y
            self.selected_image = clicked_image

        # Check if clicking inside bounding box for moving (lowest priority)
        elif self.bounding_box and self.bounding_box.is_point_inside(event.x, event.y):
            self.active_handle = 'move'
            self.handle_type = 'bbox'
            self.dragging = True
            self.drag_start_x = event.x
            self.drag_start_y = event.y

    def on_canvas_drag(self, event):
        """Handle canvas mouse drag"""
        # Handle eraser dragging
        if self.eraser_mode and self.erasing:
            if self.selected_image:
                # Erase at current position regardless of whether cursor is inside image bounds
                # This allows for smoother erasing when dragging quickly
                self.selected_image.erase_at_point(event.x, event.y, self.eraser_brush_size)
            return

        if not self.dragging:
            return

        dx = event.x - self.drag_start_x
        dy = event.y - self.drag_start_y

        if self.handle_type == 'bbox':
            if self.active_handle == 'move':
                # Move bounding box
                self.bounding_box.move(dx, dy)
            else:
                # Resize bounding box
                self.bounding_box.update_from_handle(self.active_handle, event.x, event.y)

        elif self.handle_type == 'image' and self.selected_image:
            if self.active_handle == 'move':
                # Move image
                # Convert canvas movement to image space
                canvas_scale = 1.0
                if hasattr(self.canvas, '_app_instance'):
                    canvas_scale = self.canvas._app_instance.canvas_scale

                image_dx = dx / canvas_scale
                image_dy = dy / canvas_scale
                self.selected_image.move(image_dx, image_dy)
            elif self.active_handle == 'rotation':
                # Rotate image
                self.selected_image.rotate_from_handle(event.x, event.y)
            else:
                # Resize image
                self.selected_image.resize_from_handle(self.active_handle, event.x, event.y)

        self.drag_start_x = event.x
        self.drag_start_y = event.y

    def on_canvas_release(self, event):
        """Handle canvas mouse release"""
        self.dragging = False
        self.erasing = False
        self.active_handle = None
        self.handle_type = None

    def on_canvas_right_click(self, event):
        """Handle right mouse button click to start canvas dragging"""
        self.canvas_dragging = True
        self.canvas_drag_start_x = event.x
        self.canvas_drag_start_y = event.y
        self.canvas.configure(cursor="fleur")  # Change cursor to indicate dragging mode
        self.update_status("Canvas dragging mode - drag to pan view")

    def on_canvas_right_drag(self, event):
        """Handle right mouse button drag to pan the canvas"""
        if not self.canvas_dragging:
            return

        # Calculate movement delta
        dx = event.x - self.canvas_drag_start_x
        dy = event.y - self.canvas_drag_start_y

        # Update canvas offset
        self.canvas_offset_x += dx
        self.canvas_offset_y += dy

        # Update drag start position for next movement
        self.canvas_drag_start_x = event.x
        self.canvas_drag_start_y = event.y

        # Refresh all canvas elements with new offset
        self.refresh_canvas_view()

    def on_canvas_right_release(self, event):
        """Handle right mouse button release to end canvas dragging"""
        self.canvas_dragging = False

        # Restore cursor based on current mode
        if self.eraser_mode:
            self.canvas.configure(cursor="dotbox")
        else:
            self.canvas.configure(cursor="crosshair")

        self.update_status("Canvas dragging ended")

    def on_canvas_configure(self, event):
        """Handle canvas resize"""
        # Update bounding box if it exists
        if self.bounding_box:
            # Keep bounding box centered when canvas resizes
            canvas_width = event.width
            canvas_height = event.height

            # Calculate current center
            bbox_coords = self.bounding_box.get_coords()
            current_center_x = (bbox_coords[0] + bbox_coords[2]) // 2
            current_center_y = (bbox_coords[1] + bbox_coords[3]) // 2

            # Calculate new center
            new_center_x = canvas_width // 2
            new_center_y = canvas_height // 2

            # Move bounding box to new center
            dx = new_center_x - current_center_x
            dy = new_center_y - current_center_y

            if abs(dx) > 10 or abs(dy) > 10:  # Only move if significant change
                self.bounding_box.move(dx, dy)

    def select_image(self, image):
        """Select an image and update handles"""
        # Deselect all images
        for img in self.images:
            img.selected = False
            img.draw_handles()

        if self.background_image:
            self.background_image.selected = False
            self.background_image.draw_handles()

        # Select new image
        if image:
            image.selected = True
            image.draw_handles()
            self.selected_image = image
            # Update rotation display
            self.rotation_var.set(f"{image.rotation:.1f}°")
        else:
            self.selected_image = None
            self.rotation_var.set("0°")

    def on_mouse_wheel(self, event):
        """Handle mouse wheel for zooming"""
        # Calculate zoom factor
        if event.delta > 0:
            zoom_factor = 1.1
        else:
            zoom_factor = 0.9

        # Limit zoom range
        new_scale = self.canvas_scale * zoom_factor
        if new_scale < 0.1:
            new_scale = 0.1
        elif new_scale > 5.0:
            new_scale = 5.0

        # Calculate zoom center point (mouse position)
        mouse_x = event.x
        mouse_y = event.y

        # Calculate offset adjustment to zoom towards mouse position
        old_scale = self.canvas_scale
        scale_change = new_scale / old_scale

        # Adjust offset to zoom towards mouse position
        self.canvas_offset_x = mouse_x - (mouse_x - self.canvas_offset_x) * scale_change
        self.canvas_offset_y = mouse_y - (mouse_y - self.canvas_offset_y) * scale_change

        # Update canvas scale
        self.canvas_scale = new_scale

        # Refresh all canvas elements with new zoom
        self.refresh_canvas_view()

        # Update zoom label
        self.zoom_label.configure(text=f"Zoom: {int(self.canvas_scale * 100)}%")
        self.update_status(f"Zoom: {int(self.canvas_scale * 100)}%")

    def refresh_canvas_view(self):
        """Refresh all canvas elements with current zoom and offset"""
        # Update all images
        for img in self.images:
            img.create_canvas_image()
            img.draw_handles()

        if self.background_image:
            self.background_image.create_canvas_image()
            self.background_image.draw_handles()

        # Update bounding box
        if self.bounding_box:
            self.bounding_box.create_box()

    def reset_zoom(self):
        """Reset zoom to 100% and center view"""
        self.canvas_scale = 1.0
        self.canvas_offset_x = 0
        self.canvas_offset_y = 0

        # Refresh all elements
        self.refresh_canvas_view()

        # Update zoom label
        self.zoom_label.configure(text="Zoom: 100%")
        self.update_status("Zoom reset to 100%")

    def on_delete_key(self, event):
        """Handle delete key press to remove selected image"""
        if self.selected_image:
            self.delete_selected()
            return "break"  # Prevent further processing

    def on_key_press(self, event):
        """Handle general key presses"""
        # Make sure canvas can receive focus for future key events
        if event.widget != self.canvas:
            self.root.focus_set()

        # Handle delete key specifically
        if event.keysym == 'Delete':
            self.on_delete_key(event)
            return "break"

    def on_paste_clipboard(self, event):
        """Handle Ctrl+V to paste image from clipboard"""
        try:
            # Try to get image from clipboard
            clipboard_image = ImageGrab.grabclipboard()
            if clipboard_image:
                # Save to temporary file
                temp_path = tempfile.mktemp(suffix='.png')
                clipboard_image.save(temp_path)

                # Add as foreground image
                self.add_foreground_image(temp_path)
                self.update_status("Image pasted from clipboard")
                return "break"
            else:
                self.update_status("No image found in clipboard")
                return "break"

        except Exception as e:
            print(f"Failed to paste from clipboard: {e}")
            self.update_status("Failed to paste from clipboard")
            return "break"

    def toggle_eraser_mode(self):
        """Toggle eraser mode on/off"""
        self.eraser_mode = self.eraser_var.get()

        if self.eraser_mode:
            self.canvas.configure(cursor="dotbox")
            self.update_status("Eraser mode enabled - Click and drag on selected image to erase")
            # Bind mouse motion for reticle
            self.canvas.bind('<Motion>', self.on_mouse_motion)
        else:
            self.canvas.configure(cursor="crosshair")
            self.update_status("Eraser mode disabled")
            # Remove reticle
            if self.eraser_reticle:
                self.canvas.delete(self.eraser_reticle)
                self.eraser_reticle = None
            # Unbind mouse motion
            self.canvas.unbind('<Motion>')

    def update_brush_size(self, value=None):
        """Update brush size from slider"""
        self.eraser_brush_size = self.brush_size_var.get()
        self.brush_size_label.configure(text=f"Size: {self.eraser_brush_size}px")

    def update_rembg_strength(self, value=None):
        """Update rembg strength from slider"""
        strength = self.rembg_slider_var.get()
        self.rembg_strength_var.set(f"{strength:.1f}")

    def set_rembg_strength(self, strength):
        """Set rembg strength to specific value"""
        self.rembg_strength_var.set(str(strength))
        self.rembg_slider_var.set(strength)

    def reprocess_selected_image(self):
        """Re-process the selected image with current rembg settings"""
        if not self.selected_image:
            self.update_status("No image selected to re-process")
            return

        if self.selected_image.is_background:
            self.update_status("Cannot re-process background image")
            return

        try:
            # Store current position and scale
            old_x = self.selected_image.x
            old_y = self.selected_image.y
            old_scale = self.selected_image.scale
            old_rotation = self.selected_image.rotation

            # Re-process the background removal
            self.selected_image.processed_image = self.selected_image.remove_background()

            # Restore position and scale
            self.selected_image.x = old_x
            self.selected_image.y = old_y
            self.selected_image.scale = old_scale
            self.selected_image.rotation = old_rotation

            # Refresh the display
            self.selected_image.create_canvas_image()
            self.selected_image.draw_handles()

            self.update_status("Image re-processed with new settings")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to re-process image: {str(e)}")

        # Update reticle if visible
        if self.eraser_mode and self.eraser_reticle:
            self.update_eraser_reticle()

    def on_mouse_motion(self, event):
        """Handle mouse motion for eraser reticle"""
        if self.eraser_mode:
            self.update_eraser_reticle(event.x, event.y)

    def update_eraser_reticle(self, x=None, y=None):
        """Update or create the eraser reticle"""
        if not self.eraser_mode:
            return

        # Remove existing reticle
        if self.eraser_reticle:
            self.canvas.delete(self.eraser_reticle)

        # Get mouse position if not provided
        if x is None or y is None:
            x = self.canvas.winfo_pointerx() - self.canvas.winfo_rootx()
            y = self.canvas.winfo_pointery() - self.canvas.winfo_rooty()

        # Create new reticle circle
        radius = self.eraser_brush_size // 2
        self.eraser_reticle = self.canvas.create_oval(
            x - radius, y - radius,
            x + radius, y + radius,
            outline="red", width=2, fill=""
        )

        # Keep reticle on top
        self.canvas.tag_raise(self.eraser_reticle)

    def set_canvas_ratio(self, ratio):
        """Set canvas bounding box to specific aspect ratio"""
        if not self.bounding_box:
            self.create_initial_bounding_box()

        try:
            longest_side = int(self.size_var.get())
        except ValueError:
            longest_side = 832

        self.bounding_box.set_aspect_ratio(ratio, longest_side)
        self.update_status(f"Canvas ratio set to {ratio:.2f}")

    def update_canvas_size(self, event=None):
        """Update canvas size from entry"""
        try:
            longest_side = int(self.size_var.get())
            if longest_side < 64:
                longest_side = 64
                self.size_var.set("64")

            if self.bounding_box:
                # Get current ratio
                width, height = self.bounding_box.get_dimensions()
                current_ratio = width / height if height > 0 else 1.0
                self.bounding_box.set_aspect_ratio(current_ratio, longest_side)

            self.update_status(f"Canvas size updated to {longest_side}")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number")
            self.size_var.set("832")

    def rotate_selected(self, angle, absolute=False):
        """Rotate the selected image by the given angle"""
        if not self.selected_image:
            self.update_status("No image selected to rotate")
            return

        if absolute:
            self.selected_image.rotation = angle
        else:
            self.selected_image.rotation += angle

        # Normalize angle to 0-360 range
        while self.selected_image.rotation < 0:
            self.selected_image.rotation += 360
        while self.selected_image.rotation >= 360:
            self.selected_image.rotation -= 360

        # Update display
        self.selected_image.create_canvas_image()
        self.selected_image.draw_handles()

        # Update rotation display
        self.rotation_var.set(f"{self.selected_image.rotation:.1f}°")

        self.update_status(f"Rotated to {self.selected_image.rotation:.1f}°")

    def set_rotation_from_entry(self, event=None):
        """Set rotation from the entry field"""
        if not self.selected_image:
            self.update_status("No image selected to rotate")
            return

        try:
            # Parse angle from entry (remove degree symbol if present)
            angle_text = self.rotation_var.get().replace("°", "").strip()
            angle = float(angle_text)

            # Normalize angle to 0-360 range
            while angle < 0:
                angle += 360
            while angle >= 360:
                angle -= 360

            self.selected_image.rotation = angle

            # Update display
            self.selected_image.create_canvas_image()
            self.selected_image.draw_handles()

            # Update rotation display
            self.rotation_var.set(f"{angle:.1f}°")

            self.update_status(f"Rotation set to {angle:.1f}°")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid angle")
            # Reset to current rotation
            self.rotation_var.set(f"{self.selected_image.rotation:.1f}°")

    def clear_background(self):
        """Clear the background image"""
        if self.background_image:
            self.canvas.delete(self.background_image.canvas_id)
            self.background_image = None
            self.bg_drop_label.configure(text="Drop background\nimage here\n(no background removal)")
            # Update canvas instructions visibility
            self.update_canvas_instructions_visibility()
            self.update_status("Background cleared")

    def copy_background_as_rembg(self):
        """Copy the current background image to canvas as a REMBG object with background removed"""
        if not self.background_image:
            messagebox.showwarning("Warning", "No background image to copy")
            return

        try:
            # Calculate position for the new foreground image (slightly offset from center)
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # Offset each new image slightly
            offset = len(self.images) * 20
            x = canvas_width // 2 + offset
            y = canvas_height // 2 + offset

            # Create a new foreground image from the background image path (this will apply REMBG)
            img = DraggableImage(self.canvas, self.background_image.image_path, x, y, is_background=False)
            self.images.append(img)

            # Create bounding box if this is the first foreground image
            if len(self.images) == 1:
                self.create_initial_bounding_box()

            # Hide canvas instructions since we now have images
            self.update_canvas_instructions_visibility()

            self.update_status("Background copied as REMBG object")

        except Exception as e:
            error_msg = f"Failed to copy background as REMBG: {str(e)}"
            print(error_msg)
            messagebox.showerror("Error", error_msg)
            self.update_status("Failed to copy background")

    def clear_all_images(self):
        """Clear all images"""
        # Clear foreground images
        for img in self.images:
            self.canvas.delete(img.canvas_id)
            for handle in img.handles:
                self.canvas.delete(handle)
        self.images.clear()

        # Clear background
        self.clear_background()

        # Clear selection
        self.selected_image = None

        # Update canvas instructions visibility
        self.update_canvas_instructions_visibility()

        self.update_status("All images cleared")

    def delete_selected(self):
        """Delete the selected image"""
        if self.selected_image:
            # Get filename for feedback
            filename = os.path.basename(self.selected_image.image_path)

            # Remove from canvas
            self.canvas.delete(self.selected_image.canvas_id)
            for handle in self.selected_image.handles:
                self.canvas.delete(handle)

            # Remove from list
            if self.selected_image in self.images:
                self.images.remove(self.selected_image)
                self.selected_image = None
                # Update canvas instructions visibility
                self.update_canvas_instructions_visibility()
                self.update_status(f"Deleted: {filename}")
            elif self.selected_image == self.background_image:
                self.clear_background()
                self.update_status(f"Deleted background: {filename}")
                return

            self.selected_image = None
        else:
            self.update_status("No image selected to delete")

    def export_image(self):
        """Export the composed image within the bounding box"""
        if not self.bounding_box:
            messagebox.showwarning("Warning", "No bounding box defined")
            return

        try:
            # Get target export size from canvas size setting
            try:
                target_longest_side = int(self.size_var.get())
            except (ValueError, AttributeError):
                target_longest_side = 832  # Default fallback

            # Get bounding box coordinates (original coordinates, not zoomed)
            bbox_coords = self.bounding_box.get_coords()
            original_width = int(bbox_coords[2] - bbox_coords[0])
            original_height = int(bbox_coords[3] - bbox_coords[1])

            # Calculate export dimensions based on target longest side
            if original_width >= original_height:
                # Landscape or square
                export_width = target_longest_side
                export_height = int((original_height / original_width) * target_longest_side)
            else:
                # Portrait
                export_height = target_longest_side
                export_width = int((original_width / original_height) * target_longest_side)

            # Calculate scale factor for high-quality export
            scale_factor_x = export_width / original_width
            scale_factor_y = export_height / original_height

            # Create new image at target export resolution
            result_image = Image.new('RGBA', (export_width, export_height), (0, 0, 0, 0))

            # Get all images in the bounding box area, sorted by canvas z-order
            all_images = []
            if self.background_image:
                all_images.append(self.background_image)
            all_images.extend(self.images)

            # Sort images by their canvas z-order (bottom to top)
            def get_canvas_z_order(img):
                if not img.canvas_id:
                    return -1  # Background or invalid items go first
                try:
                    # Get all canvas items
                    all_items = self.canvas.find_all()
                    # Find the index of this image's canvas_id in the list
                    # Lower index = lower z-order (drawn first, appears behind)
                    return all_items.index(img.canvas_id)
                except (ValueError, AttributeError):
                    return -1

            all_images.sort(key=get_canvas_z_order)

            # Composite images using high-quality scaling
            for img in all_images:
                img_bounds = img.get_original_bounds()  # Use original bounds

                if not img_bounds:
                    continue

                # Get high-quality export image at target resolution
                # Calculate the size this image should be at export resolution
                img_width_at_export = int((img_bounds[2] - img_bounds[0]) * scale_factor_x)
                img_height_at_export = int((img_bounds[3] - img_bounds[1]) * scale_factor_y)

                if img_width_at_export <= 0 or img_height_at_export <= 0:
                    continue

                # Create high-quality version of the image at export resolution
                high_quality_image = img.processed_image.resize(
                    (int(img.processed_image.size[0] * img.scale * scale_factor_x),
                     int(img.processed_image.size[1] * img.scale * scale_factor_y)),
                    Image.Resampling.LANCZOS
                )

                # Apply rotation if needed
                if img.rotation != 0:
                    high_quality_image = high_quality_image.rotate(
                        -img.rotation, expand=True, fillcolor=(0, 0, 0, 0)
                    )

                # Calculate intersection with bounding box (scaled coordinates)
                scaled_bbox_x1 = 0
                scaled_bbox_y1 = 0
                scaled_bbox_x2 = export_width
                scaled_bbox_y2 = export_height

                scaled_img_x1 = int((img_bounds[0] - bbox_coords[0]) * scale_factor_x)
                scaled_img_y1 = int((img_bounds[1] - bbox_coords[1]) * scale_factor_y)
                scaled_img_x2 = scaled_img_x1 + high_quality_image.size[0]
                scaled_img_y2 = scaled_img_y1 + high_quality_image.size[1]

                # Calculate intersection
                intersect_x1 = max(scaled_bbox_x1, scaled_img_x1)
                intersect_y1 = max(scaled_bbox_y1, scaled_img_y1)
                intersect_x2 = min(scaled_bbox_x2, scaled_img_x2)
                intersect_y2 = min(scaled_bbox_y2, scaled_img_y2)

                if intersect_x1 < intersect_x2 and intersect_y1 < intersect_y2:
                    # Calculate position in result image
                    result_x = intersect_x1
                    result_y = intersect_y1

                    # Calculate crop area from high-quality image
                    crop_x = intersect_x1 - scaled_img_x1
                    crop_y = intersect_y1 - scaled_img_y1
                    crop_w = intersect_x2 - intersect_x1
                    crop_h = intersect_y2 - intersect_y1

                    # Crop and paste
                    if (crop_x >= 0 and crop_y >= 0 and
                        crop_x + crop_w <= high_quality_image.size[0] and
                        crop_y + crop_h <= high_quality_image.size[1]):

                        cropped = high_quality_image.crop((crop_x, crop_y, crop_x + crop_w, crop_y + crop_h))

                        if img.is_background:
                            # Paste background without alpha
                            result_image.paste(cropped, (result_x, result_y))
                        else:
                            # Paste with alpha for foreground - ensure proper alpha handling
                            if cropped.mode == 'RGBA' and cropped.getchannel('A').getextrema()[1] > 0:
                                result_image.paste(cropped, (result_x, result_y), cropped.getchannel('A'))
                            else:
                                result_image.paste(cropped, (result_x, result_y))

            # Save the result with resolution info
            self._save_export_result(result_image, "bbox", f"{export_width}x{export_height}")

        except Exception as e:
            messagebox.showerror("Error", f"Export failed: {str(e)}")
            traceback.print_exc()

    def export_full_canvas(self):
        """Export the entire canvas content"""
        try:
            # Get canvas dimensions
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # Create new image
            result_image = Image.new('RGBA', (canvas_width, canvas_height), (0, 0, 0, 0))

            # Get all images, sorted by canvas z-order
            all_images = []
            if self.background_image:
                all_images.append(self.background_image)
            all_images.extend(self.images)

            # Sort images by their canvas z-order (bottom to top)
            def get_canvas_z_order(img):
                if not img.canvas_id:
                    return -1  # Background or invalid items go first
                try:
                    # Get all canvas items
                    all_items = self.canvas.find_all()
                    # Find the index of this image's canvas_id in the list
                    # Lower index = lower z-order (drawn first, appears behind)
                    return all_items.index(img.canvas_id)
                except (ValueError, AttributeError):
                    return -1

            all_images.sort(key=get_canvas_z_order)

            # Composite all images using original coordinates
            for img in all_images:
                img_bounds = img.get_original_bounds()  # Use original bounds
                export_image = img.get_export_image()   # Get export image

                if not img_bounds or not export_image:
                    continue

                # Calculate intersection with canvas
                intersect_x1 = max(0, img_bounds[0])
                intersect_y1 = max(0, img_bounds[1])
                intersect_x2 = min(canvas_width, img_bounds[2])
                intersect_y2 = min(canvas_height, img_bounds[3])

                if intersect_x1 < intersect_x2 and intersect_y1 < intersect_y2:
                    # Calculate position in result image
                    result_x = int(intersect_x1)
                    result_y = int(intersect_y1)

                    # Calculate crop area from source image
                    crop_x = int(intersect_x1 - img_bounds[0])
                    crop_y = int(intersect_y1 - img_bounds[1])
                    crop_w = int(intersect_x2 - intersect_x1)
                    crop_h = int(intersect_y2 - intersect_y1)

                    # Crop and paste
                    if (crop_x >= 0 and crop_y >= 0 and
                        crop_x + crop_w <= export_image.size[0] and
                        crop_y + crop_h <= export_image.size[1]):

                        cropped = export_image.crop((crop_x, crop_y, crop_x + crop_w, crop_y + crop_h))

                        if img.is_background:
                            # Paste background without alpha
                            result_image.paste(cropped, (result_x, result_y))
                        else:
                            # Paste with alpha for foreground - ensure proper alpha handling
                            if cropped.mode == 'RGBA' and cropped.getchannel('A').getextrema()[1] > 0:
                                result_image.paste(cropped, (result_x, result_y), cropped.getchannel('A'))
                            else:
                                result_image.paste(cropped, (result_x, result_y))

            # Save the result
            self._save_export_result(result_image, "full_canvas")

        except Exception as e:
            messagebox.showerror("Error", f"Full canvas export failed: {str(e)}")
            traceback.print_exc()

    def _save_export_result(self, result_image, export_type, resolution_info=None):
        """Helper method to save export results"""
        # Determine output path
        if self.images:
            first_image_path = self.images[0].image_path
        elif self.background_image:
            first_image_path = self.background_image.image_path
        else:
            messagebox.showwarning("Warning", "No images to export")
            return

        # Create output directory
        input_dir = os.path.dirname(first_image_path)
        output_dir = os.path.join(input_dir, "combined")
        os.makedirs(output_dir, exist_ok=True)

        # Generate filename with resolution info
        timestamp = int(time.time())
        if export_type == "bbox":
            if resolution_info:
                output_filename = f"photo_bash_bbox_{resolution_info}_{timestamp}.png"
            else:
                output_filename = f"photo_bash_bbox_{timestamp}.png"
        else:
            if resolution_info:
                output_filename = f"photo_bash_full_{resolution_info}_{timestamp}.png"
            else:
                output_filename = f"photo_bash_full_{timestamp}.png"
        output_path = os.path.join(output_dir, output_filename)

        # Save image with highest quality
        result_image.save(output_path, "PNG", optimize=True)

        # Store for clipboard operations
        self.last_export_path = output_path

        # Show resolution info in status
        actual_size = f"{result_image.size[0]}x{result_image.size[1]}"
        self.update_status(f"Exported: {output_filename} ({actual_size})")
        messagebox.showinfo("Success", f"Image exported to:\n{output_path}\nResolution: {actual_size}")

    def copy_to_clipboard(self):
        """Copy the composed image to clipboard"""
        if not hasattr(self, 'last_export_path') or not os.path.exists(self.last_export_path):
            # Export first if no recent export
            self.export_image()
            if not hasattr(self, 'last_export_path'):
                return

        try:
            # Copy image to clipboard
            image = Image.open(self.last_export_path)

            # Convert to bitmap for clipboard
            output = io.BytesIO()
            image.convert('RGB').save(output, 'BMP')
            data = output.getvalue()[14:]  # Remove BMP header
            output.close()

            # Copy to clipboard
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
            win32clipboard.CloseClipboard()

            self.update_status("Image copied to clipboard")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy to clipboard: {str(e)}")

    def copy_path(self):
        """Copy the last export path to clipboard"""
        if not hasattr(self, 'last_export_path'):
            messagebox.showwarning("Warning", "No exported image to copy path from")
            return

        try:
            # Copy path to clipboard
            win32clipboard.OpenClipboard()
            win32clipboard.EmptyClipboard()
            win32clipboard.SetClipboardText(self.last_export_path)
            win32clipboard.CloseClipboard()

            self.update_status("Path copied to clipboard")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to copy path: {str(e)}")

    def send_to_framepack(self):
        """Send the exported image to FramePack GUI to replace the current item"""
        # First export the image if not already done
        if not hasattr(self, 'last_export_path') or not os.path.exists(self.last_export_path):
            self.export_image()
            if not hasattr(self, 'last_export_path'):
                return

        try:
            # Create signal file for FramePack GUI to detect
            signal_file = "photo_bash_signal.txt"
            with open(signal_file, 'w') as f:
                f.write(self.last_export_path)

            self.update_status("Sent to FramePack - check FramePack GUI")
            messagebox.showinfo("Success", f"Image sent to FramePack GUI:\n{os.path.basename(self.last_export_path)}")

        except Exception as e:
            error_msg = f"Failed to send to FramePack: {str(e)}"
            print(error_msg)
            messagebox.showerror("Error", error_msg)
            self.update_status("Failed to send to FramePack")

    def load_config(self):
        """Load quick elements configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    self.quick_elements = config.get("quick_elements", {"element1": "", "element2": "", "element3": ""})
                    print(f"Loaded config: {self.quick_elements}")
            else:
                print("No config file found, using defaults")
        except Exception as e:
            print(f"Error loading config: {e}")
            self.quick_elements = {"element1": "", "element2": "", "element3": ""}

    def save_config(self):
        """Save quick elements configuration to file"""
        try:
            config = {"quick_elements": self.quick_elements}
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            print(f"Saved config: {self.quick_elements}")
        except Exception as e:
            print(f"Error saving config: {e}")

    def update_element_buttons(self):
        """Update the text of element buttons based on loaded paths"""
        buttons = {
            "element1": self.element1_button,
            "element2": self.element2_button,
            "element3": self.element3_button
        }

        for element_key, button in buttons.items():
            path = self.quick_elements.get(element_key, "")
            if path and os.path.exists(path):
                filename = os.path.basename(path)
                # Truncate long filenames
                if len(filename) > 15:
                    filename = filename[:12] + "..."
                button.configure(text=f"{element_key.title()}: {filename}")
            else:
                button.configure(text=f"{element_key.title()}: (Empty)")

    def load_element(self, element_key):
        """Load an image file for a quick element"""
        file_path = filedialog.askopenfilename(
            title=f"Select image for {element_key}",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.bmp *.gif *.tiff *.webp"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.quick_elements[element_key] = file_path
            self.save_config()
            self.update_element_buttons()
            self.update_status(f"Loaded {element_key}: {os.path.basename(file_path)}")

    def clear_element(self, element_key):
        """Clear a quick element's saved path"""
        self.quick_elements[element_key] = ""
        self.save_config()
        self.update_element_buttons()
        self.update_status(f"Cleared {element_key}")

    def add_quick_element(self, element_key):
        """Add a quick element to the scene"""
        path = self.quick_elements.get(element_key, "")

        if not path:
            self.update_status(f"{element_key} is empty - use Load button first")
            return

        if not os.path.exists(path):
            self.update_status(f"{element_key} file not found: {path}")
            # Clear the invalid path
            self.clear_element(element_key)
            return

        try:
            self.add_foreground_image(path)
            self.update_status(f"Added {element_key}: {os.path.basename(path)}")
        except Exception as e:
            self.update_status(f"Failed to add {element_key}: {str(e)}")

if __name__ == "__main__":
    try:
        root = TkinterDnD.Tk()
        app = PhotoBashingApp(root)

        # Load images from command line if provided
        if len(sys.argv) > 1:
            def delayed_load():
                try:
                    i = 1
                    while i < len(sys.argv):
                        arg = sys.argv[i]
                        if arg == "--background" and i + 1 < len(sys.argv):
                            # Next argument is the background image path
                            bg_path = sys.argv[i + 1]
                            if app.is_image_file(bg_path):
                                app.set_background_image(bg_path)
                            i += 2  # Skip both --background and the path
                        else:
                            # Regular foreground image
                            if app.is_image_file(arg):
                                app.add_foreground_image(arg)
                            i += 1
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to load images: {str(e)}")

            root.after(100, delayed_load)

        root.mainloop()

    except Exception as e:
        error_msg = f"Error: {str(e)}\n\n{traceback.format_exc()}"
        messagebox.showerror("Error", error_msg)
        raise