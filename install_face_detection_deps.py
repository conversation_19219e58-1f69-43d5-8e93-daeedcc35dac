#!/usr/bin/env python
"""
Install Enhanced Face Detection Dependencies

This script helps install the optional dependencies for enhanced face detection.
Run this to get the best face detection performance.
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr.strip()}")
        return False

def check_installation():
    """Check which packages are already installed."""
    print("Checking current installation status...")

    # Check MediaPipe
    try:
        import mediapipe
        print("✓ MediaPipe is already installed")
        mediapipe_installed = True
    except ImportError:
        print("❌ MediaPipe is not installed")
        mediapipe_installed = False
    except Exception as e:
        print(f"⚠️ MediaPipe is installed but has dependency issues: {str(e)[:100]}...")
        print("   This is often due to JAX/JAXlib version conflicts")
        print("   MediaPipe face detection may not work reliably")
        mediapipe_installed = False

    # Check face_recognition
    try:
        import face_recognition
        print("✓ face_recognition is already installed")
        face_recognition_installed = True
    except ImportError:
        print("❌ face_recognition is not installed")
        face_recognition_installed = False
    except Exception as e:
        print(f"⚠️ face_recognition is installed but has issues: {str(e)[:100]}...")
        face_recognition_installed = False

    # Check required dependencies
    try:
        import cv2
        import numpy as np
        from PIL import Image
        print("✓ Required dependencies (opencv-python, numpy, pillow) are installed")
        required_installed = True
    except ImportError as e:
        print(f"❌ Required dependency missing: {e}")
        required_installed = False

    return mediapipe_installed, face_recognition_installed, required_installed

def fix_mediapipe_dependencies():
    """Try to fix MediaPipe dependency conflicts."""
    print("\nAttempting to fix MediaPipe dependency conflicts...")

    # Common MediaPipe dependency fixes
    fixes = [
        ("pip install --upgrade jax jaxlib", "Updating JAX/JAXlib to compatible versions"),
        ("pip install mediapipe --force-reinstall --no-deps", "Reinstalling MediaPipe without dependencies"),
        ("pip install mediapipe==0.10.9", "Installing specific MediaPipe version"),
        ("pip uninstall -y jax jaxlib && pip install jax==0.4.12 jaxlib==0.4.12", "Installing compatible JAX versions")
    ]

    for command, description in fixes:
        print(f"\nTrying: {description}")
        success = run_command(command, description)
        if success:
            # Test if MediaPipe works now
            try:
                import mediapipe
                print("✓ MediaPipe dependency fix successful!")
                return True
            except Exception as e:
                print(f"Still having issues: {str(e)[:50]}...")
                continue

    print("❌ Could not automatically fix MediaPipe dependencies")
    print("MediaPipe face detection will be disabled, but other methods will still work")
    return False

def main():
    print("Enhanced Face Detection Dependencies Installer")
    print("=" * 50)
    
    mediapipe_ok, face_recognition_ok, required_ok = check_installation()
    
    if mediapipe_ok and face_recognition_ok and required_ok:
        print("\n🎉 All dependencies are already installed!")
        print("Your face detection should work at maximum performance.")
        return
    
    print("\nInstalling missing dependencies...")
    
    # Install required dependencies first
    if not required_ok:
        success = run_command(
            "pip install opencv-python numpy pillow",
            "Installing required dependencies (opencv-python, numpy, pillow)"
        )
        if not success:
            print("❌ Failed to install required dependencies. Please install manually.")
            return
    
    # Install MediaPipe
    if not mediapipe_ok:
        success = run_command(
            "pip install mediapipe",
            "Installing MediaPipe (Google's face detection)"
        )
        if not success:
            print("⚠️ MediaPipe installation failed. Face detection will still work but with reduced accuracy.")
        else:
            # Test if MediaPipe actually works (dependency conflicts are common)
            try:
                import mediapipe
                print("✓ MediaPipe installation verified")
            except Exception as e:
                print(f"⚠️ MediaPipe installed but has dependency conflicts: {str(e)[:50]}...")
                print("Attempting to fix dependency conflicts...")
                fix_mediapipe_dependencies()
    
    # Install face_recognition
    if not face_recognition_ok:
        print("\nInstalling face_recognition...")
        print("Note: This may take a while as it needs to compile dlib...")
        
        # Try different installation methods
        success = False
        
        # Method 1: Direct pip install
        if not success:
            success = run_command(
                "pip install face-recognition",
                "Installing face_recognition (Method 1: direct pip)"
            )
        
        # Method 2: Install cmake and dlib first (for Windows)
        if not success and sys.platform.startswith('win'):
            print("Trying Windows-specific installation method...")
            cmake_success = run_command(
                "pip install cmake",
                "Installing cmake (required for dlib)"
            )
            if cmake_success:
                dlib_success = run_command(
                    "pip install dlib",
                    "Installing dlib"
                )
                if dlib_success:
                    success = run_command(
                        "pip install face-recognition",
                        "Installing face_recognition (Method 2: after dlib)"
                    )
        
        if not success:
            print("⚠️ face_recognition installation failed.")
            print("This is common on some systems. Face detection will still work but with reduced accuracy.")
            print("\nManual installation options:")
            print("1. Try: conda install -c conda-forge dlib")
            print("2. Or visit: https://github.com/ageitgey/face_recognition#installation")
    
    print("\n" + "=" * 50)
    print("Installation Summary:")
    
    # Final check
    mediapipe_ok, face_recognition_ok, required_ok = check_installation()
    
    if required_ok:
        print("✓ Basic face detection will work")
    else:
        print("❌ Basic requirements not met")
    
    if mediapipe_ok:
        print("✓ Enhanced MediaPipe detection available")
    else:
        print("❌ MediaPipe detection not available")
    
    if face_recognition_ok:
        print("✓ Advanced face_recognition detection available")
    else:
        print("❌ face_recognition detection not available")
    
    print("\nYou can now test the enhanced face detection with:")
    print("python image_face_cropper.py [your_image.jpg]")

if __name__ == "__main__":
    main()
