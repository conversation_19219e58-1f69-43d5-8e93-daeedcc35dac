@echo off
:: FramePack Core Dependencies Installer
:: This batch file installs only the essential FramePack dependencies
:: with exact versions for optimal compatibility

title FramePack Core Dependencies Installer

echo ========================================
echo FramePack Core Dependencies Installer
echo ========================================
echo.
echo This will install only the essential FramePack dependencies
echo with exact versions for optimal compatibility.
echo.
echo Target Python Version: 3.10.11
echo Core packages: ~80 packages
echo.

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Python is not installed or not in the PATH.
    echo Please install Python 3.10.11 and make sure it's in your PATH.
    pause
    exit /b 1
)

:: Display Python version
echo Current Python version:
python --version
echo.

:: Check if we're in a virtual environment
python -c "import sys; print('Virtual environment detected' if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) else 'No virtual environment detected')"
echo.

:: Recommend virtual environment if not detected
python -c "import sys; exit(0 if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) else 1)" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo RECOMMENDATION: You are not in a virtual environment.
    echo It's recommended to create and activate a virtual environment first:
    echo.
    echo   python -m venv framepack_env
    echo   framepack_env\Scripts\activate
    echo.
    set /p continue="Continue anyway? (y/N): "
    if /i not "%continue%"=="y" (
        echo Installation cancelled.
        pause
        exit /b 0
    )
    echo.
)

set /p confirm="Do you want to continue with the core installation? (y/N): "
if /i not "%confirm%"=="y" (
    echo Installation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting core installation...
echo.

:: Upgrade pip first
echo ========================================
echo Upgrading pip...
echo ========================================
python -m pip install --upgrade pip

:: Install PyTorch first (special handling for CUDA version)
echo.
echo ========================================
echo Installing PyTorch with CUDA support...
echo ========================================
echo.

:: Try to install the exact dev versions first
echo Attempting to install PyTorch 2.8.0 dev versions...
pip install torch==2.8.0.dev20250627+cu128 torchvision==0.23.0.dev20250628+cu128 torchaudio==2.8.0.dev20250628+cu128 --index-url https://download.pytorch.org/whl/cu128

if %ERRORLEVEL% neq 0 (
    echo.
    echo Dev versions not available. Trying latest stable with CUDA 11.8...
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    
    if %ERRORLEVEL% neq 0 (
        echo.
        echo CUDA versions failed. Installing CPU-only versions...
        pip install torch torchvision torchaudio
        
        if %ERRORLEVEL% neq 0 (
            echo ERROR: Failed to install PyTorch. Please check your internet connection.
            pause
            exit /b 1
        )
    )
)

echo.
echo ========================================
echo Installing core dependencies...
echo ========================================
echo.

:: Install core packages from requirements file
pip install -r requirements-core-exact.txt

if %ERRORLEVEL% neq 0 (
    echo.
    echo WARNING: Some packages may have failed to install.
    echo Attempting to install with fallback versions...
    echo.
    
    :: Install critical packages individually with fallbacks
    echo Installing critical packages individually...
    
    pip install accelerate==1.6.0 || pip install accelerate
    pip install diffusers==0.33.1 || pip install diffusers
    pip install transformers==4.46.2 || pip install transformers
    pip install opencv-contrib-python==********* || pip install opencv-contrib-python
    pip install pillow==11.1.0 || pip install pillow
    pip install numpy==1.26.2 || pip install "numpy<2.0.0"
    pip install tkinterdnd2==0.4.3 || pip install tkinterdnd2
    pip install tkvideoplayer==2.3 || pip install tkvideoplayer
    pip install moviepy==1.0.3 || pip install moviepy
    pip install requests==2.31.0 || pip install requests
    pip install tqdm==4.67.1 || pip install tqdm
)

echo.
echo ========================================
echo Installation completed!
echo ========================================
echo.

:: Run dependency checker if available
if exist "check_dependencies.py" (
    echo Running dependency checker...
    echo.
    python check_dependencies.py
    echo.
) else (
    echo Dependency checker not found. You can run it manually later.
    echo.
)

echo ========================================
echo Installation Summary
echo ========================================
echo.
echo Core FramePack dependencies have been installed.
echo.
echo Next steps:
echo 1. Test the installation by running: python check_dependencies.py
echo 2. Try running the GUI: python framepack_gui.py
echo 3. For batch processing: python batch.py
echo.
echo If you encounter issues:
echo - Make sure you're using Python 3.10.11
echo - Consider using a virtual environment
echo - Check that CUDA is properly installed for GPU acceleration
echo.

pause
