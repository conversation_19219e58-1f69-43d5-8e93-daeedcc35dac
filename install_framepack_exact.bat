@echo off
:: FramePack Exact Version Installer
:: This batch file installs all FramePack dependencies with exact versions
:: as used in the working development environment

title FramePack Exact Version Installer

echo ========================================
echo FramePack Exact Version Installer
echo ========================================
echo.
echo This will install all FramePack dependencies with exact versions
echo as used in the working development environment.
echo.
echo Target Python Version: 3.10.11
echo Total packages: ~200+ packages
echo.
echo WARNING: This will install specific versions that may override
echo existing packages in your environment.
echo.

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Python is not installed or not in the PATH.
    echo Please install Python 3.10.11 and make sure it's in your PATH.
    pause
    exit /b 1
)

:: Display Python version
echo Current Python version:
python --version
echo.

:: Check if we're in a virtual environment
python -c "import sys; print('Virtual environment detected' if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) else 'No virtual environment detected')"
echo.

set /p confirm="Do you want to continue with the installation? (y/N): "
if /i not "%confirm%"=="y" (
    echo Installation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting installation...
echo.

:: Install PyTorch first (special handling for CUDA version)
echo ========================================
echo Installing PyTorch with CUDA support...
echo ========================================
echo.

:: Note: The exact dev versions may not be available via pip
:: Installing the closest stable versions with CUDA support
echo Installing PyTorch 2.8.0 dev versions...
echo Note: If dev versions are not available, this will install the latest stable versions

pip install torch==2.8.0.dev20250627+cu128 torchvision==0.23.0.dev20250628+cu128 torchaudio==2.8.0.dev20250628+cu128 --index-url https://download.pytorch.org/whl/cu128

if %ERRORLEVEL% neq 0 (
    echo.
    echo WARNING: PyTorch dev versions not available. Installing latest stable versions...
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to install PyTorch. Please check your internet connection.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo Installing remaining packages...
echo ========================================
echo.

:: Install all other packages from requirements file
pip install -r requirements-exact.txt

if %ERRORLEVEL% neq 0 (
    echo.
    echo WARNING: Some packages may have failed to install.
    echo This is normal for packages that are not available on PyPI
    echo or require special installation methods.
    echo.
    echo Continuing with verification...
)

echo.
echo ========================================
echo Installation completed!
echo ========================================
echo.

:: Run dependency checker
if exist "check_dependencies.py" (
    echo Running dependency checker...
    echo.
    python check_dependencies.py
) else (
    echo Dependency checker not found. Skipping verification.
)

echo.
echo ========================================
echo Installation Summary
echo ========================================
echo.
echo The installation is complete. Some notes:
echo.
echo 1. SageAttention: This package was installed from a local wheel file
echo    and may not be available via pip. You may need to install it separately.
echo.
echo 2. PyTorch: If dev versions were not available, stable versions were installed.
echo    This should still work for most FramePack functionality.
echo.
echo 3. Some optional packages may have failed to install if they're not
echo    compatible with your system or Python version.
echo.
echo 4. Run 'python check_dependencies.py' to verify all required packages
echo    are properly installed.
echo.

pause
