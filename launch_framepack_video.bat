@echo off
setlocal enabledelayedexpansion

:start_app
cls
echo Starting FramePack...
echo.
echo Press Ctrl+C to stop the application.
echo.

:: Activate the virtual environment
call venv\Scripts\activate.bat

:: Launch the application without opening the browser
:: Using 0.0.0.0 allows connections from other devices on the network
python demo_gradio_f1_video.py --server 0.0.0.0

:: Application has exited or was stopped with Ctrl+C
echo.
echo FramePack has been stopped.
echo.

:ask_restart
set /p restart_choice="Would you like to restart FramePack? (Y/N): "
if /i "!restart_choice!"=="Y" goto start_app
if /i "!restart_choice!"=="y" goto start_app
if /i "!restart_choice!"=="N" goto end
if /i "!restart_choice!"=="n" goto end

echo Invalid choice. Please enter Y or N.
goto ask_restart

:end
echo.
echo Exiting FramePack. Goodbye!
echo.
timeout /t 3 > nul
