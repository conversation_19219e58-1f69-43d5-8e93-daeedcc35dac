#!/usr/bin/env python3
"""
LoRA Merger GUI for FramePack Models

A simple GUI interface for merging LoRA weights into FramePack model weights.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
from pathlib import Path
import subprocess

# Import the merger functionality
from lora_merger import merge_single_lora, merge_multiple_loras, find_model_files, get_available_models

class LoRAMergerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("LoRA Merger for FramePack")
        self.root.geometry("800x700")
        
        # Variables
        self.model_var = tk.StringVar(value="lllyasviel/FramePackI2V_HY")
        self.output_dir_var = tk.StringVar(value="merged_models")
        
        # LoRA file variables
        self.lora_file_var = tk.StringVar()
        self.multiplier_var = tk.DoubleVar(value=0.8)
        
        self.lora_file_1_var = tk.StringVar()
        self.multiplier_1_var = tk.DoubleVar(value=0.8)
        self.lora_file_2_var = tk.StringVar()
        self.multiplier_2_var = tk.DoubleVar(value=0.8)
        self.lora_file_3_var = tk.StringVar()
        self.multiplier_3_var = tk.DoubleVar(value=0.8)
        
        self.merge_mode_var = tk.StringVar(value="single")
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # Title
        title_label = ttk.Label(main_frame, text="LoRA Merger for FramePack", font=("Arial", 16, "bold"))
        title_label.grid(row=row, column=0, columnspan=3, pady=(0, 20))
        row += 1
        
        # Model selection
        ttk.Label(main_frame, text="Base Model:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.model_combo = ttk.Combobox(main_frame, textvariable=self.model_var,
                                       state="readonly", width=50)
        self.model_combo.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)

        # Refresh models button
        ttk.Button(main_frame, text="Refresh Models", command=self.refresh_models).grid(row=row, column=2, padx=(5, 0), pady=5)
        row += 1
        
        # Output directory
        ttk.Label(main_frame, text="Output Directory:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_dir_var, width=40).grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(main_frame, text="Browse", command=self.browse_output_dir).grid(row=row, column=2, padx=(5, 0), pady=5)
        row += 1
        
        # Separator
        ttk.Separator(main_frame, orient='horizontal').grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        row += 1
        
        # Merge mode selection
        ttk.Label(main_frame, text="Merge Mode:", font=("Arial", 12, "bold")).grid(row=row, column=0, sticky=tk.W, pady=5)
        row += 1
        
        mode_frame = ttk.Frame(main_frame)
        mode_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Radiobutton(mode_frame, text="Single LoRA", variable=self.merge_mode_var, 
                       value="single", command=self.on_mode_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(mode_frame, text="Multiple LoRAs", variable=self.merge_mode_var, 
                       value="multiple", command=self.on_mode_change).pack(side=tk.LEFT)
        row += 1
        
        # Single LoRA frame
        self.single_frame = ttk.LabelFrame(main_frame, text="Single LoRA Settings", padding="10")
        self.single_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        self.single_frame.columnconfigure(1, weight=1)
        
        ttk.Label(self.single_frame, text="LoRA File:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(self.single_frame, textvariable=self.lora_file_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(self.single_frame, text="Browse", command=lambda: self.browse_lora_file(self.lora_file_var)).grid(row=0, column=2, padx=(5, 0), pady=5)
        
        ttk.Label(self.single_frame, text="Multiplier:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Scale(self.single_frame, from_=0.0, to=2.0, variable=self.multiplier_var, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Label(self.single_frame, textvariable=self.multiplier_var).grid(row=1, column=2, padx=(5, 0), pady=5)
        
        # Multiple LoRA frame
        self.multiple_frame = ttk.LabelFrame(main_frame, text="Multiple LoRA Settings", padding="10")
        self.multiple_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        self.multiple_frame.columnconfigure(1, weight=1)
        
        # LoRA 1
        ttk.Label(self.multiple_frame, text="LoRA 1:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(self.multiple_frame, textvariable=self.lora_file_1_var, width=30).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(self.multiple_frame, text="Browse", command=lambda: self.browse_lora_file(self.lora_file_1_var)).grid(row=0, column=2, padx=(5, 0), pady=5)
        ttk.Scale(self.multiple_frame, from_=0.0, to=2.0, variable=self.multiplier_1_var, orient=tk.HORIZONTAL, length=100).grid(row=0, column=3, padx=(5, 0), pady=5)
        ttk.Label(self.multiple_frame, textvariable=self.multiplier_1_var).grid(row=0, column=4, padx=(5, 0), pady=5)
        
        # LoRA 2
        ttk.Label(self.multiple_frame, text="LoRA 2:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(self.multiple_frame, textvariable=self.lora_file_2_var, width=30).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(self.multiple_frame, text="Browse", command=lambda: self.browse_lora_file(self.lora_file_2_var)).grid(row=1, column=2, padx=(5, 0), pady=5)
        ttk.Scale(self.multiple_frame, from_=0.0, to=2.0, variable=self.multiplier_2_var, orient=tk.HORIZONTAL, length=100).grid(row=1, column=3, padx=(5, 0), pady=5)
        ttk.Label(self.multiple_frame, textvariable=self.multiplier_2_var).grid(row=1, column=4, padx=(5, 0), pady=5)
        
        # LoRA 3
        ttk.Label(self.multiple_frame, text="LoRA 3:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(self.multiple_frame, textvariable=self.lora_file_3_var, width=30).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(self.multiple_frame, text="Browse", command=lambda: self.browse_lora_file(self.lora_file_3_var)).grid(row=2, column=2, padx=(5, 0), pady=5)
        ttk.Scale(self.multiple_frame, from_=0.0, to=2.0, variable=self.multiplier_3_var, orient=tk.HORIZONTAL, length=100).grid(row=2, column=3, padx=(5, 0), pady=5)
        ttk.Label(self.multiple_frame, textvariable=self.multiplier_3_var).grid(row=2, column=4, padx=(5, 0), pady=5)
        
        row += 1
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=3, pady=20)
        
        self.merge_button = ttk.Button(button_frame, text="Start Merge", command=self.start_merge)
        self.merge_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Check Model Cache", command=self.check_model_cache).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Open Output Folder", command=self.open_output_folder).pack(side=tk.LEFT)
        
        row += 1
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Ready")
        ttk.Label(main_frame, text="Status:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Label(main_frame, textvariable=self.progress_var).grid(row=row, column=1, columnspan=2, sticky=tk.W, pady=5)
        row += 1
        
        self.progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress_bar.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        row += 1
        
        # Log output
        ttk.Label(main_frame, text="Log Output:", font=("Arial", 10, "bold")).grid(row=row, column=0, sticky=tk.W, pady=(10, 5))
        row += 1
        
        self.log_text = scrolledtext.ScrolledText(main_frame, height=10, width=80)
        self.log_text.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        main_frame.rowconfigure(row, weight=1)
        
        # Initial mode setup
        self.on_mode_change()

        # Load available models
        self.refresh_models()
        
    def on_mode_change(self):
        """Handle merge mode change"""
        if self.merge_mode_var.get() == "single":
            self.single_frame.grid()
            self.multiple_frame.grid_remove()
        else:
            self.single_frame.grid_remove()
            self.multiple_frame.grid()
    
    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir_var.set(directory)
    
    def browse_lora_file(self, var):
        """Browse for LoRA file"""
        filename = filedialog.askopenfilename(
            title="Select LoRA File",
            filetypes=[("SafeTensors files", "*.safetensors"), ("All files", "*.*")]
        )
        if filename:
            var.set(filename)

    def refresh_models(self):
        """Refresh the list of available models"""
        try:
            # Get merged models directory from output directory
            merged_models_dir = self.output_dir_var.get()
            available_models = get_available_models(merged_models_dir)

            # Update combo box values
            self.model_combo['values'] = available_models

            # Set default if no current selection
            if not self.model_var.get() and available_models:
                self.model_var.set(available_models[0])

            self.log(f"Found {len(available_models)} available models")

            # Log model types
            standard_count = sum(1 for m in available_models if not os.path.isdir(m))
            merged_count = sum(1 for m in available_models if os.path.isdir(m))

            if standard_count > 0:
                self.log(f"  - {standard_count} standard models")
            if merged_count > 0:
                self.log(f"  - {merged_count} merged models")

        except Exception as e:
            self.log(f"Error refreshing models: {str(e)}")
    
    def log(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def check_model_cache(self):
        """Check if models are available in cache"""
        self.log("Checking model cache...")
        
        for model_name in ["lllyasviel/FramePackI2V_HY", "lllyasviel/FramePack_F1_I2V_HY_20250503"]:
            try:
                _, safetensors_files, config_file = find_model_files(model_name)
                self.log(f"✓ {model_name}: Found {len(safetensors_files)} files")
            except FileNotFoundError as e:
                self.log(f"✗ {model_name}: {str(e)}")
    
    def open_output_folder(self):
        """Open output folder in file explorer"""
        output_dir = self.output_dir_var.get()
        if os.path.exists(output_dir):
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                subprocess.run(["open", output_dir])
            else:
                subprocess.run(["xdg-open", output_dir])
        else:
            messagebox.showwarning("Warning", f"Output directory does not exist: {output_dir}")
    
    def validate_inputs(self):
        """Validate user inputs"""
        if self.merge_mode_var.get() == "single":
            if not self.lora_file_var.get():
                messagebox.showerror("Error", "Please select a LoRA file")
                return False
            if not os.path.exists(self.lora_file_var.get()):
                messagebox.showerror("Error", f"LoRA file not found: {self.lora_file_var.get()}")
                return False
        else:
            lora_files = [self.lora_file_1_var.get(), self.lora_file_2_var.get(), self.lora_file_3_var.get()]
            valid_files = [f for f in lora_files if f and os.path.exists(f)]
            
            if not valid_files:
                messagebox.showerror("Error", "Please select at least one valid LoRA file")
                return False
        
        return True
    
    def start_merge(self):
        """Start the merge process in a separate thread"""
        if not self.validate_inputs():
            return
        
        self.merge_button.config(state='disabled')
        self.progress_bar.start()
        self.progress_var.set("Merging...")
        self.log_text.delete(1.0, tk.END)
        
        # Start merge in separate thread
        thread = threading.Thread(target=self.run_merge)
        thread.daemon = True
        thread.start()
    
    def run_merge(self):
        """Run the actual merge process"""
        try:
            model_name = self.model_var.get()
            output_dir = self.output_dir_var.get()
            
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            
            if self.merge_mode_var.get() == "single":
                # Single LoRA merge
                lora_file = self.lora_file_var.get()
                multiplier = self.multiplier_var.get()
                
                self.log(f"Starting single LoRA merge...")
                self.log(f"Model: {model_name}")
                self.log(f"LoRA: {lora_file}")
                self.log(f"Multiplier: {multiplier}")
                
                output_path = merge_single_lora(model_name, lora_file, multiplier, output_dir)
                
            else:
                # Multiple LoRA merge
                lora_configs = []
                
                if self.lora_file_1_var.get() and os.path.exists(self.lora_file_1_var.get()):
                    lora_configs.append((self.lora_file_1_var.get(), self.multiplier_1_var.get()))
                
                if self.lora_file_2_var.get() and os.path.exists(self.lora_file_2_var.get()):
                    lora_configs.append((self.lora_file_2_var.get(), self.multiplier_2_var.get()))
                
                if self.lora_file_3_var.get() and os.path.exists(self.lora_file_3_var.get()):
                    lora_configs.append((self.lora_file_3_var.get(), self.multiplier_3_var.get()))
                
                self.log(f"Starting multiple LoRA merge...")
                self.log(f"Model: {model_name}")
                self.log(f"LoRAs: {len(lora_configs)} files")
                
                output_path = merge_multiple_loras(model_name, lora_configs, output_dir)
            
            self.log(f"✓ Merge completed successfully!")
            self.log(f"Output saved to: {output_path}")
            
            # Show success message
            self.root.after(0, lambda: messagebox.showinfo("Success", 
                f"LoRA merge completed successfully!\n\nOutput saved to:\n{output_path}"))
            
        except Exception as e:
            error_msg = f"Error during merge: {str(e)}"
            self.log(f"✗ {error_msg}")
            
            # Show error message
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))
        
        finally:
            # Re-enable UI
            self.root.after(0, self.merge_finished)
    
    def merge_finished(self):
        """Called when merge is finished"""
        self.progress_bar.stop()
        self.progress_var.set("Ready")
        self.merge_button.config(state='normal')

def main():
    root = tk.Tk()
    app = LoRAMergerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
