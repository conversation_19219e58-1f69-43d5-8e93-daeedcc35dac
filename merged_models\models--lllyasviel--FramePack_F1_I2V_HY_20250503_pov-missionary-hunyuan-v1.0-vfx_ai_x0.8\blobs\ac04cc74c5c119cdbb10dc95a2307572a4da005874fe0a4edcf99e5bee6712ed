{"metadata": {"total_size": 25748629632}, "weight_map": {"context_embedder.proj_in.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.proj_in.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.time_text_embed.text_embedder.linear_1.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.time_text_embed.text_embedder.linear_1.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.time_text_embed.text_embedder.linear_2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.time_text_embed.text_embedder.linear_2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.time_text_embed.timestep_embedder.linear_1.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.time_text_embed.timestep_embedder.linear_1.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.time_text_embed.timestep_embedder.linear_2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.time_text_embed.timestep_embedder.linear_2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.attn.to_k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.attn.to_out.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.attn.to_q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.attn.to_v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.ff.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.ff.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.ff.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.ff.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.norm1.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.norm1.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.norm2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.norm2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.norm_out.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.0.norm_out.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.attn.to_k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.attn.to_out.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.attn.to_q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.attn.to_v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.ff.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.ff.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.ff.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.ff.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.norm1.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.norm1.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.norm2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.norm2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.norm_out.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "context_embedder.token_refiner.refiner_blocks.1.norm_out.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.guidance_embedder.linear_1.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.guidance_embedder.linear_1.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.guidance_embedder.linear_2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.guidance_embedder.linear_2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.text_embedder.linear_1.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.text_embedder.linear_1.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.text_embedder.linear_2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.text_embedder.linear_2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.timestep_embedder.linear_1.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.timestep_embedder.linear_1.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.timestep_embedder.linear_2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_text_embed.timestep_embedder.linear_2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.add_k_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.add_k_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.add_q_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.add_q_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.add_v_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.add_v_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.norm_added_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.norm_added_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.to_add_out.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.to_add_out.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.to_k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.to_out.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.to_q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.to_v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.ff.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.ff.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.ff.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.ff.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.ff_context.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.ff_context.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.norm1_context.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.0.norm1_context.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.add_k_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.add_k_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.add_q_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.add_q_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.add_v_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.add_v_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.norm_added_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.norm_added_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.to_add_out.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.to_add_out.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.to_k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.to_out.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.to_q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.to_v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.ff.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.ff.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.ff.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.ff.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.ff_context.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.ff_context.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.norm1_context.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.1.norm1_context.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.add_k_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.add_k_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.add_q_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.add_q_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.add_v_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.add_v_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.norm_added_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.norm_added_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.to_add_out.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.to_add_out.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.to_k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.to_out.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.to_q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.to_v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.ff.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.ff.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.ff.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.ff.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.ff_context.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.ff_context.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.norm1_context.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.10.norm1_context.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.add_k_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.add_k_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.add_q_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.add_q_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.add_v_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.add_v_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.norm_added_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.norm_added_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.to_add_out.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.to_add_out.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.to_k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.to_out.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.to_q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.to_v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.ff.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.ff.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.ff.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.ff.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.ff_context.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.ff_context.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.norm1_context.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.11.norm1_context.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.add_k_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.add_k_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.add_q_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.add_q_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.add_v_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.add_v_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.norm_added_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.norm_added_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.to_add_out.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.to_add_out.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.to_k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.to_out.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.to_q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.to_v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.ff.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.ff.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.ff.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.ff.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.ff_context.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.ff_context.net.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.norm1_context.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.12.norm1_context.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.add_k_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.add_k_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.add_q_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.add_q_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.add_v_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.add_v_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.norm_added_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.norm_added_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.to_add_out.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.to_add_out.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.to_k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.to_out.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.to_q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.to_v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.ff.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.ff.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.norm1.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.norm1.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.norm1_context.linear.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.13.norm1_context.linear.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.add_k_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.add_k_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.add_q_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.add_q_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.add_v_proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.add_v_proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.norm_added_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.norm_added_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.to_add_out.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.to_add_out.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.to_k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.to_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.to_out.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.to_out.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.to_q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.to_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.to_v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.attn.to_v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.ff.net.0.proj.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.ff.net.0.proj.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.ff.net.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "transformer_blocks.2.ff.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.2.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.2.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.2.ff_context.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.2.ff_context.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.2.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.2.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.2.norm1_context.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.2.norm1_context.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.add_k_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.add_k_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.add_q_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.add_q_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.add_v_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.add_v_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.norm_added_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.norm_added_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.to_add_out.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.to_add_out.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.to_k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.to_out.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.to_q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.to_v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.ff.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.ff.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.ff.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.ff.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.ff_context.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.ff_context.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.norm1_context.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.3.norm1_context.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.add_k_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.add_k_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.add_q_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.add_q_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.add_v_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.add_v_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.norm_added_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.norm_added_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.to_add_out.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.to_add_out.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.to_k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.to_out.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.to_q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.to_v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.ff.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.ff.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.ff.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.ff.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.ff_context.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.ff_context.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.norm1_context.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.4.norm1_context.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.add_k_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.add_k_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.add_q_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.add_q_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.add_v_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.add_v_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.norm_added_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.norm_added_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.to_add_out.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.to_add_out.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.to_k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.to_out.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.to_q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.to_v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.ff.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.ff.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.ff.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.ff.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.ff_context.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.ff_context.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.norm1_context.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.5.norm1_context.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.add_k_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.add_k_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.add_q_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.add_q_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.add_v_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.add_v_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.norm_added_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.norm_added_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.to_add_out.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.to_add_out.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.to_k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.to_out.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.to_q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.to_v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.ff.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.ff.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.ff.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.ff.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.ff_context.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.ff_context.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.norm1_context.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.6.norm1_context.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.add_k_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.add_k_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.add_q_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.add_q_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.add_v_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.add_v_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.norm_added_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.norm_added_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.to_add_out.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.to_add_out.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.to_k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.to_out.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.to_q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.to_v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.ff.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.ff.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.ff.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.ff.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.ff_context.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.ff_context.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.norm1_context.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.7.norm1_context.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.add_k_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.add_k_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.add_q_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.add_q_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.add_v_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.add_v_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.norm_added_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.norm_added_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.to_add_out.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.to_add_out.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.to_k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.to_out.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.to_q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.to_v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.ff.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.ff.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.ff.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.ff.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.ff_context.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.ff_context.net.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.norm1.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.norm1.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.norm1_context.linear.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.8.norm1_context.linear.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.add_k_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.add_k_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.add_q_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.add_q_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.add_v_proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.add_v_proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.norm_added_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.norm_added_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.to_add_out.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.to_add_out.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.to_k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.to_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.to_out.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.to_out.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.to_q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.to_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.to_v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.attn.to_v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.ff.net.0.proj.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.ff.net.0.proj.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.ff.net.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "transformer_blocks.9.ff.net.2.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "transformer_blocks.9.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "transformer_blocks.9.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "transformer_blocks.9.ff_context.net.2.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "transformer_blocks.9.ff_context.net.2.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "transformer_blocks.9.norm1.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "transformer_blocks.9.norm1.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "transformer_blocks.9.norm1_context.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "transformer_blocks.9.norm1_context.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "x_embedder.proj.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "x_embedder.proj.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.0.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.1.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.10.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.11.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.12.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.13.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.14.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.15.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.16.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.17.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.18.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.19.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.2.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.20.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.20.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.20.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.20.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.3.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.norm.linear.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.proj_mlp.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.proj_mlp.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.proj_out.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.4.proj_out.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.5.attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.5.attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.5.attn.to_k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.5.attn.to_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.5.attn.to_q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.5.attn.to_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.5.attn.to_v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.5.attn.to_v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.5.norm.linear.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "single_transformer_blocks.5.norm.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.5.proj_mlp.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.5.proj_mlp.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.5.proj_out.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.5.proj_out.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.attn.to_k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.attn.to_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.attn.to_q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.attn.to_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.attn.to_v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.attn.to_v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.norm.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.norm.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.proj_mlp.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.proj_mlp.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.proj_out.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.6.proj_out.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.attn.to_k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.attn.to_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.attn.to_q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.attn.to_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.attn.to_v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.attn.to_v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.norm.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.norm.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.proj_mlp.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.proj_mlp.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.proj_out.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.7.proj_out.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.attn.to_k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.attn.to_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.attn.to_q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.attn.to_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.attn.to_v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.attn.to_v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.norm.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.norm.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.proj_mlp.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.proj_mlp.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.proj_out.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.8.proj_out.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.attn.to_k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.attn.to_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.attn.to_q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.attn.to_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.attn.to_v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.attn.to_v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.norm.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.norm.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.proj_mlp.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.proj_mlp.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.proj_out.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "single_transformer_blocks.9.proj_out.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.13.ff.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.13.ff.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.13.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.13.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.13.ff_context.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.13.ff_context.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.add_k_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.add_k_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.add_q_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.add_q_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.add_v_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.add_v_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.norm_added_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.norm_added_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.to_add_out.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.to_add_out.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.to_k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.to_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.to_out.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.to_out.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.to_q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.to_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.to_v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.attn.to_v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.ff.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.ff.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.ff.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.ff.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.ff_context.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.ff_context.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.norm1.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.norm1.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.norm1_context.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.14.norm1_context.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.add_k_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.add_k_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.add_q_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.add_q_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.add_v_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.add_v_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.norm_added_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.norm_added_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.to_add_out.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.to_add_out.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.to_k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.to_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.to_out.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.to_out.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.to_q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.to_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.to_v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.attn.to_v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.ff.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.ff.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.ff.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.ff.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.ff_context.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.ff_context.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.norm1.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.norm1.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.norm1_context.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.15.norm1_context.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.add_k_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.add_k_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.add_q_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.add_q_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.add_v_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.add_v_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.norm_added_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.norm_added_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.to_add_out.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.to_add_out.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.to_k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.to_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.to_out.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.to_out.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.to_q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.to_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.to_v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.attn.to_v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.ff.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.ff.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.ff.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.ff.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.ff_context.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.ff_context.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.norm1.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.norm1.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.norm1_context.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.16.norm1_context.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.add_k_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.add_k_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.add_q_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.add_q_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.add_v_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.add_v_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.norm_added_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.norm_added_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.to_add_out.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.to_add_out.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.to_k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.to_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.to_out.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.to_out.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.to_q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.to_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.to_v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.attn.to_v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.ff.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.ff.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.ff.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.ff.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.ff_context.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.ff_context.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.norm1.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.norm1.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.norm1_context.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.17.norm1_context.linear.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.add_k_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.add_k_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.add_q_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.add_q_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.add_v_proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.add_v_proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.norm_added_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.norm_added_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.to_add_out.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.to_add_out.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.to_k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.to_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.to_out.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.to_out.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.to_q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.to_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.to_v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.attn.to_v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.ff.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.ff.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.ff.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.ff.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.ff_context.net.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.ff_context.net.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.norm1.linear.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "transformer_blocks.18.norm1.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.18.norm1_context.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.18.norm1_context.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.add_k_proj.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.add_k_proj.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.add_q_proj.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.add_q_proj.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.add_v_proj.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.add_v_proj.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.norm_added_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.norm_added_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.to_add_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.to_add_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.to_out.0.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.to_out.0.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.ff.net.0.proj.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.ff.net.0.proj.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.ff.net.2.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.ff.net.2.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.ff_context.net.0.proj.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.ff_context.net.0.proj.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.ff_context.net.2.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.ff_context.net.2.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.norm1.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.norm1.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.norm1_context.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "transformer_blocks.19.norm1_context.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "clean_x_embedder.proj.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "clean_x_embedder.proj.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "clean_x_embedder.proj_2x.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "clean_x_embedder.proj_2x.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "clean_x_embedder.proj_4x.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "clean_x_embedder.proj_4x.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "image_projection.down.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "image_projection.down.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "image_projection.up.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "image_projection.up.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "norm_out.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "norm_out.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.20.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.20.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.20.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.20.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.20.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.20.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.20.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.20.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.20.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.20.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.21.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.22.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.23.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.24.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.25.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.26.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.27.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.28.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.29.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.30.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.31.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.proj_mlp.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.proj_out.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.32.proj_out.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.attn.to_k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.attn.to_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.attn.to_q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.attn.to_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.attn.to_v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.attn.to_v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.norm.linear.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.norm.linear.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.proj_mlp.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "single_transformer_blocks.33.proj_mlp.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.33.proj_out.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.33.proj_out.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.attn.to_k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.attn.to_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.attn.to_q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.attn.to_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.attn.to_v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.attn.to_v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.norm.linear.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.norm.linear.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.proj_mlp.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.proj_mlp.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.proj_out.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.34.proj_out.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.attn.to_k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.attn.to_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.attn.to_q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.attn.to_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.attn.to_v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.attn.to_v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.norm.linear.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.norm.linear.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.proj_mlp.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.proj_mlp.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.proj_out.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.35.proj_out.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.attn.to_k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.attn.to_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.attn.to_q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.attn.to_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.attn.to_v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.attn.to_v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.norm.linear.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.norm.linear.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.proj_mlp.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.proj_mlp.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.proj_out.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.36.proj_out.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.attn.to_k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.attn.to_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.attn.to_q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.attn.to_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.attn.to_v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.attn.to_v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.norm.linear.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.norm.linear.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.proj_mlp.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.proj_mlp.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.proj_out.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.37.proj_out.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.attn.to_k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.attn.to_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.attn.to_q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.attn.to_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.attn.to_v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.attn.to_v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.norm.linear.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.norm.linear.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.proj_mlp.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.proj_mlp.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.proj_out.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.38.proj_out.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.attn.to_k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.attn.to_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.attn.to_q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.attn.to_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.attn.to_v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.attn.to_v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.norm.linear.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.norm.linear.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.proj_mlp.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.proj_mlp.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.proj_out.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "single_transformer_blocks.39.proj_out.weight": "diffusion_pytorch_model-00006-of-00006.safetensors"}}