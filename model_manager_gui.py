#!/usr/bin/env python3
"""
FramePack Model Manager GUI

A comprehensive model management interface that combines:
- LoRA merging functionality
- Local HunyuanVideo model management
- Model swapping and organization
- Backup and restore capabilities
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
import shutil
import subprocess

from datetime import datetime


# Import the merger functionality
from lora_merger import merge_single_lora, merge_multiple_loras, find_model_files, get_available_models, find_merged_models

class ModelManagerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("FramePack Model Manager")
        self.root.geometry("1200x800")
        
        # Model management variables
        self.hf_cache_dir = os.environ.get('HF_HOME', os.path.join(os.path.dirname(__file__), 'hf_download'))
        self.models_config_file = os.path.join(os.path.dirname(__file__), 'model_manager_config.json')
        self.backup_dir = os.path.join(os.path.dirname(__file__), 'model_backups')
        
        # GUI variables
        self.current_model_var = tk.StringVar()
        self.output_dir_var = tk.StringVar(value="merged_models")
        
        # LoRA variables
        self.lora_file_var = tk.StringVar()
        self.multiplier_var = tk.DoubleVar(value=0.8)
        self.lora_file_1_var = tk.StringVar()
        self.multiplier_1_var = tk.DoubleVar(value=0.8)
        self.lora_file_2_var = tk.StringVar()
        self.multiplier_2_var = tk.DoubleVar(value=0.8)
        self.lora_file_3_var = tk.StringVar()
        self.multiplier_3_var = tk.DoubleVar(value=0.8)
        self.merge_mode_var = tk.StringVar(value="single")
        
        # Initialize
        self.ensure_directories()
        self.load_config()
        self.setup_ui()
        self.refresh_model_list()
        
    def ensure_directories(self):
        """Ensure required directories exist"""
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(os.path.dirname(self.models_config_file), exist_ok=True)
        
    def load_config(self):
        """Load model manager configuration"""
        if os.path.exists(self.models_config_file):
            try:
                with open(self.models_config_file, 'r') as f:
                    self.config = json.load(f)
            except:
                self.config = {}
        else:
            self.config = {}
            
        # Set defaults
        if 'active_models' not in self.config:
            self.config['active_models'] = {}
        if 'model_aliases' not in self.config:
            self.config['model_aliases'] = {}
        if 'backup_history' not in self.config:
            self.config['backup_history'] = []
            
    def save_config(self):
        """Save model manager configuration"""
        try:
            with open(self.models_config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save config: {e}")
    
    def setup_ui(self):
        """Setup the main UI"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Model Manager Tab
        self.model_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.model_tab, text="Model Manager")
        self.setup_model_tab()
        
        # LoRA Merger Tab
        self.lora_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.lora_tab, text="LoRA Merger")
        self.setup_lora_tab()
        
        # Settings Tab
        self.settings_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.settings_tab, text="Settings")
        self.setup_settings_tab()
        
    def setup_model_tab(self):
        """Setup the model management tab"""
        main_frame = ttk.Frame(self.model_tab, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="FramePack Model Manager", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Model list frame
        list_frame = ttk.LabelFrame(main_frame, text="Available Models", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Model list with scrollbar
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        # Treeview for model list
        columns = ('Name', 'Type', 'Status', 'Size', 'Modified')
        self.model_tree = ttk.Treeview(list_container, columns=columns, show='headings', height=10)
        
        # Configure columns
        self.model_tree.heading('Name', text='Model Name')
        self.model_tree.heading('Type', text='Type')
        self.model_tree.heading('Status', text='Status')
        self.model_tree.heading('Size', text='Size')
        self.model_tree.heading('Modified', text='Last Modified')
        
        self.model_tree.column('Name', width=300)
        self.model_tree.column('Type', width=100)
        self.model_tree.column('Status', width=100)
        self.model_tree.column('Size', width=100)
        self.model_tree.column('Modified', width=150)
        
        # Scrollbar for treeview
        tree_scroll = ttk.Scrollbar(list_container, orient=tk.VERTICAL, command=self.model_tree.yview)
        self.model_tree.configure(yscrollcommand=tree_scroll.set)
        
        self.model_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Model actions frame
        actions_frame = ttk.Frame(main_frame)
        actions_frame.pack(fill=tk.X, pady=10)
        
        # Left side buttons
        left_buttons = ttk.Frame(actions_frame)
        left_buttons.pack(side=tk.LEFT)
        
        ttk.Button(left_buttons, text="Refresh List", command=self.refresh_model_list).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="Set as Active", command=self.set_active_model).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="Create Backup", command=self.backup_model).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(left_buttons, text="Restore Backup", command=self.restore_backup).pack(side=tk.LEFT, padx=(0, 5))
        
        # Right side buttons
        right_buttons = ttk.Frame(actions_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        ttk.Button(right_buttons, text="Import Model", command=self.import_model).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(right_buttons, text="Export Model", command=self.export_model).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(right_buttons, text="Delete Model", command=self.delete_model).pack(side=tk.LEFT)
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Current Status", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Current active model
        ttk.Label(status_frame, text="Active FramePack Model:").pack(anchor=tk.W)
        self.active_framepack_label = ttk.Label(status_frame, text="Not Set", font=("Arial", 10, "bold"))
        self.active_framepack_label.pack(anchor=tk.W, padx=(20, 0))
        
        ttk.Label(status_frame, text="Active F1 Model:").pack(anchor=tk.W, pady=(5, 0))
        self.active_f1_label = ttk.Label(status_frame, text="Not Set", font=("Arial", 10, "bold"))
        self.active_f1_label.pack(anchor=tk.W, padx=(20, 0))
        
        # Log output for model tab
        log_frame = ttk.LabelFrame(main_frame, text="Activity Log", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.model_log = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.model_log.pack(fill=tk.BOTH, expand=True)
        
    def setup_lora_tab(self):
        """Setup the LoRA merger tab"""
        main_frame = ttk.Frame(self.lora_tab, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # Title
        title_label = ttk.Label(main_frame, text="LoRA Merger", font=("Arial", 16, "bold"))
        title_label.grid(row=row, column=0, columnspan=3, pady=(0, 20))
        row += 1
        
        # Model selection
        ttk.Label(main_frame, text="Target Model:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.target_model_combo = ttk.Combobox(main_frame, textvariable=self.current_model_var, 
                                              state="readonly", width=50)
        self.target_model_combo.grid(row=row, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        row += 1
        
        # Output directory
        ttk.Label(main_frame, text="Output Directory:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_dir_var, width=40).grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(main_frame, text="Browse", command=self.browse_output_dir).grid(row=row, column=2, padx=(5, 0), pady=5)
        row += 1
        
        # Separator
        ttk.Separator(main_frame, orient='horizontal').grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        row += 1
        
        # Merge mode selection
        ttk.Label(main_frame, text="Merge Mode:", font=("Arial", 12, "bold")).grid(row=row, column=0, sticky=tk.W, pady=5)
        row += 1
        
        mode_frame = ttk.Frame(main_frame)
        mode_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Radiobutton(mode_frame, text="Single LoRA", variable=self.merge_mode_var, 
                       value="single", command=self.on_mode_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(mode_frame, text="Multiple LoRAs", variable=self.merge_mode_var, 
                       value="multiple", command=self.on_mode_change).pack(side=tk.LEFT)
        row += 1
        
        # Single LoRA frame
        self.single_frame = ttk.LabelFrame(main_frame, text="Single LoRA Settings", padding="10")
        self.single_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        self.single_frame.columnconfigure(1, weight=1)
        
        ttk.Label(self.single_frame, text="LoRA File:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(self.single_frame, textvariable=self.lora_file_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(self.single_frame, text="Browse", command=lambda: self.browse_lora_file(self.lora_file_var)).grid(row=0, column=2, padx=(5, 0), pady=5)
        
        ttk.Label(self.single_frame, text="Multiplier:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Scale(self.single_frame, from_=0.0, to=2.0, variable=self.multiplier_var, orient=tk.HORIZONTAL).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Label(self.single_frame, textvariable=self.multiplier_var).grid(row=1, column=2, padx=(5, 0), pady=5)
        
        # Multiple LoRA frame
        self.multiple_frame = ttk.LabelFrame(main_frame, text="Multiple LoRA Settings", padding="10")
        self.multiple_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        self.multiple_frame.columnconfigure(1, weight=1)
        
        # LoRA entries (similar to previous implementation)
        for i, (file_var, mult_var) in enumerate([(self.lora_file_1_var, self.multiplier_1_var),
                                                  (self.lora_file_2_var, self.multiplier_2_var),
                                                  (self.lora_file_3_var, self.multiplier_3_var)], 1):
            ttk.Label(self.multiple_frame, text=f"LoRA {i}:").grid(row=i-1, column=0, sticky=tk.W, pady=5)
            ttk.Entry(self.multiple_frame, textvariable=file_var, width=30).grid(row=i-1, column=1, sticky=(tk.W, tk.E), pady=5)
            ttk.Button(self.multiple_frame, text="Browse", command=lambda v=file_var: self.browse_lora_file(v)).grid(row=i-1, column=2, padx=(5, 0), pady=5)
            ttk.Scale(self.multiple_frame, from_=0.0, to=2.0, variable=mult_var, orient=tk.HORIZONTAL, length=100).grid(row=i-1, column=3, padx=(5, 0), pady=5)
            ttk.Label(self.multiple_frame, textvariable=mult_var).grid(row=i-1, column=4, padx=(5, 0), pady=5)
        
        row += 1
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=3, pady=20)
        
        self.merge_button = ttk.Button(button_frame, text="Start Merge", command=self.start_merge)
        self.merge_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Install Merged Model", command=self.install_merged_model).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Open Output Folder", command=self.open_output_folder).pack(side=tk.LEFT)
        
        row += 1
        
        # Progress
        self.lora_progress_var = tk.StringVar(value="Ready")
        ttk.Label(main_frame, text="Status:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Label(main_frame, textvariable=self.lora_progress_var).grid(row=row, column=1, columnspan=2, sticky=tk.W, pady=5)
        row += 1
        
        self.lora_progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        self.lora_progress_bar.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        row += 1
        
        # Log output
        ttk.Label(main_frame, text="Merge Log:", font=("Arial", 10, "bold")).grid(row=row, column=0, sticky=tk.W, pady=(10, 5))
        row += 1
        
        self.lora_log = scrolledtext.ScrolledText(main_frame, height=10, width=80)
        self.lora_log.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        main_frame.rowconfigure(row, weight=1)
        
        # Initial mode setup
        self.on_mode_change()
        
    def setup_settings_tab(self):
        """Setup the settings tab"""
        main_frame = ttk.Frame(self.settings_tab, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Settings", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Paths frame
        paths_frame = ttk.LabelFrame(main_frame, text="Paths Configuration", padding="10")
        paths_frame.pack(fill=tk.X, pady=(0, 10))
        paths_frame.columnconfigure(1, weight=1)
        
        # HuggingFace cache directory
        ttk.Label(paths_frame, text="HuggingFace Cache:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.hf_cache_var = tk.StringVar(value=self.hf_cache_dir)
        ttk.Entry(paths_frame, textvariable=self.hf_cache_var, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(paths_frame, text="Browse", command=self.browse_hf_cache).grid(row=0, column=2, padx=(5, 0), pady=5)
        
        # Backup directory
        ttk.Label(paths_frame, text="Backup Directory:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.backup_dir_var = tk.StringVar(value=self.backup_dir)
        ttk.Entry(paths_frame, textvariable=self.backup_dir_var, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Button(paths_frame, text="Browse", command=self.browse_backup_dir).grid(row=1, column=2, padx=(5, 0), pady=5)
        
        # Actions frame
        actions_frame = ttk.LabelFrame(main_frame, text="Actions", padding="10")
        actions_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(actions_frame, text="Save Settings", command=self.save_settings).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(actions_frame, text="Reset to Defaults", command=self.reset_settings).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(actions_frame, text="Clear All Backups", command=self.clear_backups).pack(side=tk.LEFT)
        
        # Info frame
        info_frame = ttk.LabelFrame(main_frame, text="System Information", padding="10")
        info_frame.pack(fill=tk.BOTH, expand=True)
        
        self.info_text = scrolledtext.ScrolledText(info_frame, height=15, width=80)
        self.info_text.pack(fill=tk.BOTH, expand=True)
        
        self.update_system_info()
    
    def refresh_model_list(self):
        """Refresh the model list"""
        # Clear existing items
        for item in self.model_tree.get_children():
            self.model_tree.delete(item)

        # Get all available models using the new function
        merged_dir = self.output_dir_var.get()
        all_models = get_available_models(merged_dir)
        available_models = []

        # Standard models
        standard_models = [
            "lllyasviel/FramePackI2V_HY",
            "lllyasviel/FramePack_F1_I2V_HY_20250503"
        ]

        for model_name in standard_models:
            if model_name in all_models:
                try:
                    model_path, safetensors_files, config_file = find_model_files(model_name)

                    # Calculate size
                    total_size = 0
                    for file_path in safetensors_files:
                        if os.path.exists(file_path):
                            total_size += os.path.getsize(file_path)

                    size_str = self.format_size(total_size)

                    # Get modification time
                    mod_time = max(os.path.getmtime(f) for f in safetensors_files if os.path.exists(f))
                    mod_str = datetime.fromtimestamp(mod_time).strftime("%Y-%m-%d %H:%M")

                    # Determine type and status
                    model_type = "F1" if "F1" in model_name else "Standard"
                    status = "Active" if self.is_active_model(model_name) else "Available"

                    # Add to tree
                    self.model_tree.insert('', 'end', values=(
                        model_name.split('/')[-1],  # Short name
                        model_type,
                        status,
                        size_str,
                        mod_str
                    ))

                    available_models.append(model_name)

                except Exception as e:
                    self.model_log_message(f"Error reading standard model {model_name}: {e}")
            else:
                # Model not found, add as unavailable
                self.model_tree.insert('', 'end', values=(
                    model_name.split('/')[-1],
                    "F1" if "F1" in model_name else "Standard",
                    "Not Downloaded",
                    "N/A",
                    "N/A"
                ))

        # Process merged models
        merged_models = find_merged_models(merged_dir)
        for model_path in merged_models:
            try:
                model_name = os.path.basename(model_path)

                # Calculate size
                total_size = 0
                for root, dirs, files in os.walk(model_path):
                    for file in files:
                        if file.endswith('.safetensors'):
                            total_size += os.path.getsize(os.path.join(root, file))

                size_str = self.format_size(total_size)
                mod_time = os.path.getmtime(model_path)
                mod_str = datetime.fromtimestamp(mod_time).strftime("%Y-%m-%d %H:%M")

                self.model_tree.insert('', 'end', values=(
                    model_name,
                    "Merged",
                    "Available",
                    size_str,
                    mod_str
                ))

                available_models.append(model_path)

            except Exception as e:
                self.model_log_message(f"Error reading merged model {os.path.basename(model_path)}: {e}")

        # Update combo box
        self.target_model_combo['values'] = available_models
        if available_models and not self.current_model_var.get():
            self.current_model_var.set(available_models[0])

        # Update status labels
        self.update_status_labels()

        # Log summary
        standard_count = sum(1 for m in available_models if not os.path.isdir(m))
        merged_count = sum(1 for m in available_models if os.path.isdir(m))
        self.model_log_message(f"Refreshed model list - found {len(available_models)} models ({standard_count} standard, {merged_count} merged)")
    
    def format_size(self, size_bytes):
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"
    
    def is_active_model(self, model_name):
        """Check if a model is currently active"""
        return model_name in self.config.get('active_models', {}).values()
    
    def update_status_labels(self):
        """Update the active model status labels"""
        active_models = self.config.get('active_models', {})
        
        framepack_model = active_models.get('framepack', 'Not Set')
        f1_model = active_models.get('f1', 'Not Set')
        
        self.active_framepack_label.config(text=framepack_model.split('/')[-1] if framepack_model != 'Not Set' else 'Not Set')
        self.active_f1_label.config(text=f1_model.split('/')[-1] if f1_model != 'Not Set' else 'Not Set')
    
    def model_log_message(self, message):
        """Add message to model log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.model_log.insert(tk.END, f"[{timestamp}] {message}\n")
        self.model_log.see(tk.END)
        self.root.update_idletasks()
    
    def lora_log_message(self, message):
        """Add message to LoRA log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.lora_log.insert(tk.END, f"[{timestamp}] {message}\n")
        self.lora_log.see(tk.END)
        self.root.update_idletasks()

    # Model Management Methods
    def set_active_model(self):
        """Set selected model as active"""
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a model first")
            return

        item = self.model_tree.item(selection[0])
        model_name = item['values'][0]
        model_type = item['values'][1]

        # Determine the full model name
        if model_type == "Standard":
            full_name = "lllyasviel/FramePackI2V_HY"
            config_key = "framepack"
        elif model_type == "F1":
            full_name = "lllyasviel/FramePack_F1_I2V_HY_20250503"
            config_key = "f1"
        else:
            messagebox.showinfo("Info", "Merged models cannot be set as active directly. Use 'Install Merged Model' instead.")
            return

        # Update config
        self.config['active_models'][config_key] = full_name
        self.save_config()

        # Refresh display
        self.refresh_model_list()
        self.model_log_message(f"Set {model_name} as active {model_type} model")

    def backup_model(self):
        """Create backup of selected model"""
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a model first")
            return

        item = self.model_tree.item(selection[0])
        model_name = item['values'][0]
        model_type = item['values'][1]

        if model_type in ["Standard", "F1"]:
            # Backup original model
            full_name = "lllyasviel/FramePackI2V_HY" if model_type == "Standard" else "lllyasviel/FramePack_F1_I2V_HY_20250503"
            self.backup_original_model(full_name, model_name)
        else:
            # Backup merged model
            merged_path = os.path.join(self.output_dir_var.get(), model_name)
            self.backup_merged_model(merged_path, model_name)

    def backup_original_model(self, model_name, display_name):
        """Backup an original model from HuggingFace cache"""
        try:
            _, safetensors_files, config_file = find_model_files(model_name)

            # Create backup directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{display_name}_{timestamp}"
            backup_path = os.path.join(self.backup_dir, backup_name)
            os.makedirs(backup_path, exist_ok=True)

            self.model_log_message(f"Creating backup: {backup_name}")

            # Copy config file
            shutil.copy2(config_file, backup_path)

            # Copy safetensors files
            for i, file_path in enumerate(safetensors_files):
                filename = os.path.basename(file_path)
                dest_path = os.path.join(backup_path, filename)
                shutil.copy2(file_path, dest_path)
                self.model_log_message(f"Copied {filename} ({i+1}/{len(safetensors_files)})")

            # Copy index file if it exists
            index_file = os.path.join(os.path.dirname(config_file), "diffusion_pytorch_model.safetensors.index.json")
            if os.path.exists(index_file):
                shutil.copy2(index_file, backup_path)

            # Update backup history
            backup_info = {
                'name': backup_name,
                'original_model': model_name,
                'type': 'original',
                'timestamp': timestamp,
                'path': backup_path,
                'size': sum(os.path.getsize(f) for f in safetensors_files)
            }

            self.config['backup_history'].append(backup_info)
            self.save_config()

            self.model_log_message(f"✓ Backup completed: {backup_name}")
            messagebox.showinfo("Success", f"Model backed up successfully!\n\nBackup: {backup_name}")

        except Exception as e:
            error_msg = f"Failed to backup model: {e}"
            self.model_log_message(f"✗ {error_msg}")
            messagebox.showerror("Error", error_msg)

    def backup_merged_model(self, model_path, display_name):
        """Backup a merged model"""
        try:
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"Merged model not found: {model_path}")

            # Create backup directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{display_name}_{timestamp}"
            backup_path = os.path.join(self.backup_dir, backup_name)

            self.model_log_message(f"Creating backup: {backup_name}")

            # Copy entire directory
            shutil.copytree(model_path, backup_path)

            # Calculate size
            total_size = 0
            for root, _, files in os.walk(backup_path):
                for file in files:
                    total_size += os.path.getsize(os.path.join(root, file))

            # Update backup history
            backup_info = {
                'name': backup_name,
                'original_model': display_name,
                'type': 'merged',
                'timestamp': timestamp,
                'path': backup_path,
                'size': total_size
            }

            self.config['backup_history'].append(backup_info)
            self.save_config()

            self.model_log_message(f"✓ Backup completed: {backup_name}")
            messagebox.showinfo("Success", f"Merged model backed up successfully!\n\nBackup: {backup_name}")

        except Exception as e:
            error_msg = f"Failed to backup merged model: {e}"
            self.model_log_message(f"✗ {error_msg}")
            messagebox.showerror("Error", error_msg)

    def restore_backup(self):
        """Show backup restore dialog"""
        if not self.config.get('backup_history'):
            messagebox.showinfo("Info", "No backups available")
            return

        # Create backup selection dialog
        backup_dialog = BackupRestoreDialog(self.root, self.config['backup_history'], self)
        self.root.wait_window(backup_dialog.dialog)

    def restore_backup_by_name(self, backup_name):
        """Restore a specific backup"""
        backup_info = None
        for backup in self.config['backup_history']:
            if backup['name'] == backup_name:
                backup_info = backup
                break

        if not backup_info:
            messagebox.showerror("Error", f"Backup not found: {backup_name}")
            return

        try:
            backup_path = backup_info['path']
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"Backup files not found: {backup_path}")

            if backup_info['type'] == 'original':
                self.restore_original_backup(backup_info)
            else:
                self.restore_merged_backup(backup_info)

        except Exception as e:
            error_msg = f"Failed to restore backup: {e}"
            self.model_log_message(f"✗ {error_msg}")
            messagebox.showerror("Error", error_msg)

    def restore_original_backup(self, backup_info):
        """Restore an original model backup"""
        # This would require replacing files in HuggingFace cache
        # For safety, we'll create a new merged model instead
        messagebox.showinfo("Info",
            "Original model restoration requires manual intervention.\n\n"
            "To restore:\n"
            "1. Navigate to the backup directory\n"
            "2. Copy files to HuggingFace cache manually\n"
            f"3. Backup location: {backup_info['path']}")

        # Open backup directory
        if sys.platform == "win32":
            os.startfile(backup_info['path'])
        elif sys.platform == "darwin":
            subprocess.run(["open", backup_info['path']])
        else:
            subprocess.run(["xdg-open", backup_info['path']])

    def restore_merged_backup(self, backup_info):
        """Restore a merged model backup"""
        backup_path = backup_info['path']
        restore_name = f"restored_{backup_info['name']}"
        restore_path = os.path.join(self.output_dir_var.get(), restore_name)

        if os.path.exists(restore_path):
            if not messagebox.askyesno("Confirm", f"Restore destination already exists: {restore_name}\n\nOverwrite?"):
                return
            shutil.rmtree(restore_path)

        self.model_log_message(f"Restoring backup: {backup_info['name']}")
        shutil.copytree(backup_path, restore_path)

        self.model_log_message(f"✓ Backup restored as: {restore_name}")
        self.refresh_model_list()
        messagebox.showinfo("Success", f"Backup restored successfully!\n\nRestored as: {restore_name}")

    def import_model(self):
        """Import a model from external location"""
        source_dir = filedialog.askdirectory(title="Select Model Directory to Import")
        if not source_dir:
            return

        # Check if it's a valid model directory
        config_file = os.path.join(source_dir, 'config.json')
        if not os.path.exists(config_file):
            messagebox.showerror("Error", "Selected directory is not a valid model (missing config.json)")
            return

        # Get model name
        model_name = os.path.basename(source_dir)
        import_name = tk.simpledialog.askstring("Import Model", f"Enter name for imported model:", initialvalue=model_name)
        if not import_name:
            return

        # Import to merged models directory
        dest_path = os.path.join(self.output_dir_var.get(), import_name)

        if os.path.exists(dest_path):
            if not messagebox.askyesno("Confirm", f"Model already exists: {import_name}\n\nOverwrite?"):
                return
            shutil.rmtree(dest_path)

        try:
            self.model_log_message(f"Importing model: {import_name}")
            shutil.copytree(source_dir, dest_path)

            self.model_log_message(f"✓ Model imported: {import_name}")
            self.refresh_model_list()
            messagebox.showinfo("Success", f"Model imported successfully!\n\nImported as: {import_name}")

        except Exception as e:
            error_msg = f"Failed to import model: {e}"
            self.model_log_message(f"✗ {error_msg}")
            messagebox.showerror("Error", error_msg)

    def export_model(self):
        """Export selected model to external location"""
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a model first")
            return

        item = self.model_tree.item(selection[0])
        model_name = item['values'][0]
        model_type = item['values'][1]

        if model_type in ["Standard", "F1"]:
            messagebox.showinfo("Info", "Original models cannot be exported directly. Create a backup instead.")
            return

        # Export merged model
        source_path = os.path.join(self.output_dir_var.get(), model_name)
        if not os.path.exists(source_path):
            messagebox.showerror("Error", f"Model not found: {source_path}")
            return

        dest_dir = filedialog.askdirectory(title="Select Export Destination")
        if not dest_dir:
            return

        dest_path = os.path.join(dest_dir, model_name)

        try:
            self.model_log_message(f"Exporting model: {model_name}")
            shutil.copytree(source_path, dest_path)

            self.model_log_message(f"✓ Model exported to: {dest_path}")
            messagebox.showinfo("Success", f"Model exported successfully!\n\nExported to: {dest_path}")

        except Exception as e:
            error_msg = f"Failed to export model: {e}"
            self.model_log_message(f"✗ {error_msg}")
            messagebox.showerror("Error", error_msg)

    def delete_model(self):
        """Delete selected model"""
        selection = self.model_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a model first")
            return

        item = self.model_tree.item(selection[0])
        model_name = item['values'][0]
        model_type = item['values'][1]

        if model_type in ["Standard", "F1"]:
            messagebox.showwarning("Warning", "Cannot delete original models. They are managed by HuggingFace cache.")
            return

        # Confirm deletion
        if not messagebox.askyesno("Confirm Deletion",
            f"Are you sure you want to delete the model '{model_name}'?\n\n"
            "This action cannot be undone!"):
            return

        # Delete merged model
        model_path = os.path.join(self.output_dir_var.get(), model_name)

        try:
            if os.path.exists(model_path):
                shutil.rmtree(model_path)
                self.model_log_message(f"✓ Deleted model: {model_name}")
                self.refresh_model_list()
                messagebox.showinfo("Success", f"Model deleted successfully: {model_name}")
            else:
                messagebox.showerror("Error", f"Model not found: {model_path}")

        except Exception as e:
            error_msg = f"Failed to delete model: {e}"
            self.model_log_message(f"✗ {error_msg}")
            messagebox.showerror("Error", error_msg)

    # LoRA Merger Methods
    def on_mode_change(self):
        """Handle merge mode change"""
        if self.merge_mode_var.get() == "single":
            self.single_frame.grid()
            self.multiple_frame.grid_remove()
        else:
            self.single_frame.grid_remove()
            self.multiple_frame.grid()

    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir_var.set(directory)

    def browse_lora_file(self, var):
        """Browse for LoRA file"""
        filename = filedialog.askopenfilename(
            title="Select LoRA File",
            filetypes=[("SafeTensors files", "*.safetensors"), ("All files", "*.*")]
        )
        if filename:
            var.set(filename)

    def validate_lora_inputs(self):
        """Validate LoRA merger inputs"""
        if not self.current_model_var.get():
            messagebox.showerror("Error", "Please select a target model")
            return False

        if self.merge_mode_var.get() == "single":
            if not self.lora_file_var.get():
                messagebox.showerror("Error", "Please select a LoRA file")
                return False
            if not os.path.exists(self.lora_file_var.get()):
                messagebox.showerror("Error", f"LoRA file not found: {self.lora_file_var.get()}")
                return False
        else:
            lora_files = [self.lora_file_1_var.get(), self.lora_file_2_var.get(), self.lora_file_3_var.get()]
            valid_files = [f for f in lora_files if f and os.path.exists(f)]

            if not valid_files:
                messagebox.showerror("Error", "Please select at least one valid LoRA file")
                return False

        return True

    def start_merge(self):
        """Start the LoRA merge process"""
        if not self.validate_lora_inputs():
            return

        self.merge_button.config(state='disabled')
        self.lora_progress_bar.start()
        self.lora_progress_var.set("Merging...")
        self.lora_log.delete(1.0, tk.END)

        # Start merge in separate thread
        thread = threading.Thread(target=self.run_lora_merge)
        thread.daemon = True
        thread.start()

    def run_lora_merge(self):
        """Run the actual LoRA merge process"""
        try:
            model_name = self.current_model_var.get()
            output_dir = self.output_dir_var.get()

            # Create output directory
            os.makedirs(output_dir, exist_ok=True)

            if self.merge_mode_var.get() == "single":
                # Single LoRA merge
                lora_file = self.lora_file_var.get()
                multiplier = self.multiplier_var.get()

                self.lora_log_message(f"Starting single LoRA merge...")
                self.lora_log_message(f"Model: {model_name}")
                self.lora_log_message(f"LoRA: {os.path.basename(lora_file)}")
                self.lora_log_message(f"Multiplier: {multiplier}")

                output_path = merge_single_lora(model_name, lora_file, multiplier, output_dir)

            else:
                # Multiple LoRA merge
                lora_configs = []

                if self.lora_file_1_var.get() and os.path.exists(self.lora_file_1_var.get()):
                    lora_configs.append((self.lora_file_1_var.get(), self.multiplier_1_var.get()))

                if self.lora_file_2_var.get() and os.path.exists(self.lora_file_2_var.get()):
                    lora_configs.append((self.lora_file_2_var.get(), self.multiplier_2_var.get()))

                if self.lora_file_3_var.get() and os.path.exists(self.lora_file_3_var.get()):
                    lora_configs.append((self.lora_file_3_var.get(), self.multiplier_3_var.get()))

                self.lora_log_message(f"Starting multiple LoRA merge...")
                self.lora_log_message(f"Model: {model_name}")
                self.lora_log_message(f"LoRAs: {len(lora_configs)} files")

                for i, (lora_file, mult) in enumerate(lora_configs, 1):
                    self.lora_log_message(f"  {i}. {os.path.basename(lora_file)} (x{mult})")

                output_path = merge_multiple_loras(model_name, lora_configs, output_dir)

            self.lora_log_message(f"✓ Merge completed successfully!")
            self.lora_log_message(f"Output saved to: {output_path}")

            # Refresh model list to show new merged model
            self.root.after(0, self.refresh_model_list)

            # Show success message
            self.root.after(0, lambda: messagebox.showinfo("Success",
                f"LoRA merge completed successfully!\n\nOutput saved to:\n{output_path}"))

        except Exception as e:
            error_msg = f"Error during merge: {str(e)}"
            self.lora_log_message(f"✗ {error_msg}")

            # Show error message
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))

        finally:
            # Re-enable UI
            self.root.after(0, self.lora_merge_finished)

    def lora_merge_finished(self):
        """Called when LoRA merge is finished"""
        self.lora_progress_bar.stop()
        self.lora_progress_var.set("Ready")
        self.merge_button.config(state='normal')

    def install_merged_model(self):
        """Install a merged model as the active model"""
        # Get list of merged models
        merged_models = []
        merged_dir = self.output_dir_var.get()

        if os.path.exists(merged_dir):
            for item in os.listdir(merged_dir):
                item_path = os.path.join(merged_dir, item)
                if os.path.isdir(item_path) and os.path.exists(os.path.join(item_path, 'config.json')):
                    merged_models.append(item)

        if not merged_models:
            messagebox.showinfo("Info", "No merged models available to install")
            return

        # Show selection dialog
        install_dialog = InstallModelDialog(self.root, merged_models, self)
        self.root.wait_window(install_dialog.dialog)

    def install_model_by_name(self, model_name, target_type):
        """Install a merged model by replacing the original"""
        merged_path = os.path.join(self.output_dir_var.get(), model_name)

        if not os.path.exists(merged_path):
            messagebox.showerror("Error", f"Merged model not found: {merged_path}")
            return

        # Determine target model
        if target_type == "Standard":
            target_model = "lllyasviel/FramePackI2V_HY"
        elif target_type == "F1":
            target_model = "lllyasviel/FramePack_F1_I2V_HY_20250503"
        else:
            messagebox.showerror("Error", "Invalid target model type")
            return

        # Confirm installation
        if not messagebox.askyesno("Confirm Installation",
            f"This will replace the active {target_type} model with the merged model '{model_name}'.\n\n"
            "The original model will be backed up first.\n\n"
            "Continue?"):
            return

        try:
            # Create backup first
            self.model_log_message(f"Creating backup before installation...")
            display_name = target_model.split('/')[-1]
            self.backup_original_model(target_model, display_name)

            # Get target model path
            _, _, config_file = find_model_files(target_model)
            target_dir = os.path.dirname(config_file)

            # Copy merged model files to target location
            self.model_log_message(f"Installing merged model: {model_name}")

            # Remove existing files
            for file in os.listdir(target_dir):
                if file.endswith('.safetensors') or file == 'config.json' or file.endswith('.index.json'):
                    os.remove(os.path.join(target_dir, file))

            # Copy new files
            for file in os.listdir(merged_path):
                if file.endswith('.safetensors') or file == 'config.json' or file.endswith('.index.json'):
                    shutil.copy2(os.path.join(merged_path, file), target_dir)

            self.model_log_message(f"✓ Model installed successfully: {model_name}")
            self.refresh_model_list()

            messagebox.showinfo("Success",
                f"Merged model installed successfully!\n\n"
                f"The {target_type} model has been replaced with '{model_name}'.\n"
                f"A backup of the original model was created.")

        except Exception as e:
            error_msg = f"Failed to install model: {e}"
            self.model_log_message(f"✗ {error_msg}")
            messagebox.showerror("Error", error_msg)

    def open_output_folder(self):
        """Open output folder in file explorer"""
        output_dir = self.output_dir_var.get()
        if os.path.exists(output_dir):
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                subprocess.run(["open", output_dir])
            else:
                subprocess.run(["xdg-open", output_dir])
        else:
            messagebox.showwarning("Warning", f"Output directory does not exist: {output_dir}")

    # Settings Methods
    def browse_hf_cache(self):
        """Browse for HuggingFace cache directory"""
        directory = filedialog.askdirectory(title="Select HuggingFace Cache Directory")
        if directory:
            self.hf_cache_var.set(directory)

    def browse_backup_dir(self):
        """Browse for backup directory"""
        directory = filedialog.askdirectory(title="Select Backup Directory")
        if directory:
            self.backup_dir_var.set(directory)

    def save_settings(self):
        """Save current settings"""
        self.hf_cache_dir = self.hf_cache_var.get()
        self.backup_dir = self.backup_dir_var.get()

        # Update environment variable
        os.environ['HF_HOME'] = self.hf_cache_dir

        # Ensure directories exist
        os.makedirs(self.backup_dir, exist_ok=True)

        # Save to config
        self.config['hf_cache_dir'] = self.hf_cache_dir
        self.config['backup_dir'] = self.backup_dir
        self.save_config()

        messagebox.showinfo("Success", "Settings saved successfully!")
        self.refresh_model_list()

    def reset_settings(self):
        """Reset settings to defaults"""
        if messagebox.askyesno("Confirm Reset", "Reset all settings to defaults?"):
            default_hf_cache = os.path.join(os.path.dirname(__file__), 'hf_download')
            default_backup_dir = os.path.join(os.path.dirname(__file__), 'model_backups')

            self.hf_cache_var.set(default_hf_cache)
            self.backup_dir_var.set(default_backup_dir)

            self.save_settings()

    def clear_backups(self):
        """Clear all backup files"""
        if not messagebox.askyesno("Confirm Clear",
            "This will delete ALL backup files permanently.\n\n"
            "This action cannot be undone!\n\n"
            "Continue?"):
            return

        try:
            if os.path.exists(self.backup_dir):
                shutil.rmtree(self.backup_dir)
                os.makedirs(self.backup_dir, exist_ok=True)

            # Clear backup history
            self.config['backup_history'] = []
            self.save_config()

            self.model_log_message("✓ All backups cleared")
            messagebox.showinfo("Success", "All backups cleared successfully!")

        except Exception as e:
            error_msg = f"Failed to clear backups: {e}"
            self.model_log_message(f"✗ {error_msg}")
            messagebox.showerror("Error", error_msg)

    def update_system_info(self):
        """Update system information display"""
        info_lines = []

        # System info
        info_lines.append("=== System Information ===")
        info_lines.append(f"Platform: {sys.platform}")
        info_lines.append(f"Python: {sys.version}")
        info_lines.append("")

        # Paths
        info_lines.append("=== Paths ===")
        info_lines.append(f"HuggingFace Cache: {self.hf_cache_dir}")
        info_lines.append(f"Backup Directory: {self.backup_dir}")
        info_lines.append(f"Output Directory: {self.output_dir_var.get()}")
        info_lines.append("")

        # Model status
        info_lines.append("=== Model Status ===")
        try:
            for model_name in ["lllyasviel/FramePackI2V_HY", "lllyasviel/FramePack_F1_I2V_HY_20250503"]:
                try:
                    _, safetensors_files, _ = find_model_files(model_name)
                    total_size = sum(os.path.getsize(f) for f in safetensors_files if os.path.exists(f))
                    info_lines.append(f"{model_name.split('/')[-1]}: {self.format_size(total_size)} ({len(safetensors_files)} files)")
                except FileNotFoundError:
                    info_lines.append(f"{model_name.split('/')[-1]}: Not downloaded")
        except Exception as e:
            info_lines.append(f"Error checking models: {e}")

        info_lines.append("")

        # Backup info
        info_lines.append("=== Backup Information ===")
        backup_count = len(self.config.get('backup_history', []))
        info_lines.append(f"Total backups: {backup_count}")

        if backup_count > 0:
            total_backup_size = 0
            for backup in self.config['backup_history']:
                if 'size' in backup:
                    total_backup_size += backup['size']
            info_lines.append(f"Total backup size: {self.format_size(total_backup_size)}")

        info_lines.append("")

        # Merged models info
        info_lines.append("=== Merged Models ===")
        merged_dir = self.output_dir_var.get()
        if os.path.exists(merged_dir):
            merged_count = 0
            merged_size = 0
            for item in os.listdir(merged_dir):
                item_path = os.path.join(merged_dir, item)
                if os.path.isdir(item_path) and os.path.exists(os.path.join(item_path, 'config.json')):
                    merged_count += 1
                    for root, _, files in os.walk(item_path):
                        for file in files:
                            merged_size += os.path.getsize(os.path.join(root, file))

            info_lines.append(f"Merged models: {merged_count}")
            info_lines.append(f"Total merged size: {self.format_size(merged_size)}")
        else:
            info_lines.append("Merged models directory not found")

        # Update display
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, "\n".join(info_lines))


class BackupRestoreDialog:
    """Dialog for selecting and restoring backups"""

    def __init__(self, parent, backup_history, manager):
        self.parent = parent
        self.backup_history = backup_history
        self.manager = manager
        self.selected_backup = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Restore Backup")
        self.dialog.geometry("600x400")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.setup_ui()

    def setup_ui(self):
        """Setup the backup restore dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="Select Backup to Restore", font=("Arial", 12, "bold")).pack(pady=(0, 10))

        # Backup list
        columns = ('Name', 'Type', 'Date', 'Size')
        self.backup_tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=12)

        self.backup_tree.heading('Name', text='Backup Name')
        self.backup_tree.heading('Type', text='Type')
        self.backup_tree.heading('Date', text='Date Created')
        self.backup_tree.heading('Size', text='Size')

        self.backup_tree.column('Name', width=200)
        self.backup_tree.column('Type', width=80)
        self.backup_tree.column('Date', width=120)
        self.backup_tree.column('Size', width=100)

        # Populate backup list
        for backup in self.backup_history:
            date_str = datetime.strptime(backup['timestamp'], "%Y%m%d_%H%M%S").strftime("%Y-%m-%d %H:%M")
            size_str = self.manager.format_size(backup.get('size', 0))

            self.backup_tree.insert('', 'end', values=(
                backup['name'],
                backup['type'].title(),
                date_str,
                size_str
            ))

        self.backup_tree.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Restore", command=self.restore_selected).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Delete Backup", command=self.delete_selected).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT)

    def restore_selected(self):
        """Restore the selected backup"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a backup first")
            return

        item = self.backup_tree.item(selection[0])
        backup_name = item['values'][0]

        self.dialog.destroy()
        self.manager.restore_backup_by_name(backup_name)

    def delete_selected(self):
        """Delete the selected backup"""
        selection = self.backup_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a backup first")
            return

        item = self.backup_tree.item(selection[0])
        backup_name = item['values'][0]

        if not messagebox.askyesno("Confirm Delete", f"Delete backup '{backup_name}'?\n\nThis cannot be undone!"):
            return

        # Find and delete backup
        backup_to_delete = None
        for backup in self.backup_history:
            if backup['name'] == backup_name:
                backup_to_delete = backup
                break

        if backup_to_delete:
            try:
                # Delete backup files
                if os.path.exists(backup_to_delete['path']):
                    shutil.rmtree(backup_to_delete['path'])

                # Remove from history
                self.backup_history.remove(backup_to_delete)
                self.manager.config['backup_history'] = self.backup_history
                self.manager.save_config()

                # Remove from tree
                self.backup_tree.delete(selection[0])

                messagebox.showinfo("Success", f"Backup deleted: {backup_name}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete backup: {e}")


class InstallModelDialog:
    """Dialog for installing merged models"""

    def __init__(self, parent, merged_models, manager):
        self.parent = parent
        self.merged_models = merged_models
        self.manager = manager

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Install Merged Model")
        self.dialog.geometry("500x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.model_var = tk.StringVar()
        self.target_var = tk.StringVar(value="Standard")

        self.setup_ui()

    def setup_ui(self):
        """Setup the install dialog UI"""
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="Install Merged Model", font=("Arial", 12, "bold")).pack(pady=(0, 20))

        # Model selection
        ttk.Label(main_frame, text="Select merged model to install:").pack(anchor=tk.W, pady=(0, 5))
        model_combo = ttk.Combobox(main_frame, textvariable=self.model_var, values=self.merged_models, state="readonly")
        model_combo.pack(fill=tk.X, pady=(0, 20))

        # Target selection
        ttk.Label(main_frame, text="Replace which model:").pack(anchor=tk.W, pady=(0, 5))
        target_frame = ttk.Frame(main_frame)
        target_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Radiobutton(target_frame, text="Standard FramePack Model", variable=self.target_var, value="Standard").pack(anchor=tk.W)
        ttk.Radiobutton(target_frame, text="F1 FramePack Model", variable=self.target_var, value="F1").pack(anchor=tk.W)

        # Warning
        warning_text = ("WARNING: This will replace the selected model with the merged model.\n"
                       "The original model will be backed up first, but this is a significant change.\n"
                       "Make sure you have tested the merged model before installing it.")

        warning_label = ttk.Label(main_frame, text=warning_text, foreground="red", wraplength=450)
        warning_label.pack(pady=(0, 20))

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Install", command=self.install_model).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT)

    def install_model(self):
        """Install the selected model"""
        if not self.model_var.get():
            messagebox.showwarning("Warning", "Please select a model to install")
            return

        model_name = self.model_var.get()
        target_type = self.target_var.get()

        self.dialog.destroy()
        self.manager.install_model_by_name(model_name, target_type)


def main():
    # Add missing import for simpledialog
    import tkinter.simpledialog
    tk.simpledialog = tkinter.simpledialog

    root = tk.Tk()
    app = ModelManagerGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
