"""
A high-performance, hardware-accelerated video player for Tkinter using MoviePy and OpenCV.
This player is designed to replace the TkinterVideo and TkGifPlayer implementations
with a more reliable and efficient solution.
"""

import os
import time
import threading
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk, ImageDraw, ImageFont
from moviepy.editor import VideoFileClip

class MoviePyPlayer(ttk.Frame):
    """A high-performance video player for Tkinter using MoviePy and OpenCV"""

    def __init__(self, master, width=480, height=360, **kwargs):
        """Initialize the video player

        Args:
            master: The parent widget
            width: The width of the player (default: 480)
            height: The height of the player (default: 360)
            **kwargs: Additional keyword arguments for the Frame
        """
        super().__init__(master, **kwargs)

        # Initialize variables
        self.master = master
        self.width = width
        self.height = height
        self.is_playing = False
        self.is_loaded = False
        self.video_path = None
        self.clip = None
        self.current_frame_index = 0
        self.total_frames = 0
        self.fps = 0
        self.duration = 0
        self.loop = True
        self.playback_rate = 1.0  # Default playback rate (100%)
        self.frame_delay = 0  # Delay between frames in ms
        self.after_id = None
        self.stop_requested = False
        self.frame_buffer = []  # Buffer to store pre-loaded frames
        self.buffer_size = 10  # Number of frames to buffer
        self.buffer_thread = None
        self.buffer_lock = threading.Lock()
        self.buffer_condition = threading.Condition(self.buffer_lock)
        self.buffer_active = False

        # Get the GUI background color to match the interface
        try:
            # Try to get the background color from the parent widget
            bg_color = self.master.cget('bg')
            if not bg_color or bg_color in ['', 'SystemButtonFace']:
                # Use a light gray that matches typical GUI backgrounds
                bg_color = '#f0f0f0'
        except Exception:
            # Fallback to a standard GUI background color
            bg_color = '#f0f0f0'

        # Create a label to display the video with GUI-matching background
        self.canvas = tk.Canvas(self, width=width, height=height, bg=bg_color, highlightthickness=0)
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # Store the background color for use in other methods
        self.bg_color = bg_color

        # Video scaling mode: 'fit' (letterbox) or 'fill' (crop to fill)
        # 'fit' maintains full video content with possible black bars
        # 'fill' crops video to eliminate black bars
        self.scaling_mode = 'fit'  # Default to 'fit' to preserve video content

        # Fullscreen variables
        self.fullscreen_window = None
        self.fullscreen_canvas = None
        self.is_fullscreen = False
        self.original_parent = None
        self.original_pack_info = None

        # Create a default image
        self._create_default_image()

        # Bind events
        self.canvas.bind("<Button-1>", self.toggle_playback)
        self.canvas.bind("<Double-Button-1>", self.enter_fullscreen)

        # Bind resize events to update dimensions
        self.bind('<Configure>', self._on_resize)

    def _create_default_image(self):
        """Create a default image to display when no video is loaded"""
        try:
            # Check if the canvas still exists and is valid
            if not hasattr(self, 'canvas') or self.canvas is None:
                return

            # Check if the canvas is still a valid Tkinter widget
            try:
                # This will raise an error if the canvas has been destroyed
                self.canvas.winfo_exists()
            except Exception:
                return

            # Convert the background color to RGB tuple
            try:
                bg_color = getattr(self, 'bg_color', '#f0f0f0')
                if bg_color.startswith('#'):
                    # Convert hex to RGB
                    hex_color = bg_color.lstrip('#')
                    rgb_color = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                else:
                    # Fallback to light gray
                    rgb_color = (240, 240, 240)
            except Exception:
                # Fallback to light gray
                rgb_color = (240, 240, 240)

            # Create a blank image with the GUI background color
            image = Image.new("RGB", (self.width, self.height), color=rgb_color)
            photo = ImageTk.PhotoImage(image)

            # Clear existing images first
            try:
                self.canvas.delete("video")
            except Exception:
                pass

            # Create the new image
            try:
                self.canvas.create_image(self.width // 2, self.height // 2, image=photo, anchor=tk.CENTER, tags="video")
                self.canvas.image = photo  # Keep a reference to prevent garbage collection
            except Exception as e:
                print(f"Error creating default image: {e}")
        except Exception as e:
            print(f"Error in _create_default_image: {e}")

    def _on_resize(self, event=None):
        """Handle resize events to update player dimensions"""
        try:
            # Get the current canvas size
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # Only update if we have valid dimensions and they've changed
            if (canvas_width > 10 and canvas_height > 10 and
                (canvas_width != self.width or canvas_height != self.height)):

                # Update our internal dimensions
                self.width = canvas_width
                self.height = canvas_height

                # If we have a video loaded, refresh the current frame
                if self.is_loaded and hasattr(self, 'current_frame_index'):
                    self.after(50, lambda: self._display_frame(self.current_frame_index))
                else:
                    # Otherwise, update the default image
                    self.after(50, self._create_default_image)

        except Exception as e:
            print(f"Error in _on_resize: {e}")

    def resize_to_fit(self, width, height):
        """Manually resize the player to specific dimensions"""
        try:
            if width > 0 and height > 0:
                self.width = width
                self.height = height

                # Update the canvas size
                self.canvas.configure(width=width, height=height)

                # Refresh the display
                if self.is_loaded and hasattr(self, 'current_frame_index'):
                    self.after(10, lambda: self._display_frame(self.current_frame_index))
                else:
                    self.after(10, self._create_default_image)

        except Exception as e:
            print(f"Error in resize_to_fit: {e}")

    def load(self, video_path):
        """Load a video file

        Args:
            video_path: Path to the video file

        Returns:
            True if the video was loaded successfully, False otherwise
        """
        # Stop any current playback
        self.stop()

        # Reset variables
        self.video_path = None
        self.is_loaded = False
        self.current_frame_index = 0
        self.frame_buffer = []

        # Check if the file exists
        if not os.path.exists(video_path):
            print(f"Video file not found: {video_path}")
            return False

        # Check if the file is empty
        if os.path.getsize(video_path) == 0:
            print(f"Video file is empty: {video_path}")
            return False

        # Start loading in a separate thread to keep UI responsive
        threading.Thread(target=self._load_video_thread, args=(video_path,), daemon=True).start()
        return True

    def _load_video_thread(self, video_path):
        """Load a video file in a background thread

        Args:
            video_path: Path to the video file
        """
        try:
            # Close any existing clip
            if self.clip is not None:
                try:
                    self.clip.close()
                except Exception as close_error:
                    print(f"Error closing existing clip: {close_error}")
                self.clip = None

            # Add a small delay to ensure the file is fully accessible
            time.sleep(0.2)

            # Double-check that the file still exists and is accessible
            if not os.path.exists(video_path):
                print(f"File no longer exists: {video_path}")
                self.after(10, self._create_default_image)
                return

            if os.path.getsize(video_path) == 0:
                print(f"File is empty: {video_path}")
                self.after(10, self._create_default_image)
                return

            # Try to open the file to check if it's accessible
            try:
                with open(video_path, 'rb') as f:
                    # Just read a small amount to check access
                    f.read(1024)
            except Exception as access_error:
                print(f"File is not accessible: {video_path} - {access_error}")
                self.after(10, self._create_default_image)
                return

            # Open the video file with MoviePy with error handling
            try:
                self.clip = VideoFileClip(video_path, audio=False)
            except Exception as clip_error:
                print(f"Error creating VideoFileClip: {clip_error}")
                # Try again with a different approach
                try:
                    # Some files might need different parameters
                    from moviepy.video.io.ffmpeg_reader import FFMPEG_VideoReader
                    reader = FFMPEG_VideoReader(video_path, False)
                    self.clip = VideoFileClip(video_path, audio=False, reader=reader)
                except Exception as retry_error:
                    print(f"Second attempt failed: {retry_error}")
                    self.after(10, self._create_default_image)
                    return

            # Initialize lastread attribute using our enhanced method
            if not self._ensure_lastread_initialized():
                print("Failed to initialize lastread attribute")
                self.after(10, self._create_default_image)
                return

            # Get video properties with error handling
            try:
                self.fps = self.clip.fps
                if self.fps is None or self.fps <= 0:
                    print(f"Invalid FPS: {self.fps}, using default of 30")
                    self.fps = 30

                self.duration = self.clip.duration
                if self.duration is None or self.duration <= 0:
                    print(f"Invalid duration: {self.duration}, using default of 1.0")
                    self.duration = 1.0

                self.total_frames = int(self.fps * self.duration)
                if self.total_frames <= 0:
                    print(f"Invalid total frames: {self.total_frames}, using default of 30")
                    self.total_frames = 30
            except Exception as prop_error:
                print(f"Error getting video properties: {prop_error}")
                # Use default values
                self.fps = 30
                self.duration = 1.0
                self.total_frames = 30

            # Calculate frame delay based on FPS and playback rate
            try:
                self.frame_delay = int(1000 / (self.fps * self.playback_rate))
                if self.frame_delay <= 0:
                    self.frame_delay = 33  # ~30fps default
            except Exception:
                self.frame_delay = 33  # ~30fps default

            # Store the video path
            self.video_path = video_path
            self.is_loaded = True

            # Start the buffer thread
            self._start_buffer_thread()

            # Display the first frame (using after to ensure it runs in the main thread)
            self.after(10, lambda: self._display_frame(0))

            # Start playing automatically
            self.after(100, self.play)

            print(f"Successfully loaded video: {video_path}")
        except Exception as e:
            print(f"Error loading video {video_path}: {e}")
            # Show error message on canvas
            self.after(10, lambda: self._show_error_message(f"Error: {str(e)[:50]}..."))
            # Also create default image as fallback
            self.after(100, self._create_default_image)

    def _start_buffer_thread(self):
        """Start a thread to pre-load frames into the buffer"""
        # Stop any existing buffer thread
        self._stop_buffer_thread()

        # Start a new buffer thread
        self.buffer_active = True
        self.buffer_thread = threading.Thread(target=self._buffer_frames, daemon=True)
        self.buffer_thread.start()

    def _stop_buffer_thread(self):
        """Stop the buffer thread"""
        if self.buffer_thread is not None and self.buffer_thread.is_alive():
            with self.buffer_condition:
                self.buffer_active = False
                self.buffer_condition.notify_all()
            self.buffer_thread.join(timeout=1.0)
            self.buffer_thread = None

        # Clear the buffer completely when stopping the thread
        try:
            with self.buffer_lock:
                # Clear all PhotoImage references in the buffer
                for frame_data in self.frame_buffer:
                    if len(frame_data) > 1 and frame_data[1] is not None:
                        try:
                            frame_data[1] = None
                        except Exception:
                            pass
                self.frame_buffer.clear()
        except Exception:
            pass

    def _buffer_frames(self):
        """Buffer frames in a background thread with improved efficiency and error handling"""
        try:
            # Set a lower buffer size for smoother playback with less memory usage
            actual_buffer_size = min(self.buffer_size, 5)

            # Track the last error time to avoid spamming the console
            last_error_time = 0

            while self.buffer_active and self.is_loaded and self.clip is not None:
                try:
                    with self.buffer_condition:
                        # If buffer is full, wait for it to be consumed
                        if len(self.frame_buffer) >= actual_buffer_size:
                            # Wait for buffer space with a timeout
                            self.buffer_condition.wait(timeout=0.05)  # Shorter timeout for more responsive buffering
                            continue

                        # If stop was requested, exit the loop
                        if not self.buffer_active:
                            break

                        # Calculate which frames to buffer, focusing on the next few frames
                        start_idx = self.current_frame_index
                        end_idx = min(start_idx + actual_buffer_size, self.total_frames)

                        # Only buffer frames that aren't already in the buffer
                        existing_indices = set(f[0] for f in self.frame_buffer)

                        # Prioritize the next frame that's not in the buffer
                        next_frame_to_buffer = None
                        for idx in range(start_idx, end_idx):
                            if idx not in existing_indices:
                                next_frame_to_buffer = idx
                                break

                        # If no frame to buffer, wait a bit before checking again
                        if next_frame_to_buffer is None:
                            self.buffer_condition.wait(timeout=0.02)  # Very short timeout for responsive buffering
                            continue

                        # Release the lock while processing the frame to avoid blocking other threads
                        self.buffer_condition.release()
                        lock_released = True

                        try:
                            # Get the frame at the specified time
                            t = next_frame_to_buffer / self.fps

                            # Use a try-except block to handle potential errors in frame extraction
                            try:
                                # Use our safe frame extraction method
                                frame = self._safe_extract_frame(t)

                                # Convert to PIL Image and resize
                                pil_img = self._resize_frame(Image.fromarray(frame))

                                # Convert to PhotoImage
                                photo = ImageTk.PhotoImage(pil_img)

                                # Reacquire the lock to update the buffer
                                self.buffer_condition.acquire()
                                lock_released = False

                                # Add to buffer with its index if we're still active
                                if self.buffer_active:
                                    self.frame_buffer.append((next_frame_to_buffer, photo))

                                    # Keep the buffer sorted and limited to the buffer size
                                    self.frame_buffer.sort(key=lambda x: x[0])

                                    # Trim the buffer if it's too large
                                    if len(self.frame_buffer) > actual_buffer_size:
                                        # Remove frames that are far from the current index
                                        frames_to_keep = []
                                        for idx, photo in self.frame_buffer:
                                            if abs(idx - self.current_frame_index) < actual_buffer_size:
                                                frames_to_keep.append((idx, photo))

                                        # If we have enough frames to keep, update the buffer
                                        if len(frames_to_keep) >= actual_buffer_size // 2:
                                            self.frame_buffer = frames_to_keep

                                    # Notify that a new frame is available
                                    self.buffer_condition.notify()
                            except Exception as frame_error:
                                # Only log errors occasionally to avoid spamming the console
                                current_time = time.time()
                                if current_time - last_error_time > 5.0:  # Log at most once every 5 seconds
                                    print(f"Error extracting frame at time {t:.2f}s: {frame_error}")
                                    last_error_time = current_time

                                # Reacquire the lock if we lost it
                                if lock_released:
                                    self.buffer_condition.acquire()
                                    lock_released = False

                                # Wait a bit before trying again
                                self.buffer_condition.wait(timeout=0.1)
                        except Exception as e:
                            # Reacquire the lock if we lost it due to an exception
                            if lock_released:
                                try:
                                    self.buffer_condition.acquire()
                                    lock_released = False
                                except Exception as lock_error:
                                    print(f"Error reacquiring lock: {lock_error}")
                                    # If we can't reacquire the lock, we need to exit the loop
                                    break

                            # Only log errors occasionally to avoid spamming the console
                            current_time = time.time()
                            if current_time - last_error_time > 5.0:
                                print(f"Error buffering frame {next_frame_to_buffer}: {e}")
                                last_error_time = current_time

                            # Wait a bit before trying again
                            self.buffer_condition.wait(timeout=0.1)
                except Exception as loop_error:
                    # Handle errors in the main loop
                    print(f"Error in buffer thread main loop: {loop_error}")

                    # Make sure we don't hold the lock
                    if hasattr(self, 'buffer_condition') and self.buffer_condition._is_owned():
                        self.buffer_condition.release()

                    # Wait a bit before continuing
                    time.sleep(0.2)
        except Exception as e:
            print(f"Critical error in buffer thread: {e}")
            # Try to recover by restarting the buffer thread after a delay
            if self.buffer_active and self.is_loaded:
                print("Attempting to restart buffer thread after critical error...")
                try:
                    # Wait a bit before restarting
                    time.sleep(0.5)
                    # Start a new thread
                    self.buffer_thread = threading.Thread(target=self._buffer_frames, daemon=True)
                    self.buffer_thread.start()
                except Exception as restart_error:
                    print(f"Failed to restart buffer thread: {restart_error}")

    def _ensure_lastread_initialized(self, force=False):
        """Ensure the clip's lastread attribute is properly initialized

        Args:
            force: If True, reinitialize lastread even if it already exists
        """
        if self.clip is None:
            return False

        # Check if we need to initialize lastread
        if force or not hasattr(self.clip, 'lastread') or self.clip.lastread is None:
            try:
                # First try to use the reader if available
                if hasattr(self.clip, 'reader') and self.clip.reader is not None:
                    try:
                        # Double-check that the reader is still valid before using it
                        reader = self.clip.reader
                        if reader is not None and hasattr(reader, 'read_frame'):
                            # Try to read a frame from the reader
                            frame = reader.read_frame()
                            if frame is not None:
                                self.clip.lastread = frame
                                # Hide this message as it doesn't affect functionality
                                # print("Initialized lastread attribute from reader")
                                return True
                            else:
                                print("Reader returned None for read_frame()")
                        else:
                            print("Reader became None or invalid before read_frame() call")
                    except Exception as reader_error:
                        if not hasattr(self, '_reader_error_logged'):
                            print(f"Error initializing lastread from reader: {reader_error}")
                            self._reader_error_logged = True

                # If we get here, either there's no reader or reading failed
                # Create a dummy lastread with numpy
                import numpy as np

                # Try to get the video dimensions if available
                width, height = 128, 128  # Default size
                if hasattr(self.clip, 'size') and self.clip.size is not None:
                    try:
                        width, height = self.clip.size
                    except Exception:
                        pass  # Use default size if we can't get the clip size

                # Create a blank frame as a placeholder with appropriate dimensions
                self.clip.lastread = np.zeros((height, width, 3), dtype=np.uint8)
                print(f"Created dummy lastread attribute with size {width}x{height}")

                # Also initialize the reader's lastread if it exists
                if hasattr(self.clip, 'reader') and self.clip.reader is not None:
                    try:
                        # Double-check that the reader is still valid before using it
                        reader = self.clip.reader
                        if reader is not None and hasattr(reader, 'lastread'):
                            if not hasattr(reader, 'lastread') or reader.lastread is None:
                                reader.lastread = self.clip.lastread.copy()
                                print("Also initialized reader's lastread attribute")
                        else:
                            print("Reader became None or invalid during lastread initialization")
                    except Exception as reader_lastread_error:
                        print(f"Error initializing reader's lastread: {reader_lastread_error}")

                return True
            except Exception as e:
                print(f"Error initializing lastread: {e}")
                # Last resort attempt with minimal size
                try:
                    import numpy as np
                    self.clip.lastread = np.zeros((64, 64, 3), dtype=np.uint8)
                    print("Created fallback lastread attribute (last resort)")

                    # Also try to initialize the reader's lastread as a last resort
                    if hasattr(self.clip, 'reader') and self.clip.reader is not None:
                        try:
                            # Double-check that the reader is still valid before using it
                            reader = self.clip.reader
                            if reader is not None:
                                reader.lastread = self.clip.lastread.copy()
                        except Exception:
                            pass  # Ignore errors in last resort attempt

                    return True
                except Exception as fallback_error:
                    print(f"Critical error: Could not create fallback lastread: {fallback_error}")
                    return False
        return True  # lastread was already initialized

    def _safe_extract_frame(self, t):
        """Safely extract a frame at the specified time with comprehensive error handling

        Args:
            t: Time in seconds to extract the frame from

        Returns:
            numpy array containing the frame
        """
        # Make sure we have a valid clip
        if self.clip is None:
            # Return a blank frame if no clip
            import numpy as np
            return np.zeros((64, 64, 3), dtype=np.uint8)

        # First make sure lastread is initialized
        self._ensure_lastread_initialized()

        # Try to get the frame using the standard method
        try:
            return self.clip.get_frame(t)
        except Exception as e:
            # If that fails, try to reinitialize lastread and try again
            try:
                # Hide this error message as it doesn't affect functionality
                # print(f"First frame extraction attempt failed: {e}, reinitializing lastread")
                self._ensure_lastread_initialized(force=True)
                return self.clip.get_frame(t)
            except Exception as retry_error:
                # If that still fails, try to access the reader directly
                try:
                    print(f"Second frame extraction attempt failed: {retry_error}, trying reader directly")
                    if hasattr(self.clip, 'reader') and self.clip.reader is not None:
                        # Double-check that the reader is still valid before using it
                        reader = self.clip.reader
                        if reader is not None and hasattr(reader, 'get_frame'):
                            # Try to initialize the reader's lastread attribute
                            if not hasattr(reader, 'lastread') or reader.lastread is None:
                                import numpy as np
                                reader.lastread = np.zeros((64, 64, 3), dtype=np.uint8)

                            # Try to get a frame from the reader
                            frame = reader.get_frame(t)
                            if frame is not None:
                                return frame
                        else:
                            print("Reader became None or invalid before get_frame() call")
                except Exception as reader_error:
                    print(f"Reader direct access failed: {reader_error}")

                # If all else fails, return a blank frame (but don't spam the console)
                if not hasattr(self, '_frame_extraction_error_logged'):
                    print("All frame extraction methods failed, returning blank frame")
                    self._frame_extraction_error_logged = True
                import numpy as np
                return np.zeros((64, 64, 3), dtype=np.uint8)

    def _show_error_message(self, message):
        """Show an error message on the canvas

        Args:
            message: The error message to display
        """
        try:
            # Check if the canvas still exists and is valid
            if not hasattr(self, 'canvas') or self.canvas is None:
                return

            # Check if the canvas is still a valid Tkinter widget
            try:
                # This will raise an error if the canvas has been destroyed
                self.canvas.winfo_exists()
            except Exception:
                return

            # Convert the background color to RGB tuple for error background
            try:
                bg_color = getattr(self, 'bg_color', '#f0f0f0')
                if bg_color.startswith('#'):
                    # Convert hex to RGB and darken it slightly for error state
                    hex_color = bg_color.lstrip('#')
                    rgb_color = tuple(max(0, int(hex_color[i:i+2], 16) - 30) for i in (0, 2, 4))
                else:
                    # Fallback to darker gray
                    rgb_color = (200, 200, 200)
            except Exception:
                # Fallback to darker gray
                rgb_color = (200, 200, 200)

            # Create an error background image with GUI-matching color
            image = Image.new("RGB", (self.width, self.height), color=rgb_color)
            draw = ImageDraw.Draw(image)

            # Try to load a font
            try:
                font_title = ImageFont.truetype("arial.ttf", 14)
                font_message = ImageFont.truetype("arial.ttf", 10)
            except Exception:
                # Use default font if truetype fails
                font_title = None
                font_message = None

            # Draw the error message with appropriate contrast
            title = "Video Load Error"
            text_color = (80, 80, 80)  # Dark gray text for better readability
            draw.text((self.width // 2, self.height // 2 - 20), title,
                      fill=text_color, font=font_title, anchor="mm")
            draw.text((self.width // 2, self.height // 2 + 20), message,
                      fill=text_color, font=font_message, anchor="mm")

            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(image)

            # Clear existing images first
            try:
                self.canvas.delete("video")
            except Exception:
                pass

            # Create the new image
            try:
                self.canvas.create_image(self.width // 2, self.height // 2,
                                         image=photo, anchor=tk.CENTER, tags="video")
                self.canvas.image = photo  # Keep a reference to prevent garbage collection
            except Exception as e:
                print(f"Error creating error message image: {e}")
        except Exception as e:
            print(f"Error showing error message: {e}")

    def _resize_frame(self, frame):
        """Resize a frame to fit the player while maintaining aspect ratio"""
        # Get the original dimensions
        orig_width, orig_height = frame.size

        # Calculate the scaling factors
        width_ratio = self.width / orig_width
        height_ratio = self.height / orig_height

        if self.scaling_mode == 'fill':
            # Use the larger ratio to fill the space (may crop video)
            ratio = max(width_ratio, height_ratio)
        else:
            # Use the smaller ratio to fit the entire video (may have bars)
            ratio = min(width_ratio, height_ratio)

        # Calculate the new dimensions
        new_width = int(orig_width * ratio)
        new_height = int(orig_height * ratio)

        # Resize the frame
        resized_frame = frame.resize((new_width, new_height), Image.LANCZOS)

        # If using 'fill' mode and the frame is larger than canvas, crop it
        if self.scaling_mode == 'fill' and (new_width > self.width or new_height > self.height):
            # Calculate crop box to center the image
            left = max(0, (new_width - self.width) // 2)
            top = max(0, (new_height - self.height) // 2)
            right = left + min(self.width, new_width)
            bottom = top + min(self.height, new_height)

            # Crop the frame
            resized_frame = resized_frame.crop((left, top, right, bottom))

        return resized_frame

    def set_scaling_mode(self, mode):
        """Set the video scaling mode

        Args:
            mode: 'fit' to show full video with possible bars, 'fill' to crop video to eliminate bars
        """
        if mode in ['fit', 'fill']:
            self.scaling_mode = mode
            # If a video is loaded, refresh the current frame to apply the new scaling
            if self.is_loaded:
                self._display_frame(self.current_frame_index)

    def _display_frame(self, frame_index):
        """Display the frame at the specified index with improved efficiency"""
        if not self.is_loaded or self.clip is None:
            return

        # Ensure frame index is within bounds
        frame_index = max(0, min(frame_index, self.total_frames - 1))
        self.current_frame_index = frame_index

        # Use a local variable to track if we need to load the frame directly
        need_direct_load = True
        frame_photo = None

        # First check if the frame is in the buffer (faster)
        try:
            with self.buffer_lock:
                for idx, photo in self.frame_buffer:
                    if idx == frame_index:
                        frame_photo = photo
                        need_direct_load = False
                        break
        except Exception as buffer_error:
            print(f"Error checking buffer for frame {frame_index}: {buffer_error}")

        # If frame is not in buffer, load it directly
        if need_direct_load:
            try:
                # Get the frame at the specified time
                t = frame_index / self.fps

                # ENHANCED LASTREAD HANDLING: Always check and initialize lastread before accessing frames
                self._ensure_lastread_initialized()

                # Load the frame with enhanced error handling
                try:
                    # Use our safe frame extraction method
                    frame = self._safe_extract_frame(t)
                except Exception as frame_error:
                    print(f"Error getting frame at time {t:.2f}s: {frame_error}")

                    # Create a blank frame as fallback
                    import numpy as np
                    frame = np.zeros((64, 64, 3), dtype=np.uint8)

                # Convert to PIL Image and resize
                pil_img = self._resize_frame(Image.fromarray(frame))

                # Convert to PhotoImage
                frame_photo = ImageTk.PhotoImage(pil_img)

                # Add to buffer for future use if there's space
                try:
                    with self.buffer_lock:
                        if len(self.frame_buffer) < self.buffer_size:
                            self.frame_buffer.append((frame_index, frame_photo))
                            self.frame_buffer.sort(key=lambda x: x[0])
                except Exception as buffer_add_error:
                    print(f"Error adding frame to buffer: {buffer_add_error}")
                    # Continue anyway since we already have the frame

            except Exception as e:
                print(f"Error loading frame {frame_index}: {e}")
                # Try to recover by showing a default image
                self.after_idle(self._create_default_image)
                return

        # If we have a frame to display, update the canvas in the main thread
        if frame_photo:
            # Use a local function to update the canvas
            def update_canvas(photo):
                try:
                    # Check if the canvas still exists and is valid
                    if not hasattr(self, 'canvas') or self.canvas is None:
                        return

                    # Check if the canvas is still a valid Tkinter widget
                    try:
                        # This will raise an error if the canvas has been destroyed
                        if not self.canvas.winfo_exists():
                            return
                    except Exception:
                        return

                    # Display the frame
                    try:
                        self.canvas.delete("video")
                    except Exception:
                        pass

                    try:
                        self.canvas.create_image(
                            self.width // 2,
                            self.height // 2,
                            image=photo,
                            anchor=tk.CENTER,
                            tags="video"
                        )
                        self.canvas.image = photo  # Keep a reference to prevent garbage collection
                    except Exception as e:
                        print(f"Error creating image on canvas: {e}")
                except Exception as canvas_error:
                    print(f"Error updating canvas: {canvas_error}")

            # Schedule the update in the main thread with safer approach
            try:
                if hasattr(self, 'after_idle') and callable(self.after_idle):
                    self.after_idle(lambda: update_canvas(frame_photo))
                else:
                    # Fallback if after_idle is not available
                    update_canvas(frame_photo)
            except Exception as e:
                print(f"Error scheduling canvas update: {e}")

    def play(self):
        """Start playing the video"""
        if not self.is_loaded or self.is_playing:
            return

        self.is_playing = True
        self.stop_requested = False
        self._play_next_frame()

    def _play_next_frame(self):
        """Play the next frame of the video with improved error handling and performance"""
        # Early exit if we're not supposed to be playing
        if not self.is_playing or not self.is_loaded or self.stop_requested:
            return

        # Cancel any existing scheduled frame to prevent duplicate scheduling
        if self.after_id:
            try:
                self.after_cancel(self.after_id)
            except Exception as cancel_error:
                print(f"Error canceling scheduled frame: {cancel_error}")
            self.after_id = None

        # Use a try-except block for the entire frame display and scheduling process
        try:
            # Display the current frame - this is the most likely place for errors
            try:
                self._display_frame(self.current_frame_index)
            except Exception as display_error:
                print(f"Error displaying frame {self.current_frame_index}: {display_error}")
                # If we can't display the current frame, try to continue with the next one
                # Don't re-raise the exception, just continue with scheduling the next frame

            # Calculate the next frame index
            next_frame = self.current_frame_index + 1

            # Check if we've reached the end
            if next_frame >= self.total_frames:
                if self.loop:
                    # Loop back to the beginning
                    next_frame = 0

                    # Schedule the next frame with a slightly longer delay for smoother looping
                    try:
                        self.after_id = self.after(int(self.frame_delay * 1.2), self._play_next_frame)
                    except Exception as schedule_error:
                        print(f"Error scheduling next frame: {schedule_error}")
                        # Try a fallback scheduling with a fixed delay
                        self.after_id = self.after(100, self._play_next_frame)
                else:
                    # Stop at the end
                    self.is_playing = False

                    # Generate an event to notify that playback has ended
                    try:
                        self.event_generate("<<Ended>>", when="tail")
                    except Exception as event_error:
                        print(f"Error generating end event: {event_error}")
                        # If we can't generate the event, try a direct approach
                        if hasattr(self, 'on_ended') and callable(self.on_ended):
                            try:
                                self.on_ended()
                            except Exception as callback_error:
                                print(f"Error calling on_ended callback: {callback_error}")
                    return
            else:
                # Schedule the next frame with adaptive timing
                # Use a shorter delay if we're behind schedule, longer if we're ahead
                try:
                    # Get the current time
                    current_time = time.time()

                    # If this is the first frame, initialize the frame timing
                    if not hasattr(self, 'last_frame_time'):
                        self.last_frame_time = current_time

                    # Calculate the time since the last frame
                    time_since_last_frame = current_time - self.last_frame_time

                    # Calculate the expected time between frames
                    expected_time = self.frame_delay / 1000.0

                    # Calculate the adjustment factor
                    if time_since_last_frame > 0:
                        adjustment = expected_time / time_since_last_frame
                        # Clamp the adjustment to a reasonable range
                        adjustment = max(0.5, min(1.5, adjustment))
                    else:
                        adjustment = 1.0

                    # Calculate the adjusted delay
                    adjusted_delay = int(self.frame_delay * adjustment)

                    # Clamp the delay to a reasonable range
                    adjusted_delay = max(1, min(100, adjusted_delay))

                    # Schedule the next frame with the adjusted delay
                    self.after_id = self.after(adjusted_delay, self._play_next_frame)

                    # Update the last frame time
                    self.last_frame_time = current_time
                except Exception as schedule_error:
                    print(f"Error in adaptive scheduling: {schedule_error}")
                    # Fall back to the standard delay
                    self.after_id = self.after(self.frame_delay, self._play_next_frame)

            # Update the frame index after scheduling to ensure smooth playback
            self.current_frame_index = next_frame

        except Exception as e:
            print(f"Error in _play_next_frame: {e}")

            # Track error count to detect persistent issues
            if not hasattr(self, 'play_error_count'):
                self.play_error_count = 0
            self.play_error_count += 1

            # If we have too many errors, try more drastic recovery measures
            if self.play_error_count > 5:
                print("Multiple playback errors detected, attempting recovery...")
                try:
                    # Reset error count
                    self.play_error_count = 0

                    # Clear the buffer to force fresh frame loading
                    with self.buffer_lock:
                        self.frame_buffer = []

                    # Jump to a different part of the video
                    recovery_frame = (self.current_frame_index + 10) % self.total_frames
                    self.current_frame_index = recovery_frame

                    # Schedule with a longer delay to give the system time to recover
                    self.after_id = self.after(self.frame_delay * 3, self._play_next_frame)
                except Exception as recovery_error:
                    print(f"Error during advanced playback recovery: {recovery_error}")
                    # Last resort: try to restart playback from the beginning with a long delay
                    try:
                        self.current_frame_index = 0
                        self.after_id = self.after(500, self._play_next_frame)  # 500ms delay
                    except Exception as final_error:
                        print(f"Critical error in playback, stopping: {final_error}")
                        self.is_playing = False
            else:
                # For occasional errors, just try to continue with the next frame
                try:
                    # Increment the frame counter to avoid getting stuck
                    self.current_frame_index = (self.current_frame_index + 1) % self.total_frames
                    # Schedule with a slightly longer delay
                    self.after_id = self.after(self.frame_delay * 1.5, self._play_next_frame)
                except Exception as simple_recovery_error:
                    print(f"Error during simple playback recovery: {simple_recovery_error}")
                    # If even this fails, try a very basic approach
                    try:
                        self.after_id = self.after(200, self._play_next_frame)  # 200ms delay
                    except Exception as basic_error:
                        print(f"Basic recovery failed, stopping playback: {basic_error}")
                        self.is_playing = False

    def pause(self):
        """Pause playback"""
        self.is_playing = False

        # Cancel any scheduled frame
        if self.after_id:
            self.after_cancel(self.after_id)
            self.after_id = None

    def stop(self):
        """Stop playback and reset to the first frame"""
        self.pause()
        self.stop_requested = True
        self.current_frame_index = 0

        # Clear the buffer
        with self.buffer_lock:
            self.frame_buffer = []

        # Display the first frame if available
        if self.is_loaded and self.clip is not None:
            self._display_frame(0)

    def toggle_playback(self, _=None):
        """Toggle between play and pause

        Args:
            _: Ignored parameter, typically an event from a button click
        """
        if self.is_playing:
            self.pause()
        else:
            self.play()

    def is_paused(self):
        """Check if playback is paused"""
        return self.is_loaded and not self.is_playing

    def enter_fullscreen(self, event=None):
        """Enter fullscreen mode"""
        if self.is_fullscreen or not self.is_loaded:
            return

        try:
            # Store original parent and pack info
            self.original_parent = self.canvas.master
            self.original_pack_info = self.canvas.pack_info()

            # Detect which monitor the main GUI window is on
            main_window = self._get_main_window()
            target_monitor = self._detect_current_monitor(main_window)

            # Create fullscreen window
            self.fullscreen_window = tk.Toplevel()
            self.fullscreen_window.title("Video Fullscreen")
            self.fullscreen_window.configure(bg='black')

            # Position the window on the correct monitor before going fullscreen
            if target_monitor:
                # Set the window geometry to the target monitor
                geometry = f"{target_monitor['width']}x{target_monitor['height']}+{target_monitor['x']}+{target_monitor['y']}"
                self.fullscreen_window.geometry(geometry)
                print(f"Positioning fullscreen window on monitor: {geometry}")

            # Now go fullscreen
            self.fullscreen_window.attributes('-fullscreen', True)
            self.fullscreen_window.attributes('-topmost', True)

            # Create fullscreen canvas
            self.fullscreen_canvas = tk.Canvas(
                self.fullscreen_window,
                bg='black',
                highlightthickness=0
            )
            self.fullscreen_canvas.pack(fill=tk.BOTH, expand=True)

            # Bind fullscreen events
            self.fullscreen_window.bind('<Escape>', self.exit_fullscreen)
            self.fullscreen_window.bind('<Double-Button-1>', self.exit_fullscreen)
            self.fullscreen_window.bind('<Button-1>', self.toggle_playback)
            self.fullscreen_window.bind('<space>', self.toggle_playback)
            self.fullscreen_window.bind('<Key>', self.handle_fullscreen_key)
            self.fullscreen_canvas.bind('<Button-1>', self.toggle_playback)
            self.fullscreen_canvas.bind('<Double-Button-1>', self.exit_fullscreen)

            # Focus the window to receive key events
            self.fullscreen_window.focus_set()

            # Reparent canvas to fullscreen window
            self.canvas.pack_forget()
            self.canvas = self.fullscreen_canvas

            # Update dimensions for fullscreen
            screen_width = self.fullscreen_window.winfo_screenwidth()
            screen_height = self.fullscreen_window.winfo_screenheight()
            self.width = screen_width
            self.height = screen_height

            self.is_fullscreen = True

            # Refresh current frame for fullscreen
            if self.is_loaded:
                self.after(50, lambda: self._display_frame(self.current_frame_index))

        except Exception as e:
            print(f"Error entering fullscreen: {e}")

    def exit_fullscreen(self, event=None):
        """Exit fullscreen mode"""
        if not self.is_fullscreen:
            return

        try:
            # Store current canvas reference
            old_canvas = self.canvas

            # Restore original canvas
            self.canvas = tk.Canvas(
                self.original_parent,
                width=self.original_pack_info.get('width', 300),
                height=self.original_pack_info.get('height', 300),
                bg=self.bg_color,
                highlightthickness=0
            )

            # Restore original bindings
            self.canvas.bind("<Button-1>", self.toggle_playback)
            self.canvas.bind("<Double-Button-1>", self.enter_fullscreen)

            # Pack the canvas back
            self.canvas.pack(**self.original_pack_info)

            # Restore original dimensions
            self.width = self.original_pack_info.get('width', 300)
            self.height = self.original_pack_info.get('height', 300)

            # Destroy fullscreen window
            if self.fullscreen_window:
                self.fullscreen_window.destroy()
                self.fullscreen_window = None

            self.fullscreen_canvas = None
            self.is_fullscreen = False

            # Refresh current frame for normal view
            if self.is_loaded:
                self.after(50, lambda: self._display_frame(self.current_frame_index))

        except Exception as e:
            print(f"Error exiting fullscreen: {e}")

    def handle_fullscreen_key(self, event):
        """Handle keyboard events in fullscreen mode"""
        try:
            if event.keysym == 'space':
                self.toggle_playback()
            elif event.keysym == 'Escape':
                self.exit_fullscreen()
        except Exception as e:
            print(f"Error handling fullscreen key: {e}")

    def _get_main_window(self):
        """Get the main window (root) from the widget hierarchy"""
        try:
            widget = self.canvas
            while widget.master is not None:
                widget = widget.master
            return widget
        except Exception as e:
            print(f"Error getting main window: {e}")
            return None

    def _detect_current_monitor(self, window):
        """Detect which monitor the given window is currently on"""
        try:
            if not window:
                return None

            # Get window position and size
            window.update_idletasks()  # Ensure geometry is up to date
            window_x = window.winfo_x()
            window_y = window.winfo_y()
            window_width = window.winfo_width()
            window_height = window.winfo_height()

            # Calculate window center
            window_center_x = window_x + window_width // 2
            window_center_y = window_y + window_height // 2

            print(f"Main window position: {window_x}, {window_y}, size: {window_width}x{window_height}")
            print(f"Window center: {window_center_x}, {window_center_y}")

            # Get all available monitors
            monitors = self._get_monitors()

            # Find which monitor contains the window center
            for monitor in monitors:
                if (monitor['x'] <= window_center_x < monitor['x'] + monitor['width'] and
                    monitor['y'] <= window_center_y < monitor['y'] + monitor['height']):
                    print(f"Window is on monitor: {monitor}")
                    return monitor

            # Fallback: return primary monitor (usually the first one)
            if monitors:
                print(f"Window not clearly on any monitor, using primary: {monitors[0]}")
                return monitors[0]

            return None

        except Exception as e:
            print(f"Error detecting current monitor: {e}")
            return None

    def _get_monitors(self):
        """Get information about all available monitors"""
        try:
            monitors = []

            # Try to get monitor information using tkinter
            try:
                # Get primary screen dimensions
                screen_width = self.canvas.winfo_screenwidth()
                screen_height = self.canvas.winfo_screenheight()

                # For now, we'll use a simple approach that works with most setups
                # This assumes the primary monitor starts at (0, 0)
                primary_monitor = {
                    'x': 0,
                    'y': 0,
                    'width': screen_width,
                    'height': screen_height
                }
                monitors.append(primary_monitor)

                # Try to detect additional monitors by checking if the window can be positioned at negative coordinates
                # This is a simple heuristic for multi-monitor setups
                try:
                    # Test if we can position a window at negative coordinates (common for multi-monitor)
                    test_window = tk.Toplevel()
                    test_window.withdraw()  # Hide it
                    test_window.geometry("1x1-1920+0")  # Try to position at -1920, 0
                    test_window.update_idletasks()

                    # If this works, we likely have a monitor to the left
                    test_x = test_window.winfo_x()
                    if test_x < 0:
                        # Add a monitor to the left
                        left_monitor = {
                            'x': -screen_width,
                            'y': 0,
                            'width': screen_width,
                            'height': screen_height
                        }
                        monitors.insert(0, left_monitor)  # Insert at beginning
                        print("Detected monitor to the left of primary")

                    test_window.destroy()

                except Exception:
                    pass  # Multi-monitor detection failed, use single monitor

                print(f"Detected {len(monitors)} monitor(s): {monitors}")
                return monitors

            except Exception as e:
                print(f"Error getting screen dimensions: {e}")
                # Fallback to a default monitor
                return [{'x': 0, 'y': 0, 'width': 1920, 'height': 1080}]

        except Exception as e:
            print(f"Error getting monitor information: {e}")
            # Return a default monitor as fallback
            return [{'x': 0, 'y': 0, 'width': 1920, 'height': 1080}]

    def seek(self, frame_index):
        """Seek to a specific frame"""
        if not self.is_loaded:
            return

        # Ensure the frame index is valid
        frame_index = max(0, min(frame_index, self.total_frames - 1))

        # Set the current frame
        self.current_frame_index = frame_index

        # Display the frame
        self._display_frame(frame_index)

    def set_loop(self, loop):
        """Set whether to loop playback"""
        self.loop = loop

    def set_playback_rate(self, rate):
        """Set the playback rate as a percentage (0.05 to 2.0)

        Args:
            rate: Playback rate as a float (0.05 = 5%, 1.0 = 100%, 2.0 = 200%)
        """
        # Ensure rate is within valid range
        rate = max(0.05, min(2.0, rate))

        # Only update if the rate has changed
        if rate != self.playback_rate:
            # Store the new rate
            self.playback_rate = rate

            # Update the frame delay
            if self.is_loaded and self.fps > 0:
                self.frame_delay = int(1000 / (self.fps * self.playback_rate))

            # Return the actual rate used (may be clamped)
            return self.playback_rate

    def destroy(self):
        """Clean up resources when the widget is destroyed"""
        try:
            # Exit fullscreen if active
            if self.is_fullscreen:
                try:
                    self.exit_fullscreen()
                except Exception:
                    pass

            # Set flags to prevent further operations
            self.is_loaded = False
            self.is_playing = False
            self.stop_requested = True

            # Cancel any pending after callbacks
            if hasattr(self, 'after_id') and self.after_id:
                try:
                    self.after_cancel(self.after_id)
                except Exception:
                    pass
                self.after_id = None

            # Stop playback
            try:
                self.stop()
            except Exception:
                pass

            # Stop the buffer thread
            try:
                self._stop_buffer_thread()
            except Exception:
                pass

            # Close the video clip with enhanced cleanup
            if self.clip is not None:
                try:
                    # Close the clip safely
                    if hasattr(self.clip, 'close'):
                        self.clip.close()

                    # Also close the reader if it exists
                    if hasattr(self.clip, 'reader') and self.clip.reader is not None:
                        try:
                            # Double-check that the reader is still valid before using it
                            reader = self.clip.reader
                            if reader is not None and hasattr(reader, 'close'):
                                reader.close()
                        except Exception:
                            pass
                        try:
                            self.clip.reader = None
                        except Exception:
                            pass

                    # Clear lastread attribute safely
                    if hasattr(self.clip, 'lastread'):
                        try:
                            self.clip.lastread = None
                        except Exception:
                            pass

                except Exception as e:
                    print(f"Error closing video clip: {e}")
                finally:
                    # Always clear the clip reference
                    try:
                        self.clip = None
                    except Exception:
                        pass

            # Clear references to prevent memory leaks
            if hasattr(self, 'canvas') and self.canvas is not None:
                try:
                    self.canvas.delete("all")
                    self.canvas.image = None
                except Exception:
                    pass

            # Clear the buffer completely
            if hasattr(self, 'frame_buffer'):
                try:
                    # Clear all PhotoImage references in the buffer
                    for frame_data in self.frame_buffer:
                        if len(frame_data) > 1 and frame_data[1] is not None:
                            try:
                                # PhotoImage objects don't have explicit cleanup, but clearing reference helps
                                frame_data[1] = None
                            except Exception:
                                pass
                    self.frame_buffer.clear()
                except Exception:
                    pass
                self.frame_buffer = []

            # Force garbage collection to help release memory
            try:
                import gc
                gc.collect()
            except Exception:
                pass

        except Exception as e:
            print(f"Error during MoviePyPlayer cleanup: {e}")
        finally:
            # Call the parent's destroy method
            try:
                super().destroy()
            except Exception as e:
                print(f"Error in parent destroy: {e}")
