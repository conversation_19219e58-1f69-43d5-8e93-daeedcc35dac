#!/usr/bin/env python3
"""
Performance Comparison Script for FramePack Preview Windows

This script compares the performance characteristics of different video player implementations:
1. Original MoviePyPlayer (if available)
2. High-Performance pyvidplayer2
3. Basic OpenCV implementation

Run this to see which video player performs best on your system.
"""

import time
import os
import sys
import psutil
import threading
from contextlib import contextmanager

# Test video player imports
print("=== FramePack Video Player Performance Comparison ===\n")

# Test pyvidplayer2 (High-Performance)
try:
    from pyvidplayer2 import VideoTkinter
    PYVIDPLAYER2_AVAILABLE = True
    print("✅ pyvidplayer2 (High-Performance): Available")
except ImportError:
    PYVIDPLAYER2_AVAILABLE = False
    print("❌ pyvidplayer2 (High-Performance): Not Available")

# Test MoviePyPlayer (Original)
try:
    from moviepy_player import MoviePyPlayer
    MOVIEPY_AVAILABLE = True
    print("✅ MoviePyPlayer (Original): Available")
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("❌ MoviePyPlayer (Original): Not Available")

# Test OpenCV (Basic)
try:
    import cv2
    OPENCV_AVAILABLE = True
    print("✅ OpenCV (Basic): Available")
except ImportError:
    OPENCV_AVAILABLE = False
    print("❌ OpenCV (Basic): Not Available")

print()

@contextmanager
def performance_monitor(name):
    """Context manager to monitor CPU and memory usage"""
    process = psutil.Process()
    start_time = time.time()
    start_cpu = process.cpu_percent()
    start_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"🔄 Testing {name}...")
    yield
    
    end_time = time.time()
    end_cpu = process.cpu_percent()
    end_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    duration = end_time - start_time
    cpu_usage = (start_cpu + end_cpu) / 2
    memory_delta = end_memory - start_memory
    
    print(f"   ⏱️  Duration: {duration:.2f}s")
    print(f"   🖥️  CPU Usage: {cpu_usage:.1f}%")
    print(f"   💾 Memory Delta: {memory_delta:+.1f}MB")
    print()

def test_file_scanning_performance():
    """Test file scanning performance"""
    print("📁 File Scanning Performance Test")
    print("-" * 40)
    
    # Test os.listdir (old method)
    with performance_monitor("os.listdir"):
        for _ in range(100):
            files = os.listdir('.')
            mp4_files = [f for f in files if f.endswith('.mp4')]
    
    # Test os.scandir (new method)
    with performance_monitor("os.scandir"):
        for _ in range(100):
            mp4_files = []
            with os.scandir('.') as entries:
                for entry in entries:
                    if entry.is_file() and entry.name.endswith('.mp4'):
                        mp4_files.append(entry.name)

def test_import_performance():
    """Test import performance of different video players"""
    print("📦 Import Performance Test")
    print("-" * 40)
    
    if PYVIDPLAYER2_AVAILABLE:
        with performance_monitor("pyvidplayer2 import"):
            for _ in range(10):
                import importlib
                importlib.reload(sys.modules.get('pyvidplayer2', __import__('pyvidplayer2')))
    
    if MOVIEPY_AVAILABLE:
        with performance_monitor("MoviePyPlayer import"):
            for _ in range(10):
                import importlib
                if 'moviepy_player' in sys.modules:
                    importlib.reload(sys.modules['moviepy_player'])
    
    if OPENCV_AVAILABLE:
        with performance_monitor("OpenCV import"):
            for _ in range(10):
                import importlib
                importlib.reload(sys.modules.get('cv2', __import__('cv2')))

def create_test_video():
    """Create a small test video for performance testing"""
    if not OPENCV_AVAILABLE:
        return None
    
    try:
        import numpy as np
        
        # Create a simple test video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter('test_performance.mp4', fourcc, 30.0, (640, 480))
        
        for i in range(90):  # 3 seconds at 30fps
            # Create a simple animated frame
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            cv2.circle(frame, (320 + int(100 * np.sin(i * 0.1)), 240), 50, (0, 255, 0), -1)
            cv2.putText(frame, f'Frame {i}', (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            out.write(frame)
        
        out.release()
        print("✅ Created test video: test_performance.mp4")
        return 'test_performance.mp4'
    except Exception as e:
        print(f"❌ Failed to create test video: {e}")
        return None

def cleanup_test_files():
    """Clean up test files"""
    test_files = ['test_performance.mp4']
    for file in test_files:
        try:
            if os.path.exists(file):
                os.remove(file)
                print(f"🗑️  Cleaned up: {file}")
        except Exception as e:
            print(f"❌ Failed to clean up {file}: {e}")

def main():
    """Main performance comparison function"""
    print("🚀 Starting Performance Comparison Tests...\n")
    
    # System information
    print("💻 System Information")
    print("-" * 40)
    print(f"   CPU: {psutil.cpu_count()} cores")
    print(f"   Memory: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f}GB")
    print(f"   Python: {sys.version.split()[0]}")
    print()
    
    # Test file scanning performance
    test_file_scanning_performance()
    
    # Test import performance
    test_import_performance()
    
    # Create test video for video player testing
    test_video = create_test_video()
    
    if test_video:
        print("🎬 Video Player Performance Test")
        print("-" * 40)
        print("Note: This test only measures initialization time.")
        print("Actual playback performance varies significantly.\n")
        
        # Test pyvidplayer2 performance
        if PYVIDPLAYER2_AVAILABLE:
            with performance_monitor("pyvidplayer2 initialization"):
                try:
                    import tkinter as tk
                    root = tk.Tk()
                    root.withdraw()  # Hide window
                    
                    player = VideoTkinter(
                        path=test_video,
                        master=root,
                        chunk_size=5,
                        max_threads=1,
                        max_chunks=3,
                        no_audio=True
                    )
                    time.sleep(0.5)  # Let it initialize
                    player.close()
                    root.destroy()
                except Exception as e:
                    print(f"   ❌ Error: {e}")
        
        # Test OpenCV performance
        if OPENCV_AVAILABLE:
            with performance_monitor("OpenCV VideoCapture"):
                try:
                    cap = cv2.VideoCapture(test_video)
                    for _ in range(30):  # Read 30 frames
                        ret, frame = cap.read()
                        if not ret:
                            break
                    cap.release()
                except Exception as e:
                    print(f"   ❌ Error: {e}")
    
    # Performance recommendations
    print("🎯 Performance Recommendations")
    print("-" * 40)
    
    if PYVIDPLAYER2_AVAILABLE:
        print("✅ RECOMMENDED: Use pyvidplayer2 for best performance")
        print("   - Hardware acceleration support")
        print("   - Efficient memory management")
        print("   - Professional video playback features")
        print("   - Active development and support")
    elif MOVIEPY_AVAILABLE:
        print("⚠️  FALLBACK: MoviePyPlayer available")
        print("   - Custom implementation")
        print("   - Limited format support")
        print("   - Higher CPU usage")
    elif OPENCV_AVAILABLE:
        print("⚠️  BASIC: OpenCV only")
        print("   - Basic video reading capabilities")
        print("   - No advanced playback features")
        print("   - Manual frame management required")
    else:
        print("❌ ERROR: No video players available!")
        print("   - Install pyvidplayer2: pip install pyvidplayer2")
        print("   - Or ensure OpenCV is available")
    
    print()
    
    # Cleanup
    cleanup_test_files()
    
    print("✅ Performance comparison completed!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        cleanup_test_files()
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        cleanup_test_files()
