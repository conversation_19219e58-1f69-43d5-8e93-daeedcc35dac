# FramePack - Core Dependencies with Exact Versions
# Essential packages only for running FramePack
# Python 3.10.11 target version

# Core ML/AI Dependencies
torch==2.8.0.dev20250627+cu128
torchvision==0.23.0.dev20250628+cu128
torchaudio==2.8.0.dev20250628+cu128
accelerate==1.6.0
diffusers==0.33.1
transformers==4.46.2
tokenizers==0.20.3
sentencepiece==0.2.0
torchsde==0.2.6
einops==0.8.1
safetensors==0.5.3

# Computer Vision and Image Processing
opencv-contrib-python==*********
pillow==11.1.0
numpy==1.26.2
scipy==1.12.0

# Face Detection (Optional but recommended)
mediapipe==0.10.21
face-recognition==1.3.0
face-recognition-models==0.3.0
dlib==20.0.0

# GUI Framework and Components
tkinterdnd2==0.4.3
tkvideoplayer==2.3
pyperclip==1.9.0

# Video Processing
moviepy==1.0.3
av==12.1.0

# Web Interface (Optional)
gradio==5.23.0
gradio_client==1.8.0

# HTTP and Networking
requests==2.31.0
httpx==0.28.1

# Utilities
tqdm==4.67.1
psutil==7.0.0

# Data Processing
pydantic==2.11.3
pydantic_core==2.33.1

# Background Removal (Optional)
rembg==2.0.66

# Mathematical Computing
sympy==1.14.0

# File Operations
send2trash==1.8.3

# Configuration
pyyaml==6.0.2

# Async Support
anyio==4.9.0

# Core Dependencies
certifi==2025.1.31
charset-normalizer==3.4.1
urllib3==2.4.0
idna==3.10
packaging==25.0
filelock==3.18.0
fsspec==2025.3.2
regex==2024.11.6
six==1.17.0
python-dateutil==2.9.0.post0

# Type Checking
typing-extensions==4.13.2
annotated-types==0.7.0

# Markup Processing
markupsafe==3.0.2
jinja2==3.1.6

# JSON Processing
orjson==3.10.16
jsonschema==4.24.0

# Protobuf
protobuf==4.25.8

# Intel Optimizations (Windows)
intel-openmp==2021.4.0
mkl==2021.4.0
tbb==2021.13.1

# CUDA Support (Windows)
triton-windows==3.2.0.post13

# ML Acceleration
numba==0.59.1
llvmlite==0.42.0

# Windows-specific
pywin32==310

# HuggingFace Hub
huggingface-hub==0.30.2

# Image Codecs
tifffile==2025.5.10
imageio==2.37.0

# Additional Core Utilities
colorama==0.4.6
click==8.1.8
rich==14.0.0
