# FramePack - Exact Package Versions
# Generated from virtual environment on 2025-07-04
# Python 3.10.11 target version
# All packages with exact versions as installed in the working environment

# Core ML/AI Dependencies
torch==2.8.0.dev20250627+cu128
torchvision==0.23.0.dev20250628+cu128
torchaudio==2.8.0.dev20250628+cu128
accelerate==1.6.0
diffusers==0.33.1
transformers==4.46.2
tokenizers==0.20.3
sentencepiece==0.2.0
torchsde==0.2.6
einops==0.8.1
safetensors==0.5.3

# Computer Vision and Image Processing
opencv-contrib-python==*********
opencv-python==*********
pillow==11.1.0
numpy==1.26.2
scipy==1.12.0
scikit-image==0.25.2
imageio==2.37.0
imageio-ffmpeg==0.4.9

# Face Detection and Recognition
mediapipe==0.10.21
face-recognition==1.3.0
face-recognition-models==0.3.0
dlib==20.0.0

# GUI Framework and Components
tkinterdnd2==0.4.3
tkvideoplayer==2.3
pyperclip==1.9.0

# Video Processing
moviepy==1.0.3
av==12.1.0
ffmpeg-python==0.2.0

# Web Interface
gradio==5.23.0
gradio_client==1.8.0
fastapi==0.115.12
starlette==0.46.2
uvicorn==0.34.2

# HTTP and Networking
requests==2.31.0
httpx==0.28.1
httpcore==1.0.8
websockets==15.0.1
websocket-client==1.8.0

# Utilities and Tools
tqdm==4.67.1
pyperclip==1.9.0
psutil==7.0.0
click==8.1.8
typer==0.15.2
rich==14.0.0

# Data Processing
pandas==2.2.3
pydantic==2.11.3
pydantic_core==2.33.1

# Development and Testing Tools
pytest==8.4.1
pytest-cov==6.2.1
black==25.1.0
flake8==7.3.0
mypy==1.16.1
mypy_extensions==1.1.1

# Documentation
sphinx==8.1.3
sphinx-rtd-theme==3.0.2

# Jupyter and Interactive Development
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-core==5.8.1
jupyter-server==2.16.0
jupyterlab==4.4.3
ipython==8.37.0
ipykernel==6.29.5

# Audio Processing
sounddevice==0.5.2
pydub==0.25.1

# Background Removal
rembg==2.0.66

# Mathematical and Scientific Computing
sympy==1.14.0
matplotlib==3.10.3

# File and System Operations
send2trash==1.8.3
pathspec==0.12.1

# Serialization and Configuration
pyyaml==6.0.2
tomlkit==0.13.2
tomli==2.2.1

# Async and Concurrency
anyio==4.9.0
async-lru==2.0.5

# Cryptography and Security
cffi==1.17.1
pycparser==2.22

# Color and Logging
colorama==0.4.6
coloredlogs==15.0.1

# Image and Video Codecs
tifffile==2025.5.10
decord==0.6.0

# Machine Learning Utilities
huggingface-hub==0.30.2
safehttpx==0.1.6

# Windows-specific
pywin32==310
pywinpty==2.0.15

# Additional Core Dependencies
certifi==2025.1.31
charset-normalizer==3.4.1
urllib3==2.4.0
idna==3.10
packaging==25.0
filelock==3.18.0
fsspec==2025.3.2
regex==2024.11.6
six==1.17.0
python-dateutil==2.9.0.post0
pytz==2025.2
tzdata==2025.2

# Type Checking and Validation
typing-extensions==4.13.2
annotated-types==0.7.0

# Markup and Text Processing
markupsafe==3.0.2
jinja2==3.1.6
markdown-it-py==3.0.0
mdurl==0.1.2

# JSON and Data Formats
orjson==3.10.16
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jsonpointer==3.0.0

# Protobuf and Serialization
protobuf==4.25.8

# Intel Optimizations
intel-openmp==2021.4.0
mkl==2021.4.0
tbb==2021.13.1

# CUDA and GPU Support (Windows)
triton-windows==3.2.0.post13

# Additional ML/AI Libraries
numba==0.59.1
llvmlite==0.42.0
onnxruntime==1.22.0

# Special Installation Note for SageAttention
# sageattention==2.2.0+cu128torch2.8.0 (installed from local wheel)
# This package was installed from: sageattention-2.2.0+cu128torch2.8.0-cp310-cp310-win_amd64.whl
# You may need to download this wheel separately or build from source

# JAX (optional for some advanced features)
jax==0.6.2
jaxlib==0.6.2

# Additional utilities found in environment
absl-py==2.3.0
arrow==1.3.0
babel==2.17.0
beautifulsoup4==4.13.4
bleach==6.2.0
comm==0.2.2
configparser==7.2.0
contourpy==1.3.2
coverage==7.9.1
cycler==0.12.1
debugpy==1.8.14
decorator==4.4.2
defusedxml==0.7.1
docutils==0.21.2
exceptiongroup==1.2.2
executing==2.2.0
fastjsonschema==2.21.1
ffmpy==0.5.0
flatbuffers==25.2.10
fonttools==4.58.4
fqdn==1.5.1
future==1.0.0
groovy==0.1.2
h11==0.14.0
humanfriendly==10.0
imagesize==1.4.1
importlib_metadata==8.6.1
iniconfig==2.1.0
ipywidgets==8.1.7
isoduration==20.11.0
jedi==0.19.2
json5==0.12.0
jupyter-events==0.12.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_server_terminals==0.5.3
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.15
kiwisolver==1.4.8
lazy_loader==0.4
matplotlib-inline==0.1.7
mccabe==0.7.0
mistune==3.1.3
ml_dtypes==0.5.1
mpmath==1.3.0
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.4.2
notebook==7.4.3
notebook_shim==0.2.4
opt_einsum==3.4.0
overrides==7.7.0
pandocfilters==1.5.1
parso==0.8.4
platformdirs==4.3.8
pluggy==1.6.0
pooch==1.8.2
proglog==0.1.11
prometheus_client==0.22.1
prompt_toolkit==3.0.51
pure_eval==0.2.3
pycodestyle==2.14.0
pyflakes==3.4.0
pygments==2.19.1
pymatting==1.1.14
pyparsing==3.2.3
pyreadline3==3.5.4
python-json-logger==3.3.0
python-multipart==0.0.20
python-vlc==3.0.21203
pyzmq==27.0.0
referencing==0.36.2
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rpds-py==0.25.1
ruff==0.11.6
semantic-version==2.10.0
shellingham==1.5.4
sniffio==1.3.1
snowballstemmer==3.0.1
soupsieve==2.7
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jquery==4.1
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
stack-data==0.6.3
terminado==0.18.1
tinycss2==1.4.0
tornado==6.5.1
traitlets==5.14.3
trampoline==0.1.2
types-python-dateutil==2.9.0.20250516
typing-inspection==0.4.0
uri-template==1.3.0
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
widgetsnbextension==4.0.14
zipp==3.21.0
