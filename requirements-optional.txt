# Optional requirements for FramePack
# These packages provide enhanced functionality but are not required for basic operation

# Enhanced Face Detection
mediapipe>=0.10.0
face-recognition>=1.3.0
dlib  # Required by face-recognition on some systems

# Enhanced Video Processing
ffmpeg-python  # For advanced video operations
imageio-ffmpeg  # Alternative video backend

# GPU Acceleration (CUDA)
# Uncomment the appropriate line for your CUDA version:
# torch-audio  # For audio processing in videos
# torchvision  # For additional computer vision operations

# Memory optimization
psutil  # For memory monitoring

# Advanced image processing
scikit-image  # For advanced image operations
imageio  # For reading various image formats

# Networking and downloads
urllib3
certifi

# Progress bars and UI enhancements
rich  # For better console output and progress bars

# File format support
h5py  # For HDF5 files (some models)
