accelerate==1.6.0
aiofiles==23.2.1
annotated-types==0.7.0
anyio==4.9.0
certifi==2025.4.26
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
Cython==0.29.37
decorator==4.4.2
diffusers==0.33.1
einops==0.8.1
exceptiongroup==1.2.2
fastapi==0.115.12
ffmpeg-python==0.2.0
ffmpy==0.5.0
filelock==3.13.1
fsspec==2024.6.1
future==1.0.0
gradio==5.23.0
gradio_client==1.8.0
groovy==0.1.2
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.30.2
idna==3.10
imageio==2.37.0
imageio-ffmpeg==0.4.9
importlib_metadata==8.7.0
Jinja2==3.1.3
markdown-it-py==3.0.0
MarkupSafe==2.1.5
mdurl==0.1.2
moviepy==1.0.3
mpmath==1.3.0
networkx==3.3
numpy==1.26.2
opencv-contrib-python==*********
orjson==3.10.18
packaging==25.0
pandas==2.2.3
pillow==11.1.0
proglog==0.1.11
psutil==7.0.0
pydantic==2.11.4
pydantic_core==2.33.2
pydub==0.25.1
Pygments==2.19.1
pyperclip==1.9.0
python-dateutil==2.9.0.post0
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
regex==2024.11.6
requests==2.31.0
rich==14.0.0
ruff==0.11.7
safehttpx==0.1.6
safetensors==0.5.3
sageattention==1.0.6
scipy==1.12.0
semantic-version==2.10.0
sentencepiece==0.2.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
starlette==0.46.2
sympy==1.13.1
tkinterdnd2==0.4.3
tokenizers==0.20.3
tomlkit==0.13.2
torch==2.5.1+cu121
torchaudio==2.5.1+cu121
torchsde==0.2.6
torchvision==0.20.1+cu121
tqdm==4.67.1
trampoline==0.1.2
transformers==4.46.2
triton @ https://github.com/woct0rdho/triton-windows/releases/download/v3.1.0-windows.post9/triton-3.1.0-cp310-cp310-win_amd64.whl
typer==0.15.3
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
urllib3==2.4.0
uvicorn==0.34.2
websockets==15.0.1
zipp==3.21.0
