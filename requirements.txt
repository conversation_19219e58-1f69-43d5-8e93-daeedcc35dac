# Core ML/AI Dependencies
torch>=2.0.0+cu118 --index-url https://download.pytorch.org/whl/cu118
torchvision>=0.15.0+cu118 --index-url https://download.pytorch.org/whl/cu118
torchaudio>=2.0.0+cu118 --index-url https://download.pytorch.org/whl/cu118
accelerate==1.6.0
diffusers==0.33.1
transformers==4.46.2
sentencepiece==0.2.0
torchsde==0.2.6
einops
safetensors

# Computer Vision and Image Processing
opencv-contrib-python>=4.8.0
pillow>=10.0.0
numpy>=1.24.0,<2.0.0
scipy>=1.10.0

# Optional Face Detection (for enhanced face cropping)
mediapipe
face-recognition

# GUI and Video Processing
tkinterdnd2
tkvideoplayer
moviepy

# Web Interface
gradio>=4.0.0

# Utilities
requests>=2.28.0
av>=10.0.0
tqdm>=4.64.0
pyperclip>=1.8.0

# Additional dependencies found in codebase
# configparser is built-in in Python 3.2+, no need to install separately

# Note: Some packages like tkinter, json, os, sys, threading, etc. are built-in Python modules
