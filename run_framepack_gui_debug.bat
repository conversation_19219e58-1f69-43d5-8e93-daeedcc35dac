@echo off
echo FramePack GUI with Drag and Drop Debugging
echo =========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    goto :end
)

echo Checking tkinterdnd2 installation...
python -c "import tkinterdnd2" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Warning: tkinterdnd2 is not installed. Attempting to install it...
    python -m pip install tkinterdnd2
    if %ERRORLEVEL% neq 0 (
        echo Error: Failed to install tkinterdnd2. Drag and drop will not work.
    ) else (
        echo Successfully installed tkinterdnd2.
    )
)

echo.
echo Starting FramePack GUI with debugging enabled...
echo (Drag and drop events will be logged to the console)
echo.

python framepack_gui.py %*

:end
pause
