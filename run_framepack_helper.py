import json
import subprocess
import sys
import os
import tempfile
import argparse

def run_framepack(settings_file=None, use_f1=False):
    """Helper script to run FramePack with settings from JSON file

    Args:
        settings_file (str, optional): Path to the settings JSON file.
                                      Defaults to 'framepack_default_settings.json'.
        use_f1 (bool, optional): Whether to use the F1 model. Defaults to False.
    """
    try:
        # Use default settings file if none provided
        if settings_file is None:
            settings_file = 'framepack_default_settings.json'

        print(f"Loading settings from: {settings_file}")

        # Load settings from JSON file
        with open(settings_file, 'r') as f:
            settings = json.load(f)

        # Determine which model to use
        model_type = settings.get('model_type', 'standard')
        use_f1_model = use_f1 or (model_type == 'f1')

        # Handle custom model selection
        custom_model_path = None
        if model_type == 'custom':
            custom_model_name = settings.get('custom_model', '')
            if custom_model_name and custom_model_name != "(Select a merged model)":
                custom_model_path = os.path.join("merged_models", custom_model_name)

                # Check if the custom model path contains 'f1' (case insensitive) to determine script
                if 'f1' in custom_model_name.lower():
                    use_f1_model = True
                    print(f"Using custom F1 merged model: {custom_model_name}")
                else:
                    print(f"Using custom standard merged model: {custom_model_name}")
            else:
                print("Warning: Custom model selected but no model chosen, falling back to standard model")

        # Build command and print model selection info
        if use_f1_model:
            cmd = [sys.executable, 'batch_f1_lock.py']
            if model_type == 'f1':
                print("Using F1 model")
            elif custom_model_path:
                pass  # Already printed above
            else:
                print("Using F1 model (forced by --use_f1 flag)")
        else:
            cmd = [sys.executable, 'batch.py']
            if model_type == 'standard':
                print("Using standard FramePack model")
            elif custom_model_path:
                pass  # Already printed above
            else:
                print("Using standard FramePack model (default)")

        # Add custom model path if specified
        if custom_model_path:
            cmd.extend(['--custom_model_path', custom_model_path])

        # Handle input mode
        input_mode = settings.get('input_mode', 'directory')
        selected_files = settings.get('selected_files', [])

        # Create a temporary file for file lists if needed
        temp_file = None

        if input_mode == 'directory':
            # Directory mode - use input_dir
            cmd.extend(['--input_dir', settings.get('input_dir', 'input')])
        elif input_mode == 'unified' and selected_files:
            # Unified mode - create a temporary file with all items (directories, files, URLs)
            temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt')
            for item in selected_files:
                temp_file.write(f"{item}\n")
            temp_file.close()
            cmd.extend(['--unified-list', temp_file.name])
        elif input_mode == 'files' and selected_files:
            # Files mode - use --files parameter with selected files
            cmd.append('--files')
            cmd.extend(selected_files)
        elif input_mode == 'urls' and selected_files:
            # URLs mode - create a temporary file with URLs
            temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt')
            for url in selected_files:
                temp_file.write(f"{url}\n")
            temp_file.close()
            cmd.extend(['--url-list', temp_file.name])
        elif input_mode == 'combined' and selected_files:
            # Combined mode - create a temporary file with mixed content
            temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt')
            for item in selected_files:
                temp_file.write(f"{item}\n")
            temp_file.close()
            cmd.extend(['--combined-list', temp_file.name])
        else:
            # Fallback to directory mode
            cmd.extend(['--input_dir', settings.get('input_dir', 'input')])

        # Add output directory
        cmd.extend(['--output_dir', settings.get('output_dir', 'output')])

        # Handle prompt
        if settings.get('fallback_prompt'):
            cmd.extend(['--prompt', settings.get('fallback_prompt')])

        # Handle negative prompt (only for standard models, not F1)
        if not use_f1_model and settings.get('negative_prompt'):
            cmd.extend(['--negative_prompt', settings.get('negative_prompt')])

        cmd.extend(['--seed', str(settings.get('seed', -1))])
        cmd.extend(['--video_length', str(settings.get('video_length', 5.0))])
        cmd.extend(['--steps', str(settings.get('steps', 25))])
        cmd.extend(['--distilled_cfg', str(settings.get('distilled_cfg', 10.0))])
        cmd.extend(['--gpu_memory', str(settings.get('gpu_memory', 6.0))])
        cmd.extend(['--mp4_crf', str(settings.get('mp4_crf', 16))])

        # Note: CFG parameter is not supported by either batch script
        # Standard batch.py uses fixed CFG=1.0, F1 batch scripts also use fixed CFG=1.0
        # Only distilled_cfg is configurable

        # Boolean flags
        if settings.get('use_teacache', True):
            cmd.append('--use_teacache')

        if settings.get('randomize_order', False):
            cmd.append('--randomize_order')

        if settings.get('clear_processed_list', True):
            cmd.append('--clear_processed_list')

        # Handle image prompt option
        if settings.get('use_image_prompt', True):
            cmd.append('--use_image_prompt')
        else:
            cmd.append('--no_image_prompt')

        if settings.get('overwrite', False):
            cmd.append('--overwrite')

        if settings.get('fix_encoding', True):
            cmd.append('--fix_encoding')
        else:
            cmd.append('--no_fix_encoding')

        if settings.get('use_prompt_list_file', False):
            cmd.append('--use_prompt_list_file')
            cmd.extend(['--prompt_list_file', settings.get('prompt_list_file', 'prompt_list.txt')])

        if settings.get('copy_to_input', True):
            cmd.append('--copy_to_input')
        else:
            cmd.append('--no_copy_to_input')

        # Handle apply_all_prompts setting
        if settings.get('apply_all_prompts', False):
            cmd.append('--apply_all_prompts')

        # Handle latent_window_size setting (for F1 model)
        if use_f1_model and 'latent_window_size' in settings:
            cmd.extend(['--latent_window_size', str(settings.get('latent_window_size', 9))])

        # Handle LoRA settings
        # For F1 models, use single LoRA parameters (F1 only supports one LoRA at a time)
        if use_f1_model:
            # Check for F1-style single LoRA first
            lora_file = settings.get('lora_file', '')
            lora_multiplier = settings.get('lora_multiplier', 0.8)

            # If no F1-style LoRA, check for standard-style LoRAs and use the first one
            if not lora_file:
                lora_file_1 = settings.get('lora_file_1', '')
                lora_multiplier_1 = settings.get('lora_multiplier_1', 0.8)
                if lora_file_1:
                    lora_file = lora_file_1
                    lora_multiplier = lora_multiplier_1
                    print(f"Note: F1 model only supports single LoRA. Using first LoRA: {lora_file_1}")

            if lora_file:
                cmd.extend(['--lora_file', lora_file])
                cmd.extend(['--lora_multiplier', str(lora_multiplier)])
        else:
            # For standard models, use multiple LoRA parameters
            lora_file_1 = settings.get('lora_file_1', '')
            lora_multiplier_1 = settings.get('lora_multiplier_1', 0.8)
            lora_file_2 = settings.get('lora_file_2', '')
            lora_multiplier_2 = settings.get('lora_multiplier_2', 0.8)
            lora_file_3 = settings.get('lora_file_3', '')
            lora_multiplier_3 = settings.get('lora_multiplier_3', 0.8)

            if lora_file_1:
                cmd.extend(['--lora_file_1', lora_file_1])
                cmd.extend(['--lora_multiplier_1', str(lora_multiplier_1)])
            if lora_file_2:
                cmd.extend(['--lora_file_2', lora_file_2])
                cmd.extend(['--lora_multiplier_2', str(lora_multiplier_2)])
            if lora_file_3:
                cmd.extend(['--lora_file_3', lora_file_3])
                cmd.extend(['--lora_multiplier_3', str(lora_multiplier_3)])

        # Handle FP8 optimization setting
        if settings.get('fp8_optimization', False):
            cmd.append('--fp8_optimization')

        # Handle allow_duplicates setting (this doesn't have a direct batch.py parameter,
        # but we'll include it for future compatibility)
        # Currently, this is handled in the GUI only

        # Create directories if they don't exist
        if input_mode == 'directory':
            os.makedirs(settings.get('input_dir', 'input'), exist_ok=True)
        os.makedirs(settings.get('output_dir', 'output'), exist_ok=True)

        # Print the command
        print("Running command:", " ".join(cmd))
        print()

        # Execute the command
        result = subprocess.run(cmd)

        # Clean up temporary file if created
        if temp_file and os.path.exists(temp_file.name):
            try:
                os.unlink(temp_file.name)
            except Exception as e:
                print(f"Warning: Could not delete temporary file {temp_file.name}: {e}")

        return result.returncode

    except Exception as e:
        print(f"ERROR: Failed to run FramePack: {str(e)}")
        return 1

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run FramePack with settings from a JSON file")
    parser.add_argument("settings", nargs="?", type=str, help="Path to the settings JSON file (default: framepack_default_settings.json)")
    parser.add_argument("--use_f1", action="store_true", help="Use the F1 model instead of the regular model")
    args = parser.parse_args()

    # Run FramePack with the specified settings file and model
    sys.exit(run_framepack(args.settings, args.use_f1))
