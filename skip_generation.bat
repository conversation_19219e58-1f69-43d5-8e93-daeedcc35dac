@echo off
echo FramePack Skip Generation Command
echo ================================
echo.
echo This will create a skip flag to tell <PERSON>ameP<PERSON> to skip the current generation.
echo.

:: Create the skip generation flag file
echo Creating skip generation flag...
echo Skip generation requested at %date% %time% > skip_generation.flag

if exist skip_generation.flag (
    echo ✓ Skip generation flag created successfully!
    echo.
    echo The current generation will be skipped when FramePack checks for the flag.
    echo This typically happens between frames or at the start of the next generation cycle.
) else (
    echo ✗ Failed to create skip generation flag!
    echo Please check file permissions in the FramePack directory.
)

echo.
echo Press any key to exit...
pause >nul
