#!/usr/bin/env python3
"""
FramePack Skip Generation Command
Creates a skip flag to tell FramePack to skip the current generation.
"""

import os
import time

def create_skip_flag():
    """Create the skip generation flag file"""
    try:
        skip_flag_path = "skip_generation.flag"
        
        print("FramePack Skip Generation Command")
        print("=" * 40)
        print()
        print("Creating skip generation flag...")
        
        with open(skip_flag_path, 'w') as f:
            f.write(f"Skip generation requested at {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        if os.path.exists(skip_flag_path):
            print("✓ Skip generation flag created successfully!")
            print()
            print("The current generation will be skipped when FramePack checks for the flag.")
            print("This typically happens between frames or at the start of the next generation cycle.")
            return True
        else:
            print("✗ Failed to create skip generation flag!")
            print("Please check file permissions in the FramePack directory.")
            return False
            
    except Exception as e:
        print(f"✗ Error creating skip generation flag: {e}")
        return False

if __name__ == "__main__":
    success = create_skip_flag()
    print()
    if success:
        print("Skip command sent successfully!")
    else:
        print("Failed to send skip command!")
    
    input("Press Enter to exit...")
