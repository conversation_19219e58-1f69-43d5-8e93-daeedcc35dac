"""
Standalone Preview Window for FramePack - OpenCV Version
High-Performance Video Player using OpenCV as fallback

Contains only the third column preview components: image preview, latent animation preview, and output video preview
with auto-resizing panes and control buttons (Force Stop All, Stop, Skip)

Uses OpenCV for reliable video playback when pyvidplayer2 is not available.
"""

import os
import time
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import configparser
import threading

# Import OpenCV for video playback
try:
    import cv2
    OPENCV_AVAILABLE = True
    print("OpenCV loaded successfully for video playback.")
except ImportError:
    print("OpenCV not available.")
    OPENCV_AVAILABLE = False
except Exception as e:
    print(f"Error importing OpenCV: {e}")
    OPENCV_AVAILABLE = False


class OpenCVVideoPlayer:
    """Simple OpenCV-based video player for Tkinter"""
    
    def __init__(self, video_path, canvas, loop=True):
        self.video_path = video_path
        self.canvas = canvas
        self.loop = loop
        self.cap = None
        self.playing = False
        self.current_frame = None
        self.fps = 30
        self.frame_delay = 33  # milliseconds
        self.total_frames = 0
        self.current_frame_num = 0
        
        self.load_video()
    
    def load_video(self):
        """Load the video file"""
        try:
            if self.cap:
                self.cap.release()
            
            self.cap = cv2.VideoCapture(self.video_path)
            if not self.cap.isOpened():
                print(f"Error: Could not open video {self.video_path}")
                return False
            
            # Get video properties
            self.fps = self.cap.get(cv2.CAP_PROP_FPS) or 30
            self.frame_delay = int(1000 / self.fps)
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            print(f"Loaded video: {os.path.basename(self.video_path)} ({self.total_frames} frames, {self.fps:.1f} fps)")
            return True
            
        except Exception as e:
            print(f"Error loading video: {e}")
            return False
    
    def play(self):
        """Start video playback"""
        self.playing = True
        self.update_frame()
    
    def stop(self):
        """Stop video playback"""
        self.playing = False
    
    def update_frame(self):
        """Update the current frame"""
        if not self.playing or not self.cap:
            return
        
        try:
            ret, frame = self.cap.read()
            if not ret:
                if self.loop:
                    # Restart video
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    self.current_frame_num = 0
                    ret, frame = self.cap.read()
                else:
                    self.playing = False
                    return
            
            if ret:
                # Convert BGR to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Resize frame to fit canvas
                canvas_width = self.canvas.winfo_width()
                canvas_height = self.canvas.winfo_height()
                
                if canvas_width > 1 and canvas_height > 1:
                    # Calculate scaling to fit canvas while maintaining aspect ratio
                    frame_height, frame_width = frame_rgb.shape[:2]
                    scale_x = canvas_width / frame_width
                    scale_y = canvas_height / frame_height
                    scale = min(scale_x, scale_y)
                    
                    new_width = int(frame_width * scale)
                    new_height = int(frame_height * scale)
                    
                    # Resize frame
                    frame_resized = cv2.resize(frame_rgb, (new_width, new_height))
                    
                    # Convert to PIL Image and then to PhotoImage
                    pil_image = Image.fromarray(frame_resized)
                    photo = ImageTk.PhotoImage(pil_image)
                    
                    # Clear canvas and draw frame
                    self.canvas.delete("all")
                    x = canvas_width // 2
                    y = canvas_height // 2
                    self.canvas.create_image(x, y, image=photo, anchor=tk.CENTER)
                    
                    # Keep reference to prevent garbage collection
                    self.current_frame = photo
                    
                    self.current_frame_num += 1
            
            # Schedule next frame update
            if self.playing:
                self.canvas.after(self.frame_delay, self.update_frame)
                
        except Exception as e:
            print(f"Error updating frame: {e}")
            self.playing = False
    
    def close(self):
        """Close the video player"""
        self.playing = False
        if self.cap:
            self.cap.release()
            self.cap = None


class OpenCVPreviewWindow:
    def __init__(self, root):
        self.root = root
        self.root.title("FramePack Preview Window - OpenCV")
        self.root.geometry("900x1000")
        self.root.resizable(True, True)
        self.root.minsize(500, 600)
        
        # Initialize preview state tracking
        self.preview_state = {
            'latent_loading': False,
            'output_loading': False,
            'image_loading': False,
            'current_latent': None,
            'current_output': None,
            'current_image': None,
        }
        
        # Initialize video player variables
        self.latent_video_player = None
        self.output_video_player = None
        self.image_preview_photo = None
        
        # Performance settings
        self.update_interval = 2000  # Update every 2 seconds
        
        # Create the UI
        self.create_ui()
        
        # Start preview monitoring
        if OPENCV_AVAILABLE:
            self.root.after(3000, self.update_previews)  # Start after UI is ready
        
        # Load window state
        self.load_window_state()
        
        # Bind window close event to save state
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Bind resize events
        self.root.bind('<Configure>', self.on_window_resize)
    
    def create_ui(self):
        """Create the user interface"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control buttons frame at the top
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Control buttons
        self.force_stop_all_button = ttk.Button(
            control_frame,
            text="Force Stop All",
            command=self.force_stop_all,
            width=15
        )
        self.force_stop_all_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(
            control_frame,
            text="Stop",
            command=self.stop_generation,
            width=10
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.skip_button = ttk.Button(
            control_frame,
            text="Skip",
            command=self.skip_generation,
            width=10
        )
        self.skip_button.pack(side=tk.LEFT, padx=5)
        
        # Performance indicator
        self.performance_label = ttk.Label(
            control_frame,
            text="OpenCV Mode (Reliable Fallback)" if OPENCV_AVAILABLE else "No Video Support",
            font=('Arial', 8)
        )
        self.performance_label.pack(side=tk.RIGHT, padx=5)
        
        # Create main vertical paned window for previews
        self.main_preview_paned_window = ttk.PanedWindow(main_frame, orient=tk.VERTICAL)
        self.main_preview_paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Create top previews frame (image and latent side by side)
        top_previews_frame = ttk.Frame(self.main_preview_paned_window)
        self.main_preview_paned_window.add(top_previews_frame, weight=1)
        
        # Create horizontal paned window for image and latent previews
        self.preview_paned_window = ttk.PanedWindow(top_previews_frame, orient=tk.HORIZONTAL)
        self.preview_paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Create preview frames
        self.create_image_preview_frame()
        self.create_latent_preview_frame()
        self.create_output_preview_frame()
        
        # Bind double-click to set equal pane sizes
        self.preview_paned_window.bind('<Double-Button-1>', self.set_equal_preview_pane_sizes)
    
    def create_image_preview_frame(self):
        """Create the image preview frame"""
        image_preview_frame = ttk.LabelFrame(self.preview_paned_window, text="Image Preview")
        self.preview_paned_window.add(image_preview_frame, weight=1)
        
        # Create frame to hold the image preview
        self.image_preview_frame = ttk.Frame(image_preview_frame)
        self.image_preview_frame.pack(fill=tk.BOTH, padx=5, pady=5, expand=True)
        
        # Create label to display the image
        self.image_preview_label = ttk.Label(
            self.image_preview_frame, 
            text="No image available - Start generation to see preview"
        )
        self.image_preview_label.pack(fill=tk.BOTH, expand=True)
        
        # Create status label
        self.image_preview_status_label = ttk.Label(
            image_preview_frame, 
            text="No image available"
        )
        self.image_preview_status_label.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Bind double-click to refresh
        self.image_preview_status_label.bind("<Double-Button-1>", self.refresh_image_preview)
    
    def create_latent_preview_frame(self):
        """Create the latent preview frame with OpenCV video player"""
        latent_preview_frame = ttk.LabelFrame(self.preview_paned_window, text="Latent Preview (OpenCV)")
        self.preview_paned_window.add(latent_preview_frame, weight=1)
        
        # Create canvas for video player
        self.latent_video_canvas = tk.Canvas(
            latent_preview_frame,
            bg='black',
            highlightthickness=0
        )
        self.latent_video_canvas.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Create status label
        self.latent_preview_status_label = ttk.Label(
            latent_preview_frame, 
            text="No latent preview available - Start generation to see preview"
        )
        self.latent_preview_status_label.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Bind double-click to refresh
        self.latent_preview_status_label.bind("<Double-Button-1>", self.refresh_latent_preview)
    
    def create_output_preview_frame(self):
        """Create the output video preview frame with OpenCV video player"""
        output_preview_frame = ttk.LabelFrame(self.main_preview_paned_window, text="Output Video Preview (OpenCV)")
        self.main_preview_paned_window.add(output_preview_frame, weight=1)
        
        # Create canvas for video player
        self.output_video_canvas = tk.Canvas(
            output_preview_frame,
            bg='black',
            highlightthickness=0
        )
        self.output_video_canvas.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Create status label
        self.output_preview_status_label = ttk.Label(
            output_preview_frame, 
            text="No output video available - Complete generation to see result"
        )
        self.output_preview_status_label.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Bind double-click to refresh
        self.output_preview_status_label.bind("<Double-Button-1>", self.refresh_output_preview)

    # Control button methods
    def force_stop_all(self):
        """Force terminate all FramePack processes"""
        try:
            # Create stop flag files
            stop_files = ["stop_framepack.flag", "stop_queue.flag", "skip_generation.flag"]
            for stop_file in stop_files:
                try:
                    with open(stop_file, 'w') as f:
                        f.write(f"Force stop all requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"Created {stop_file}")
                except Exception as e:
                    print(f"Error creating {stop_file}: {e}")

            print("Force Stop All: Created all stop flag files")

        except Exception as e:
            print(f"Error in force_stop_all: {e}")

    def stop_generation(self):
        """Stop the current generation"""
        try:
            # Create stop queue flag
            with open("stop_queue.flag", 'w') as f:
                f.write(f"Stop requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("Stop: Created stop_queue.flag")

        except Exception as e:
            print(f"Error in stop_generation: {e}")

    def skip_generation(self):
        """Skip the current generation"""
        try:
            # Create skip generation flag
            with open("skip_generation.flag", 'w') as f:
                f.write(f"Skip requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("Skip: Created skip_generation.flag")

        except Exception as e:
            print(f"Error in skip_generation: {e}")

    # Preview update methods
    def update_previews(self):
        """Update all preview displays"""
        try:
            # Update image preview
            self.update_image_preview()

            # Update latent preview
            self.update_latent_preview()

            # Update output preview
            self.update_output_preview()

        except Exception as e:
            print(f"Error updating previews: {e}")
        finally:
            # Schedule next update
            self.root.after(self.update_interval, self.update_previews)

    def update_image_preview(self):
        """Update the image preview"""
        try:
            if self.preview_state['image_loading']:
                return

            # Find latest start image
            image_path = self.find_latest_start_image()
            if image_path and image_path != self.preview_state['current_image']:
                if os.path.exists(image_path):
                    self.load_image_preview(image_path)

        except Exception as e:
            print(f"Error updating image preview: {e}")

    def update_latent_preview(self):
        """Update the latent preview with OpenCV video player"""
        try:
            if self.preview_state['latent_loading']:
                return

            # Find latest latent preview
            video_path = self.find_latest_latent_preview()
            if video_path and video_path != self.preview_state['current_latent']:
                if os.path.exists(video_path) and os.path.getsize(video_path) > 1000:
                    self.preview_state['latent_loading'] = True
                    success = self.create_latent_video_player(video_path)
                    if success:
                        self.preview_state['current_latent'] = video_path
                        self.latent_preview_status_label.config(
                            text=f"Playing: {os.path.basename(video_path)} (OpenCV)"
                        )
                    self.preview_state['latent_loading'] = False

        except Exception as e:
            print(f"Error updating latent preview: {e}")
            self.preview_state['latent_loading'] = False

    def update_output_preview(self):
        """Update the output preview with OpenCV video player"""
        try:
            if self.preview_state['output_loading']:
                return

            # Find latest output video
            video_path = self.find_latest_output_video()
            if video_path and video_path != self.preview_state['current_output']:
                if os.path.exists(video_path) and os.path.getsize(video_path) > 1000:
                    self.preview_state['output_loading'] = True
                    success = self.create_output_video_player(video_path)
                    if success:
                        self.preview_state['current_output'] = video_path
                        self.output_preview_status_label.config(
                            text=f"Playing: {os.path.basename(video_path)} (OpenCV)"
                        )
                    self.preview_state['output_loading'] = False

        except Exception as e:
            print(f"Error updating output preview: {e}")
            self.preview_state['output_loading'] = False
