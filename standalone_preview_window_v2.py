"""
Standalone Preview Window for FramePack - Version 2
High-Performance Video Player using pyvidplayer2

Contains only the third column preview components: image preview, latent animation preview, and output video preview
with auto-resizing panes and control buttons (Force Stop All, Stop, Skip)

Uses pyvidplayer2 for superior video performance with hardware acceleration support.
"""

import os
import time
import threading
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import configparser

# Import high-performance video player
try:
    import pyvidplayer2
    from pyvidplayer2 import VideoTkinter
    PYVIDPLAYER2_AVAILABLE = True
    print("pyvidplayer2 loaded successfully for high-performance video playback.")
    print(f"pyvidplayer2 version: {getattr(pyvidplayer2, '__version__', 'unknown')}")
except ImportError as e:
    print(f"pyvidplayer2 not available. Install with: pip install pyvidplayer2")
    print(f"Import error: {e}")
    PYVIDPLAYER2_AVAILABLE = False
except Exception as e:
    print(f"Error importing pyvidplayer2: {e}")
    PYVIDPLAYER2_AVAILABLE = False


class HighPerformancePreviewWindow:
    def __init__(self, root):
        self.root = root
        self.root.title("FramePack Preview Window - High Performance")
        self.root.geometry("900x1000")
        self.root.resizable(True, True)
        self.root.minsize(500, 600)
        
        # Initialize preview state tracking
        self.preview_state = {
            'latent_loading': False,
            'output_loading': False,
            'image_loading': False,
            'current_latent': None,
            'current_output': None,
            'current_image': None,
        }
        
        # Initialize video player variables
        self.latent_video_player = None
        self.output_video_player = None
        self.image_preview_photo = None
        self.current_image_preview = None
        
        # Performance settings
        self.update_interval = 1000  # Update every 1 second for better performance
        self.video_scaling_mode = 'fit'  # Scale videos to fit while maintaining aspect ratio
        
        # Create the UI
        self.create_ui()
        
        # Start preview monitoring
        if PYVIDPLAYER2_AVAILABLE:
            self.root.after(2000, self.update_previews)  # Start after UI is ready
        
        # Load window state
        self.load_window_state()
        
        # Bind window close event to save state
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Bind resize events
        self.root.bind('<Configure>', self.on_window_resize)
    
    def create_ui(self):
        """Create the user interface"""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control buttons frame at the top
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Control buttons
        self.force_stop_all_button = ttk.Button(
            control_frame,
            text="Force Stop All",
            command=self.force_stop_all,
            width=15
        )
        self.force_stop_all_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(
            control_frame,
            text="Stop",
            command=self.stop_generation,
            width=10
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        self.skip_button = ttk.Button(
            control_frame,
            text="Skip",
            command=self.skip_generation,
            width=10
        )
        self.skip_button.pack(side=tk.LEFT, padx=5)
        
        # Performance indicator
        self.performance_label = ttk.Label(
            control_frame,
            text="High-Performance Mode: pyvidplayer2" if PYVIDPLAYER2_AVAILABLE else "Standard Mode",
            font=('Arial', 8)
        )
        self.performance_label.pack(side=tk.RIGHT, padx=5)
        
        # Create main vertical paned window for previews
        self.main_preview_paned_window = ttk.PanedWindow(main_frame, orient=tk.VERTICAL)
        self.main_preview_paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Note: ttk.PanedWindow doesn't support sashwidth configuration
        # The sash width is controlled by the theme
        
        # Create top previews frame (image and latent side by side)
        top_previews_frame = ttk.Frame(self.main_preview_paned_window)
        self.main_preview_paned_window.add(top_previews_frame, weight=1)
        
        # Create horizontal paned window for image and latent previews
        self.preview_paned_window = ttk.PanedWindow(top_previews_frame, orient=tk.HORIZONTAL)
        self.preview_paned_window.pack(fill=tk.BOTH, expand=True)
        
        # Note: ttk.PanedWindow doesn't support sashwidth configuration
        # The sash width is controlled by the theme
        
        # Create preview frames
        self.create_image_preview_frame()
        self.create_latent_preview_frame()
        self.create_output_preview_frame()
        
        # Bind resize events
        self.main_preview_paned_window.bind('<Configure>', self.on_preview_pane_resize)
        self.main_preview_paned_window.bind('<ButtonRelease-1>', self.on_preview_pane_resize)
        self.preview_paned_window.bind('<Configure>', self.on_preview_pane_resize)
        self.preview_paned_window.bind('<ButtonRelease-1>', self.on_preview_pane_resize)
        
        # Bind double-click to set equal pane sizes
        self.preview_paned_window.bind('<Double-Button-1>', self.set_equal_preview_pane_sizes)
    
    def create_image_preview_frame(self):
        """Create the image preview frame"""
        image_preview_frame = ttk.LabelFrame(self.preview_paned_window, text="Image Preview")
        self.preview_paned_window.add(image_preview_frame, weight=1)
        
        # Create frame to hold the image preview
        self.image_preview_frame = ttk.Frame(image_preview_frame)
        self.image_preview_frame.pack(fill=tk.BOTH, padx=5, pady=5, expand=True)
        
        # Create label to display the image
        self.image_preview_label = ttk.Label(
            self.image_preview_frame, 
            text="No image available - Start generation to see preview"
        )
        self.image_preview_label.pack(fill=tk.BOTH, expand=True)
        
        # Create status label
        self.image_preview_status_label = ttk.Label(
            image_preview_frame, 
            text="No image available"
        )
        self.image_preview_status_label.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Bind double-click to refresh
        self.image_preview_status_label.bind("<Double-Button-1>", self.refresh_image_preview)
    
    def create_latent_preview_frame(self):
        """Create the latent preview frame with high-performance video player"""
        latent_preview_frame = ttk.LabelFrame(self.preview_paned_window, text="Latent Preview (High-Performance)")
        self.preview_paned_window.add(latent_preview_frame, weight=1)
        
        # Create frame to hold the video player
        self.latent_video_frame = ttk.Frame(latent_preview_frame)
        self.latent_video_frame.pack(fill=tk.BOTH, padx=2, pady=2, expand=True)
        
        # Initialize placeholder for video player (will be created when needed)
        self.latent_video_placeholder = ttk.Label(
            self.latent_video_frame,
            text="High-Performance Video Player Ready\nWaiting for latent preview..." if PYVIDPLAYER2_AVAILABLE else "pyvidplayer2 not available",
            justify=tk.CENTER
        )
        self.latent_video_placeholder.pack(fill=tk.BOTH, expand=True)
        
        # Create status label
        self.latent_preview_status_label = ttk.Label(
            latent_preview_frame, 
            text="No latent preview available - Start generation to see preview"
        )
        self.latent_preview_status_label.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Bind double-click to refresh
        self.latent_preview_status_label.bind("<Double-Button-1>", self.refresh_latent_preview)
    
    def create_output_preview_frame(self):
        """Create the output video preview frame with high-performance video player"""
        output_preview_frame = ttk.LabelFrame(self.main_preview_paned_window, text="Output Video Preview (High-Performance)")
        self.main_preview_paned_window.add(output_preview_frame, weight=1)
        
        # Create frame to hold the output video player
        self.output_video_frame = ttk.Frame(output_preview_frame)
        self.output_video_frame.pack(fill=tk.BOTH, padx=2, pady=2, expand=True)
        
        # Initialize placeholder for video player (will be created when needed)
        self.output_video_placeholder = ttk.Label(
            self.output_video_frame,
            text="High-Performance Video Player Ready\nWaiting for output video..." if PYVIDPLAYER2_AVAILABLE else "pyvidplayer2 not available",
            justify=tk.CENTER
        )
        self.output_video_placeholder.pack(fill=tk.BOTH, expand=True)
        
        # Create status label
        self.output_preview_status_label = ttk.Label(
            output_preview_frame, 
            text="No output video available - Complete generation to see result"
        )
        self.output_preview_status_label.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        # Bind double-click to refresh
        self.output_preview_status_label.bind("<Double-Button-1>", self.refresh_output_preview)

    # Control button methods
    def force_stop_all(self):
        """Force terminate all FramePack processes"""
        try:
            # Create stop flag files
            stop_files = ["stop_framepack.flag", "stop_queue.flag", "skip_generation.flag"]
            for stop_file in stop_files:
                try:
                    with open(stop_file, 'w') as f:
                        f.write(f"Force stop all requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"Created {stop_file}")
                except Exception as e:
                    print(f"Error creating {stop_file}: {e}")

            print("Force Stop All: Created all stop flag files")

        except Exception as e:
            print(f"Error in force_stop_all: {e}")

    def stop_generation(self):
        """Stop the current generation"""
        try:
            # Create stop queue flag
            with open("stop_queue.flag", 'w') as f:
                f.write(f"Stop requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("Stop: Created stop_queue.flag")

        except Exception as e:
            print(f"Error in stop_generation: {e}")

    def skip_generation(self):
        """Skip the current generation"""
        try:
            # Create skip generation flag
            with open("skip_generation.flag", 'w') as f:
                f.write(f"Skip requested at {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("Skip: Created skip_generation.flag")

        except Exception as e:
            print(f"Error in skip_generation: {e}")

    # High-performance video player methods
    def create_latent_video_player(self, video_path):
        """Create high-performance latent video player"""
        try:
            if not PYVIDPLAYER2_AVAILABLE:
                return False

            # Remove placeholder
            if hasattr(self, 'latent_video_placeholder'):
                self.latent_video_placeholder.destroy()

            # Remove existing canvas
            if hasattr(self, 'latent_video_canvas'):
                self.latent_video_canvas.destroy()

            # Close existing player
            if self.latent_video_player:
                try:
                    self.latent_video_player.close()
                except:
                    pass

            # Create new video player
            self.latent_video_player = VideoTkinter(
                path=video_path,
                chunk_size=5,  # Smaller chunks for better responsiveness
                max_threads=1,  # Single thread for stability
                max_chunks=3,   # Limit memory usage
                speed=1.0,
                no_audio=True,  # Disable audio for latent previews
                interp='linear'  # Good balance of quality and performance
            )

            # Create canvas for video player
            self.latent_video_canvas = tk.Canvas(
                self.latent_video_frame,
                width=self.latent_video_player.current_size[0],
                height=self.latent_video_player.current_size[1],
                highlightthickness=0,
                bg='black'
            )
            self.latent_video_canvas.pack(fill=tk.BOTH, expand=True)

            # Start playback
            self.latent_video_player.play()

            # Start update loop
            self.update_latent_video_canvas()

            print(f"Created high-performance latent video player for: {os.path.basename(video_path)}")
            return True

        except Exception as e:
            print(f"Error creating latent video player: {e}")
            # Restore placeholder on error
            self.latent_video_placeholder = ttk.Label(
                self.latent_video_frame,
                text=f"Error loading video:\n{str(e)}",
                justify=tk.CENTER
            )
            self.latent_video_placeholder.pack(fill=tk.BOTH, expand=True)
            return False

    def create_output_video_player(self, video_path):
        """Create high-performance output video player"""
        try:
            if not PYVIDPLAYER2_AVAILABLE:
                return False

            # Remove placeholder
            if hasattr(self, 'output_video_placeholder'):
                self.output_video_placeholder.destroy()

            # Remove existing canvas
            if hasattr(self, 'output_video_canvas'):
                self.output_video_canvas.destroy()

            # Close existing player
            if self.output_video_player:
                try:
                    self.output_video_player.close()
                except:
                    pass

            # Create new video player
            self.output_video_player = VideoTkinter(
                path=video_path,
                chunk_size=10,  # Larger chunks for output videos
                max_threads=1,  # Single thread for stability
                max_chunks=5,   # More chunks for smoother playback
                speed=1.0,
                no_audio=False,  # Enable audio for output videos
                interp='linear'  # Good balance of quality and performance
            )

            # Create canvas for video player
            self.output_video_canvas = tk.Canvas(
                self.output_video_frame,
                width=self.output_video_player.current_size[0],
                height=self.output_video_player.current_size[1],
                highlightthickness=0,
                bg='black'
            )
            self.output_video_canvas.pack(fill=tk.BOTH, expand=True)

            # Start playback
            self.output_video_player.play()

            # Start update loop
            self.update_output_video_canvas()

            print(f"Created high-performance output video player for: {os.path.basename(video_path)}")
            return True

        except Exception as e:
            print(f"Error creating output video player: {e}")
            # Restore placeholder on error
            self.output_video_placeholder = ttk.Label(
                self.output_video_frame,
                text=f"Error loading video:\n{str(e)}",
                justify=tk.CENTER
            )
            self.output_video_placeholder.pack(fill=tk.BOTH, expand=True)
            return False

    # Canvas update methods for VideoTkinter
    def update_latent_video_canvas(self):
        """Update the latent video canvas"""
        try:
            if (hasattr(self, 'latent_video_player') and
                self.latent_video_player and
                hasattr(self, 'latent_video_canvas') and
                self.latent_video_canvas.winfo_exists()):

                # Draw video frame on canvas
                canvas_width = self.latent_video_canvas.winfo_width()
                canvas_height = self.latent_video_canvas.winfo_height()

                if canvas_width > 1 and canvas_height > 1:
                    # Calculate center position
                    center_x = canvas_width / 2
                    center_y = canvas_height / 2

                    # Draw the video frame
                    self.latent_video_player.draw(
                        self.latent_video_canvas,
                        (center_x, center_y),
                        force_draw=False
                    )

                # Continue updating if video is active
                if self.latent_video_player.active:
                    self.root.after(16, self.update_latent_video_canvas)  # ~60 FPS

        except Exception as e:
            print(f"Error updating latent video canvas: {e}")

    def update_output_video_canvas(self):
        """Update the output video canvas"""
        try:
            if (hasattr(self, 'output_video_player') and
                self.output_video_player and
                hasattr(self, 'output_video_canvas') and
                self.output_video_canvas.winfo_exists()):

                # Draw video frame on canvas
                canvas_width = self.output_video_canvas.winfo_width()
                canvas_height = self.output_video_canvas.winfo_height()

                if canvas_width > 1 and canvas_height > 1:
                    # Calculate center position
                    center_x = canvas_width / 2
                    center_y = canvas_height / 2

                    # Draw the video frame
                    self.output_video_player.draw(
                        self.output_video_canvas,
                        (center_x, center_y),
                        force_draw=False
                    )

                # Continue updating if video is active
                if self.output_video_player.active:
                    self.root.after(16, self.update_output_video_canvas)  # ~60 FPS

        except Exception as e:
            print(f"Error updating output video canvas: {e}")

    # Preview update methods
    def update_previews(self):
        """Update all preview displays with high performance"""
        try:
            # Update image preview
            self.update_image_preview()

            # Update latent preview
            self.update_latent_preview()

            # Update output preview
            self.update_output_preview()

        except Exception as e:
            print(f"Error updating previews: {e}")
        finally:
            # Schedule next update with adaptive interval
            self.root.after(self.update_interval, self.update_previews)

    def update_image_preview(self):
        """Update the image preview"""
        try:
            if self.preview_state['image_loading']:
                return

            # Find latest start image
            image_path = self.find_latest_start_image()
            if image_path and image_path != self.preview_state['current_image']:
                if os.path.exists(image_path):
                    self.load_image_preview(image_path)

        except Exception as e:
            print(f"Error updating image preview: {e}")

    def update_latent_preview(self):
        """Update the latent preview with high-performance video player"""
        try:
            if self.preview_state['latent_loading']:
                return

            # Find latest latent preview
            video_path = self.find_latest_latent_preview()
            if video_path and video_path != self.preview_state['current_latent']:
                if os.path.exists(video_path) and os.path.getsize(video_path) > 1000:  # Minimum file size check
                    self.preview_state['latent_loading'] = True
                    success = self.create_latent_video_player(video_path)
                    if success:
                        self.preview_state['current_latent'] = video_path
                        self.latent_preview_status_label.config(
                            text=f"Playing: {os.path.basename(video_path)} (High-Performance)"
                        )
                    self.preview_state['latent_loading'] = False

        except Exception as e:
            print(f"Error updating latent preview: {e}")
            self.preview_state['latent_loading'] = False

    def update_output_preview(self):
        """Update the output preview with high-performance video player"""
        try:
            if self.preview_state['output_loading']:
                return

            # Find latest output video
            video_path = self.find_latest_output_video()
            if video_path and video_path != self.preview_state['current_output']:
                if os.path.exists(video_path) and os.path.getsize(video_path) > 1000:  # Minimum file size check
                    self.preview_state['output_loading'] = True
                    success = self.create_output_video_player(video_path)
                    if success:
                        self.preview_state['current_output'] = video_path
                        self.output_preview_status_label.config(
                            text=f"Playing: {os.path.basename(video_path)} (High-Performance)"
                        )
                    self.preview_state['output_loading'] = False

        except Exception as e:
            print(f"Error updating output preview: {e}")
            self.preview_state['output_loading'] = False

    # File finding methods (optimized for performance)
    def find_latest_start_image(self):
        """Find the latest start image with caching"""
        try:
            # Check for start images in current directory
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.webp']
            start_images = []

            # Use os.scandir for better performance
            with os.scandir('.') as entries:
                for entry in entries:
                    if entry.is_file():
                        name_lower = entry.name.lower()
                        if any(name_lower.endswith(ext) for ext in image_extensions):
                            if 'start' in name_lower or 'input' in name_lower:
                                start_images.append((entry.name, entry.stat().st_mtime))

            if start_images:
                # Return the most recently modified
                start_images.sort(key=lambda x: x[1], reverse=True)
                return start_images[0][0]

        except Exception as e:
            print(f"Error finding latest start image: {e}")
        return None

    def find_latest_latent_preview(self):
        """Find the latest latent preview file with performance optimization"""
        try:
            # Check temp folder first (highest priority)
            temp_dir = "temp"
            if os.path.exists(temp_dir):
                try:
                    with os.scandir(temp_dir) as entries:
                        preview_files = []
                        for entry in entries:
                            if (entry.is_file() and
                                entry.name.startswith("latest_latent_preview_") and
                                entry.name.endswith(".mp4")):
                                preview_files.append((entry.name, entry.stat().st_mtime))

                    if preview_files:
                        preview_files.sort(key=lambda x: x[1], reverse=True)
                        return os.path.join(temp_dir, preview_files[0][0])
                except OSError:
                    pass

            # Check latent_previews folder (fallback)
            latent_dir = "latent_previews"
            if os.path.exists(latent_dir):
                try:
                    with os.scandir(latent_dir) as entries:
                        preview_files = []
                        for entry in entries:
                            if entry.is_file() and entry.name.endswith(".mp4"):
                                preview_files.append((entry.name, entry.stat().st_mtime))

                    if preview_files:
                        preview_files.sort(key=lambda x: x[1], reverse=True)
                        return os.path.join(latent_dir, preview_files[0][0])
                except OSError:
                    pass

        except Exception as e:
            print(f"Error finding latest latent preview: {e}")
        return None

    def find_latest_output_video(self):
        """Find the latest output video with performance optimization"""
        try:
            # Check outputs folder first (highest priority)
            outputs_dir = "outputs"
            if os.path.exists(outputs_dir):
                try:
                    with os.scandir(outputs_dir) as entries:
                        video_files = []
                        for entry in entries:
                            if entry.is_file() and entry.name.endswith(".mp4"):
                                video_files.append((entry.name, entry.stat().st_mtime))

                    if video_files:
                        video_files.sort(key=lambda x: x[1], reverse=True)
                        return os.path.join(outputs_dir, video_files[0][0])
                except OSError:
                    pass

            # Check current directory (fallback)
            try:
                with os.scandir('.') as entries:
                    video_files = []
                    for entry in entries:
                        if entry.is_file() and entry.name.endswith(".mp4"):
                            video_files.append((entry.name, entry.stat().st_mtime))

                if video_files:
                    video_files.sort(key=lambda x: x[1], reverse=True)
                    return video_files[0][0]
            except OSError:
                pass

        except Exception as e:
            print(f"Error finding latest output video: {e}")
        return None

    # Image loading methods
    def load_image_preview(self, image_path):
        """Load and display an image preview with performance optimization"""
        try:
            self.preview_state['image_loading'] = True

            # Load and resize the image
            with Image.open(image_path) as img:
                # Get available space in the preview frame
                self.image_preview_frame.update_idletasks()
                available_width = self.image_preview_frame.winfo_width()
                available_height = self.image_preview_frame.winfo_height()

                # Use reasonable defaults if frame hasn't been sized yet
                if available_width <= 1:
                    available_width = 400
                if available_height <= 1:
                    available_height = 300

                # Add padding
                max_width = max(200, available_width - 20)
                max_height = max(150, available_height - 20)

                # Calculate size maintaining aspect ratio
                img_width, img_height = img.size
                scale = min(max_width / img_width, max_height / img_height)
                new_width = int(img_width * scale)
                new_height = int(img_height * scale)

                # Resize image with high-quality resampling
                img_resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                # Convert to PhotoImage
                photo = ImageTk.PhotoImage(img_resized)

                # Update the label
                self.image_preview_label.config(image=photo, text="")
                self.image_preview_photo = photo  # Keep reference

                # Update status
                self.image_preview_status_label.config(text=f"Loaded: {os.path.basename(image_path)}")
                self.preview_state['current_image'] = image_path

                print(f"Loaded image preview: {os.path.basename(image_path)}")

        except Exception as e:
            print(f"Error loading image preview: {e}")
            self.image_preview_status_label.config(text=f"Error loading image: {str(e)}")
        finally:
            self.preview_state['image_loading'] = False

    # Refresh methods
    def refresh_image_preview(self, event=None):
        """Refresh the image preview"""
        try:
            print("Refreshing image preview...")
            self.preview_state['current_image'] = None
            self.image_preview_status_label.config(text="Refreshing image preview...")
            self.root.after(100, self.update_image_preview)
        except Exception as e:
            print(f"Error refreshing image preview: {e}")

    def refresh_latent_preview(self, event=None):
        """Refresh the latent preview"""
        try:
            print("Refreshing latent preview...")
            self.preview_state['current_latent'] = None

            # Stop current video player
            if self.latent_video_player:
                try:
                    self.latent_video_player.close()
                    self.latent_video_player = None
                except:
                    pass

            # Remove canvas
            if hasattr(self, 'latent_video_canvas'):
                try:
                    self.latent_video_canvas.destroy()
                except:
                    pass

            # Restore placeholder
            for widget in self.latent_video_frame.winfo_children():
                widget.destroy()

            self.latent_video_placeholder = ttk.Label(
                self.latent_video_frame,
                text="Refreshing latent preview...",
                justify=tk.CENTER
            )
            self.latent_video_placeholder.pack(fill=tk.BOTH, expand=True)

            self.latent_preview_status_label.config(text="Refreshing latent preview...")
            self.root.after(100, self.update_latent_preview)
        except Exception as e:
            print(f"Error refreshing latent preview: {e}")

    def refresh_output_preview(self, event=None):
        """Refresh the output preview"""
        try:
            print("Refreshing output preview...")
            self.preview_state['current_output'] = None

            # Stop current video player
            if self.output_video_player:
                try:
                    self.output_video_player.close()
                    self.output_video_player = None
                except:
                    pass

            # Remove canvas
            if hasattr(self, 'output_video_canvas'):
                try:
                    self.output_video_canvas.destroy()
                except:
                    pass

            # Restore placeholder
            for widget in self.output_video_frame.winfo_children():
                widget.destroy()

            self.output_video_placeholder = ttk.Label(
                self.output_video_frame,
                text="Refreshing output preview...",
                justify=tk.CENTER
            )
            self.output_video_placeholder.pack(fill=tk.BOTH, expand=True)

            self.output_preview_status_label.config(text="Refreshing output preview...")
            self.root.after(100, self.update_output_preview)
        except Exception as e:
            print(f"Error refreshing output preview: {e}")

    # Resize handling methods
    def on_preview_pane_resize(self, event=None):
        """Handle resizing of the preview panes"""
        try:
            # Schedule the resize operation to avoid conflicts during dragging
            if hasattr(self, '_resize_after_id'):
                self.root.after_cancel(self._resize_after_id)
            self._resize_after_id = self.root.after(100, self._perform_preview_resize)
        except Exception as e:
            print(f"Error scheduling preview resize: {e}")

    def _perform_preview_resize(self):
        """Perform the actual preview resizing"""
        try:
            # Update frames to get current dimensions
            self.root.update_idletasks()

            # Auto-center the sash to keep equal preview sizes
            self._auto_center_preview_sash()

            # Trigger image preview update if we have an image
            if hasattr(self, 'image_preview_photo') and self.image_preview_photo:
                current_image = self.preview_state.get('current_image')
                if current_image and os.path.exists(current_image):
                    self.root.after(50, lambda: self.load_image_preview(current_image))

        except Exception as e:
            print(f"Error performing preview resize: {e}")

    def _auto_center_preview_sash(self):
        """Automatically center the sash between image and latent previews"""
        try:
            if hasattr(self, 'preview_paned_window') and self.preview_paned_window:
                total_width = self.preview_paned_window.winfo_width()
                if total_width > 0:
                    half_width = total_width // 2
                    try:
                        self.preview_paned_window.sashpos(0, half_width)
                    except:
                        pass
        except:
            pass

    def set_equal_preview_pane_sizes(self, event=None):
        """Set equal sizes for image and latent preview panes when double-clicking the sash"""
        try:
            if hasattr(self, 'preview_paned_window') and self.preview_paned_window:
                self.root.update_idletasks()
                total_width = self.preview_paned_window.winfo_width()
                if total_width > 0:
                    half_width = total_width // 2
                    try:
                        self.preview_paned_window.sashpos(0, half_width)
                        print(f"Set equal preview pane sizes: {half_width}px each")
                    except Exception as e:
                        print(f"Error setting sash position: {e}")
        except Exception as e:
            print(f"Error setting equal preview pane sizes: {e}")

    def on_window_resize(self, event=None):
        """Handle window resize events"""
        if event and event.widget == self.root:
            # Save window state when resized
            self.root.after(1000, self.save_window_state)  # Longer delay for performance

    # Window state management (optimized for performance)
    def save_window_state(self):
        """Save window position, size, and pane positions"""
        try:
            config = configparser.ConfigParser()
            config_file = "standalone_preview_window_v2.ini"

            # Load existing config if it exists
            if os.path.exists(config_file):
                config.read(config_file)

            # Ensure sections exist
            if not config.has_section('window'):
                config.add_section('window')
            if not config.has_section('panes'):
                config.add_section('panes')
            if not config.has_section('performance'):
                config.add_section('performance')

            # Save window geometry
            geometry = self.root.geometry()
            config.set('window', 'geometry', geometry)

            # Save window state (normal/maximized)
            state = self.root.state()
            config.set('window', 'state', state)

            # Save performance settings
            config.set('performance', 'update_interval', str(self.update_interval))
            config.set('performance', 'video_scaling_mode', self.video_scaling_mode)

            # Save pane positions
            try:
                # Save main preview paned window sash positions
                if hasattr(self, 'main_preview_paned_window'):
                    sash_positions = []
                    for i in range(len(self.main_preview_paned_window.panes()) - 1):
                        try:
                            pos = self.main_preview_paned_window.sashpos(i)
                            sash_positions.append(str(pos))
                        except:
                            pass
                    if sash_positions:
                        config.set('panes', 'main_sash_positions', ','.join(sash_positions))

                # Save preview paned window sash positions
                if hasattr(self, 'preview_paned_window'):
                    sash_positions = []
                    for i in range(len(self.preview_paned_window.panes()) - 1):
                        try:
                            pos = self.preview_paned_window.sashpos(i)
                            sash_positions.append(str(pos))
                        except:
                            pass
                    if sash_positions:
                        config.set('panes', 'preview_sash_positions', ','.join(sash_positions))
            except Exception as e:
                print(f"Error saving pane positions: {e}")

            # Write config file
            with open(config_file, 'w') as f:
                config.write(f)

        except Exception as e:
            print(f"Error saving window state: {e}")

    def load_window_state(self):
        """Load window position, size, and pane positions"""
        try:
            config_file = "standalone_preview_window_v2.ini"
            if not os.path.exists(config_file):
                return

            config = configparser.ConfigParser()
            config.read(config_file)

            # Restore performance settings
            if config.has_option('performance', 'update_interval'):
                try:
                    self.update_interval = int(config.get('performance', 'update_interval'))
                    print(f"Restored update interval: {self.update_interval}ms")
                except:
                    pass

            if config.has_option('performance', 'video_scaling_mode'):
                try:
                    self.video_scaling_mode = config.get('performance', 'video_scaling_mode')
                    print(f"Restored video scaling mode: {self.video_scaling_mode}")
                except:
                    pass

            # Restore window geometry
            if config.has_option('window', 'geometry'):
                geometry = config.get('window', 'geometry')
                try:
                    self.root.geometry(geometry)
                    print(f"Restored window geometry: {geometry}")
                except Exception as e:
                    print(f"Error restoring window geometry: {e}")

            # Restore window state
            if config.has_option('window', 'state'):
                state = config.get('window', 'state')
                try:
                    if state == 'zoomed':  # maximized
                        self.root.state('zoomed')
                        print("Restored maximized window state")
                except Exception as e:
                    print(f"Error restoring window state: {e}")

            # Restore pane positions (after a delay to ensure UI is ready)
            self.root.after(1500, self._restore_pane_positions, config)

        except Exception as e:
            print(f"Error loading window state: {e}")

    def _restore_pane_positions(self, config):
        """Restore pane positions from config"""
        try:
            # Restore main preview paned window sash positions
            if config.has_option('panes', 'main_sash_positions'):
                main_sash_str = config.get('panes', 'main_sash_positions')
                if main_sash_str and hasattr(self, 'main_preview_paned_window'):
                    try:
                        sash_positions = [int(pos) for pos in main_sash_str.split(',')]
                        for i, pos in enumerate(sash_positions):
                            if i < len(self.main_preview_paned_window.panes()) - 1:
                                try:
                                    self.main_preview_paned_window.sashpos(i, pos)
                                except Exception as e:
                                    print(f"Error setting main sash position {i} to {pos}: {e}")
                        print(f"Restored main pane sizes: {sash_positions}")
                    except Exception as e:
                        print(f"Error restoring main pane sizes: {e}")

            # Restore preview paned window sash positions
            if config.has_option('panes', 'preview_sash_positions'):
                preview_sash_str = config.get('panes', 'preview_sash_positions')
                if preview_sash_str and hasattr(self, 'preview_paned_window'):
                    try:
                        sash_positions = [int(pos) for pos in preview_sash_str.split(',')]
                        for i, pos in enumerate(sash_positions):
                            if i < len(self.preview_paned_window.panes()) - 1:
                                try:
                                    self.preview_paned_window.sashpos(i, pos)
                                except Exception as e:
                                    print(f"Error setting preview sash position {i} to {pos}: {e}")
                        print(f"Restored preview pane sizes: {sash_positions}")
                    except Exception as e:
                        print(f"Error restoring preview pane sizes: {e}")

        except Exception as e:
            print(f"Error restoring pane positions: {e}")

    def on_closing(self):
        """Handle window closing with proper cleanup"""
        try:
            print("High-Performance Preview Window closing...")

            # Save window state
            self.save_window_state()

            # Stop and cleanup video players
            if self.latent_video_player:
                try:
                    self.latent_video_player.close()
                    print("Closed latent video player")
                except Exception as e:
                    print(f"Error closing latent video player: {e}")

            if self.output_video_player:
                try:
                    self.output_video_player.close()
                    print("Closed output video player")
                except Exception as e:
                    print(f"Error closing output video player: {e}")

            print("High-Performance Preview Window cleanup completed")

        except Exception as e:
            print(f"Error during window closing: {e}")
        finally:
            self.root.destroy()


def main():
    """Main function to run the high-performance standalone preview window"""
    print("Starting FramePack High-Performance Preview Window...")
    print(f"pyvidplayer2 available: {PYVIDPLAYER2_AVAILABLE}")

    root = tk.Tk()
    app = HighPerformancePreviewWindow(root)

    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("Interrupted by user")
        app.on_closing()
    except Exception as e:
        print(f"Error in main loop: {e}")
        app.on_closing()


if __name__ == "__main__":
    main()
