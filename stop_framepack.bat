@echo off
echo FramePack Stop Command (Legacy)
echo ===============================
echo.
echo This will create a stop flag to tell FramePack to stop the current generation.
echo This is the legacy stop method - for newer functionality use skip_generation.bat
echo or force_terminate_all.bat instead.
echo.

:: Create the legacy stop flag file
echo Creating stop flag...
echo Stop requested at %date% %time% > stop_framepack.flag

if exist stop_framepack.flag (
    echo ✓ Stop flag created successfully!
    echo.
    echo The current generation will stop when FramePack checks for the flag.
    echo This typically happens between frames or at the start of the next generation cycle.
) else (
    echo ✗ Failed to create stop flag!
    echo Please check file permissions in the FramePack directory.
)

echo.
echo Press any key to exit...
pause >nul
