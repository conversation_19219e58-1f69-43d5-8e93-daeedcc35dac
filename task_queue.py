import time
import threading
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Callable
import uuid

@dataclass
class Task:
    id: str
    params: Dict[str, Any]
    status: str = "queued"  # queued, processing, completed, failed, cancelled
    result_file: Optional[str] = None
    error: Optional[str] = None
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None

class TaskQueue:
    def __init__(self):
        self.queue: List[Task] = []
        self.completed_tasks: List[Task] = []
        self.current_task: Optional[Task] = None
        self.lock = threading.Lock()
        self.is_processing = False
        self.on_queue_update: Optional[Callable] = None
        
    def add_task(self, params: Dict[str, Any]) -> str:
        """Add a new task to the queue and return its ID"""
        with self.lock:
            task_id = str(uuid.uuid4())
            task = Task(id=task_id, params=params)
            self.queue.append(task)
            if self.on_queue_update:
                self.on_queue_update()
            return task_id
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a queued task by ID. Returns True if successful."""
        with self.lock:
            # Try to find and remove from queue
            for i, task in enumerate(self.queue):
                if task.id == task_id:
                    task.status = "cancelled"
                    self.completed_tasks.append(task)
                    self.queue.pop(i)
                    if self.on_queue_update:
                        self.on_queue_update()
                    return True
            return False
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get the current status of the queue and all tasks"""
        with self.lock:
            return {
                "current": self.current_task,
                "queued": self.queue.copy(),
                "completed": self.completed_tasks.copy(),
                "is_processing": self.is_processing
            }
    
    def start_processing(self, worker_func: Callable) -> None:
        """Start processing the queue if not already processing"""
        with self.lock:
            if self.is_processing:
                return
            self.is_processing = True
        
        # Start processing in a separate thread
        threading.Thread(target=self._process_queue, args=(worker_func,), daemon=True).start()
    
    def _process_queue(self, worker_func: Callable) -> None:
        """Process tasks in the queue sequentially"""
        while True:
            # Get the next task
            with self.lock:
                if not self.queue:
                    self.is_processing = False
                    self.current_task = None
                    if self.on_queue_update:
                        self.on_queue_update()
                    break
                
                self.current_task = self.queue.pop(0)
                self.current_task.status = "processing"
                self.current_task.started_at = time.time()
                if self.on_queue_update:
                    self.on_queue_update()
            
            # Process the task
            try:
                # Call the worker function with task parameters
                result = worker_func(**self.current_task.params)
                
                # Update task status
                with self.lock:
                    if isinstance(result, str) and result.endswith(('.mp4', '.gif')):
                        self.current_task.result_file = result
                    self.current_task.status = "completed"
                    self.current_task.completed_at = time.time()
                    self.completed_tasks.append(self.current_task)
                    if self.on_queue_update:
                        self.on_queue_update()
            
            except Exception as e:
                # Handle task failure
                with self.lock:
                    self.current_task.status = "failed"
                    self.current_task.error = str(e)
                    self.current_task.completed_at = time.time()
                    self.completed_tasks.append(self.current_task)
                    if self.on_queue_update:
                        self.on_queue_update()