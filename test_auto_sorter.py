#!/usr/bin/env python
"""
Test script for auto_sorter.py to verify it handles the seconds label scheme correctly.
"""

import os
import sys
from auto_sorter import parse_filename, extract_file_info

def test_parse_filename():
    """Test the parse_filename function with various filename formats."""
    test_cases = [
        # Standard timestamp format
        ("250420_121919_242_3623_37.mp4", ("250420_121919_242_3623", None)),
        ("250420_121919_242_3623_37_24s.mp4", ("250420_121919_242_3623", None)),
        ("250426_085120_706_7278_19_seed357798872.mp4", ("250426_085120_706_7278", "357798872")),
        ("250426_085120_706_7278_19_seed357798872_24s.mp4", ("250426_085120_706_7278", "357798872")),
        
        # Generic filename format
        ("image_name.mp4", ("image_name", None)),
        ("image_name_5s.mp4", ("image_name", None)),
        ("image_name_seed123456.mp4", ("image_name", "123456")),
        ("image_name_seed123456_5s.mp4", ("image_name", "123456")),
        
        # Invalid formats
        ("image.jpg", None),
        ("invalid_format", None)
    ]
    
    print("Testing parse_filename function:")
    for filename, expected in test_cases:
        result = parse_filename(filename)
        status = "✓" if result == expected else "✗"
        print(f"{status} {filename}: Got {result}, Expected {expected}")

def test_extract_file_info():
    """Test the extract_file_info function with various filename formats."""
    test_cases = [
        # Standard timestamp format
        ("250420_121919_242_3623_37.mp4", {
            "filename": "250420_121919_242_3623_37.mp4",
            "base_id": "250420_121919_242_3623",
            "suffix": "37",
            "seed": "",
            "duration": ""
        }),
        ("250420_121919_242_3623_37_24s.mp4", {
            "filename": "250420_121919_242_3623_37_24s.mp4",
            "base_id": "250420_121919_242_3623",
            "suffix": "37",
            "seed": "",
            "duration": "24"
        }),
        ("250426_085120_706_7278_19_seed357798872_24s.mp4", {
            "filename": "250426_085120_706_7278_19_seed357798872_24s.mp4",
            "base_id": "250426_085120_706_7278",
            "suffix": "19",
            "seed": "357798872",
            "duration": "24"
        }),
        
        # Generic filename format
        ("image_name_5s.mp4", {
            "filename": "image_name_5s.mp4",
            "base_id": "image_name",
            "suffix": "",
            "seed": "",
            "duration": "5"
        }),
        ("image_name_seed123456_5s.mp4", {
            "filename": "image_name_seed123456_5s.mp4",
            "base_id": "image_name",
            "suffix": "",
            "seed": "123456",
            "duration": "5"
        })
    ]
    
    print("\nTesting extract_file_info function:")
    for filename, expected in test_cases:
        result = extract_file_info(filename)
        # Check if all expected keys match
        status = "✓"
        for key in expected:
            if result.get(key) != expected.get(key):
                status = "✗"
                break
        print(f"{status} {filename}:")
        if status == "✗":
            print(f"  Got: {result}")
            print(f"  Expected: {expected}")

if __name__ == "__main__":
    test_parse_filename()
    test_extract_file_info()
