#!/usr/bin/env python
"""
Test script for auto_sorter.py to verify that no files are deleted.
This script tests that:
1. The sort_outputs function only copies files to the sorted directory
2. No files are deleted in either the outputs or sorted directories
"""

import os
import sys
import shutil
import tempfile
from auto_sorter import auto_sort_after_generation

def create_test_files(directory, filenames):
    """Create test files in the specified directory."""
    os.makedirs(directory, exist_ok=True)
    
    for filename in filenames:
        file_path = os.path.join(directory, filename)
        # Create a file with some content (size will vary)
        with open(file_path, 'wb') as f:
            # Make files with different sizes
            size = 1000 + filenames.index(filename) * 500
            f.write(b'X' * size)
    
    print(f"Created {len(filenames)} test files in {directory}")

def test_auto_sort_no_delete():
    """Test that auto_sort_after_generation doesn't delete any files."""
    print("\n=== Testing auto_sort_after_generation with no deletion ===")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create outputs directory
        outputs_dir = os.path.join(temp_dir, "outputs")
        os.makedirs(outputs_dir, exist_ok=True)
        
        # Create test files in outputs directory
        test_files = [
            "250420_121919_242_3623_37.mp4",
            "250420_121919_242_3623_38.mp4",  # Same base_id, different suffix
            "250420_121919_242_3623_39.mp4",  # Same base_id, different suffix
            "250421_121919_242_3623_37.mp4"   # Different base_id
        ]
        create_test_files(outputs_dir, test_files)
        
        # Run auto_sort_after_generation
        print("\nRunning auto_sort_after_generation:")
        num_copied, num_deleted = auto_sort_after_generation(outputs_dir)
        
        # Check if original files are still there
        remaining_files = os.listdir(outputs_dir)
        # Remove 'sorted' from the list to count only files
        if 'sorted' in remaining_files:
            remaining_files.remove('sorted')
        
        print(f"Original files remaining: {len(remaining_files)}")
        
        if len(remaining_files) == len(test_files):
            print("✅ All original files were preserved")
        else:
            print("❌ Some original files were deleted")
        
        # Check the sorted directory
        sorted_dir = os.path.join(outputs_dir, "sorted")
        if os.path.exists(sorted_dir):
            sorted_files = os.listdir(sorted_dir)
            print(f"Files in sorted directory: {len(sorted_files)}")
            
            # We should have 2 files in the sorted directory (one for each unique base_id)
            expected_count = 2  # One for each unique base_id
            if len(sorted_files) == expected_count:
                print(f"✅ Sorted directory has the expected number of files ({expected_count})")
            else:
                print(f"❌ Sorted directory has {len(sorted_files)} files, expected {expected_count}")
                
            # Verify that num_deleted is always 0
            if num_deleted == 0:
                print("✅ num_deleted is correctly reported as 0")
            else:
                print(f"❌ num_deleted is {num_deleted}, expected 0")
        else:
            print("❌ Sorted directory was not created")

if __name__ == "__main__":
    test_auto_sort_no_delete()
    
    print("\nAll tests completed.")
