#!/usr/bin/env python
"""
Test script for auto_sorter.py to verify the safety features work correctly.
This script tests that:
1. The process_duplicates function refuses to delete files in non-sorted directories
2. The sort_and_dedupe_outputs function only deletes files in the sorted directory
"""

import os
import sys
import shutil
import tempfile
from auto_sorter import process_duplicates, sort_and_dedupe_outputs

def create_test_files(directory, filenames):
    """Create test files in the specified directory."""
    os.makedirs(directory, exist_ok=True)
    
    for filename in filenames:
        file_path = os.path.join(directory, filename)
        # Create a file with some content (size will vary)
        with open(file_path, 'wb') as f:
            # Make files with different sizes
            size = 1000 + filenames.index(filename) * 500
            f.write(b'X' * size)
    
    print(f"Created {len(filenames)} test files in {directory}")

def test_process_duplicates_safety():
    """Test that process_duplicates refuses to delete files in non-sorted directories."""
    print("\n=== Testing process_duplicates safety feature ===")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test files
        test_files = [
            "250420_121919_242_3623_37.mp4",
            "250420_121919_242_3623_38.mp4"  # Same base_id, different suffix
        ]
        create_test_files(temp_dir, test_files)
        
        # Try to process duplicates without is_sorted_dir=True
        print("\nTrying to process duplicates without is_sorted_dir=True:")
        num_deleted = process_duplicates(temp_dir)
        
        # Check if any files were deleted
        remaining_files = os.listdir(temp_dir)
        print(f"Files remaining: {len(remaining_files)}")
        
        if len(remaining_files) == len(test_files) and num_deleted == 0:
            print("✅ Safety check passed: No files were deleted")
        else:
            print("❌ Safety check failed: Files were deleted when they shouldn't have been")
        
        # Try with is_sorted_dir=True
        print("\nTrying to process duplicates with is_sorted_dir=True:")
        num_deleted = process_duplicates(temp_dir, is_sorted_dir=True)
        
        # Check if files were deleted
        remaining_files = os.listdir(temp_dir)
        print(f"Files remaining: {len(remaining_files)}")
        
        if len(remaining_files) < len(test_files) and num_deleted > 0:
            print("✅ Deduplication worked correctly with is_sorted_dir=True")
        else:
            print("❌ Deduplication failed with is_sorted_dir=True")

def test_sort_and_dedupe_outputs():
    """Test that sort_and_dedupe_outputs only deletes files in the sorted directory."""
    print("\n=== Testing sort_and_dedupe_outputs function ===")
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create outputs directory
        outputs_dir = os.path.join(temp_dir, "outputs")
        os.makedirs(outputs_dir, exist_ok=True)
        
        # Create test files in outputs directory
        test_files = [
            "250420_121919_242_3623_37.mp4",
            "250420_121919_242_3623_38.mp4",  # Same base_id, different suffix
            "250420_121919_242_3623_39.mp4",  # Same base_id, different suffix
            "250421_121919_242_3623_37.mp4"   # Different base_id
        ]
        create_test_files(outputs_dir, test_files)
        
        # Run sort_and_dedupe_outputs
        print("\nRunning sort_and_dedupe_outputs:")
        num_copied, num_deleted = sort_and_dedupe_outputs(outputs_dir)
        
        # Check if original files are still there
        remaining_files = os.listdir(outputs_dir)
        # Remove 'sorted' from the list to count only files
        if 'sorted' in remaining_files:
            remaining_files.remove('sorted')
        
        print(f"Original files remaining: {len(remaining_files)}")
        
        if len(remaining_files) == len(test_files):
            print("✅ All original files were preserved")
        else:
            print("❌ Some original files were deleted")
        
        # Check the sorted directory
        sorted_dir = os.path.join(outputs_dir, "sorted")
        if os.path.exists(sorted_dir):
            sorted_files = os.listdir(sorted_dir)
            print(f"Files in sorted directory: {len(sorted_files)}")
            
            # We should have 2 files in the sorted directory (one for each unique base_id)
            expected_count = 2  # One for each unique base_id
            if len(sorted_files) == expected_count:
                print(f"✅ Sorted directory has the expected number of files ({expected_count})")
            else:
                print(f"❌ Sorted directory has {len(sorted_files)} files, expected {expected_count}")
        else:
            print("❌ Sorted directory was not created")

if __name__ == "__main__":
    test_process_duplicates_safety()
    test_sort_and_dedupe_outputs()
    
    print("\nAll tests completed.")
