#!/usr/bin/env python3
"""
Test script to reproduce the completion detection issue during queue processing.
This simulates the problem where the GUI doesn't detect completion signals during queue processing.
"""

import os
import time
import threading
import tkinter as tk
from tkinter import ttk

class TestCompletionDetection:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Test Completion Detection")
        self.root.geometry("600x400")
        
        # Simulate queue processing state
        self.is_processing_queue = False
        self.consolidated_monitoring_active = False
        self.last_consolidated_check = 0
        
        # Create UI
        self.create_ui()
        
        # Initialize monitoring
        self.init_consolidated_monitoring()
    
    def create_ui(self):
        """Create the test UI"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="Ready", font=("Arial", 12))
        self.status_label.pack(pady=10)
        
        # Queue processing checkbox
        self.queue_var = tk.BooleanVar()
        self.queue_checkbox = ttk.Checkbutton(
            main_frame, 
            text="Simulate Queue Processing", 
            variable=self.queue_var,
            command=self.toggle_queue_processing
        )
        self.queue_checkbox.pack(pady=5)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        self.create_signal_button = ttk.Button(
            button_frame, 
            text="Create Completion Signal", 
            command=self.create_completion_signal
        )
        self.create_signal_button.pack(side=tk.LEFT, padx=5)
        
        self.clear_signal_button = ttk.Button(
            button_frame, 
            text="Clear Signal", 
            command=self.clear_completion_signal
        )
        self.clear_signal_button.pack(side=tk.LEFT, padx=5)
        
        # Log area
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.log_text = tk.Text(log_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log(self, message):
        """Add a message to the log"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def toggle_queue_processing(self):
        """Toggle queue processing simulation"""
        self.is_processing_queue = self.queue_var.get()
        if self.is_processing_queue:
            self.log("Queue processing ENABLED - testing completion detection during queue processing")
            self.status_label.config(text="Queue Processing Active")
        else:
            self.log("Queue processing DISABLED - testing normal completion detection")
            self.status_label.config(text="Normal Processing")
    
    def create_completion_signal(self):
        """Create a completion signal file"""
        try:
            with open("framepack_completed.signal", "w") as f:
                f.write("SUCCESS\n")
            self.log("Created framepack_completed.signal with status: SUCCESS")
        except Exception as e:
            self.log(f"Error creating completion signal: {e}")
    
    def clear_completion_signal(self):
        """Clear the completion signal file"""
        try:
            if os.path.exists("framepack_completed.signal"):
                os.remove("framepack_completed.signal")
                self.log("Removed framepack_completed.signal")
            else:
                self.log("No completion signal file to remove")
        except Exception as e:
            self.log(f"Error removing completion signal: {e}")
    
    def init_consolidated_monitoring(self):
        """Initialize consolidated monitoring system"""
        try:
            self.consolidated_monitoring_active = True
            self.last_consolidated_check = 0
            
            # Start the monitoring loop
            self.root.after(2000, self.consolidated_monitor_check)
            self.log("Consolidated monitoring initialized and started")
        except Exception as e:
            self.log(f"Error initializing consolidated monitoring: {e}")
    
    def consolidated_monitor_check(self):
        """Single consolidated monitoring check that handles all monitoring needs"""
        try:
            if not self.consolidated_monitoring_active:
                return
            
            current_time = time.time()
            
            # Throttle checks to prevent excessive polling
            if current_time - self.last_consolidated_check < 2:
                self.root.after(2000, self.consolidated_monitor_check)
                return
            
            self.last_consolidated_check = current_time
            
            # Update backend state (this is where the fix should work)
            self.update_backend_state()
            
            # Schedule next check
            interval = 3000 if self.is_processing_queue else 2000
            self.root.after(interval, self.consolidated_monitor_check)
            
        except Exception as e:
            self.log(f"Error in consolidated monitoring: {e}")
            # Continue monitoring even if there's an error
            self.root.after(5000, self.consolidated_monitor_check)
    
    def update_backend_state(self):
        """Update backend state based on current conditions - THIS IS THE FIXED VERSION"""
        try:
            # Check for completion signal first (highest priority)
            if os.path.exists("framepack_completed.signal"):
                self.log("Backend completed - state updated to idle")
                
                # CRITICAL FIX: During queue processing, we need to trigger completion handling
                if self.is_processing_queue:
                    self.log("Queue processing active - triggering completion detection")
                    # Read the completion signal to determine status
                    try:
                        with open("framepack_completed.signal", 'r') as f:
                            status = f.read().strip()
                        self.log(f"Completion status during queue processing: {status}")
                        
                        # Trigger the completion handler directly
                        self.root.after_idle(lambda: self.handle_completion(status))
                        return
                    except Exception as e:
                        self.log(f"Error reading completion signal during queue processing: {e}")
                        # Fallback to generic success handling
                        self.root.after_idle(lambda: self.handle_completion("SUCCESS"))
                        return
                else:
                    # Normal non-queue processing
                    self.log("Backend completed - normal processing mode")
                    self.root.after_idle(lambda: self.handle_completion("SUCCESS"))
            
        except Exception as e:
            self.log(f"Error updating backend state: {e}")
    
    def handle_completion(self, status):
        """Handle completion of the process with a specific status"""
        try:
            self.log(f"COMPLETION DETECTED! Status: {status}")
            
            # Remove the completion signal file
            try:
                if os.path.exists("framepack_completed.signal"):
                    os.remove("framepack_completed.signal")
                    self.log("Removed completion signal file")
            except Exception as e:
                self.log(f"Error removing completion signal: {e}")
            
            # Update status
            if self.is_processing_queue:
                self.status_label.config(text="Queue Processing - Job Completed")
                self.log("Queue processing continues to next item...")
            else:
                self.status_label.config(text="Processing Complete")
                self.log("Normal processing completed")
            
        except Exception as e:
            self.log(f"Error handling completion: {e}")
    
    def run(self):
        """Run the test application"""
        self.log("Test application started")
        self.log("Instructions:")
        self.log("1. Check 'Simulate Queue Processing' to test queue mode")
        self.log("2. Click 'Create Completion Signal' to simulate job completion")
        self.log("3. Watch the log to see if completion is detected")
        self.log("")
        self.log("Expected behavior:")
        self.log("- With queue processing OFF: Should detect completion normally")
        self.log("- With queue processing ON: Should detect completion with the fix")
        
        self.root.mainloop()

if __name__ == "__main__":
    app = TestCompletionDetection()
    app.run()
