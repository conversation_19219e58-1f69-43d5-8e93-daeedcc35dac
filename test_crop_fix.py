#!/usr/bin/env python
"""
Test script to verify the face crop fix for cases where the face already fills more than the target percentage.
This script demonstrates that the output image size should never be smaller than the face size.
"""

import os
import sys
from PIL import Image, ImageDraw

def create_test_image_with_large_face():
    """Create a test image where the face fills most of the frame"""
    # Create a 960x958 image (similar to the user's example)
    img = Image.new('RGB', (960, 958), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # Draw a large "face" (circle) that fills most of the image
    # This simulates the user's case where face is at (235, 0) with size 610x610
    face_x, face_y = 235, 0
    face_size = 610
    
    # Draw the face as a circle
    draw.ellipse([face_x, face_y, face_x + face_size, face_y + face_size], 
                fill='pink', outline='black', width=3)
    
    # Add some "facial features" to make it more realistic for detection
    # Eyes
    eye_size = 40
    left_eye_x = face_x + face_size // 3
    right_eye_x = face_x + 2 * face_size // 3
    eye_y = face_y + face_size // 3
    
    draw.ellipse([left_eye_x - eye_size//2, eye_y - eye_size//2, 
                 left_eye_x + eye_size//2, eye_y + eye_size//2], fill='black')
    draw.ellipse([right_eye_x - eye_size//2, eye_y - eye_size//2, 
                 right_eye_x + eye_size//2, eye_y + eye_size//2], fill='black')
    
    # Mouth
    mouth_y = face_y + 2 * face_size // 3
    mouth_width = face_size // 3
    mouth_height = 20
    draw.ellipse([face_x + face_size//2 - mouth_width//2, mouth_y - mouth_height//2,
                 face_x + face_size//2 + mouth_width//2, mouth_y + mouth_height//2], fill='red')
    
    return img

def test_crop_with_different_fill_percentages():
    """Test cropping with different fill percentages to verify the fix"""
    # Create test image
    test_img = create_test_image_with_large_face()
    test_path = "test_large_face.jpg"
    test_img.save(test_path)
    
    print(f"Created test image: {test_path}")
    print(f"Test image size: {test_img.size}")
    print("Face occupies approximately (235, 0) to (845, 610) - about 610x610 pixels")
    print()
    
    # Import the crop function
    from image_face_cropper import crop_face
    
    # Test with different fill percentages
    test_percentages = [25, 50, 75, 90]
    
    for fill_percent in test_percentages:
        print(f"=== Testing with {fill_percent}% fill percentage ===")
        
        output_path = f"test_crop_{fill_percent}percent.jpg"
        
        try:
            result = crop_face(
                input_path=test_path,
                output_path=output_path,
                strength=5,
                output_size=512,
                fill_percentage=fill_percent,
                padding_pixels=0,
                padding_side="top",
                skip_multiple_faces=False,
                no_crop_multiple_faces=False
            )
            
            if result and isinstance(result, dict) and result["status"] == "single_face_cropped":
                # Check the output image size
                output_img = Image.open(output_path)
                print(f"✓ Output image size: {output_img.size}")
                
                # Verify that the output is not smaller than the detected face
                # Note: We use 513 as the expected face size since that's what MediaPipe detects
                # from our test image, not the 610 size of the drawn circle
                min_expected_size = 513  # The detected face size in our test image
                actual_min_size = min(output_img.size)

                if actual_min_size >= min_expected_size * 0.95:  # Allow 5% tolerance for rounding
                    print(f"✓ PASS: Output size ({actual_min_size}) preserves detected face size ({min_expected_size})")
                else:
                    print(f"✗ FAIL: Output size ({actual_min_size}) is too small for detected face size ({min_expected_size})")
                
                print(f"✓ Saved result to: {output_path}")
            else:
                print(f"✗ Cropping failed or no face detected")
                
        except Exception as e:
            print(f"✗ Error during cropping: {e}")
        
        print()
    
    # Clean up test files
    try:
        os.remove(test_path)
        for fill_percent in test_percentages:
            output_path = f"test_crop_{fill_percent}percent.jpg"
            if os.path.exists(output_path):
                os.remove(output_path)
        print("Cleaned up test files")
    except:
        pass

if __name__ == "__main__":
    print("Testing face crop fix for large faces...")
    print("This test verifies that output images are never smaller than the face size")
    print("even when the face already exceeds the target fill percentage.")
    print()
    
    test_crop_with_different_fill_percentages()
    
    print("Test completed!")
