@echo off
setlocal enabledelayedexpansion
echo This script simulates dragging and dropping files onto launch_framepack_gui.bat
echo It will pass the test files as arguments to the batch file

REM Get the current directory
set "CURRENT_DIR=%CD%"

REM Create test directory if it doesn't exist
if not exist test_files mkdir test_files

REM Create empty test files
echo Creating test files...
echo. > "test_files\test1.jpg"
echo. > "test_files\test2.png"
echo. > "test_files\test3.txt"
echo. > "test_files\File with spaces.jpg"
echo. > "test_files\Special_Chars_(1).png"

REM Get absolute paths for the test files
set "file1=%CURRENT_DIR%\test_files\test1.jpg"
set "file2=%CURRENT_DIR%\test_files\test2.png"
set "file3=%CURRENT_DIR%\test_files\File with spaces.jpg"
set "file4=%CURRENT_DIR%\test_files\Special_Chars_(1).png"

REM Call the launcher with the test files
echo.
echo Calling launch_framepack_gui.bat with test files...
echo.
call launch_framepack_gui.bat "%file1%" "%file2%" "%file3%" "%file4%"

echo.
echo Test completed.
echo.
