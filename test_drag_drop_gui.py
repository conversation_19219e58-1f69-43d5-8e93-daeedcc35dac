"""
Test script for the drag and drop functionality in FramePack GUI.

This script creates a simple window with a listbox that supports drag and drop,
allowing you to test if tkinterdnd2 is working correctly on your system.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

try:
    from tkinterdnd2 import TkinterDnD, DND_FILES
    TKDND_AVAILABLE = True
except ImportError:
    print("TkinterDnD2 not available. Drag and drop will be disabled.")
    print("To enable drag and drop, install tkinterdnd2: pip install tkinterdnd2")
    TKDND_AVAILABLE = False

class TestDragDropApp:
    def __init__(self, root):
        self.root = root
        self.root.title("FramePack Drag & Drop Test")
        self.root.geometry("500x400")
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create a label with instructions
        ttk.Label(main_frame, text="Drag and drop image files here to test:").pack(pady=10)
        
        # Create a frame for the listbox and scrollbar
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Create listbox with scrollbar
        self.files_listbox = tk.Listbox(list_frame, width=50, height=10)
        self.files_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.files_listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.files_listbox.configure(yscrollcommand=scrollbar.set)
        
        # Add drag and drop support if available
        if TKDND_AVAILABLE:
            self.files_listbox.drop_target_register(DND_FILES)
            self.files_listbox.dnd_bind('<<Drop>>', self.drop_files)
            self.files_listbox.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.files_listbox.dnd_bind('<<DragLeave>>', self.drag_leave)
            
            # Add a label to indicate drag and drop capability
            self.drag_drop_label = ttk.Label(main_frame, text="Drag and drop files here", foreground="gray")
            self.drag_drop_label.pack(pady=5)
        else:
            ttk.Label(main_frame, text="Drag and drop not available - install tkinterdnd2", foreground="red").pack(pady=5)
        
        # Add a clear button
        ttk.Button(main_frame, text="Clear List", command=self.clear_list).pack(pady=10)
        
        # Add status label
        self.status_label = ttk.Label(main_frame, text="Ready")
        self.status_label.pack(pady=5)
    
    def drop_files(self, event):
        """Handle files dropped onto the listbox"""
        if not TKDND_AVAILABLE:
            return
            
        # Get the dropped data (file paths)
        file_paths = self.root.tk.splitlist(event.data)
        
        # Clear the listbox
        self.clear_list()
        
        # Process the dropped files
        if file_paths:
            # Valid image extensions
            valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
            
            # Add each valid file to the list
            valid_count = 0
            for file_path in file_paths:
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    ext = os.path.splitext(file_path)[1].lower()
                    if ext in valid_extensions:
                        self.files_listbox.insert(tk.END, f"{os.path.basename(file_path)} ({file_path})")
                        valid_count += 1
                    else:
                        self.files_listbox.insert(tk.END, f"[INVALID] {os.path.basename(file_path)} - not an image")
                else:
                    self.files_listbox.insert(tk.END, f"[ERROR] {file_path} - file not found")
            
            self.status_label.config(text=f"Added {valid_count} valid files")
            
            # Restore normal appearance
            self.drag_leave(None)
    
    def drag_enter(self, event):
        """Handle drag enter event"""
        if not TKDND_AVAILABLE:
            return
            
        # Change the appearance of the listbox to indicate it's a drop target
        self.files_listbox.config(background="#e0f0ff")  # Light blue background
        self.drag_drop_label.config(foreground="#0066cc", text="Drop files here")  # Blue text
        self.status_label.config(text="Ready to drop")
    
    def drag_leave(self, event):
        """Handle drag leave event"""
        if not TKDND_AVAILABLE:
            return
            
        # Restore the normal appearance
        self.files_listbox.config(background="white")
        self.drag_drop_label.config(foreground="gray", text="Drag and drop files here")
        if self.files_listbox.size() == 0:
            self.status_label.config(text="Ready")
    
    def clear_list(self):
        """Clear the listbox"""
        self.files_listbox.delete(0, tk.END)
        self.status_label.config(text="List cleared")

def main():
    # Use TkinterDnD.Tk() if available, otherwise fallback to standard tk.Tk()
    if TKDND_AVAILABLE:
        root = TkinterDnD.Tk()
    else:
        root = tk.Tk()
        
    app = TestDragDropApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
