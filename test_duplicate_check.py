#!/usr/bin/env python
"""
Test script to verify that the update_processed_files_txt function correctly checks
for duplicate entries before appending to the processed_files.txt file.
"""

import os
import sys
import shutil
import tempfile
from pathlib import Path
import traceback

# Import the update_processed_files_txt function from auto_sorter.py
from auto_sorter import update_processed_files_txt

def test_duplicate_check():
    """Test that the update_processed_files_txt function checks for duplicates"""
    print("\n=== Testing duplicate check in update_processed_files_txt function ===")
    
    try:
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"Created temporary directory: {temp_dir}")
            
            # Define test file paths
            test_file_path_1 = "test_file_1.mp4"
            test_file_path_2 = "test_file_2.mp4"
            
            # First update - should add the file
            print(f"\nTest 1: Adding {test_file_path_1} for the first time")
            result1 = update_processed_files_txt(test_file_path_1, temp_dir, debug=True)
            if result1:
                print(f"✅ Successfully added {test_file_path_1} to processed_files.txt")
            else:
                print(f"❌ Failed to add {test_file_path_1} to processed_files.txt")
                return False
            
            # Second update with the same file - should detect duplicate and not add
            print(f"\nTest 2: Adding {test_file_path_1} again (should detect duplicate)")
            result2 = update_processed_files_txt(test_file_path_1, temp_dir, debug=True)
            if result2:
                print(f"✅ Function returned success for duplicate file (as expected)")
            else:
                print(f"❌ Function returned failure for duplicate file (unexpected)")
                return False
            
            # Check the content of processed_files.txt
            processed_files_path = os.path.join(temp_dir, "processed_files.txt")
            with open(processed_files_path, 'r') as f:
                content = f.read().splitlines()
                
            # Verify that the file only contains one entry
            if content.count(test_file_path_1) == 1:
                print(f"✅ Verified that {test_file_path_1} appears exactly once in processed_files.txt")
            else:
                print(f"❌ File appears {content.count(test_file_path_1)} times in processed_files.txt (expected 1)")
                return False
            
            # Add a different file - should add it
            print(f"\nTest 3: Adding a different file {test_file_path_2}")
            result3 = update_processed_files_txt(test_file_path_2, temp_dir, debug=True)
            if result3:
                print(f"✅ Successfully added {test_file_path_2} to processed_files.txt")
            else:
                print(f"❌ Failed to add {test_file_path_2} to processed_files.txt")
                return False
            
            # Check the content of processed_files.txt again
            with open(processed_files_path, 'r') as f:
                content = f.read().splitlines()
                
            # Verify that both files are in the list
            if test_file_path_1 in content and test_file_path_2 in content:
                print(f"✅ Verified that both files are in processed_files.txt")
            else:
                print(f"❌ Not all files were found in processed_files.txt")
                return False
            
            # Verify that each file appears exactly once
            if content.count(test_file_path_1) == 1 and content.count(test_file_path_2) == 1:
                print(f"✅ Verified that each file appears exactly once in processed_files.txt")
            else:
                print(f"❌ Files appear multiple times in processed_files.txt")
                return False
            
            print("\n✅ All tests passed successfully!")
            return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_duplicate_check()
    sys.exit(0 if success else 1)
