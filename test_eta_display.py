#!/usr/bin/env python3
"""
Test script to verify ETA display functionality in FramePack GUI
This script simulates progress updates by creating a progress file
"""

import time
import os

def simulate_progress():
    """Simulate generation progress by writing to a progress file"""
    progress_file = "framepack_progress.txt"
    total_steps = 30
    
    print("Starting ETA display test...")
    print("This will simulate a 30-step generation process")
    
    try:
        for step in range(1, total_steps + 1):
            # Write progress to file
            with open(progress_file, 'w') as f:
                f.write(f"Step {step}/{total_steps} - Total frames: {step * 4}")
            
            print(f"Step {step}/{total_steps} ({step/total_steps*100:.1f}%)")
            
            # Wait 2 seconds between steps to simulate real generation
            time.sleep(2)
        
        print("Test completed!")
        
        # Clean up
        if os.path.exists(progress_file):
            os.remove(progress_file)
            print("Cleaned up progress file")
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        # Clean up
        if os.path.exists(progress_file):
            os.remove(progress_file)
            print("Cleaned up progress file")

if __name__ == "__main__":
    simulate_progress()
