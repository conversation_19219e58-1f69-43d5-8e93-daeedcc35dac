#!/usr/bin/env python3
"""
Test script to verify exit code handling between different systems.
This will help identify if there are differences in how exit codes are handled.
"""

import sys
import os
import subprocess
import tempfile

def test_exit_code(code):
    """Test a specific exit code"""
    print(f"Testing exit code: {code}")
    
    # Create a temporary Python script that exits with the specified code
    test_script = f"""
import sys
print("Test script running...")
print(f"About to exit with code: {code}")
sys.exit({code})
"""
    
    # Write the test script to a temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_script)
        temp_script_path = f.name
    
    try:
        # Run the test script and capture the exit code
        result = subprocess.run([sys.executable, temp_script_path], 
                              capture_output=True, text=True)
        
        print(f"  Expected exit code: {code}")
        print(f"  Actual exit code: {result.returncode}")
        print(f"  stdout: {result.stdout.strip()}")
        if result.stderr:
            print(f"  stderr: {result.stderr.strip()}")
        
        # Check if the exit code matches
        if result.returncode == code:
            print(f"  ✓ Exit code matches")
        else:
            print(f"  ✗ Exit code mismatch!")
        
        print()
        return result.returncode == code
        
    finally:
        # Clean up the temporary script
        try:
            os.unlink(temp_script_path)
        except:
            pass

def test_signal_termination():
    """Test signal termination behavior"""
    print("Testing signal termination behavior...")
    
    # Create a script that sets up signal handlers similar to batch_f1_video.py
    test_script = """
import sys
import signal
import time

def signal_handler(signum, frame):
    print(f"Received signal {signum}")
    sys.exit(1)

# Register signal handlers
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)

print("Signal handlers registered")
print("Script will run for 2 seconds then exit normally")
time.sleep(2)
print("Exiting normally with code 0")
sys.exit(0)
"""
    
    # Write the test script to a temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_script)
        temp_script_path = f.name
    
    try:
        # Run the test script
        result = subprocess.run([sys.executable, temp_script_path], 
                              capture_output=True, text=True, timeout=5)
        
        print(f"  Exit code: {result.returncode}")
        print(f"  stdout: {result.stdout.strip()}")
        if result.stderr:
            print(f"  stderr: {result.stderr.strip()}")
        
        print()
        return result.returncode
        
    except subprocess.TimeoutExpired:
        print("  Script timed out (this shouldn't happen)")
        return -999
    finally:
        # Clean up the temporary script
        try:
            os.unlink(temp_script_path)
        except:
            pass

def test_batch_file_exit_codes():
    """Test how batch files handle different exit codes"""
    print("Testing batch file exit code handling...")
    
    # Create a test batch file
    batch_content = f"""@echo off
echo Testing batch file exit code handling
{sys.executable} -c "import sys; print('Python script running'); sys.exit(-1)"
set RESULT=%ERRORLEVEL%
echo Python exit code captured as: %RESULT%
if %RESULT% EQU -1 (
    echo Batch file detected exit code -1
) else if %RESULT% EQU 255 (
    echo Batch file converted -1 to 255
) else if %RESULT% EQU 1 (
    echo Batch file converted -1 to 1
) else (
    echo Batch file got unexpected code: %RESULT%
)
exit /b %RESULT%
"""
    
    # Write the batch file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.bat', delete=False) as f:
        f.write(batch_content)
        temp_batch_path = f.name
    
    try:
        # Run the batch file
        result = subprocess.run([temp_batch_path], 
                              capture_output=True, text=True, shell=True)
        
        print(f"  Batch file exit code: {result.returncode}")
        print(f"  stdout: {result.stdout.strip()}")
        if result.stderr:
            print(f"  stderr: {result.stderr.strip()}")
        
        print()
        return result.returncode
        
    finally:
        # Clean up the temporary batch file
        try:
            os.unlink(temp_batch_path)
        except:
            pass

def main():
    """Main test function"""
    print("Exit Code Testing Tool")
    print("=" * 50)
    
    print(f"Python version: {sys.version}")
    print(f"Operating system: {os.name}")
    print(f"Platform: {sys.platform}")
    print()
    
    # Test various exit codes
    test_codes = [0, 1, -1, 2, 255]
    
    print("Testing direct exit codes:")
    print("-" * 30)
    
    results = {}
    for code in test_codes:
        results[code] = test_exit_code(code)
    
    # Test signal termination
    print("Testing signal termination:")
    print("-" * 30)
    signal_result = test_signal_termination()
    
    # Test batch file handling (Windows only)
    if os.name == 'nt':
        print("Testing batch file exit code handling:")
        print("-" * 30)
        batch_result = test_batch_file_exit_codes()
    else:
        batch_result = None
        print("Batch file testing skipped (not Windows)")
    
    # Summary
    print("=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    print("Direct exit code tests:")
    for code, success in results.items():
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"  Exit code {code}: {status}")
    
    print(f"\nSignal termination test: Exit code {signal_result}")
    
    if batch_result is not None:
        print(f"Batch file test: Exit code {batch_result}")
    
    # Check for potential issues
    print("\nPotential Issues:")
    if not results.get(-1, True):
        print("  ⚠️  Exit code -1 is not handled consistently")
    if batch_result and batch_result != -1:
        print(f"  ⚠️  Batch file converted exit code -1 to {batch_result}")
    
    print("\nThis information can help diagnose why the FramePack process")
    print("might be failing with exit code -1 on some systems but not others.")

if __name__ == "__main__":
    main()
