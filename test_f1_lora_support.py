#!/usr/bin/env python3
"""
Test script to verify F1 LoRA support functionality
"""

import sys
import os
import subprocess

def test_f1_lora_arguments():
    """Test that F1 lock script accepts and converts LoRA arguments correctly"""
    print("Testing F1 LoRA support...")
    
    # Test 1: Single LoRA with new format
    print("\n1. Testing single LoRA with new format (--lora_file_1)")
    cmd = [
        sys.executable, "batch_f1_lock.py",
        "--lora_file_1", "test_lora.safetensors",
        "--lora_multiplier_1", "0.8",
        "--help"  # Use help to avoid actually running the model
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✓ F1 lock script accepts --lora_file_1 and --lora_multiplier_1")
        else:
            print(f"✗ F1 lock script failed with exit code {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Error running F1 lock script: {e}")
        return False
    
    # Test 2: Multiple LoRAs (should warn and use first one)
    print("\n2. Testing multiple LoRAs (should warn about F1 limitation)")
    cmd = [
        sys.executable, "batch_f1_lock.py",
        "--lora_file_1", "lora1.safetensors",
        "--lora_multiplier_1", "0.8",
        "--lora_file_2", "lora2.safetensors",
        "--lora_multiplier_2", "0.6",
        "--lora_file_3", "lora3.safetensors",
        "--lora_multiplier_3", "0.4",
        "--help"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✓ F1 lock script accepts multiple LoRA arguments")
        else:
            print(f"✗ F1 lock script failed with multiple LoRAs: {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Error running F1 lock script with multiple LoRAs: {e}")
        return False
    
    # Test 3: Backward compatibility with old format
    print("\n3. Testing backward compatibility (--lora_file)")
    cmd = [
        sys.executable, "batch_f1_lock.py",
        "--lora_file", "old_format_lora.safetensors",
        "--lora_multiplier", "0.7",
        "--help"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✓ F1 lock script maintains backward compatibility")
        else:
            print(f"✗ F1 lock script failed with old format: {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Error running F1 lock script with old format: {e}")
        return False
    
    print("\n✓ All F1 LoRA support tests passed!")
    return True

def test_argument_conversion():
    """Test the argument conversion logic"""
    print("\n4. Testing argument conversion logic...")
    
    # Import the conversion function
    sys.path.insert(0, os.path.dirname(__file__))
    try:
        from batch_f1_lock import convert_lora_arguments
        
        # Test conversion with multiple LoRA arguments
        original_args = [
            "--input_dir", "input",
            "--lora_file_1", "lora1.safetensors",
            "--lora_multiplier_1", "0.8",
            "--lora_file_2", "lora2.safetensors",
            "--lora_multiplier_2", "0.6",
            "--prompt", "test prompt"
        ]
        
        converted_args = convert_lora_arguments(original_args, "lora1.safetensors", 0.8)
        
        # Check that multiple LoRA arguments are removed
        has_lora_file_1 = any("--lora_file_1" in arg for arg in converted_args)
        has_lora_file_2 = any("--lora_file_2" in arg for arg in converted_args)
        
        # Check that single LoRA arguments are added
        has_lora_file = "--lora_file" in converted_args
        has_lora_multiplier = "--lora_multiplier" in converted_args
        
        if not has_lora_file_1 and not has_lora_file_2 and has_lora_file and has_lora_multiplier:
            print("✓ Argument conversion works correctly")
            print(f"  Original: {original_args}")
            print(f"  Converted: {converted_args}")
        else:
            print("✗ Argument conversion failed")
            print(f"  has_lora_file_1: {has_lora_file_1}")
            print(f"  has_lora_file_2: {has_lora_file_2}")
            print(f"  has_lora_file: {has_lora_file}")
            print(f"  has_lora_multiplier: {has_lora_multiplier}")
            print(f"  Converted: {converted_args}")
            return False
            
    except ImportError as e:
        print(f"✗ Could not import conversion function: {e}")
        return False
    except Exception as e:
        print(f"✗ Error testing conversion logic: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("F1 LoRA Support Test Suite")
    print("=" * 40)
    
    success = True
    success &= test_f1_lora_arguments()
    success &= test_argument_conversion()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 All tests passed! F1 LoRA support is working correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
