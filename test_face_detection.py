#!/usr/bin/env python
"""
Test Enhanced Face Detection

This script helps test the enhanced face detection capabilities.
It will show you which detection methods work on your test images.
"""

import os
import sys
from pathlib import Path

# Import the enhanced face cropper
try:
    from image_face_cropper import (
        detect_faces_mediapipe, 
        detect_faces_face_recognition, 
        detect_faces_haar_improved,
        MEDIAPIPE_AVAILABLE,
        FACE_RECOGNITION_AVAILABLE,
        crop_face
    )
    import cv2
    import numpy as np
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please make sure opencv-python and numpy are installed:")
    print("pip install opencv-python numpy")
    sys.exit(1)

def test_detection_methods(image_path):
    """Test all available detection methods on an image."""
    if not os.path.exists(image_path):
        print(f"Error: Image file not found: {image_path}")
        return
    
    print(f"\nTesting detection methods on: {os.path.basename(image_path)}")
    print("=" * 60)
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"Error: Could not load image: {image_path}")
        return
    
    h, w = image.shape[:2]
    print(f"Image size: {w}x{h}")
    
    results = {}
    
    # Test MediaPipe
    if MEDIAPIPE_AVAILABLE:
        print("\n1. Testing MediaPipe Face Detection...")
        faces = detect_faces_mediapipe(image)
        results['MediaPipe'] = faces
        if faces:
            print(f"   ✓ Found {len(faces)} face(s)")
            for i, (x, y, w, h) in enumerate(faces):
                print(f"     Face {i+1}: ({x}, {y}) size {w}x{h}")
        else:
            print("   ❌ No faces detected")
    else:
        print("\n1. MediaPipe Face Detection: Not available")
        print("   Install with: pip install mediapipe")
    
    # Test face_recognition
    if FACE_RECOGNITION_AVAILABLE:
        print("\n2. Testing face_recognition Library...")
        faces = detect_faces_face_recognition(image)
        results['face_recognition'] = faces
        if faces:
            print(f"   ✓ Found {len(faces)} face(s)")
            for i, (x, y, w, h) in enumerate(faces):
                print(f"     Face {i+1}: ({x}, {y}) size {w}x{h}")
        else:
            print("   ❌ No faces detected")
    else:
        print("\n2. face_recognition Library: Not available")
        print("   Install with: pip install face-recognition")
    
    # Test Improved Haar
    print("\n3. Testing Improved Haar Cascade...")
    faces = detect_faces_haar_improved(image, strength=5)
    results['Improved Haar'] = faces
    if faces:
        print(f"   ✓ Found {len(faces)} face(s)")
        for i, (x, y, w, h) in enumerate(faces):
            print(f"     Face {i+1}: ({x}, {y}) size {w}x{h}")
    else:
        print("   ❌ No faces detected")
    
    # Test Original Haar
    print("\n4. Testing Original Haar Cascade...")
    try:
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))
        faces = [(x, y, w, h) for (x, y, w, h) in faces]  # Convert to list of tuples
        results['Original Haar'] = faces
        if faces:
            print(f"   ✓ Found {len(faces)} face(s)")
            for i, (x, y, w, h) in enumerate(faces):
                print(f"     Face {i+1}: ({x}, {y}) size {w}x{h}")
        else:
            print("   ❌ No faces detected")
    except Exception as e:
        print(f"   ❌ Error: {e}")
        results['Original Haar'] = []
    
    # Summary
    print("\n" + "=" * 60)
    print("DETECTION SUMMARY:")
    successful_methods = []
    for method, faces in results.items():
        if faces:
            successful_methods.append(f"{method} ({len(faces)} faces)")
        else:
            print(f"❌ {method}: No faces detected")
    
    if successful_methods:
        print("✓ Successful methods:")
        for method in successful_methods:
            print(f"  - {method}")
        
        # Test actual cropping
        print(f"\nTesting actual face cropping...")
        output_path = str(Path(image_path).parent / f"{Path(image_path).stem}_test_cropped{Path(image_path).suffix}")
        result = crop_face(image_path, output_path, strength=5, output_size=608, fill_percentage=60)
        if result:
            print(f"✓ Face cropping successful: {output_path}")
        else:
            print("❌ Face cropping failed")
    else:
        print("❌ No detection methods found faces in this image")
        print("   This image may not contain detectable faces or may need preprocessing")

def main():
    print("Enhanced Face Detection Tester")
    print("=" * 40)
    
    # Show available methods
    methods = []
    if MEDIAPIPE_AVAILABLE:
        methods.append("MediaPipe")
    if FACE_RECOGNITION_AVAILABLE:
        methods.append("face_recognition")
    methods.extend(["Improved Haar", "Original Haar"])
    
    print(f"Available detection methods: {', '.join(methods)}")
    
    if not MEDIAPIPE_AVAILABLE:
        print("💡 For best results, install MediaPipe: pip install mediapipe")
    if not FACE_RECOGNITION_AVAILABLE:
        print("💡 For angle detection, install face_recognition: pip install face-recognition")
    
    # Get test images
    if len(sys.argv) > 1:
        # Use command line arguments
        for image_path in sys.argv[1:]:
            test_detection_methods(image_path)
    else:
        # Interactive mode
        print("\nEnter image file paths to test (press Enter with empty input to finish):")
        while True:
            image_path = input("Image file path: ").strip()
            if not image_path:
                break
            
            # Remove quotes if present
            if (image_path.startswith('"') and image_path.endswith('"')) or \
               (image_path.startswith("'") and image_path.endswith("'")):
                image_path = image_path[1:-1]
            
            if os.path.isfile(image_path):
                test_detection_methods(image_path)
            else:
                print(f"File not found: {image_path}")

if __name__ == "__main__":
    main()
