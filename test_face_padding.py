#!/usr/bin/env python
"""
Test script for the new face cropping padding feature
"""

import os
import sys
from image_face_cropper import crop_face

def test_padding_feature():
    """Test the new padding feature with different settings"""
    
    # Test image path (you'll need to provide a real image path)
    test_image = "input/test.png"  # Use the test image in input folder
    
    if not os.path.exists(test_image):
        print(f"Test image not found: {test_image}")
        print("Please provide a test image with a face to test the padding feature")
        return
    
    # Test different padding configurations
    test_configs = [
        {"padding_pixels": 0, "padding_side": "top", "description": "No padding (baseline)"},
        {"padding_pixels": 64, "padding_side": "top", "description": "64px padding on top"},
        {"padding_pixels": 64, "padding_side": "bottom", "description": "64px padding on bottom"},
        {"padding_pixels": 64, "padding_side": "left", "description": "64px padding on left"},
        {"padding_pixels": 64, "padding_side": "right", "description": "64px padding on right"},
        {"padding_pixels": 64, "padding_side": "top_bottom", "description": "64px padding on top and bottom"},
        {"padding_pixels": 64, "padding_side": "left_right", "description": "64px padding on left and right"},
        {"padding_pixels": 128, "padding_side": "top", "description": "128px padding on top"},
    ]
    
    print("Testing face cropping with padding feature...")
    print("=" * 60)
    
    for i, config in enumerate(test_configs):
        print(f"\nTest {i+1}: {config['description']}")
        print(f"Padding: {config['padding_pixels']} pixels on {config['padding_side']}")
        
        # Generate output filename
        output_path = f"test_output_{i+1}_{config['padding_side']}_{config['padding_pixels']}px.jpg"
        
        try:
            result = crop_face(
                input_path=test_image,
                output_path=output_path,
                strength=5,
                output_size=608,
                fill_percentage=60,
                padding_pixels=config['padding_pixels'],
                padding_side=config['padding_side']
            )
            
            if result:
                print(f"✓ Success: {output_path}")
            else:
                print(f"✗ Failed: No face detected or processing error")
                
        except Exception as e:
            print(f"✗ Error: {e}")
    
    print("\n" + "=" * 60)
    print("Testing complete!")
    print("Check the generated output images to see the padding effects.")

if __name__ == "__main__":
    test_padding_feature()
