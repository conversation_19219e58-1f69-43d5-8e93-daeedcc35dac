from pathlib import Path
import os

# Create a temporary directory for testing
test_dir = Path("test_filter_dir")
os.makedirs(test_dir, exist_ok=True)

# Create some test files
test_files = [
    "normal_image.png",
    "expanded_image.png",
    "image_expanded.png",
    "rotated_image.jpg",
    "image_rotated.jpg",
    "normal_image2.jpg",
    "EXPANDED_image.png",  # Test case insensitivity
    "image_ROTATED.jpg",   # Test case insensitivity
]

# Create empty files
for filename in test_files:
    with open(test_dir / filename, 'w') as f:
        f.write("")

# Define a simplified version of the function for testing
def get_image_files_test(directory):
    """Get all image files from directory"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp']
    image_files = []

    for ext in image_extensions:
        image_files.extend([f for f in Path(directory).glob(f'*{ext}') if f.is_file()])
        image_files.extend([f for f in Path(directory).glob(f'*{ext.upper()}') if f.is_file()])

    # Filter out any non-image files that might have been caught
    image_files = [f for f in image_files if f.suffix.lower() in image_extensions]
    
    # Filter out files with "expanded" or "rotated" in their names
    excluded_keywords = ["expanded", "rotated"]
    excluded_files = [f for f in image_files if any(keyword.lower() in f.stem.lower() for keyword in excluded_keywords)]
    filtered_files = [f for f in image_files if not any(keyword.lower() in f.stem.lower() for keyword in excluded_keywords)]
    
    if excluded_files:
        print(f"\nAutomatically excluding {len(excluded_files)} files with 'expanded' or 'rotated' in their names:")
        for i, img in enumerate(excluded_files):
            print(f"  {i+1}. {img.name}")

    # Remove duplicates while preserving order
    seen = set()
    unique_image_files = []
    for f in filtered_files:
        if f not in seen:
            seen.add(f)
            unique_image_files.append(f)

    return sorted(unique_image_files)

# Test the function
print("\nTesting get_image_files function:")
files = get_image_files_test(test_dir)
print("\nFiles that will be processed:")
for i, f in enumerate(files):
    print(f"  {i+1}. {f.name}")

# Clean up
import shutil
shutil.rmtree(test_dir)
