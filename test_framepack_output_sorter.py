#!/usr/bin/env python
"""
Test script for framepack_output_sorter.py to verify it handles the seconds label scheme correctly.
"""

import os
import sys
from framepack_output_sorter import parse_filename

def test_parse_filename():
    """Test the parse_filename function with various filename formats."""
    test_cases = [
        # Standard timestamp format
        ("250420_121919_242_3623_37.mp4", "250420_121919_242_3623"),
        ("250420_121919_242_3623_37_24s.mp4", "250420_121919_242_3623"),
        ("250426_085120_706_7278_19_seed357798872.mp4", "250426_085120_706_7278"),
        ("250426_085120_706_7278_19_seed357798872_24s.mp4", "250426_085120_706_7278"),
        
        # Generic filename format
        ("image_name.mp4", "image_name"),
        ("image_name_5s.mp4", "image_name"),
        ("image_name_seed123456.mp4", "image_name"),
        ("image_name_seed123456_5s.mp4", "image_name"),
        
        # Invalid formats
        ("image.jpg", None),
        ("invalid_format", None)
    ]
    
    print("Testing parse_filename function:")
    for filename, expected in test_cases:
        result = parse_filename(filename)
        status = "✓" if result == expected else "✗"
        print(f"{status} {filename}: Got {result}, Expected {expected}")

if __name__ == "__main__":
    test_parse_filename()
