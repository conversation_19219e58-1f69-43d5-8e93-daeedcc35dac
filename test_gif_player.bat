@echo off
REM Test the custom GIF player for latent previews

echo Testing GIF player for latent previews...

REM Check if Python is installed
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is not installed or not in the PATH.
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Install Pillow if not already installed
echo Checking for Pillow...
python -c "import PIL" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Installing Pillow...
    pip install pillow
    if %ERRORLEVEL% NEQ 0 (
        echo Error: Failed to install Pillow.
        echo Please try installing it manually: pip install pillow
        pause
        exit /b 1
    )
    echo Pillow installed successfully.
) else (
    echo Pillow is already installed.
)

REM Create latent_previews directory if it doesn't exist
if not exist "latent_previews" (
    echo Creating latent_previews directory...
    mkdir latent_previews
)

REM Create temp directory if it doesn't exist
if not exist "temp" (
    echo Creating temp directory...
    mkdir temp
)

REM Check if we have any GIF files in the latent_previews directory
set "gif_found=0"
for %%f in (latent_previews\*.gif) do (
    set "gif_found=1"
    goto :check_gif_done
)
:check_gif_done

if "%gif_found%"=="0" (
    echo No GIF files found in latent_previews directory.
    echo Running generate_sample_previews.py to create sample videos...
    
    REM Check if ffmpeg is installed
    ffmpeg -version >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo Error: ffmpeg is not installed or not in the PATH.
        echo Please install ffmpeg and try again.
        echo You can download ffmpeg from https://ffmpeg.org/download.html
        pause
        exit /b 1
    )
    
    REM Generate sample videos
    python generate_sample_previews.py
    
    echo Converting MP4 to GIF...
    python generate_gif_preview.py --all
)

REM Run the GIF player test
echo Running GIF player test...
python -c "from tk_gif_player import TkGifPlayer; import tkinter as tk; import glob; import os; root = tk.Tk(); root.title('GIF Player Test'); player = TkGifPlayer(root, width=350, height=240); player.pack(padx=10, pady=10); gif_files = glob.glob('latent_previews/*.gif'); if gif_files: player.load(gif_files[0]); player.play(); print(f'Loaded GIF: {os.path.basename(gif_files[0])}'); tk.Label(root, text=f'Loaded GIF: {os.path.basename(gif_files[0])}').pack(); else: tk.Label(root, text='No GIF files found').pack(); tk.Button(root, text='Play', command=player.play).pack(side=tk.LEFT, padx=5); tk.Button(root, text='Pause', command=player.pause).pack(side=tk.LEFT, padx=5); tk.Button(root, text='Stop', command=player.stop).pack(side=tk.LEFT, padx=5); root.mainloop()"

echo Test completed.
pause
