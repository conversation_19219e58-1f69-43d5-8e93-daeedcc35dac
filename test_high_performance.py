#!/usr/bin/env python3

import tkinter as tk
from tkinter import ttk

print("Testing high-performance preview window...")

try:
    from pyvidplayer2 import VideoTkinter
    print("✅ pyvidplayer2 imported successfully!")
    
    # Test basic functionality
    root = tk.Tk()
    root.title("High-Performance Test")
    root.geometry("400x300")
    
    label = ttk.Label(root, text="High-Performance Preview Window\nTest Successful!", justify=tk.CENTER)
    label.pack(expand=True)
    
    # Test if we can create a VideoTkinter instance (without a video file)
    try:
        # This will fail without a video file, but we can test the import
        print("VideoTkinter class available:", VideoTkinter)
        status_label = ttk.Label(root, text="pyvidplayer2 VideoTkinter: Available", foreground="green")
    except Exception as e:
        print(f"VideoTkinter test error: {e}")
        status_label = ttk.Label(root, text="pyvidplayer2 VideoTkinter: Error", foreground="red")
    
    status_label.pack(pady=10)
    
    close_button = ttk.Button(root, text="Close", command=root.destroy)
    close_button.pack(pady=10)
    
    print("Test window created successfully!")
    root.mainloop()
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Error: {e}")

print("Test completed.")
