"""
Test script specifically for listbox drag and drop functionality.
This creates a minimal window with a listbox that supports drag and drop.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox

try:
    from tkinterdnd2 import TkinterDnD, DND_FILES
    TKDND_AVAILABLE = True
except ImportError:
    print("TkinterDnD2 not available. Drag and drop will be disabled.")
    print("To enable drag and drop, install tkinterdnd2: pip install tkinterdnd2")
    TKDND_AVAILABLE = False

class ListboxDnDTest:
    def __init__(self, root):
        self.root = root
        self.root.title("Listbox Drag & Drop Test")
        self.root.geometry("400x300")
        
        # Create main frame
        main_frame = ttk.Frame(root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create a label
        ttk.Label(main_frame, text="Drag and drop files onto the listbox:").pack(pady=(0, 10))
        
        # Create a frame for the listbox with a visible border
        listbox_frame = ttk.Frame(main_frame, borderwidth=2, relief="solid")
        listbox_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create the listbox
        self.listbox = tk.Listbox(listbox_frame)
        self.listbox.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)
        
        # Add a scrollbar
        scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=self.listbox.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.listbox.configure(yscrollcommand=scrollbar.set)
        
        # Add a status label
        self.status_label = ttk.Label(main_frame, text="Ready")
        self.status_label.pack(pady=10)
        
        # Register drop targets if tkinterdnd2 is available
        if TKDND_AVAILABLE:
            # Register the listbox as a drop target
            self.listbox.drop_target_register(DND_FILES)
            self.listbox.dnd_bind('<<Drop>>', self.drop_files)
            self.listbox.dnd_bind('<<DragEnter>>', self.drag_enter)
            self.listbox.dnd_bind('<<DragLeave>>', self.drag_leave)
            
            # Also register the listbox frame
            listbox_frame.drop_target_register(DND_FILES)
            listbox_frame.dnd_bind('<<Drop>>', self.drop_files)
            listbox_frame.dnd_bind('<<DragEnter>>', self.drag_enter)
            listbox_frame.dnd_bind('<<DragLeave>>', self.drag_leave)
            
            # Store the frame for styling
            self.listbox_frame = listbox_frame
        else:
            ttk.Label(main_frame, text="Drag and drop not available", foreground="red").pack(pady=10)
    
    def drop_files(self, event):
        """Handle files dropped onto the widget"""
        try:
            # Get the dropped data (file paths)
            file_paths = self.root.tk.splitlist(event.data)
            print(f"Received drop event with data: {event.data}")
            
            # Clear the listbox
            self.listbox.delete(0, tk.END)
            
            # Add each file to the listbox
            for file_path in file_paths:
                # Remove quotes if present
                if file_path.startswith('"') and file_path.endswith('"'):
                    file_path = file_path[1:-1]
                
                # Normalize path
                file_path = os.path.normpath(file_path)
                
                # Add to listbox
                self.listbox.insert(tk.END, os.path.basename(file_path))
                print(f"Added file: {file_path}")
            
            # Update status
            self.status_label.config(text=f"Added {len(file_paths)} files")
            
            # Show a message box
            messagebox.showinfo("Files Added", f"Added {len(file_paths)} files to the list")
        except Exception as e:
            print(f"Error during drop: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            # Restore normal appearance
            self.listbox.config(background="white")
            self.listbox_frame.config(relief="solid")
    
    def drag_enter(self, event):
        """Handle drag enter event"""
        try:
            print(f"Drag enter event on {event.widget}")
            
            # Change appearance to indicate it's a drop target
            self.listbox.config(background="#e0f0ff")
            self.listbox_frame.config(relief="groove", borderwidth=3)
            self.status_label.config(text="Drop files here!")
        except Exception as e:
            print(f"Error in drag_enter: {str(e)}")
    
    def drag_leave(self, event):
        """Handle drag leave event"""
        try:
            print(f"Drag leave event on {event.widget}")
            
            # Restore normal appearance
            self.listbox.config(background="white")
            self.listbox_frame.config(relief="solid", borderwidth=2)
            self.status_label.config(text="Ready")
        except Exception as e:
            print(f"Error in drag_leave: {str(e)}")

def main():
    # Use TkinterDnD.Tk() if available, otherwise fallback to standard tk.Tk()
    if TKDND_AVAILABLE:
        root = TkinterDnD.Tk()
    else:
        root = tk.Tk()
    
    app = ListboxDnDTest(root)
    root.mainloop()

if __name__ == "__main__":
    main()
