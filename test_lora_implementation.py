#!/usr/bin/env python3
"""
Test script to verify LoRA implementation in FramePack batch.py
"""

import os
import sys
import torch
from pathlib import Path

def test_lora_imports():
    """Test that LoRA utilities can be imported"""
    try:
        from utils.lora_utils import merge_lora_to_state_dict, convert_hunyuan_to_framepack
        from utils.fp8_optimization_utils import optimize_state_dict_with_fp8, apply_fp8_monkey_patch
        print("✓ LoRA utilities imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import LoRA utilities: {e}")
        return False

def test_batch_script_args():
    """Test that batch.py accepts LoRA arguments"""
    try:
        # Import the parse_args function from batch.py
        sys.path.insert(0, os.path.dirname(__file__))
        from batch import parse_args
        
        # Test with LoRA arguments
        test_args = [
            '--lora_file', 'test_lora.safetensors',
            '--lora_multiplier', '0.8',
            '--fp8_optimization'
        ]
        
        # Temporarily replace sys.argv
        original_argv = sys.argv
        sys.argv = ['batch.py'] + test_args
        
        try:
            args = parse_args()
            print("✓ Batch script accepts LoRA arguments")
            print(f"  LoRA file: {args.lora_file}")
            print(f"  LoRA multiplier: {args.lora_multiplier}")
            print(f"  FP8 optimization: {args.fp8_optimization}")
            return True
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        print(f"✗ Failed to parse LoRA arguments: {e}")
        return False

def test_transformer_loading():
    """Test that the transformer loading function works"""
    try:
        sys.path.insert(0, os.path.dirname(__file__))
        from batch import load_transformer_with_lora
        
        print("✓ Transformer loading function available")
        print("  Note: Actual model loading requires GPU and model files")
        return True
    except Exception as e:
        print(f"✗ Failed to access transformer loading function: {e}")
        return False

def test_file_structure():
    """Test that required files and directories exist"""
    required_files = [
        'utils/__init__.py',
        'utils/lora_utils.py',
        'utils/fp8_optimization_utils.py',
        'batch.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} exists")
        else:
            print(f"✗ {file_path} missing")
            all_exist = False
    
    return all_exist

def main():
    """Run all tests"""
    print("Testing LoRA implementation in FramePack...")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("LoRA Imports", test_lora_imports),
        ("Batch Script Arguments", test_batch_script_args),
        ("Transformer Loading", test_transformer_loading),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"  Test failed!")
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! LoRA implementation appears to be working.")
        print("\nNext steps:")
        print("1. Test with an actual LoRA file:")
        print("   python batch.py --lora_file path/to/your/lora.safetensors --lora_multiplier 0.8")
        print("2. Test with FP8 optimization:")
        print("   python batch.py --fp8_optimization")
        print("3. Test with both:")
        print("   python batch.py --lora_file path/to/your/lora.safetensors --lora_multiplier 0.8 --fp8_optimization")
    else:
        print("✗ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
