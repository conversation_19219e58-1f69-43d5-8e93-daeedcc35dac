#!/usr/bin/env python3
"""
Test script to verify LoRA keyword integration in batch_f1.py
This tests the functions without requiring the full CUDA environment.
"""

import sys
import os

# Add the current directory to the path so we can import from batch_f1
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the LoRA keyword functions from batch_f1
from batch_f1 import get_lora_keywords, append_keywords_to_prompt

def test_lora_keyword_functions():
    """Test the LoRA keyword functions"""
    print("=" * 60)
    print("Testing LoRA Keyword Integration in batch_f1.py")
    print("=" * 60)
    
    # Test 1: Test with existing LoRA file that has a txt file
    print("\nTest 1: Testing with existing LoRA file")
    lora_file = "lora/cumshot-v1-18epo-hunyuan-k3nk.safetensors"
    keywords = get_lora_keywords(
        lora_file_1=lora_file,
        lora_file_2=None,
        lora_file_3=None,
        append_lora_keywords=True
    )
    print(f"Keywords found: '{keywords}'")
    
    # Test 2: Test appending keywords to prompt
    print("\nTest 2: Testing prompt keyword appending")
    original_prompt = "a beautiful girl"
    final_prompt = append_keywords_to_prompt(
        original_prompt,
        lora_file_1=lora_file,
        lora_file_2=None,
        lora_file_3=None,
        append_lora_keywords=True
    )
    print(f"Original prompt: '{original_prompt}'")
    print(f"Final prompt: '{final_prompt}'")
    
    # Test 3: Test with feature disabled
    print("\nTest 3: Testing with feature disabled")
    disabled_prompt = append_keywords_to_prompt(
        original_prompt,
        lora_file_1=lora_file,
        lora_file_2=None,
        lora_file_3=None,
        append_lora_keywords=False
    )
    print(f"Disabled result: '{disabled_prompt}' (should be unchanged)")
    
    # Test 4: Test with multiple LoRAs
    print("\nTest 4: Testing with multiple LoRAs")
    multi_keywords = get_lora_keywords(
        lora_file_1="lora/cumshot-v1-18epo-hunyuan-k3nk.safetensors",
        lora_file_2="lora/facefuck_epoch400.safetensors",
        lora_file_3="lora/fullnelson-258epo-hunyuan-k3nk.safetensors",
        append_lora_keywords=True
    )
    print(f"Multiple LoRA keywords: '{multi_keywords}'")
    
    multi_prompt = append_keywords_to_prompt(
        "test prompt",
        lora_file_1="lora/cumshot-v1-18epo-hunyuan-k3nk.safetensors",
        lora_file_2="lora/facefuck_epoch400.safetensors",
        lora_file_3="lora/fullnelson-258epo-hunyuan-k3nk.safetensors",
        append_lora_keywords=True
    )
    print(f"Multi-LoRA final prompt: '{multi_prompt}'")
    
    print("\n" + "=" * 60)
    print("Integration test completed successfully!")
    print("=" * 60)

if __name__ == "__main__":
    test_lora_keyword_functions()
