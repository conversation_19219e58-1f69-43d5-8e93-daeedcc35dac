#!/usr/bin/env python3
"""
Test script to demonstrate the LoRA keywords auto-append feature.
This script simulates the functionality without running the full GUI.
"""

import os

class MockVar:
    """Mock tkinter variable for testing"""
    def __init__(self, value=None):
        self.value = value

    def get(self):
        return self.value

    def set(self, value):
        self.value = value

class MockFramePackGUI:
    """Mock class to test the LoRA keywords functionality"""

    def __init__(self):
        # Initialize mock variables
        self.append_lora_keywords = MockVar(True)
        self.lora_file_1 = MockVar("example_style.safetensors")
        self.lora_file_2 = MockVar("")
        self.lora_file_3 = MockVar("")
    
    def get_lora_keywords(self):
        """Get keywords from LoRA .txt files and return them as a comma-separated string"""
        if not self.append_lora_keywords.get():
            return ""
        
        keywords = []
        
        # Check all three LoRA slots
        lora_configs = [
            (self.lora_file_1.get().strip(), 1),
            (self.lora_file_2.get().strip(), 2),
            (self.lora_file_3.get().strip(), 3)
        ]
        
        for lora_file, lora_index in lora_configs:
            if lora_file and lora_file != "(None - Disabled)":
                # Get the base name without extension
                lora_base_name = os.path.splitext(lora_file)[0]
                
                # Try different possible locations for the txt file
                txt_file_paths = []
                
                # If it's just a filename, check in the lora folder
                if not os.path.isabs(lora_file) and not os.path.exists(lora_file):
                    lora_path = os.path.join("lora", lora_file)
                    if os.path.exists(lora_path):
                        txt_file_paths.append(os.path.join("lora", os.path.splitext(os.path.basename(lora_file))[0] + ".txt"))
                    else:
                        # Also try with the original path
                        txt_file_paths.append(lora_base_name + ".txt")
                else:
                    # Full path provided
                    txt_file_paths.append(lora_base_name + ".txt")
                
                # Try to read keywords from the txt file
                for txt_file_path in txt_file_paths:
                    try:
                        if os.path.exists(txt_file_path):
                            with open(txt_file_path, 'r', encoding='utf-8') as f:
                                # Read the first line and strip whitespace
                                first_line = f.readline().strip()
                                if first_line:
                                    keywords.append(first_line)
                                    print(f"✓ Loaded keywords from {os.path.basename(txt_file_path)}: {first_line}")
                                    break  # Found keywords, no need to try other paths
                    except Exception as e:
                        print(f"Warning: Could not read keywords from {txt_file_path}: {e}")
        
        # Join all keywords with ", " separator
        if keywords:
            result = ", ".join(keywords)
            print(f"✓ Total LoRA keywords to append: {result}")
            return result
        
        return ""

    def append_keywords_to_prompt(self, original_prompt):
        """Append LoRA keywords to the prompt if the setting is enabled"""
        if not self.append_lora_keywords.get():
            return original_prompt
        
        keywords = self.get_lora_keywords()
        if keywords:
            if original_prompt.strip():
                return f"{original_prompt}, {keywords}"
            else:
                return keywords
        
        return original_prompt

def test_lora_keywords():
    """Test the LoRA keywords functionality"""
    print("=" * 60)
    print("Testing LoRA Keywords Auto-Append Feature")
    print("=" * 60)
    
    # Create mock GUI instance
    gui = MockFramePackGUI()
    
    # Test cases
    test_cases = [
        ("a beautiful sunset", "Basic prompt with keywords"),
        ("", "Empty prompt with keywords"),
        ("detailed landscape, mountains", "Existing prompt with keywords"),
    ]
    
    print(f"\nLoRA Configuration:")
    print(f"  LoRA 1: {gui.lora_file_1.get()}")
    print(f"  LoRA 2: {gui.lora_file_2.get() or '(None)'}")
    print(f"  LoRA 3: {gui.lora_file_3.get() or '(None)'}")
    print(f"  Append Keywords: {gui.append_lora_keywords.get()}")
    
    print(f"\nTest Results:")
    print("-" * 40)
    
    for original_prompt, description in test_cases:
        result = gui.append_keywords_to_prompt(original_prompt)
        print(f"\nTest: {description}")
        print(f"  Original: '{original_prompt}'")
        print(f"  Result:   '{result}'")
    
    # Test with feature disabled
    print(f"\n" + "=" * 40)
    print("Testing with feature DISABLED:")
    print("=" * 40)
    
    gui.append_lora_keywords.set(False)
    
    for original_prompt, description in test_cases:
        result = gui.append_keywords_to_prompt(original_prompt)
        print(f"\nTest: {description}")
        print(f"  Original: '{original_prompt}'")
        print(f"  Result:   '{result}' (should be unchanged)")

if __name__ == "__main__":
    test_lora_keywords()
