#!/usr/bin/env python3
"""
Test script for LoRA Merger functionality
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Import merger functions
from lora_merger import find_model_files, load_model_state_dict

def test_model_detection():
    """Test if models can be detected in cache"""
    print("Testing model detection...")
    
    models_to_test = [
        "lllyasviel/FramePackI2V_HY",
        "lllyasviel/FramePack_F1_I2V_HY_20250503"
    ]
    
    for model_name in models_to_test:
        try:
            model_path, safetensors_files, config_file = find_model_files(model_name)
            print(f"✓ {model_name}:")
            print(f"  - Found {len(safetensors_files)} safetensors files")
            print(f"  - Config file: {os.path.basename(config_file)}")
            print(f"  - Model path: {model_path}")
            
            # Test loading a small portion of the model
            print(f"  - Testing model loading...")
            try:
                # Load just the first file to test
                from safetensors.torch import load_file
                first_file_dict = load_file(safetensors_files[0])
                print(f"  - First file contains {len(first_file_dict)} tensors")
                
                # Show some tensor names and shapes
                tensor_names = list(first_file_dict.keys())[:3]
                for name in tensor_names:
                    shape = first_file_dict[name].shape
                    dtype = first_file_dict[name].dtype
                    print(f"    - {name}: {shape} ({dtype})")
                
                print(f"  ✓ Model loading test passed")
                
            except Exception as e:
                print(f"  ✗ Model loading test failed: {e}")
                
        except FileNotFoundError as e:
            print(f"✗ {model_name}: {e}")
        except Exception as e:
            print(f"✗ {model_name}: Unexpected error - {e}")
        
        print()

def test_lora_utils():
    """Test LoRA utility imports"""
    print("Testing LoRA utility imports...")
    
    try:
        from utils.lora_utils import merge_lora_to_state_dict, convert_hunyuan_to_framepack
        print("✓ LoRA utilities imported successfully")
        
        # Test function signatures
        import inspect
        merge_sig = inspect.signature(merge_lora_to_state_dict)
        print(f"✓ merge_lora_to_state_dict signature: {merge_sig}")
        
        convert_sig = inspect.signature(convert_hunyuan_to_framepack)
        print(f"✓ convert_hunyuan_to_framepack signature: {convert_sig}")
        
    except ImportError as e:
        print(f"✗ Failed to import LoRA utilities: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error testing LoRA utilities: {e}")
        return False
    
    return True

def test_safetensors_operations():
    """Test safetensors save/load operations"""
    print("Testing safetensors operations...")
    
    try:
        import torch
        from safetensors.torch import save_file, load_file
        
        # Create test tensors
        test_dict = {
            'test_tensor_1': torch.randn(10, 20, dtype=torch.float32),
            'test_tensor_2': torch.randn(5, 5, dtype=torch.bfloat16),
            'test_tensor_3': torch.randn(100, dtype=torch.float16)
        }
        
        # Test save/load cycle
        with tempfile.NamedTemporaryFile(suffix='.safetensors', delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        try:
            # Save
            save_file(test_dict, tmp_path)
            print(f"✓ Saved test tensors to {tmp_path}")
            
            # Load
            loaded_dict = load_file(tmp_path)
            print(f"✓ Loaded {len(loaded_dict)} tensors")
            
            # Verify
            for key in test_dict:
                if key not in loaded_dict:
                    print(f"✗ Missing tensor: {key}")
                    return False
                
                if not torch.equal(test_dict[key], loaded_dict[key]):
                    print(f"✗ Tensor mismatch: {key}")
                    return False
            
            print("✓ All tensors match after save/load cycle")
            
        finally:
            # Clean up
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
        
    except Exception as e:
        print(f"✗ SafeTensors operations failed: {e}")
        return False
    
    return True

def test_output_directory():
    """Test output directory creation and permissions"""
    print("Testing output directory operations...")
    
    try:
        # Create temporary output directory
        with tempfile.TemporaryDirectory() as tmp_dir:
            test_output_dir = os.path.join(tmp_dir, "test_merged_models")
            
            # Test directory creation
            os.makedirs(test_output_dir, exist_ok=True)
            print(f"✓ Created output directory: {test_output_dir}")
            
            # Test file creation
            test_file = os.path.join(test_output_dir, "test_file.txt")
            with open(test_file, 'w') as f:
                f.write("test content")
            print(f"✓ Created test file: {test_file}")
            
            # Test subdirectory creation
            sub_dir = os.path.join(test_output_dir, "test_model_dir")
            os.makedirs(sub_dir, exist_ok=True)
            print(f"✓ Created subdirectory: {sub_dir}")
            
            # Test file operations in subdirectory
            config_file = os.path.join(sub_dir, "config.json")
            with open(config_file, 'w') as f:
                f.write('{"test": "config"}')
            print(f"✓ Created config file: {config_file}")
            
    except Exception as e:
        print(f"✗ Output directory operations failed: {e}")
        return False
    
    return True

def check_dependencies():
    """Check if all required dependencies are available"""
    print("Checking dependencies...")
    
    required_modules = [
        'torch',
        'safetensors',
        'tqdm',
        'pathlib',
        'json',
        'shutil'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            print(f"✗ {module} - MISSING")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n✗ Missing dependencies: {', '.join(missing_modules)}")
        return False
    else:
        print("✓ All dependencies available")
        return True

def main():
    """Run all tests"""
    print("="*60)
    print("LoRA Merger Test Suite")
    print("="*60)
    print()
    
    tests = [
        ("Dependencies", check_dependencies),
        ("SafeTensors Operations", test_safetensors_operations),
        ("Output Directory Operations", test_output_directory),
        ("LoRA Utilities", test_lora_utils),
        ("Model Detection", test_model_detection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running {test_name} test...")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✓ {test_name} test PASSED")
                passed += 1
            else:
                print(f"✗ {test_name} test FAILED")
        except Exception as e:
            print(f"✗ {test_name} test FAILED with exception: {e}")
        
        print()
    
    print("="*60)
    print(f"Test Results: {passed}/{total} tests passed")
    print("="*60)
    
    if passed == total:
        print("🎉 All tests passed! LoRA Merger should work correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
