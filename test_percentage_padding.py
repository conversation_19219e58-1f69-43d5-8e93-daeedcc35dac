#!/usr/bin/env python
"""
Test the new percentage padding feature.
This demonstrates how percentage padding works compared to pixel padding.
"""

def demonstrate_percentage_padding():
    print("=== PERCENTAGE PADDING FEATURE DEMONSTRATION ===")
    print()
    
    # Example scenario
    image_width, image_height = 1000, 1200
    face_x, face_y, face_w, face_h = 300, 400, 200, 200
    face_center_x = face_x + face_w // 2  # 400
    face_center_y = face_y + face_h // 2  # 500
    
    print(f"Example Image: {image_width}x{image_height}")
    print(f"Face: {face_w}x{face_h} at ({face_x}, {face_y})")
    print(f"Face Center: ({face_center_x}, {face_center_y})")
    print()
    
    # Calculate available space in each direction
    available_left = face_center_x  # 400
    available_right = image_width - face_center_x  # 600
    available_top = face_center_y  # 500
    available_bottom = image_height - face_center_y  # 700
    
    print("Available space from face center:")
    print(f"  Left: {available_left}px")
    print(f"  Right: {available_right}px")
    print(f"  Top: {available_top}px")
    print(f"  Bottom: {available_bottom}px")
    print()
    
    # Test different padding percentages and sides
    test_cases = [
        ("Bottom", 50, available_bottom),
        ("Top", 30, available_top),
        ("Left", 25, available_left),
        ("Right", 40, available_right),
        ("Top and Bottom", 20, min(available_top, available_bottom)),
        ("Left and Right", 35, min(available_left, available_right))
    ]
    
    print("PERCENTAGE PADDING CALCULATIONS:")
    print("=" * 50)
    
    for side, percent, available_space in test_cases:
        padding_pixels = int((percent / 100.0) * available_space)
        
        print(f"{side} - {percent}%:")
        print(f"  Available space: {available_space}px")
        print(f"  {percent}% of {available_space}px = {padding_pixels}px")
        
        # Show what this means for cropping
        if side == "Bottom":
            crop_bottom = face_center_y + padding_pixels
            print(f"  Crop would extend to y={crop_bottom} (vs image height {image_height})")
        elif side == "Top":
            crop_top = face_center_y - padding_pixels
            print(f"  Crop would start at y={crop_top}")
        elif side == "Left":
            crop_left = face_center_x - padding_pixels
            print(f"  Crop would start at x={crop_left}")
        elif side == "Right":
            crop_right = face_center_x + padding_pixels
            print(f"  Crop would extend to x={crop_right} (vs image width {image_width})")
        
        print()
    
    print("COMPARISON WITH PIXEL PADDING:")
    print("=" * 40)
    print("Fixed 100px padding on bottom:")
    print(f"  Always adds 100px regardless of image size")
    print(f"  On 1000px image: 100px = 10% of available space")
    print(f"  On 2000px image: 100px = 5% of available space")
    print()
    print("50% padding on bottom:")
    print(f"  On 1000px image: 50% of 700px = 350px")
    print(f"  On 2000px image: 50% of 1200px = 600px")
    print(f"  Scales proportionally with image size!")
    print()
    
    print("BENEFITS OF PERCENTAGE PADDING:")
    print("✓ Consistent relative spacing across different image sizes")
    print("✓ More intuitive for artistic composition")
    print("✓ Automatically adapts to available space")
    print("✓ Prevents excessive padding that exceeds image bounds")

if __name__ == "__main__":
    demonstrate_percentage_padding()
