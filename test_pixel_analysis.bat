@echo off
setlocal enabledelayedexpansion

echo FramePack Pixel Analyzer
echo ========================
echo.
echo This tool analyzes images for black and transparent pixels
echo and shows whether they would be filtered by FramePack.
echo.

REM Check if any files were dropped
if "%~1"=="" (
    echo No files provided!
    echo.
    echo Usage: Drag and drop image files onto this batch file
    echo Or run: test_pixel_analysis.bat "path\to\image.png"
    echo.
    pause
    exit /b 1
)

REM Get the directory where this batch file is located (FramePack directory)
set "FRAMEPACK_DIR=%~dp0"
echo FramePack directory: %FRAMEPACK_DIR%

REM Change to the FramePack directory
cd /d "%FRAMEPACK_DIR%"

REM Activate the virtual environment
echo Activating FramePack virtual environment...
if exist "venv\Scripts\activate.bat" (
    call "venv\Scripts\activate.bat"
    echo Virtual environment activated.
) else (
    echo Warning: Virtual environment not found. Using system Python.
)
echo.

REM Run the Python script with all provided arguments
echo Running pixel analysis...
echo.
python test_pixel_analysis.py %*

REM Deactivate virtual environment if it was activated
if exist "venv\Scripts\activate.bat" (
    deactivate
)

echo.
echo Analysis complete!
pause
