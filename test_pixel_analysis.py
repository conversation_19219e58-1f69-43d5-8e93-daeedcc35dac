#!/usr/bin/env python3
"""
Test script to analyze black and transparent pixels in images.
Can be used from command line or drag-and-drop via batch file.
"""

import sys
import os
from pathlib import Path
from PIL import Image
import numpy as np

def analyze_image_pixels(image_path, threshold=0.1):
    """Analyze black and transparent pixels in an image"""
    try:
        print(f"\nAnalyzing: {image_path}")
        print("=" * 60)
        
        with Image.open(image_path) as img:
            print(f"Image size: {img.size[0]}x{img.size[1]} pixels")
            print(f"Image mode: {img.mode}")
            
            # Convert to RGBA to handle transparency
            img_rgba = img.convert('RGBA')
            img_array = np.array(img_rgba)
            
            total_pixels = img_array.shape[0] * img_array.shape[1]
            print(f"Total pixels: {total_pixels:,}")
            
            # Check for transparent pixels (alpha channel = 0)
            transparent_pixels = np.sum(img_array[:, :, 3] == 0)
            transparent_percentage = transparent_pixels / total_pixels
            
            # Check for black pixels (RGB all close to 0, but not transparent)
            # Consider pixels black if all RGB values are <= 10 (to account for slight variations)
            rgb_array = img_array[:, :, :3]
            black_pixels = np.sum(
                (np.all(rgb_array <= 10, axis=2)) & (img_array[:, :, 3] > 0)
            )
            black_percentage = black_pixels / total_pixels
            
            # Total problematic pixels
            problem_pixels = transparent_pixels + black_pixels
            total_percentage = problem_pixels / total_pixels
            
            print(f"\nPixel Analysis:")
            print(f"  Transparent pixels: {transparent_pixels:,} ({transparent_percentage:.2%})")
            print(f"  Black pixels:       {black_pixels:,} ({black_percentage:.2%})")
            print(f"  Total problematic:  {problem_pixels:,} ({total_percentage:.2%})")
            
            print(f"\nThreshold Analysis (>{threshold:.1%}):")
            if total_percentage > threshold:
                print(f"  ❌ WOULD BE SKIPPED - {total_percentage:.2%} > {threshold:.1%}")
                print(f"     This image has too many black/transparent pixels")
            else:
                print(f"  ✅ WOULD BE PROCESSED - {total_percentage:.2%} <= {threshold:.1%}")
                print(f"     This image passes the filter")
            
            # Show some sample pixel values for debugging
            print(f"\nSample pixel analysis (first 10 pixels):")
            for i in range(min(10, img_array.shape[0])):
                for j in range(min(10, img_array.shape[1])):
                    r, g, b, a = img_array[i, j]
                    is_transparent = a == 0
                    is_black = (r <= 10 and g <= 10 and b <= 10 and a > 0)
                    status = "TRANSPARENT" if is_transparent else "BLACK" if is_black else "NORMAL"
                    if status != "NORMAL":
                        print(f"    Pixel ({i},{j}): RGBA({r},{g},{b},{a}) - {status}")
                        break
                if status != "NORMAL":
                    break
            
            return total_percentage
            
    except Exception as e:
        print(f"❌ Error analyzing {image_path}: {e}")
        return None

def main():
    """Main function"""
    print("FramePack Pixel Analyzer")
    print("=" * 60)
    
    if len(sys.argv) < 2:
        print("Usage: python test_pixel_analysis.py <image_file> [image_file2] ...")
        print("Or drag and drop image files onto the batch file.")
        input("\nPress Enter to exit...")
        return
    
    # Process all provided image files
    for i, image_path in enumerate(sys.argv[1:], 1):
        path = Path(image_path)
        
        if not path.exists():
            print(f"\n❌ File not found: {image_path}")
            continue
            
        if not path.is_file():
            print(f"\n❌ Not a file: {image_path}")
            continue
            
        # Check if it's an image file
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.webp', '.gif', '.tiff', '.tif']
        if path.suffix.lower() not in image_extensions:
            print(f"\n❌ Not a supported image file: {image_path}")
            print(f"   Supported extensions: {', '.join(image_extensions)}")
            continue
        
        # Analyze the image
        percentage = analyze_image_pixels(path)
        
        if i < len(sys.argv) - 1:
            print("\n" + "=" * 60)
    
    print(f"\nAnalysis complete!")
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
