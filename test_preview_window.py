#!/usr/bin/env python3
"""
Test script for the FramePack Preview Window
"""

import tkinter as tk
from framepack_preview_window import FramePackPreviewWindow

def test_preview_window():
    """Test the preview window"""
    print("Testing FramePack Preview Window...")
    
    root = tk.Tk()
    
    try:
        app = FramePackPreviewWindow(root)
        print("Preview window created successfully!")
        
        # Run for a short time then close
        def close_after_test():
            print("Test completed - closing window")
            root.destroy()
        
        # Close after 3 seconds for testing
        root.after(3000, close_after_test)
        
        root.mainloop()
        
    except Exception as e:
        print(f"Error creating preview window: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            root.destroy()
        except:
            pass

if __name__ == "__main__":
    test_preview_window()
