#!/usr/bin/env python
"""
Test script to verify that the processed_files.txt file is being updated correctly.
This script tests that:
1. The processed_files.txt file is created if it doesn't exist
2. The file path is correctly appended to the processed_files.txt file
3. The file path can be verified in the processed_files.txt file
"""

import os
import sys
import shutil
import tempfile
from pathlib import Path
import traceback

# Import the update_processed_files_txt function from auto_sorter.py
from auto_sorter import update_processed_files_txt, auto_sort_after_generation

def test_update_processed_files_txt():
    """Test the update_processed_files_txt function"""
    print("\n=== Testing update_processed_files_txt function ===")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Created temporary directory: {temp_dir}")
        
        # Test file path
        test_file_path = os.path.join(temp_dir, "test_file.mp4")
        
        # Create a dummy test file
        with open(test_file_path, 'w') as f:
            f.write("Test file content")
        
        print(f"Created test file: {test_file_path}")
        
        # Test updating the processed_files.txt file
        result = update_processed_files_txt(test_file_path, temp_dir, debug=True)
        
        # Check if the processed_files.txt file was created
        processed_files_path = os.path.join(temp_dir, "processed_files.txt")
        if os.path.exists(processed_files_path):
            print(f"✅ processed_files.txt file was created at {processed_files_path}")
        else:
            print(f"❌ processed_files.txt file was not created")
            return False
        
        # Check if the file path was added to the processed_files.txt file
        with open(processed_files_path, 'r') as f:
            content = f.read()
            if test_file_path in content:
                print(f"✅ File path was successfully added to processed_files.txt")
            else:
                print(f"❌ File path was not found in processed_files.txt")
                return False
        
        # Test the function's return value
        if result:
            print(f"✅ update_processed_files_txt function returned True")
        else:
            print(f"❌ update_processed_files_txt function returned False")
            return False
        
        return True

def test_auto_sort_after_generation():
    """Test the auto_sort_after_generation function with input_dir and original_input_file parameters"""
    print("\n=== Testing auto_sort_after_generation function ===")
    
    # Create a temporary directory structure for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Created temporary directory: {temp_dir}")
        
        # Create input and output directories
        input_dir = os.path.join(temp_dir, "input")
        output_dir = os.path.join(temp_dir, "output")
        os.makedirs(input_dir, exist_ok=True)
        os.makedirs(output_dir, exist_ok=True)
        
        # Create a test input file
        input_file_path = os.path.join(input_dir, "test_input.mp4")
        with open(input_file_path, 'w') as f:
            f.write("Test input file content")
        
        # Create a test output file
        output_file_path = os.path.join(output_dir, "test_output.mp4")
        with open(output_file_path, 'w') as f:
            f.write("Test output file content")
        
        print(f"Created test input file: {input_file_path}")
        print(f"Created test output file: {output_file_path}")
        
        # Call auto_sort_after_generation with the input_dir and original_input_file parameters
        try:
            auto_sort_after_generation(
                outputs_folder=output_dir,
                recent_file=os.path.basename(output_file_path),
                input_dir=input_dir,
                original_input_file=input_file_path
            )
            
            # Check if the processed_files.txt file was created
            processed_files_path = os.path.join(input_dir, "processed_files.txt")
            if os.path.exists(processed_files_path):
                print(f"✅ processed_files.txt file was created at {processed_files_path}")
            else:
                print(f"❌ processed_files.txt file was not created")
                return False
            
            # Check if the file path was added to the processed_files.txt file
            with open(processed_files_path, 'r') as f:
                content = f.read()
                if input_file_path in content:
                    print(f"✅ File path was successfully added to processed_files.txt")
                else:
                    print(f"❌ File path was not found in processed_files.txt")
                    return False
            
            return True
        
        except Exception as e:
            print(f"❌ Error during auto_sort_after_generation: {e}")
            traceback.print_exc()
            return False

if __name__ == "__main__":
    # Run the tests
    update_result = test_update_processed_files_txt()
    auto_sort_result = test_auto_sort_after_generation()
    
    # Print the overall test results
    print("\n=== Test Results ===")
    print(f"update_processed_files_txt: {'✅ PASSED' if update_result else '❌ FAILED'}")
    print(f"auto_sort_after_generation: {'✅ PASSED' if auto_sort_result else '❌ FAILED'}")
    
    # Exit with appropriate status code
    sys.exit(0 if update_result and auto_sort_result else 1)
