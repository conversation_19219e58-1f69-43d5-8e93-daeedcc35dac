#!/usr/bin/env python3
"""
Test script to verify that prompt chains are fully compatible with LoRA support.
This script tests the data structures and functions to ensure LoRA settings
are properly captured, stored, and applied in prompt chains.
"""

import json
import sys
import os

def test_prompt_chain_lora_data_structure():
    """Test that prompt chain data structure includes all LoRA settings"""
    
    print("Testing prompt chain LoRA data structure...")
    
    # Simulate the settings that get_current_generation_settings() should capture
    mock_settings = {
        "seed": 12345,
        "video_length": 15.0,
        "steps": 50,
        "cfg": 2.0,
        "distilled_cfg": 8.0,
        "model_type": "FramePack",
        "custom_model": "",
        "latent_window_size": 32,
        "use_teacache": False,
        "gpu_memory": 24.0,
        "mp4_crf": 16,
        "negative_prompt": "blurry, low quality",
        "use_image_prompt": True,
        "use_noise": False,
        "pixel_trick": False,
        "overwrite": True,
        "fix_encoding": True,
        "copy_to_input": False,
        # LoRA settings (supports up to 3 LoRAs)
        "lora_file_1": "path/to/style.safetensors",
        "lora_multiplier_1": 0.8,
        "lora_file_2": "path/to/character.safetensors",
        "lora_multiplier_2": 0.6,
        "lora_file_3": "path/to/lighting.safetensors",
        "lora_multiplier_3": 0.4,
        "fp8_optimization": True,
        "append_lora_keywords": True,
        # Prompt chain settings
        "f1_chain_use_video": False,
        # Backward compatibility
        "lora_file": "path/to/style.safetensors",
        "lora_multiplier": 0.8
    }
    
    # Create a prompt chain step with LoRA settings
    chain_step = {
        "prompt": "A beautiful landscape with mountains and rivers",
        "settings": mock_settings
    }
    
    # Test that all LoRA settings are present
    lora_settings_keys = [
        "lora_file_1", "lora_multiplier_1",
        "lora_file_2", "lora_multiplier_2", 
        "lora_file_3", "lora_multiplier_3",
        "fp8_optimization", "append_lora_keywords",
        "lora_file", "lora_multiplier"  # Backward compatibility
    ]
    
    missing_keys = []
    for key in lora_settings_keys:
        if key not in chain_step["settings"]:
            missing_keys.append(key)
    
    if missing_keys:
        print(f"❌ FAIL: Missing LoRA settings keys: {missing_keys}")
        return False
    else:
        print("✅ PASS: All LoRA settings keys present in prompt chain step")
        return True

def test_prompt_chain_serialization():
    """Test that prompt chain with LoRA settings can be serialized/deserialized"""
    
    print("\nTesting prompt chain serialization with LoRA settings...")
    
    # Create a prompt chain with multiple steps, each with different LoRA settings
    prompt_chain = [
        {
            "prompt": "A cyberpunk cityscape at night",
            "settings": {
                "seed": 123,
                "video_length": 10.0,
                "model_type": "FramePack",
                "lora_file_1": "cyberpunk_style.safetensors",
                "lora_multiplier_1": 0.9,
                "lora_file_2": "",
                "lora_multiplier_2": 0.8,
                "lora_file_3": "",
                "lora_multiplier_3": 0.8,
                "fp8_optimization": True,
                "append_lora_keywords": True
            }
        },
        {
            "prompt": "The same cityscape transitioning to dawn",
            "settings": {
                "seed": 456,
                "video_length": 12.0,
                "model_type": "F1",
                "lora_file_1": "lighting_effects.safetensors",
                "lora_multiplier_1": 0.7,
                "lora_file_2": "time_transition.safetensors",
                "lora_multiplier_2": 0.5,
                "lora_file_3": "",
                "lora_multiplier_3": 0.8,
                "fp8_optimization": False,
                "append_lora_keywords": False
            }
        },
        {
            "prompt": "Bright daylight revealing the city details",
            "settings": {
                "seed": 789,
                "video_length": 8.0,
                "model_type": "FramePack",
                "lora_file_1": "detail_enhancer.safetensors",
                "lora_multiplier_1": 0.6,
                "lora_file_2": "architectural_style.safetensors",
                "lora_multiplier_2": 0.8,
                "lora_file_3": "color_grading.safetensors",
                "lora_multiplier_3": 0.4,
                "fp8_optimization": True,
                "append_lora_keywords": True
            }
        }
    ]
    
    try:
        # Test JSON serialization
        json_str = json.dumps(prompt_chain, indent=2)
        
        # Test JSON deserialization
        deserialized_chain = json.loads(json_str)
        
        # Verify the data integrity
        if len(deserialized_chain) != len(prompt_chain):
            print("❌ FAIL: Chain length mismatch after serialization")
            return False
        
        for i, (original, deserialized) in enumerate(zip(prompt_chain, deserialized_chain)):
            if original["prompt"] != deserialized["prompt"]:
                print(f"❌ FAIL: Prompt mismatch in step {i+1}")
                return False
            
            # Check LoRA settings specifically
            lora_keys = ["lora_file_1", "lora_multiplier_1", "lora_file_2", "lora_multiplier_2", 
                        "lora_file_3", "lora_multiplier_3", "fp8_optimization", "append_lora_keywords"]
            
            for key in lora_keys:
                if original["settings"].get(key) != deserialized["settings"].get(key):
                    print(f"❌ FAIL: LoRA setting '{key}' mismatch in step {i+1}")
                    return False
        
        print("✅ PASS: Prompt chain with LoRA settings serializes/deserializes correctly")
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Serialization error: {e}")
        return False

def test_backward_compatibility():
    """Test backward compatibility with old LoRA format"""
    
    print("\nTesting backward compatibility with old LoRA format...")
    
    # Test old format (single LoRA)
    old_format_settings = {
        "seed": 123,
        "video_length": 10.0,
        "lora_file": "old_style.safetensors",
        "lora_multiplier": 0.7
    }
    
    # Test new format (multiple LoRAs)
    new_format_settings = {
        "seed": 123,
        "video_length": 10.0,
        "lora_file_1": "new_style.safetensors",
        "lora_multiplier_1": 0.8,
        "lora_file_2": "character.safetensors",
        "lora_multiplier_2": 0.6,
        "lora_file_3": "",
        "lora_multiplier_3": 0.8,
        # Should also include backward compatibility
        "lora_file": "new_style.safetensors",
        "lora_multiplier": 0.8
    }
    
    # Both formats should be valid
    try:
        json.dumps(old_format_settings)
        json.dumps(new_format_settings)
        print("✅ PASS: Both old and new LoRA formats are serializable")
        return True
    except Exception as e:
        print(f"❌ FAIL: Backward compatibility error: {e}")
        return False

def main():
    """Run all compatibility tests"""
    
    print("🧪 Testing Prompt Chain LoRA Compatibility")
    print("=" * 50)
    
    tests = [
        test_prompt_chain_lora_data_structure,
        test_prompt_chain_serialization,
        test_backward_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 SUCCESS: Prompt chains are fully compatible with LoRA support!")
        return 0
    else:
        print("❌ FAILURE: Some compatibility issues found")
        return 1

if __name__ == "__main__":
    sys.exit(main())
