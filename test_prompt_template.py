#!/usr/bin/env python
"""
test_prompt_template.py

This script tests the DEFAULT_PROMPT_TEMPLATE to understand how it formats prompts.
"""

from diffusers.pipelines.hunyuan_video.pipeline_hunyuan_video import DEFAULT_PROMPT_TEMPLATE

def test_prompt_template():
    """Test the DEFAULT_PROMPT_TEMPLATE with various prompts"""
    print("DEFAULT_PROMPT_TEMPLATE:")
    print(DEFAULT_PROMPT_TEMPLATE)
    print("\nTemplate:")
    print(DEFAULT_PROMPT_TEMPLATE["template"])
    print("\nCrop Start:")
    print(DEFAULT_PROMPT_TEMPLATE["crop_start"])
    
    # Test prompts
    test_prompts = [
        "A beautiful sunset over the ocean",
        "A person dancing",
        "",  # Empty prompt
        "特殊字符和非英语文本测试",  # Non-English text
        "A" * 100,  # Long prompt
    ]
    
    # Test each prompt
    for i, prompt in enumerate(test_prompts):
        print(f"\nTest {i+1}: Formatting prompt: {prompt}")
        formatted_prompt = DEFAULT_PROMPT_TEMPLATE["template"].format(prompt)
        print(f"Formatted prompt (length: {len(formatted_prompt)}):")
        print(formatted_prompt)

if __name__ == "__main__":
    test_prompt_template()
