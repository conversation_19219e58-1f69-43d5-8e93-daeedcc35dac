#!/usr/bin/env python3

import tkinter as tk
from tkinter import ttk

print("Testing pyvidplayer2 import...")

try:
    from pyvidplayer2 import VideoTkinter
    print("✅ pyvidplayer2 imported successfully!")
    AVAILABLE = True
except ImportError as e:
    print(f"❌ ImportError: {e}")
    AVAILABLE = False
except Exception as e:
    print(f"❌ Error: {e}")
    AVAILABLE = False

print(f"pyvidplayer2 available: {AVAILABLE}")

if AVAILABLE:
    print("Creating test window...")
    root = tk.Tk()
    root.title("pyvidplayer2 Test")
    root.geometry("400x300")
    
    label = ttk.Label(root, text="pyvidplayer2 is working!")
    label.pack(pady=50)
    
    print("Test window created successfully!")
    root.mainloop()
else:
    print("Cannot test - pyvidplayer2 not available")
