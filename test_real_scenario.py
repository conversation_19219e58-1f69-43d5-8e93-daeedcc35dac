#!/usr/bin/env python
"""
Test the fix with a scenario similar to the user's original problem.
This simulates the case where a face already fills a large portion of the image.
"""

import os
from PIL import Image, ImageDraw

def create_realistic_test_image():
    """Create a test image similar to the user's example: 960x958 with a large face"""
    # Create a 960x958 image (matching user's example)
    img = Image.new('RGB', (960, 958), color='lightgray')
    draw = ImageDraw.Draw(img)
    
    # Create a large face that fills most of the image (similar to user's 610x610 face)
    # Position it at (235, 0) like in the user's example
    face_x, face_y = 235, 0
    face_width, face_height = 610, 610
    
    # Draw face background
    draw.rectangle([face_x, face_y, face_x + face_width, face_y + face_height], 
                  fill='peachpuff', outline='brown', width=2)
    
    # Add facial features for better detection
    # Eyes
    eye_size = 50
    left_eye_x = face_x + face_width // 3
    right_eye_x = face_x + 2 * face_width // 3
    eye_y = face_y + face_height // 3
    
    draw.ellipse([left_eye_x - eye_size//2, eye_y - eye_size//2, 
                 left_eye_x + eye_size//2, eye_y + eye_size//2], fill='white', outline='black')
    draw.ellipse([left_eye_x - 15, eye_y - 15, left_eye_x + 15, eye_y + 15], fill='black')
    
    draw.ellipse([right_eye_x - eye_size//2, eye_y - eye_size//2, 
                 right_eye_x + eye_size//2, eye_y + eye_size//2], fill='white', outline='black')
    draw.ellipse([right_eye_x - 15, eye_y - 15, right_eye_x + 15, eye_y + 15], fill='black')
    
    # Nose
    nose_x = face_x + face_width // 2
    nose_y = face_y + face_height // 2
    draw.polygon([(nose_x, nose_y - 20), (nose_x - 15, nose_y + 20), (nose_x + 15, nose_y + 20)], 
                fill='rosybrown', outline='brown')
    
    # Mouth
    mouth_y = face_y + 2 * face_height // 3
    mouth_width = face_width // 4
    draw.ellipse([nose_x - mouth_width//2, mouth_y - 15, 
                 nose_x + mouth_width//2, mouth_y + 15], fill='red', outline='darkred')
    
    return img

def test_user_scenario():
    """Test the specific scenario from the user's example"""
    print("Testing scenario similar to user's original problem...")
    print("Original issue: 960x958 image with 610x610 face, 25% fill resulted in 169x201 output")
    print("Expected fix: Output should never be smaller than the detected face size")
    print()
    
    # Create test image
    test_img = create_realistic_test_image()
    test_path = "realistic_test.jpg"
    test_img.save(test_path)
    
    print(f"Created test image: {test_path}")
    print(f"Image size: {test_img.size}")
    print("Large face positioned at (235, 0) with size ~610x610")
    print()
    
    # Import the crop function
    from image_face_cropper import crop_face
    
    # Test with 25% fill (the problematic case from user's example)
    print("=== Testing with 25% fill percentage (user's problematic case) ===")
    
    output_path = "realistic_test_25percent.jpg"
    
    try:
        result = crop_face(
            input_path=test_path,
            output_path=output_path,
            strength=5,
            output_size=512,  # Using 512 instead of 608 to match test app
            fill_percentage=25,
            padding_pixels=96,  # User had 96 pixels padding
            padding_side="bottom",  # User had bottom padding
            skip_multiple_faces=False,
            no_crop_multiple_faces=False
        )
        
        if result and isinstance(result, dict) and result["status"] == "single_face_cropped":
            # Check the output image size
            output_img = Image.open(output_path)
            print(f"✓ Output image size: {output_img.size}")
            
            # The key test: output should not be tiny like the original 169x201
            min_output_size = min(output_img.size)
            if min_output_size > 300:  # Much larger than the problematic 169x201
                print(f"✓ SUCCESS: Output size ({min_output_size}) is much larger than the problematic 169x201")
                print("✓ Fix is working - face size is preserved!")
            else:
                print(f"✗ PROBLEM: Output size ({min_output_size}) is still too small")
            
            print(f"✓ Saved result to: {output_path}")
        else:
            print(f"✗ Cropping failed or no face detected")
            
    except Exception as e:
        print(f"✗ Error during cropping: {e}")
    
    # Clean up
    try:
        os.remove(test_path)
        if os.path.exists(output_path):
            os.remove(output_path)
        print("\nCleaned up test files")
    except:
        pass

if __name__ == "__main__":
    test_user_scenario()
