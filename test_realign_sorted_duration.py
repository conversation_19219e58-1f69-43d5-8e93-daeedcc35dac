#!/usr/bin/env python
"""
Test script to verify that the framepack_realign_sorted.py script works correctly
with video duration as the selection criterion.
"""

import os
import sys
import shutil
import tempfile
import subprocess
import time

def create_test_mp4(file_path, duration_seconds, size_bytes=None):
    """
    Create a test MP4 file with the specified duration.
    
    Args:
        file_path: Path to the output file
        duration_seconds: Duration of the video in seconds
        size_bytes: Optional size of the file in bytes (if not specified, will be proportional to duration)
    """
    # Create a directory for the file if it doesn't exist
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # If size_bytes is not specified, make it proportional to duration
    if size_bytes is None:
        size_bytes = int(duration_seconds * 1000)
    
    # Create a temporary text file with the specified size
    temp_txt = file_path + ".txt"
    with open(temp_txt, 'wb') as f:
        f.write(b'X' * size_bytes)
    
    # Use FFmpeg to create an MP4 file with the specified duration
    cmd = [
        'ffmpeg', '-y', '-f', 'lavfi', '-i', f'color=c=black:s=320x240:d={duration_seconds}',
        '-c:v', 'libx264', '-tune', 'stillimage', '-pix_fmt', 'yuv420p',
        file_path
    ]
    
    try:
        subprocess.run(cmd, check=True, capture_output=True)
        print(f"Created {os.path.basename(file_path)} with duration {duration_seconds} seconds")
        
        # Verify the file was created
        if not os.path.exists(file_path):
            print(f"Error: Failed to create {file_path}")
            return False
        
        # Clean up the temporary text file
        os.remove(temp_txt)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error creating {file_path}: {e}")
        print(f"FFmpeg stderr: {e.stderr.decode('utf-8')}")
        return False

def test_realign_sorted_duration():
    """Test that the realignment script works correctly with video duration."""
    print("\n=== Testing realignment script with video duration ===")
    
    # Check if FFmpeg is available
    try:
        subprocess.run(['ffmpeg', '-version'], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Error: FFmpeg is not available. Please install FFmpeg and make sure it's in your PATH.")
        return
    
    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create outputs directory
        outputs_dir = os.path.join(temp_dir, "outputs")
        os.makedirs(outputs_dir, exist_ok=True)
        
        # Create sorted directory
        sorted_dir = os.path.join(outputs_dir, "sorted")
        os.makedirs(sorted_dir, exist_ok=True)
        
        # Create test files in outputs directory with different durations and sizes
        # Group 1: Files with different durations but similar sizes
        create_test_mp4(os.path.join(outputs_dir, "250420_121919_242_3623_37.mp4"), 3.0, 5000)  # 3 seconds, 5KB
        create_test_mp4(os.path.join(outputs_dir, "250420_121919_242_3623_38_4s.mp4"), 4.0, 3000)  # 4 seconds, 3KB (longest but smaller)
        create_test_mp4(os.path.join(outputs_dir, "250420_121919_242_3623_39.mp4"), 2.0, 4000)  # 2 seconds, 4KB
        
        # Group 2: Files with different durations and sizes
        create_test_mp4(os.path.join(outputs_dir, "250421_121919_242_3623_37_seed123456.mp4"), 2.0, 6000)  # 2 seconds, 6KB
        create_test_mp4(os.path.join(outputs_dir, "250421_121919_242_3623_38_seed123456_5s.mp4"), 5.0, 2000)  # 5 seconds, 2KB (longest but smallest)
        create_test_mp4(os.path.join(outputs_dir, "250421_121919_242_3623_39_seed123456.mp4"), 3.0, 4000)  # 3 seconds, 4KB
        
        # Create test files in sorted directory with incorrect files
        create_test_mp4(os.path.join(sorted_dir, "250420_121919_242_3623_37.mp4"), 3.0, 5000)  # Not the longest
        create_test_mp4(os.path.join(sorted_dir, "250421_121919_242_3623_37_seed123456.mp4"), 2.0, 6000)  # Not the longest
        
        # Run the realignment script with duration as the criterion
        print("\nRunning realignment script with duration as the criterion:")
        cmd = [sys.executable, "framepack_realign_sorted.py", "--outputs-dir", outputs_dir, "--clean"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        print(result.stdout)
        
        # Check the sorted directory after realignment
        print("\nChecking sorted directory after realignment:")
        sorted_files_after = os.listdir(sorted_dir)
        print(f"Files in sorted directory: {len(sorted_files_after)}")
        
        # Expected files (longest from each group)
        expected_files = [
            "250420_121919_242_3623_38_4s.mp4",  # Longest from Group 1 (4 seconds)
            "250421_121919_242_3623_38_seed123456_5s.mp4",  # Longest from Group 2 (5 seconds)
        ]
        
        # Check if we have the expected files
        for expected_file in expected_files:
            if expected_file in sorted_files_after:
                print(f"✅ Found expected file in sorted directory: {expected_file}")
            else:
                print(f"❌ Missing expected file in sorted directory: {expected_file}")
        
        # Check if any unexpected files are present
        for sorted_file in sorted_files_after:
            if sorted_file not in expected_files:
                print(f"❌ Unexpected file in sorted directory: {sorted_file}")
        
        # Run the realignment script with size as the criterion
        print("\nRunning realignment script with size as the criterion:")
        cmd = [sys.executable, "framepack_realign_sorted.py", "--outputs-dir", outputs_dir, "--clean", "--use-size"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        print(result.stdout)
        
        # Check the sorted directory after realignment with size
        print("\nChecking sorted directory after realignment with size:")
        sorted_files_after_size = os.listdir(sorted_dir)
        print(f"Files in sorted directory: {len(sorted_files_after_size)}")
        
        # Expected files after size criterion (largest from each group)
        expected_files_after_size = [
            "250420_121919_242_3623_37.mp4",  # Largest from Group 1 (5KB)
            "250421_121919_242_3623_37_seed123456.mp4",  # Largest from Group 2 (6KB)
        ]
        
        # Check if we have the expected files
        for expected_file in expected_files_after_size:
            if expected_file in sorted_files_after_size:
                print(f"✅ Found expected file in sorted directory: {expected_file}")
            else:
                print(f"❌ Missing expected file in sorted directory: {expected_file}")
        
        # Check if any unexpected files are present
        for sorted_file in sorted_files_after_size:
            if sorted_file not in expected_files_after_size:
                print(f"❌ Unexpected file in sorted directory: {sorted_file}")

if __name__ == "__main__":
    test_realign_sorted_duration()
    
    print("\nTest completed.")
