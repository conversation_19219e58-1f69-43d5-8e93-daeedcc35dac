#!/usr/bin/env python
"""
Simple test to verify the image cropping functionality works.
"""

import os
import sys

def test_import():
    """Test if we can import the image_face_cropper module"""
    print("Testing image_face_cropper import...")
    
    # Try multiple possible locations for the image_face_cropper module
    import importlib.util
    
    possible_paths = [
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "image_face_cropper.py"),
        os.path.join(os.getcwd(), "image_face_cropper.py"),
        os.path.join(os.path.dirname(__file__), "image_face_cropper.py"),
        "image_face_cropper.py"
    ]
    
    cropper_module = None
    successful_path = None
    
    # Try each path until we find the module
    for path in possible_paths:
        print(f"Trying path: {path}")
        if os.path.exists(path):
            try:
                spec = importlib.util.spec_from_file_location("image_face_cropper", path)
                cropper_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(cropper_module)
                successful_path = path
                print(f"✓ Successfully loaded from: {path}")
                break
            except Exception as e:
                print(f"✗ Failed to load from {path}: {e}")
                continue
        else:
            print(f"✗ File not found: {path}")
    
    if cropper_module is None:
        # Last resort: try direct import
        try:
            import image_face_cropper as cropper_module
            successful_path = "direct import"
            print(f"✓ Successfully loaded via direct import")
        except ImportError as e:
            print(f"✗ Direct import failed: {e}")
            return False
    
    # Test if the crop_face function exists
    if hasattr(cropper_module, 'crop_face'):
        print("✓ crop_face function found")
        return True
    else:
        print("✗ crop_face function not found")
        return False

def main():
    print("=== Simple Crop Test ===")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Script directory: {os.path.dirname(os.path.abspath(__file__))}")
    print()
    
    if test_import():
        print("\n✓ SUCCESS: Image cropper module loaded successfully!")
        print("The GUI app should work now.")
    else:
        print("\n✗ FAILED: Could not load image cropper module")
        print("Please check that image_face_cropper.py is in the same directory as this script.")
    
    print("\nPress Enter to continue...")
    input()

if __name__ == "__main__":
    main()
