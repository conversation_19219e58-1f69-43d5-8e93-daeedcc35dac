#!/usr/bin/env python3
"""
Test script to verify that auto crop temp files are preserved and not deleted.
This simulates the batch script behavior to ensure temp files remain in the temp folder.
"""

import os
import sys
import time
import glob
from PIL import Image
import numpy as np

def test_temp_file_preservation():
    """Test that auto crop temp files are preserved and not deleted"""
    
    print("Testing Auto Crop Temp File Preservation")
    print("=" * 50)
    
    # Test image path (use any image you have)
    test_image = "test_image.jpg"  # You'll need to provide a test image
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        print("Please provide a test image with a face to test temp file preservation")
        return False
    
    try:
        # Import the crop_face function
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        from image_face_cropper import crop_face
        
        # Create temp directory
        temp_dir_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
        os.makedirs(temp_dir_path, exist_ok=True)
        
        print(f"Using test image: {test_image}")
        print(f"Temp directory: {temp_dir_path}")
        print()
        
        # Count existing temp files before test
        existing_temp_files = glob.glob(os.path.join(temp_dir_path, "auto_crop_*.png"))
        print(f"Existing auto crop temp files: {len(existing_temp_files)}")
        
        # Simulate the batch script auto crop logic
        auto_crop_to_face = True
        auto_crop_fill_percentage = 60
        auto_crop_padding_pixels = 64
        auto_crop_padding_side = "top"
        skip_multiple_faces = False
        
        print(f"Simulating batch script auto crop:")
        print(f"  - Fill percentage: {auto_crop_fill_percentage}%")
        print(f"  - Padding: {auto_crop_padding_pixels}px on {auto_crop_padding_side}")
        print()
        
        if auto_crop_to_face:
            print(f"Auto crop to face enabled with {auto_crop_fill_percentage}% fill")
            
            # Generate a unique temporary filename (same as batch script)
            temp_filename = f"auto_crop_{int(time.time() * 1000)}.png"
            temp_cropped_path = os.path.join(temp_dir_path, temp_filename)
            
            print(f"Creating temp file: {temp_cropped_path}")
            
            # Crop the face directly from the original image path (same as batch script)
            result = crop_face(
                input_path=test_image,
                output_path=temp_cropped_path,
                strength=5,
                output_size=608,
                fill_percentage=auto_crop_fill_percentage,
                padding_pixels=auto_crop_padding_pixels,
                padding_side=auto_crop_padding_side,
                skip_multiple_faces=skip_multiple_faces
            )

            if result and os.path.exists(temp_cropped_path):
                # Load the cropped image (with padding applied) - same as batch script
                input_image = np.array(Image.open(temp_cropped_path).convert('RGB'))
                print(f"✅ Auto crop to face successful (with padding: {auto_crop_padding_pixels}px on {auto_crop_padding_side})")
                print(f"✅ Temp file preserved: {temp_cropped_path}")
                print(f"✅ Loaded cropped image with shape: {input_image.shape}")
                
                # Verify the file still exists (this is the key test)
                if os.path.exists(temp_cropped_path):
                    file_size = os.path.getsize(temp_cropped_path)
                    print(f"✅ Temp file confirmed to exist: {file_size} bytes")
                    
                    # Check if we can still read the file
                    try:
                        test_reload = np.array(Image.open(temp_cropped_path).convert('RGB'))
                        print(f"✅ Temp file can be reloaded: {test_reload.shape}")
                        return True
                    except Exception as e:
                        print(f"❌ Error reloading temp file: {e}")
                        return False
                else:
                    print(f"❌ Temp file was deleted unexpectedly!")
                    return False
            else:
                print(f"❌ Auto crop to face failed - no face detected")
                return False
        else:
            print("Auto crop is disabled")
            return True
            
    except Exception as e:
        print(f"❌ Error in temp file preservation test: {e}")
        import traceback
        traceback.print_exc()
        return False

def list_temp_files():
    """List all auto crop temp files in the temp directory"""
    
    print("\nListing Auto Crop Temp Files")
    print("=" * 50)
    
    temp_dir_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
    
    if not os.path.exists(temp_dir_path):
        print(f"Temp directory does not exist: {temp_dir_path}")
        return
    
    # Find all auto crop temp files
    auto_crop_files = glob.glob(os.path.join(temp_dir_path, "auto_crop_*.png"))
    
    print(f"Temp directory: {temp_dir_path}")
    print(f"Auto crop temp files found: {len(auto_crop_files)}")
    print()
    
    if auto_crop_files:
        for i, file_path in enumerate(auto_crop_files, 1):
            file_size = os.path.getsize(file_path)
            file_time = os.path.getmtime(file_path)
            time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_time))
            print(f"{i}. {os.path.basename(file_path)}")
            print(f"   Size: {file_size:,} bytes")
            print(f"   Modified: {time_str}")
            print()
    else:
        print("No auto crop temp files found.")

def simulate_batch_processing():
    """Simulate multiple batch processing runs to test temp file accumulation"""
    
    print("\nSimulating Multiple Batch Processing Runs")
    print("=" * 50)
    
    test_image = "test_image.jpg"
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        return False
    
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        from image_face_cropper import crop_face
        
        temp_dir_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
        os.makedirs(temp_dir_path, exist_ok=True)
        
        # Simulate 3 batch processing runs
        for run in range(1, 4):
            print(f"Batch run {run}:")
            
            # Generate unique temp filename for this run
            temp_filename = f"auto_crop_{int(time.time() * 1000)}_{run}.png"
            temp_cropped_path = os.path.join(temp_dir_path, temp_filename)
            
            # Crop with different padding for each run
            padding_pixels = 32 * run  # 32, 64, 96
            
            result = crop_face(
                input_path=test_image,
                output_path=temp_cropped_path,
                strength=5,
                output_size=608,
                fill_percentage=60,
                padding_pixels=padding_pixels,
                padding_side="top",
                skip_multiple_faces=False
            )
            
            if result and os.path.exists(temp_cropped_path):
                input_image = np.array(Image.open(temp_cropped_path).convert('RGB'))
                print(f"  ✅ Created: {os.path.basename(temp_cropped_path)} (padding: {padding_pixels}px)")
                print(f"  ✅ Shape: {input_image.shape}")
            else:
                print(f"  ❌ Failed to create temp file for run {run}")
                return False
            
            # Small delay to ensure unique timestamps
            time.sleep(0.1)
        
        # Count final temp files
        auto_crop_files = glob.glob(os.path.join(temp_dir_path, "auto_crop_*.png"))
        print(f"\nTotal auto crop temp files after simulation: {len(auto_crop_files)}")
        
        return len(auto_crop_files) >= 3
        
    except Exception as e:
        print(f"❌ Error in batch processing simulation: {e}")
        return False

if __name__ == "__main__":
    print("Auto Crop Temp File Preservation Test")
    print("=" * 60)
    print()
    
    # Test 1: Basic temp file preservation
    test1_result = test_temp_file_preservation()
    
    # Test 2: List existing temp files
    list_temp_files()
    
    # Test 3: Simulate multiple batch runs
    test3_result = simulate_batch_processing()
    
    # Final temp file listing
    list_temp_files()
    
    print()
    print("=" * 60)
    if test1_result and test3_result:
        print("🎉 ALL TESTS PASSED! Temp files are being preserved correctly.")
        print()
        print("✅ Auto crop temp files are not deleted after use")
        print("✅ Temp files remain accessible in the temp folder")
        print("✅ Multiple batch runs create multiple preserved temp files")
        print("✅ Temp files can be reloaded and inspected")
    else:
        print("❌ Some tests failed. Check the error messages above.")
        print()
        if not test1_result:
            print("❌ Basic temp file preservation failed")
        if not test3_result:
            print("❌ Multiple batch run simulation failed")
