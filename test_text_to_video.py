#!/usr/bin/env python3
"""
Test script for deterministic text-to-video generation in FramePack.

This script demonstrates how to use the new transparent PNG detection and 
deterministic seed control for pure text-to-video generation.

Usage:
    python test_text_to_video.py

The script will:
1. Create a transparent PNG image
2. Use it to trigger text-to-video mode
3. Generate a video using deterministic noise with a specific seed
"""

import os
import sys
import torch
import numpy as np
from PIL import Image
from pathlib import Path

# Add the current directory to the path so we can import FramePack modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_transparent_test_image(width=512, height=512, output_path="test_transparent.png"):
    """Create a fully transparent PNG image for testing text-to-video mode."""
    # Create a fully transparent RGBA image
    transparent_img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    transparent_img.save(output_path, format='PNG')
    print(f"Created transparent test image: {output_path}")
    return output_path

def test_transparent_detection():
    """Test the transparent PNG detection logic."""
    print("Testing transparent PNG detection...")
    
    # Create test images
    transparent_path = create_transparent_test_image(512, 512, "test_transparent.png")
    
    # Create a non-transparent image for comparison
    solid_img = Image.new('RGB', (512, 512), (255, 0, 0))  # Red image
    solid_path = "test_solid.png"
    solid_img.save(solid_path, format='PNG')
    print(f"Created solid test image: {solid_path}")
    
    # Test the detection logic
    from diffusers_helper.utils import load_start_latent_with_t2v_support
    
    # Mock parameters for testing
    device = "cpu"  # Use CPU for testing
    H, W = 512, 512
    T_frames = 1
    seed = 42
    
    print("\n--- Testing transparent image ---")
    try:
        # This should trigger text-to-video mode
        start_latent, is_text_to_video = load_start_latent_with_t2v_support(
            image_path=transparent_path,
            device=device,
            H=H,
            W=W,
            T_frames=T_frames,
            vae=None,  # We'll skip VAE for this test
            seed=seed
        )
        print(f"Transparent image result: is_text_to_video = {is_text_to_video}")
        if is_text_to_video:
            print(f"✅ Transparent PNG correctly detected as text-to-video mode")
            print(f"Generated noise latent shape: {start_latent.shape}")
        else:
            print(f"❌ Transparent PNG not detected as text-to-video mode")
    except Exception as e:
        print(f"Error testing transparent image: {e}")
    
    print("\n--- Testing solid image ---")
    try:
        # This should NOT trigger text-to-video mode (but will fail without VAE)
        start_latent, is_text_to_video = load_start_latent_with_t2v_support(
            image_path=solid_path,
            device=device,
            H=H,
            W=W,
            T_frames=T_frames,
            vae=None,  # We'll skip VAE for this test
            seed=seed
        )
        print(f"Solid image result: is_text_to_video = {is_text_to_video}")
        if not is_text_to_video:
            print(f"✅ Solid PNG correctly detected as image-to-video mode")
        else:
            print(f"❌ Solid PNG incorrectly detected as text-to-video mode")
    except Exception as e:
        print(f"Expected error for solid image (no VAE provided): {e}")
    
    print("\n--- Testing None image path ---")
    try:
        # This should trigger text-to-video mode
        start_latent, is_text_to_video = load_start_latent_with_t2v_support(
            image_path=None,
            device=device,
            H=H,
            W=W,
            T_frames=T_frames,
            vae=None,  # We'll skip VAE for this test
            seed=seed
        )
        print(f"None image path result: is_text_to_video = {is_text_to_video}")
        if is_text_to_video:
            print(f"✅ None image path correctly detected as text-to-video mode")
            print(f"Generated noise latent shape: {start_latent.shape}")
        else:
            print(f"❌ None image path not detected as text-to-video mode")
    except Exception as e:
        print(f"Error testing None image path: {e}")
    
    # Clean up test files
    try:
        os.remove(transparent_path)
        os.remove(solid_path)
        print(f"\nCleaned up test files")
    except Exception as e:
        print(f"Error cleaning up test files: {e}")

def test_deterministic_noise():
    """Test deterministic noise generation with different seeds."""
    print("\n" + "="*50)
    print("Testing deterministic noise generation...")
    
    device = "cpu"
    H, W = 512, 512
    T_frames = 1
    
    # Test with the same seed multiple times
    seed = 12345
    print(f"\nGenerating noise with seed {seed} (3 times):")
    
    noise_tensors = []
    for i in range(3):
        generator = torch.Generator(device=device).manual_seed(seed)
        noise = torch.randn(1, 4, T_frames, H//8, W//8, device=device, dtype=torch.float32, generator=generator)
        noise_tensors.append(noise)
        print(f"  Run {i+1}: First few values = {noise.flatten()[:5].tolist()}")
    
    # Check if all tensors are identical
    all_same = True
    for i in range(1, len(noise_tensors)):
        if not torch.allclose(noise_tensors[0], noise_tensors[i]):
            all_same = False
            break
    
    if all_same:
        print(f"✅ Deterministic noise generation working correctly - all runs identical")
    else:
        print(f"❌ Deterministic noise generation failed - runs differ")
    
    # Test with different seeds
    print(f"\nGenerating noise with different seeds:")
    seed1, seed2 = 12345, 54321
    
    generator1 = torch.Generator(device=device).manual_seed(seed1)
    noise1 = torch.randn(1, 4, T_frames, H//8, W//8, device=device, dtype=torch.float32, generator=generator1)
    
    generator2 = torch.Generator(device=device).manual_seed(seed2)
    noise2 = torch.randn(1, 4, T_frames, H//8, W//8, device=device, dtype=torch.float32, generator=generator2)
    
    print(f"  Seed {seed1}: First few values = {noise1.flatten()[:5].tolist()}")
    print(f"  Seed {seed2}: First few values = {noise2.flatten()[:5].tolist()}")
    
    if not torch.allclose(noise1, noise2):
        print(f"✅ Different seeds produce different noise - working correctly")
    else:
        print(f"❌ Different seeds produce identical noise - something is wrong")

def main():
    """Main test function."""
    print("FramePack Text-to-Video Test Script")
    print("="*50)
    
    # Test 1: Transparent PNG detection
    test_transparent_detection()
    
    # Test 2: Deterministic noise generation
    test_deterministic_noise()
    
    print("\n" + "="*50)
    print("Test Summary:")
    print("- Transparent PNG detection: Tests if fully transparent images trigger T2V mode")
    print("- Deterministic noise: Tests if the same seed produces identical noise")
    print("- Integration: Both features work together for reproducible T2V generation")
    print("\nTo use in practice:")
    print("1. Create a transparent PNG or use None as image_path")
    print("2. Set a specific seed value for reproducible results")
    print("3. The system will automatically use deterministic noise for T2V mode")

if __name__ == "__main__":
    main()
