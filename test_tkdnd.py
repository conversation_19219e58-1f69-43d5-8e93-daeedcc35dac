"""
Simple test script to verify that tkinterdnd2 is working correctly.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk

try:
    from tkinterdnd2 import TkinterDnD, DND_FILES
    print("tkinterdnd2 imported successfully")
    TKDND_AVAILABLE = True
except ImportError:
    print("Failed to import tkinterdnd2")
    TKDND_AVAILABLE = False

def main():
    print(f"Python version: {sys.version}")
    print(f"TKDND_AVAILABLE: {TKDND_AVAILABLE}")
    
    if TKDND_AVAILABLE:
        print("Creating TkinterDnD.Tk() window")
        root = TkinterDnD.Tk()
    else:
        print("Creating standard tk.Tk() window")
        root = tk.Tk()
    
    root.title("TkinterDnD Test")
    root.geometry("400x300")
    
    # Create a label
    label = ttk.Label(root, text="TkinterDnD Test Window")
    label.pack(pady=20)
    
    # Create a frame with a visible border
    frame = ttk.Frame(root, borderwidth=2, relief="solid")
    frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # Create a label inside the frame
    drop_label = ttk.Label(frame, text="Drag and drop files here")
    drop_label.pack(pady=50)
    
    if TKDND_AVAILABLE:
        # Register the frame as a drop target
        frame.drop_target_register(DND_FILES)
        
        # Define drop event handler
        def drop(event):
            print(f"Drop event received: {event.data}")
            drop_label.config(text=f"Dropped: {event.data}")
        
        # Bind the drop event
        frame.dnd_bind('<<Drop>>', drop)
        
        # Status label
        status_label = ttk.Label(root, text="Drag and drop is ENABLED", foreground="green")
        status_label.pack(pady=10)
    else:
        # Status label
        status_label = ttk.Label(root, text="Drag and drop is DISABLED", foreground="red")
        status_label.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    main()
