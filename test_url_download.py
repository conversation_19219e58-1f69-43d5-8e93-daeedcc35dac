import os
import sys
import urllib.request
import urllib.parse
import urllib.error
import ssl
import hashlib
import time

def download_image(url, output_path):
    """Simple function to download an image from a URL"""
    try:
        # Create SSL context that ignores certificate validation
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        # Set up headers to mimic a browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # Create a request with headers
        req = urllib.request.Request(url, headers=headers)
        
        # Download the image with a timeout
        with urllib.request.urlopen(req, context=ssl_context, timeout=10) as response, open(output_path, 'wb') as out_file:
            out_file.write(response.read())
            
        print(f"Successfully downloaded image to {output_path}")
        return True
    except Exception as e:
        print(f"Error downloading image: {e}")
        return False

if __name__ == "__main__":
    # Test URL
    url = "https://snworksceo.imgix.net/bdh/df3cc68a-26bb-4fdd-adf5-27cac10d62a0.sized-1000x1000.png?w=1000"
    
    # Create temp directory if it doesn't exist
    os.makedirs("./temp", exist_ok=True)
    
    # Output path
    output_path = "./temp/test_image.png"
    
    # Download the image
    success = download_image(url, output_path)
    
    if success:
        print("Test successful!")
    else:
        print("Test failed!")
