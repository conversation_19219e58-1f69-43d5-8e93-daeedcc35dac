@echo off
setlocal enabledelayedexpansion
title FramePack URL Test

REM Create temp directory if it doesn't exist
if not exist "temp" mkdir "temp"

REM Get the URL from command line
set "url=%~1"

echo Testing URL: %url%

REM Download the image
call venv\Scripts\python.exe download_url_image.py "%url%" > "temp\url_output.txt"

REM Check the result
set "found_path="
for /f "tokens=1,* delims==" %%a in (temp\url_output.txt) do (
    if "%%a"=="DOWNLOADED_PATH" (
        echo Downloaded image to: %%b
        set "downloaded_path=%%b"
        set "found_path=1"
    ) else (
        echo %%a %%b
    )
)

if not defined found_path (
    echo Error: Failed to download image from URL
    type "temp\url_output.txt"
    goto :end
)

REM Create a temporary file to store the file path
set "temp_file=temp\framepack_temp_files.txt"
if exist "%temp_file%" del "%temp_file%"
echo %downloaded_path%> "%temp_file%"

REM Launch the GUI with the file list
echo Launching GUI with file list: %temp_file%
call venv\Scripts\python.exe framepack_gui.py --file-list "%temp_file%"

REM Clean up
:end
if exist "temp\url_output.txt" del "temp\url_output.txt"
if exist "%temp_file%" del "%temp_file%"

echo Test completed.
pause
