@echo off
echo FramePack URL Processing Test
echo ============================
echo.

REM Create a temporary URL list file
echo Creating temporary URL list file...
echo https://upload.wikimedia.org/wikipedia/commons/thumb/e/ea/<PERSON>_<PERSON>gh_-_Starry_Night_-_Google_Art_Project.jpg/1280px-<PERSON>_<PERSON>_-_Starry_Night_-_Google_Art_Project.jpg > test_urls.txt
echo https://upload.wikimedia.org/wikipedia/commons/thumb/b/b9/Cas<PERSON>_<PERSON>_<PERSON>_-_<PERSON>_above_the_sea_of_fog.jpg/800px-Caspar_<PERSON>_<PERSON>_-_<PERSON>_above_the_sea_of_fog.jpg >> test_urls.txt

echo.
echo URL list created with 2 test images.
echo.

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
)

echo.
echo Running batch.py with URL list...
echo.

python batch.py --url-list test_urls.txt --output_dir output --video_length 2 --steps 10

echo.
echo Test completed.
echo.

REM Clean up the temporary URL list file
echo Cleaning up temporary URL list file...
del test_urls.txt

echo.
echo Press any key to exit...
pause > nul
