#!/usr/bin/env python3
"""
Test script to verify all video processing dependencies work in venv310
"""

def test_video_dependencies():
    print("Testing video processing dependencies in venv310...")
    print("=" * 60)

    # Test Python path
    import sys
    print("Python executable:", sys.executable)
    print("Python path entries:")
    for i, path in enumerate(sys.path):
        print(f"  {i}: {path}")
    print()

    try:
        import av
        print(f"✅ PyAV version: {av.__version__}")
    except ImportError as e:
        print(f"❌ PyAV import failed: {e}")
        return False

    # Test moviepy step by step
    try:
        import moviepy
        print(f"✅ MoviePy base imported from: {moviepy.__file__}")
    except ImportError as e:
        print(f"❌ MoviePy base import failed: {e}")
        return False

    try:
        import moviepy.editor
        print("✅ MoviePy.editor imported successfully")
    except ImportError as e:
        print(f"❌ MoviePy.editor import failed: {e}")
        print("Trying to debug moviepy.editor import...")
        try:
            import moviepy
            print(f"MoviePy version: {moviepy.__version__}")
            print(f"MoviePy location: {moviepy.__file__}")
            import os
            moviepy_dir = os.path.dirname(moviepy.__file__)
            print(f"MoviePy directory contents: {os.listdir(moviepy_dir)}")
        except Exception as debug_e:
            print(f"Debug failed: {debug_e}")
        return False
    
    try:
        import mediapipe
        print(f"✅ MediaPipe version: {mediapipe.__version__}")
    except ImportError as e:
        print(f"❌ MediaPipe import failed: {e}")
        return False
    
    try:
        import face_recognition
        print("✅ Face Recognition imported successfully")
    except ImportError as e:
        print(f"❌ Face Recognition import failed: {e}")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV version: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy version: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    print("=" * 60)
    print("🎉 ALL VIDEO PROCESSING DEPENDENCIES WORKING IN VENV310!")
    print("✅ PyAV is ready for video generation")
    print("✅ All supporting libraries are functional")
    return True

if __name__ == "__main__":
    success = test_video_dependencies()
    if success:
        print("\n🚀 Your video generation setup is complete and ready!")
    else:
        print("\n⚠️ Some dependencies are missing. Please check the errors above.")
