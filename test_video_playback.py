import os
import sys
import tkinter as tk
from tkVideoPlayer import TkinterVideo
import time
import subprocess
import cv2
import numpy as np

def create_test_video(output_path, frames=10, width=256, height=256, fps=12.5):
    """Create a simple test video with numbered frames to verify playback"""
    # Create a video writer
    fourcc = cv2.VideoWriter_fourcc(*'XVID')  # XVID codec is widely compatible
    video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    if not video_writer.isOpened():
        # Try another codec
        fourcc = cv2.VideoWriter_fourcc(*'MJPG')
        video_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Create frames with frame numbers
    for i in range(frames):
        # Create a blank frame
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add a colored rectangle that changes with each frame
        color = ((i * 25) % 255, (i * 50) % 255, (i * 100) % 255)
        cv2.rectangle(frame, (50, 50), (width-50, height-50), color, -1)
        
        # Add frame number text
        cv2.putText(frame, f"Frame {i+1}", (width//4, height//2), 
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # Write the frame
        video_writer.write(frame)
    
    # Release the video writer
    video_writer.release()
    print(f"Created test video: {output_path}")
    
    # Also create an ffmpeg version
    ffmpeg_output = output_path.replace(".mp4", "_ffmpeg.mp4")
    try:
        # Create a directory for temporary frames
        temp_dir = "temp_test_frames"
        os.makedirs(temp_dir, exist_ok=True)
        
        # Save individual frames
        for i in range(frames):
            # Create a blank frame
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            # Add a colored rectangle that changes with each frame
            color = ((i * 25) % 255, (i * 50) % 255, (i * 100) % 255)
            cv2.rectangle(frame, (50, 50), (width-50, height-50), color, -1)
            
            # Add frame number text
            cv2.putText(frame, f"Frame {i+1}", (width//4, height//2), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            # Save the frame
            frame_path = os.path.join(temp_dir, f"frame_{i:04d}.png")
            cv2.imwrite(frame_path, frame)
        
        # Use ffmpeg to create a video
        ffmpeg_cmd = [
            "ffmpeg",
            "-framerate", str(fps),
            "-i", os.path.join(temp_dir, "frame_%04d.png"),
            "-c:v", "mpeg4",  # Use the most compatible codec
            "-q:v", "1",      # Highest quality
            "-pix_fmt", "yuv420p",
            "-y",
            ffmpeg_output
        ]
        subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)
        print(f"Created ffmpeg test video: {ffmpeg_output}")
        
        # Clean up temporary frames
        for file in os.listdir(temp_dir):
            os.remove(os.path.join(temp_dir, file))
        os.rmdir(temp_dir)
    except Exception as e:
        print(f"Error creating ffmpeg test video: {e}")
    
    return output_path, ffmpeg_output

class VideoPlayerApp:
    def __init__(self, root, video_path):
        self.root = root
        self.root.title("Video Player Test")
        self.root.geometry("800x600")
        
        # Create a frame for the video player
        self.video_frame = tk.Frame(root)
        self.video_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create the video player
        self.player = TkinterVideo(self.video_frame, scaled=True)
        self.player.pack(fill="both", expand=True)
        
        # Create control buttons
        self.control_frame = tk.Frame(root)
        self.control_frame.pack(fill="x", padx=10, pady=10)
        
        self.play_button = tk.Button(self.control_frame, text="Play", command=self.play)
        self.play_button.pack(side="left", padx=5)
        
        self.pause_button = tk.Button(self.control_frame, text="Pause", command=self.pause)
        self.pause_button.pack(side="left", padx=5)
        
        self.stop_button = tk.Button(self.control_frame, text="Stop", command=self.stop)
        self.stop_button.pack(side="left", padx=5)
        
        self.reload_button = tk.Button(self.control_frame, text="Reload", command=self.reload)
        self.reload_button.pack(side="left", padx=5)
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        self.status_label = tk.Label(root, textvariable=self.status_var, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side="bottom", fill="x")
        
        # Load the video
        self.video_path = video_path
        self.load_video()
        
        # Bind events
        self.player.bind("<<Loaded>>", self.on_loaded)
        self.player.bind("<<Ended>>", self.on_ended)
        
    def load_video(self):
        try:
            self.status_var.set(f"Loading video: {self.video_path}")
            self.player.load(self.video_path)
            self.status_var.set(f"Loaded video: {self.video_path}")
        except Exception as e:
            self.status_var.set(f"Error loading video: {e}")
    
    def play(self):
        try:
            self.player.play()
            self.status_var.set("Playing")
        except Exception as e:
            self.status_var.set(f"Error playing: {e}")
    
    def pause(self):
        try:
            self.player.pause()
            self.status_var.set("Paused")
        except Exception as e:
            self.status_var.set(f"Error pausing: {e}")
    
    def stop(self):
        try:
            self.player.stop()
            self.status_var.set("Stopped")
        except Exception as e:
            self.status_var.set(f"Error stopping: {e}")
    
    def reload(self):
        try:
            self.player.stop()
            time.sleep(0.5)  # Give it time to stop
            self.load_video()
            self.player.play()
            self.status_var.set("Reloaded and playing")
        except Exception as e:
            self.status_var.set(f"Error reloading: {e}")
    
    def on_loaded(self, event):
        info = self.player.video_info()
        self.status_var.set(f"Video loaded. Duration: {info['duration']:.2f}s, FPS: {info['framerate']}")
        # Auto-play when loaded
        self.player.play()
    
    def on_ended(self, event):
        self.status_var.set("Video ended")
        # Auto-replay
        self.player.play()

def main():
    # Create test videos
    test_video_path = "test_video.mp4"
    test_video_path, ffmpeg_test_video_path = create_test_video(test_video_path)
    
    # Check if a specific video path was provided
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
    else:
        # Default to the ffmpeg test video which should be more compatible
        video_path = ffmpeg_test_video_path
    
    # Create the Tkinter app
    root = tk.Tk()
    app = VideoPlayerApp(root, video_path)
    root.mainloop()

if __name__ == "__main__":
    main()
