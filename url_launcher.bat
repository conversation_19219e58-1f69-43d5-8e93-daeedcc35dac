@echo off
setlocal enabledelayedexpansion
title FramePack URL Launcher

REM Get the directory where the batch file is located
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo ===============================================
echo           FRAMEPACK URL LAUNCHER
echo ===============================================
echo.

REM Check if Python is installed
where python >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    color 0C
    echo.
    echo Error: Python is not installed or not in your PATH.
    echo Please install Python and make sure it's added to your PATH.
    echo.
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist venv\Scripts\activate.bat (
    echo Virtual environment not found. Checking if we can create it...

    REM Try to create virtual environment
    echo Creating virtual environment...
    python -m venv venv
    if %ERRORLEVEL% NEQ 0 (
        color 0C
        echo.
        echo Error: Failed to create virtual environment.
        echo Please make sure you have the venv module installed.
        echo You may need to run: pip install virtualenv
        echo.
        pause
        exit /b 1
    )

    echo Virtual environment created successfully.
    echo Installing required packages...

    REM Activate and install requirements
    call venv\Scripts\activate.bat
    pip install -r requirements.txt
    if %ERRORLEVEL% NEQ 0 (
        color 0C
        echo.
        echo Error: Failed to install required packages.
        echo Please check your internet connection and try again.
        echo.
        pause
        exit /b 1
    )

    echo Required packages installed successfully.
) else (
    echo Virtual environment found.
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Check if URL was provided
if "%~1"=="" (
    echo No URL provided. Please provide a URL to an image.
    echo Usage: %~nx0 "https://example.com/image.jpg"
    pause
    exit /b 1
)

REM Get the URL from command line
set "url=%~1"

echo Processing URL: %url%

REM Create temp directory if it doesn't exist
if not exist "temp" mkdir "temp"

REM Download the image
call venv\Scripts\python.exe download_url_image.py "%url%" > "temp\url_output.txt"

REM Check the result
set "found_path="
for /f "tokens=1,* delims==" %%a in (temp\url_output.txt) do (
    if "%%a"=="DOWNLOADED_PATH" (
        echo Downloaded image to: %%b
        set "downloaded_path=%%b"
        set "found_path=1"
    ) else (
        echo %%a %%b
    )
)

if not defined found_path (
    echo Error: Failed to download image from URL
    type "temp\url_output.txt"
    pause
    exit /b 1
)

REM Create a temporary file to store the file path
set "temp_file=temp\framepack_temp_files.txt"
if exist "%temp_file%" del "%temp_file%"
echo %downloaded_path%> "%temp_file%"

REM Launch the GUI with the file list
echo Launching GUI with file list: %temp_file%
call venv\Scripts\python.exe framepack_gui.py --file-list "%temp_file%"

REM Clean up
if exist "temp\url_output.txt" del "temp\url_output.txt"
if exist "%temp_file%" del "%temp_file%"

echo.
echo URL processing completed successfully!
echo.
