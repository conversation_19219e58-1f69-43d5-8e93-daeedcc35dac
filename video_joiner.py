# video_joiner.py
import sys
import os
from pathlib import Path
import subprocess
import tempfile
import time
import msvcrt  # For Windows console input
import re
from datetime import datetime

# Supported video formats
SUPPORTED_FORMATS = ('.mp4', '.avi', '.mov', '.mkv', '.wmv')

def get_creation_time(file_path):
    """Get file creation time"""
    return os.path.getctime(file_path)

def generate_unique_filename(output_path):
    """Generate unique filename if conflict exists"""
    base = output_path.stem
    ext = output_path.suffix
    parent = output_path.parent
    counter = 1

    while output_path.exists():
        new_name = f"{base}_{counter}{ext}"
        output_path = parent / new_name
        counter += 1

    return output_path

def join_videos(video_files, sort_by_date=True, output_filename=None):
    """Join video files while preserving quality

    Args:
        video_files: List of video file paths
        sort_by_date: If True, sort files by creation date (default); if False, preserve input order
        output_filename: Optional custom output filename (full path)

    Returns:
        str: Path to the joined video file, or None if failed
    """
    if not video_files:
        print("No video files provided")
        return None

    # Sort files by creation date by default
    if sort_by_date:
        print("Sorting files by creation date (oldest to newest)...")
        video_files.sort(key=get_creation_time)
    else:
        print("Using files in the order provided...")

    # Get output path
    if output_filename:
        output_path = Path(output_filename)
    else:
        first_video = Path(video_files[0])
        output_name = f"{first_video.stem}_joined{first_video.suffix}"
        output_path = first_video.parent / output_name

    output_path = generate_unique_filename(output_path)

    # Create temporary file list
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        for video in video_files:
            f.write(f"file '{Path(video).absolute()}'\n")
        temp_list = f.name

    try:
        # Use FFmpeg to concatenate videos
        cmd = [
            'ffmpeg',
            '-f', 'concat',
            '-safe', '0',
            '-i', temp_list,
            '-c', 'copy',  # Copy streams without re-encoding
            '-map', '0',   # Copy all streams
            str(output_path)
        ]

        subprocess.run(cmd, check=True)
        print(f"\nVideos successfully joined: {output_path}")
        return str(output_path)

    except subprocess.CalledProcessError as e:
        print(f"\nError joining videos: {e}")
        return None
    except Exception as e:
        print(f"\nAn error occurred: {e}")
        return None
    finally:
        # Clean up temporary file
        try:
            os.unlink(temp_list)
        except:
            pass

def is_video_file(file_path):
    """Check if a file is a supported video file"""
    return os.path.isfile(file_path) and file_path.lower().endswith(SUPPORTED_FORMATS)

def find_prompt_chain_videos(output_dir, job_id=None, recent_minutes=30):
    """Find all videos generated for a specific prompt chain job or recent videos

    Args:
        output_dir: Directory containing the output videos
        job_id: The job ID to search for (timestamp-based identifier), optional
        recent_minutes: If no job_id, find videos created in the last N minutes

    Returns:
        list: List of video file paths sorted by creation time
    """
    output_path = Path(output_dir)
    if not output_path.exists():
        print(f"Output directory does not exist: {output_dir}")
        return []

    video_files = []
    current_time = time.time()

    if job_id:
        # Try exact job ID match first (with chain suffixes like _p1, _p2, etc.)
        pattern = re.compile(rf'^{re.escape(job_id)}_p\d+_\d+_\d+s\.mp4$')

        for file_path in output_path.glob("*.mp4"):
            if pattern.match(file_path.name):
                video_files.append(file_path)

        # If no chain suffix matches, try without chain suffix (legacy format)
        if not video_files:
            legacy_pattern = re.compile(rf'^{re.escape(job_id)}_\d+_\d+s\.mp4$')

            for file_path in output_path.glob("*.mp4"):
                if legacy_pattern.match(file_path.name):
                    video_files.append(file_path)

        # If still no exact matches, try partial timestamp match (same date/time prefix)
        if not video_files and len(job_id) >= 8:
            # Extract date part (YYYYMMDD) and try to match with chain suffixes
            date_part = job_id[:8]
            partial_pattern = re.compile(rf'^{re.escape(date_part)}_\d+_p\d+_\d+_\d+s\.mp4$')

            for file_path in output_path.glob("*.mp4"):
                if partial_pattern.match(file_path.name):
                    video_files.append(file_path)
    else:
        # Find recent videos if no job ID provided
        cutoff_time = current_time - (recent_minutes * 60)

        for file_path in output_path.glob("*.mp4"):
            # Check if it matches FramePack output pattern
            if re.match(r'^\d+_\d+_\d+_\d+_\d+s\.mp4$', file_path.name):
                file_time = get_creation_time(file_path)
                if file_time >= cutoff_time:
                    video_files.append(file_path)

    # Sort by creation time to maintain generation order
    video_files.sort(key=get_creation_time)

    print(f"Found {len(video_files)} videos for job {job_id or 'recent'}")
    for video in video_files:
        print(f"  - {video.name}")

    return [str(f) for f in video_files]

def join_prompt_chain_videos(output_dir, job_id=None, custom_name=None):
    """Join all videos from a prompt chain job

    Args:
        output_dir: Directory containing the output videos
        job_id: The job ID to search for (optional)
        custom_name: Optional custom name for the joined video

    Returns:
        str: Path to the joined video file, or None if failed
    """
    video_files = find_prompt_chain_videos(output_dir, job_id)

    if len(video_files) < 2:
        if job_id:
            print(f"Need at least 2 videos to join, found {len(video_files)} for job {job_id}")
            # Try finding recent videos if job ID search failed
            print("Trying to find recent videos instead...")
            video_files = find_prompt_chain_videos(output_dir, job_id=None, recent_minutes=10)

            if len(video_files) < 2:
                print(f"Still only found {len(video_files)} recent videos")
                return None
        else:
            print(f"Need at least 2 videos to join, found {len(video_files)} recent videos")
            return None

    # Create sorted subfolder for joined videos
    sorted_dir = Path(output_dir) / "sorted"
    sorted_dir.mkdir(exist_ok=True)

    # Generate output filename in the sorted subfolder
    if custom_name:
        output_filename = sorted_dir / f"{custom_name}.mp4"
    else:
        # Use timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if job_id:
            output_filename = sorted_dir / f"{job_id}_chain_joined_{timestamp}.mp4"
        else:
            output_filename = sorted_dir / f"recent_chain_joined_{timestamp}.mp4"

    print(f"Joining {len(video_files)} videos from prompt chain...")

    result = join_videos(video_files, sort_by_date=False, output_filename=str(output_filename))

    # Create completion signal for GUI detection
    if result:
        try:
            signal_file = "video_joiner_completed.signal"
            with open(signal_file, 'w') as f:
                f.write(f"SUCCESS\n{result}\n{datetime.now().isoformat()}")
            print(f"Video joiner completion signal created: {signal_file}")
        except Exception as e:
            print(f"Warning: Could not create completion signal: {e}")

    return result

def process_input(input_text, video_files):
    """Process input text to extract file paths"""
    # First, try to identify if the input contains multiple paths
    # Look for common separators or patterns in the input

    # Check if input has quotes which might indicate multiple paths
    has_quotes = '"' in input_text or "'" in input_text

    # Check for common separators
    has_separators = any(sep in input_text for sep in ['\n', ',', ';'])

    # If we have quotes or separators, try to parse multiple paths
    if has_quotes or has_separators:
        # Try to extract paths using various methods
        paths = []

        # Method 1: Split by common separators
        for separator in ['\n', ',', ';']:
            if separator in input_text:
                potential_paths = input_text.split(separator)
                for p in potential_paths:
                    p = p.strip()
                    if p:
                        paths.append(p)
                break  # Use the first separator found

        # Method 2: If no separators but has quotes, try to extract quoted paths
        if not paths and has_quotes:
            import re
            # Find all text within quotes (both single and double quotes)
            quoted_paths = re.findall(r'["\']([^"\']+)["\']', input_text)
            paths.extend(quoted_paths)

            # Also look for unquoted paths
            # Remove quoted sections first
            remaining = re.sub(r'["\'][^"\']+["\']', '', input_text)
            # Split by whitespace and filter out empty strings
            unquoted = [p for p in remaining.split() if p.strip()]
            paths.extend(unquoted)

        # If we still don't have paths, treat the whole input as one path
        if not paths:
            paths = [input_text.strip()]
    else:
        # No obvious separators, treat as a single path or space-separated paths
        paths = [p for p in input_text.split() if p.strip()]
        if not paths:
            paths = [input_text.strip()]

    # Process each path
    added = False
    for path in paths:
        path = path.strip()
        if not path:
            continue

        # Remove surrounding quotes if present
        if (path.startswith('"') and path.endswith('"')) or (path.startswith("'") and path.endswith("'")):
            path = path[1:-1]

        # Check if it's a valid video file
        if is_video_file(path):
            if path not in video_files:
                video_files.append(path)
                # Display the path with quotes to make it clear, especially for paths with spaces
                print(f'Added: "{path}" ')  # Note the space after the closing quote
                added = True
        else:
            print(f'Skipped (not a supported video file): "{path}" ')

    return added

def display_help():
    """Display help information"""
    print("\nVideo Joiner - CLI Version")
    print("==========================")
    print("Commands:")
    print("  help       - Show this help message")
    print("  list       - List all added video files")
    print("  remove N   - Remove file at position N (starting from 1)")
    print("  clear      - Clear all files")
    print("  sort       - Sort files by creation date (oldest to newest)")
    print("  join       - Join all added video files (sorted by creation date)")
    print("  preserve   - Join files in the order provided (no sorting)")
    print("  quit       - Exit the program")
    print("  [empty]    - Press Enter with no input to join videos when ready")
    print("\nSupported formats:", ", ".join(SUPPORTED_FORMATS))
    print("\nTo add files:")
    print("- Drag and drop files directly into this console window")
    print("- Type or paste the full path to a video file")
    print("- Multiple files can be separated by commas, semicolons, or newlines")
    print("\nCommand line usage:")
    print("  python video_joiner.py file1.mp4 file2.mp4 file3.mp4")
    print("  python video_joiner.py --preserve-order file1.mp4 file2.mp4")
    print("  python video_joiner.py --no-sort file1.mp4 file2.mp4")
    print("\nQuick usage:")
    print("1. Drag and drop all your video files into the window")
    print("2. Press Enter with no input to automatically join them")
    print("\nBy default, files are sorted by creation date (oldest to newest) before joining.")
    print("Use --preserve-order or --no-sort to keep files in the order provided.")

def main():
    """Main function for the CLI video joiner"""
    video_files = []
    preserve_order = False
    has_command_line_args = len(sys.argv) > 1

    # Process initial command line arguments
    for arg in sys.argv[1:]:
        if arg in ('--preserve-order', '--no-sort'):
            preserve_order = True
            print("Order preservation mode enabled - files will be joined in the order provided.")
        elif is_video_file(arg):
            video_files.append(arg)
            print(f'Added from command line: "{arg}" ')

    # If we have command line arguments and enough files, join automatically
    if has_command_line_args and len(video_files) >= 2:
        print(f"\nFound {len(video_files)} video files from command line arguments.")
        if preserve_order:
            print("Proceeding to join videos in the order provided...")
        else:
            print("Proceeding to join videos sorted by creation date (oldest to newest)...")
        result = join_videos(video_files, sort_by_date=not preserve_order)
        if result:
            print(f"\nSuccess! Videos joined: {result}")
            return  # Exit successfully
        else:
            print("\nError: Failed to join videos.")
            sys.exit(1)  # Exit with error code
    elif has_command_line_args and len(video_files) < 2:
        print(f"\nFound only {len(video_files)} video file(s) from command line arguments.")
        print("Need at least 2 video files to join.")
        sys.exit(1)  # Exit with error code

    # Display welcome message and instructions for interactive mode
    print("\nVideo Joiner - CLI Version")
    print("==========================")
    print(f"Supported formats: {', '.join(SUPPORTED_FORMATS)}")
    print("\nDrag and drop video files into this window, or type file paths.")
    print("When done adding files, just press Enter to join them.")
    print("Files will be sorted by creation date (oldest to newest) before joining.")
    print("Type 'help' for more information or 'list' to see added files.")

    # Main input loop (only for interactive mode)
    while True:
        # Show prompt with file count
        print(f"\n[{len(video_files)} files] > ", end="", flush=True)

        # Get user input
        user_input = ""
        while True:
            # Check if there's input available
            if msvcrt.kbhit():
                char = msvcrt.getwch()

                # Handle Enter key
                if char == '\r':
                    print()  # Move to next line
                    break

                # Handle backspace
                elif char == '\b':
                    if user_input:
                        user_input = user_input[:-1]
                        print("\b \b", end="", flush=True)

                # Handle other characters
                else:
                    user_input += char
                    print(char, end="", flush=True)

            # Short sleep to prevent high CPU usage
            time.sleep(0.01)

        # Process commands
        input_lower = user_input.lower().strip()

        # Empty input - if we have enough files, proceed with joining
        if not input_lower:
            if len(video_files) >= 2:
                print("\nEmpty input with files ready - proceeding to join videos...")
                print("Videos will be sorted by creation date (oldest to newest) before joining.")
                result = join_videos(video_files)  # Uses default sort_by_date=True
                if result:
                    print(f"\nSuccess! Videos joined: {result}")
                    print("Exiting after successful join.")
                    break  # Exit the loop after successful join
                else:
                    print("\nJoin failed. You can try again or type 'quit' to exit.")
            elif len(video_files) == 0:
                print("No files added yet. Add some video files first.")
            else:
                print("Need at least 2 video files to join. Add more files.")

        # Help command
        elif input_lower == 'help':
            display_help()

        # List command
        elif input_lower == 'list':
            if video_files:
                print("\nVideo files to join:")
                for i, file in enumerate(video_files, 1):
                    # Display with quotes and a space at the end to make paths with spaces clear
                    print(f'{i}. "{file}" ')
            else:
                print("\nNo video files added yet.")

        # Remove command
        elif input_lower.startswith('remove '):
            try:
                index = int(input_lower.split(' ')[1]) - 1
                if 0 <= index < len(video_files):
                    removed = video_files.pop(index)
                    print(f'Removed: "{removed}" ')
                else:
                    print(f"Invalid index. Use 1 to {len(video_files)}.")
            except (ValueError, IndexError):
                print("Invalid remove command. Format: remove N")

        # Clear command
        elif input_lower == 'clear':
            video_files.clear()
            print("All files cleared.")

        # Sort command
        elif input_lower == 'sort':
            if video_files:
                video_files.sort(key=get_creation_time)
                print("Files sorted by creation date (oldest to newest).")
            else:
                print("No files to sort.")

        # Preserve order join command
        elif input_lower in ('preserve', 'join-preserve', 'join-no-sort'):
            if len(video_files) >= 2:
                print("Joining videos in the order provided (no sorting)...")
                result = join_videos(video_files, sort_by_date=False)
                if result:
                    print(f"\nSuccess! Videos joined: {result}")
                    print("Exiting after successful join.")
                    break  # Exit the loop after successful join
                else:
                    print("\nJoin failed. You can try again or type 'quit' to exit.")
            else:
                print("Need at least 2 video files to join.")

        # Join command
        elif input_lower == 'join':
            if len(video_files) >= 2:
                print("Videos will be sorted by creation date (oldest to newest) before joining.")
                result = join_videos(video_files)  # Uses default sort_by_date=True
                if result:
                    print(f"\nSuccess! Videos joined: {result}")
                    print("Exiting after successful join.")
                    break  # Exit the loop after successful join
                else:
                    print("\nJoin failed. You can try again or type 'quit' to exit.")
            else:
                print("Need at least 2 video files to join.")

        # Quit command
        elif input_lower in ('quit', 'exit', 'q'):
            print("Exiting Video Joiner.")
            break

        # Process as file path(s)
        elif user_input.strip():
            process_input(user_input, video_files)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nProgram interrupted. Exiting.")
    except Exception as e:
        print(f"\nAn error occurred: {e}")
        # Only pause for input if not running with command line arguments
        if len(sys.argv) <= 1:
            input("\nPress Enter to exit...")
        sys.exit(1)